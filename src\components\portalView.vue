<template>
  <div class="portal-home">
    <div class="portal-head">
      <kade-header>
        <template #title>
          <div class="portal-head-logo">
            <img class="logo" :src="logo" alt="卡德智能" />
            <span class="desc">
              {{ $route.meta.label }}
            </span>
          </div>
        </template>
      </kade-header>
    </div>
    <div class="portal-body">
      <router-view></router-view>
    </div>
  </div>
</template>
<script>
// 门户视图
import Header from '@/components/header';
import Logo from '@/assets/new_logo.png';
export default {
  components: {
    'kade-header': Header,
  },
  setup() {
    return {
      logo: Logo, 
    }
  }
}
</script>
<style lang="scss">
.portal-home{
  width: 100%;
  height: 100%;
  padding-top: 50px;
  box-sizing: border-box;
  position: relative;
  .portal-head{
    width: 100%;
    position: absolute;
    left: 0;
    top: 0;
    .portal-head-logo{
      display: flex;
      justify-content: flex-start;
      align-items: center;
      .logo{
        width: 191px;
      }
      .desc{
        color: #000;;
        border-left: 1px solid #AEAEAE;
        padding-left: 20px;
        margin-left: 20px;
      }
    }
  }
  .portal-panel{
    border-radius: 12px;
    background-color: #fff;
    padding: 72px 20px 20px 20px;
    box-sizing: border-box;
    position: relative;
    .portal-panel-header{
      display: flex;
      justify-content: space-between;
      padding-bottom: 20px;
      position: absolute;
      top: 0;
      width: 100%;
      left: 0;
      padding: 20px;
      box-sizing: border-box;
      .left,.right{
        display: flex;
        align-items: center;
      }
      &.blue{
        .left .title{
          &::before{
            background: linear-gradient(90deg, rgba(59, 147, 255, 0.5) 0%, rgba(255, 255, 255, 0) 100%);
          }
          &::after{
            background-color: #3B9CFF;
          }          
        }
      }
      &.green{
        .left .title{
          &::before{
            background: linear-gradient(90deg, rgba(65, 213, 0, 0.5) 0%, rgba(255, 255, 255, 0) 100%);
          }
          &::after{
            background-color: #41D500;
          }          
        }
      }
      &.yellow{
        .left .title{
          &::before{
            background: linear-gradient(90deg, rgba(255, 157, 52, 0.5) 0%, rgba(255, 255, 255, 0) 100%);
          }
          &::after{
            background-color: #FF9D34;
          }          
        }
      }            
      .left{
        justify-content: flex-start;
        .title{
          font-size: 18px;
          color: #000;
          position: relative;
          font-weight: 600;
          padding-left: 20px;
          &::before,&::after{
            display: block;
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            height: 100%;
          }
          &::before{
            width: 80px;
            border-radius: 2px;
            opacity: 0.7;
          }
          &::after{
            width: 4px;
            height: 100%;
            border-radius: 2px;
          }
        }
        .icon{
          margin-left: 30px;
          cursor: pointer;
          i{
            color: #3B9CFF;
            margin-right: 5px;
          }
          .text{
            color: #999;
          }
        }
      }
      .right{
        justify-content: flex-end;
        .el-input__inner{
          border-radius: 16px;
          background-color: #f6f6f6;
        }
        .el-input__icon{
          color: #3B9CFF;
          font-size: 16px;
        }
        .more{
          cursor: pointer;
          .text{
            color: #3B93FF;
          }
          i{
            color: #bbb;
            margin-left: 5px;
          }
        }
      }
    } 
    .portal-panel-body{
      height: 100%;
      overflow-y: auto;
      &::-webkit-scrollbar {
        display: none;
      }
      -ms-overflow-style: none;
      scrollbar-width: none;
    }   
  }
  .portal-body{
    width: 100%;
    height: 100%;
    padding: 20px;
    box-sizing: border-box;
    background-color: $panel-main-bg;
  }
}
</style>