
<template>
  <div class="cashRecharge" v-loading="state.loading">
    <kade-route-card style="height: auto">
      <kade-table-filter @search="search()" @reset="reset()">
        <el-form inline size="mini" label-width="100px">
          <el-form-item label="结算方式:">
            <el-select v-model="state.form.settlementMethod" placeholder="请选择">
              <el-option v-for="(item, index) in settlementMethodList" :key="index" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="统计时间:">
            <el-date-picker v-model="state.requestDate" type="datetimerange" unlink-panels :default-time="state.defaultTime" range-separator="~" start-placeholder="请选择日期" end-placeholder="请选择日期">
            </el-date-picker>
          </el-form-item>
          <!-- <el-form-item label="按入账时间:">
            <el-checkbox v-model="state.timeType"></el-checkbox>
          </el-form-item> -->
        </el-form>
      </kade-table-filter>
      <kade-table-wrap title="系统结算报表">
        <template #extra>
          <el-button @click="exportClick()" icon="el-icon-daoru" size="mini" class="btn-blue">导出查询明细</el-button>
          <el-button size="mini" @click="handlePrint" class="btn-purple" icon="el-icon-printer">打印</el-button>
        </template>
        <el-table style="width: 100%" :data="state.dataList" ref="multipleTable" height="55vh" border stripe>
          <el-table-column v-for="(item,index) in column" :key="index" :width="item.width" :label="item.label" :prop="item.prop" align="center" show-overflow-tooltip>
            <template v-if="item.render" #default="scope">
              {{item.render(scope.row[item.prop],scope.row.settlementMethod)}}
            </template>
            <template v-else #default="scope">
              {{item.isDict?dictionaryFilter(scope.row[item.prop]):scope.row[item.prop]}}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150" align="center">
            <template #default="scope">
              <div class="green" v-if="!scope.row.type" @click="detailClick(scope.row)">
                查看详情
              </div>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination">
          <el-pagination background :current-page="state.form.pageNum" :page-size="state.form.pageSize" layout="total, sizes, prev, pager, next, jumper" :page-sizes="[6, 10, 20, 50, 100]" :total="state.total" @current-change="handlePageChange" @size-change="handleSizeChange">
          </el-pagination>
        </div>
      </kade-table-wrap>
    </kade-route-card>
    <kade-summary-details v-model:modelValue="state.isShow" :rowData="state.rowData" @update:modelValue="state.isShow=$event" />
  </div>
</template>
<script>
import {
  ElButton,
  ElForm,
  ElFormItem,
  ElSelect,
  ElOption,
  ElTable,
  ElTableColumn,
  ElPagination,
  ElDatePicker,
} from "element-plus";
import { timeStr, monthStr } from "@/utils/date.js";
import { reactive } from "@vue/reactivity";
import { downloadXlsx, print } from "@/utils";
import { useDict } from "@/hooks/useDict.js";
import {
  systemSettlement,
  systemSettlementExport,
  systemSettlementPrint,
  systemSettlementDetail,
} from "@/applications/eccard-finance/api";
import { onMounted } from "@vue/runtime-core";
import details from "./components/details.vue";
const column = [
  { label: "结算方式", prop: "settlementMethod", width: "", isDict: true },
  {
    label: "结算时间",
    prop: "settlementDate",
    width: "170",
    render: (val, type) => {
      console.log(val, type);
      if (type == "YEAR_SETTLEMENT") {
        return val && new Date(val).getFullYear()
      } else if (type == "MONTH_SETTLEMENT") {
        return val && monthStr(val)
      } else {
        return val && timeStr(val)
      }
    },

  },
  { label: "期初结转", prop: "lastAmount", width: "", render: (val) => typeof val == 'number' ? val.toFixed(2) : (val ? val : '--'), },
  { label: "本期收入", prop: "nowIncomeAmount", width: "", render: (val) => typeof val == 'number' ? val.toFixed(2) : (val ? val : '--'), },
  { label: "本期支出", prop: "nowExpendAmount", width: "", render: (val) => typeof val == 'number' ? val.toFixed(2) : (val ? val : '--'), },
  { label: "本期结余", prop: "nowBalanceAmount", width: "", render: (val) => typeof val == 'number' ? val.toFixed(2) : (val ? val : '--'), },
  { label: "期末结余", prop: "endBalanceAmount", width: "", render: (val) => typeof val == 'number' ? val.toFixed(2) : (val ? val : '--'), },
];
export default {
  components: {
    "el-button": ElButton,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    "el-select": ElSelect,
    "el-option": ElOption,
    "el-table": ElTable,
    "el-table-column": ElTableColumn,
    "el-pagination": ElPagination,
    ElDatePicker,
    "kade-summary-details": details,
  },
  setup() {
    const settlementMethodList = useDict("SYSTEM_SETTLEMENT_METHOD");
    const state = reactive({
      loading: false,
      isShow: false,
      // timeType: true,
      selectName: "",
      form: {
        settlementMethod: "DAILY_SETTLEMENT",
        pageNum: 1,
        pageSize: 10,
      },
      total: 0,
      requestDate: [],
      defaultTime: [
        new Date(new Date(new Date().toLocaleDateString()).getTime()),
        new Date(
          new Date(new Date().toLocaleDateString()).getTime() +
          24 * 60 * 60 * 1000 -
          1
        ),
      ],
      rowData: {},
    });


    const getList = async () => {
      if (state.requestDate && state.requestDate.length) {
        state.form.startSettlementDate = timeStr(state.requestDate[0]);
        state.form.endSettlementDate = timeStr(state.requestDate[1]);
      } else {
        delete state.form.startSettlementDate;
        delete state.form.endSettlementDate;
      }
      state.loading = true;
      // state.form.timeType = state.timeType ? 2 : 1;
      try {
        let { data: { generate, pageInfo: { list, total } } } = await systemSettlement(state.form);
        state.dataList = list
        state.total = total
        if (list.length) {
          generate.settlementMethod = "合计"
          state.dataList.push({ ...generate, type: 1 })
        }
        state.loading = false;
      }
      catch {
        state.loading = false;
      }

    };
    const detailClick = async (row) => {
      state.loading = true;
      try {
        let { data, code } = await systemSettlementDetail({ settlementDate: timeStr(row.settlementDate), settlementMethod: row.settlementMethod })
        if (code === 0) {
          state.rowData = data
          state.isShow = true
        }
        state.loading = false;
      }
      catch {
        state.loading = false;
      }
    }
    const search = () => {
      state.form.pageNum = 1;
      state.form.pageSize = 10;
      getList();
    };

    const exportClick = async () => {
      state.loading = true
      try {
        let res = await systemSettlementExport(state.form);
        downloadXlsx(res, "系统结算报表.xlsx")
        state.loading = false
      }
      catch {
        state.loading = false
      }

    };

    const handlePrint = async () => {
      state.loading = true
      try {
        let { data, code } = await systemSettlementPrint(state.form)
        if (code === 0) {
          print(data)
        }
        state.loading = false
      }
      catch {
        state.loading = false

      }

    }
    const handlePageChange = (val) => {
      state.form.pageNum = val;
      getList();
    };
    const handleSizeChange = (val) => {
      state.form.pageNum = 1;
      state.form.pageSize = val;
      getList();
    };
    const handleClose = () => {
      state.dialogVisible = false;
    };
    const reset = () => {
      state.form = {
        settlementMethod: "DAILY_SETTLEMENT",
        pageNum: 1,
        pageSize: 10,
      };
      state.requestDate = [];
    };
    onMounted(() => {
      getList();
    });
    return {
      column,
      settlementMethodList,
      state,
      timeStr,
      handleClose,
      exportClick,
      handlePrint,
      getList,
      detailClick,
      search,
      handlePageChange,
      handleSizeChange,
      reset,
    };
  },
};
</script>
<style lang="scss" scoped>
:deep(.el-divider--horizontal) {
  margin: 0 0 10px 0;
}
.cashRecharge {
  min-height: 680px;
  :deep(.search-box) {
    .el-input__inner {
      width: 193px;
    }
    .el-date-editor {
      width: 400px;
    }
  }
}
:deep(.el-dialog__header) {
  border-bottom: 1px solid #efefef;
}
:deep(.el-dialog__headerbtn) {
  font-size: 20px;
  top: 10px;
  right: 10px;
}
:deep(.el-dialog__header) {
  padding: 10px 20px;
}
:deep(.el-overlay) {
  position: absolute;
  background-color: rgba(0, 0, 0, 0.2);
}
:deep(.el-dialog__footer) {
  text-align: center;
}
:deep(.el-dialog__title) {
  font-size: 14px;
}
</style>