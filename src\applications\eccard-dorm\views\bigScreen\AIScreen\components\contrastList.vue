<template>
  <div class="contrast-list">
    <div class="contrast-list-title">未归人员</div>
    <el-carousel height="100%" direction="vertical" :autoplay="true" :interval="5000" indicator-position="none">
      <el-carousel-item v-for="item in 4" :key="item" style="color:#fff">
        <div class="person-box">
          <div class="person-item" v-for="(item, index) in 5" :key="index">
            <img class="img1" src="" alt="">
            <img class="img2" src="" alt="">
            <div class="msg">
              <div class="name">西瓜</div>
              <div class="group">一年级三班</div>
              <div class="room">101</div>
              <div class="date">2022-03-01 22:10:00</div>
              <div class="status">体温异常</div>
            </div>
          </div>
        </div>
      </el-carousel-item>
    </el-carousel>
  </div>
</template>
<script>
import { ElCarousel, ElCarouselItem } from "element-plus"
export default {
  components: {
    ElCarousel, ElCarouselItem
  },
  setup() {

  }
}
</script>
<style lang="scss" scoped>
.contrast-list {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .contrast-list-title {
    font-weight: 700;
    font-size: 18px;
    color: #0EE4F9;
    margin: 5px 10px 10px;
  }

  .person-box {
    background: #001034;

    width: 100%;
    height: 100%;
    overflow-y: auto;

    .person-item {
      box-sizing: border-box;
      padding: 12px;
      // background: #08266f;
      display: flex;
      align-items: center;

      &:nth-child(2n-1) {
        background: rgba(215, 215, 215, 0.196);
      }

      .img1 {
        width: 130px;
        height: 146px;
        margin-right: 10px;
      }

      .img2 {
        width: 107px;
        height: 120px;
      }

      .msg {
        flex: 1;
        text-align: center;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;

        .name {
          font-weight: 700;
          font-size: 20px;
          color: #fff;
        }

        .group {
          font-size: 13px;
          margin: 10px 0;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
        }

        .room {
          font-weight: 600;
          font-size: 18px;
          color: #fff;
        }

        .date {
          font: 14px/28px arial;
        }

        .status {
          font-size: 20px;
          color: rgb(217, 0, 27);
          line-height: 28px;
        }
      }
    }
  }

}

.el-carousel {
  width: 100%;
  height: 100%;
}
</style>