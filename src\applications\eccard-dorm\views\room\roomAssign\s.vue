<template>
  <div class="padding-box">
    <kade-table-wrap>
      <div class="main-box">
        <div class="area">
          <el-tree class="area-tree" :data="state.areaCheckTreeList" :props="defaultProps" default-expand-all @node-click="handleNodeClick">
            <template #default="{ node }">
              <div class="custom-tree-node hasbtn">
                <span class="left">
                  <i class="el-icon-location-information"></i>
                  <span style="margin-left: 5px; color: #333">{{node.label}}</span>
                </span>
              </div>
            </template>
          </el-tree>
        </div>
        <div class="room">
          <el-form label-width="78px" class="form-box" size="mini" inline>
            <el-form-item label="区域">
              <kade-area-select-tree
                style="width: 100%"
                :value="state.form.areaId"
                valueKey="id"
                :multiple="false"
                @valueChange="(val) => (state.form.areaId = val.id)"
              />
            </el-form-item>
            <el-form-item label="所属楼栋">
              <el-select>
                <el-option v-for="(item,index) in 10" :key="index" :label="item" :value="index"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="所属单元">
              <el-select>
                <el-option v-for="(item,index) in 10" :key="index" :label="item" :value="index"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="组织机构">
              <kade-dept-select-tree
                style="width: 100%"
                :value="state.form.deptPath"
                valueKey="deptPath"
                :multiple="false"
                @valueChange="(val) => (state.form.deptPath = val.deptPath)"
              />
            </el-form-item>
            <el-form-item label="分配状态">
              <el-select>
                <el-option v-for="(item,index) in 10" :key="index" :label="item" :value="index"></el-option>
              </el-select>
            </el-form-item>
            <el-button icon="el-icon-search" type="primary" size="mini" style="margin-left:20px">搜索</el-button>
          </el-form>
          <div class="bread-box">
            <div class="bread">计算机学院>网络技术>2022级>2班（男生：100人，女生100人，共200人）</div>
            <div class="button-box">
              <el-button size="mini" type="primary" @click="handleExport">导入分配</el-button>
              <el-button size="mini" type="primary">自动分配</el-button>
              <el-button size="mini" type="success">保存分配</el-button>
            </div>
          </div>
          <div class="building" v-for="(item,index) in 5" :key="index">
            <div class="building-header">
              <div><span class="el-icon-office-building blue" style="font-size:20px"></span>1楼</div>
              <div>房间数：6/10</div>
            </div>
            <div class="building-box">
              <div class="building-item" v-for="(v,i) in 10" :key="i" :style="{backgroundColor:i>5?'#0079feb0':'#aaaaaab0'}">
                <div class="box-top">
                  <el-checkbox v-model="checked1" label="101房间" size="large"></el-checkbox>
                  <div>床位：0/6</div>
                </div>
                <div class="box-bottom">计算机专业2021级3班</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </kade-table-wrap>
    <kade-room-export :isShow="state.isExport" @close="state.isExport=false"/>
  </div>
</template>
<script>
import { ElTree,ElButton,ElForm,ElFormItem,ElSelect,ElOption,ElCheckbox } from "element-plus";
import { onMounted, reactive } from '@vue/runtime-core';
import { makeTree } from "@/utils/index.js";
import { getUserAreaPurview } from "@/applications/eccard-basic-data/api";
import areaSelectTree from "@/components/tree/areaSelectTree.vue";
import deptSelectTree from "@/components/tree/deptSelectTree.vue";
import roomExport from "./components/export"
const defaultProps = {
  children: "children",
  label: "areaName",
};
export default {
  components: {
    ElTree,
    ElButton,
    ElForm,
    ElFormItem,
    ElSelect,
    ElOption,
    ElCheckbox,
    "kade-area-select-tree": areaSelectTree,
    "kade-dept-select-tree": deptSelectTree,
    "kade-room-export":roomExport
  },
  setup() {
    const state=reactive({
      isExport:false,
      areaCheckTreeList:[],
      form:{}
    })
    const queryAreaCheckList = () => {
      getUserAreaPurview().then((res) => {
        let arr = makeTree(res.data, "id", "areaParentId", "children");
        state.areaCheckTreeList = [...arr];
      });
    };
    const handleExport=()=>{
      state.isExport=true
    }
    onMounted(()=>{
      queryAreaCheckList()
    })
    return {
      defaultProps,
      state,
      handleExport
    };
  },
};
</script>
<style lang="scss" scoped>
.kade-table-wrap {
  padding-bottom: 0px;
  min-width: 1170px;
}
.main-box {
  min-width: 1170px;
  height: 75vh;
  display: flex;
  border-top: 1px solid #eeeeee;
  .area {
    width: 240px;
    min-width: 240px;
    height: 100%;
    border-right: 1px solid #eeeeee;
    padding: 10px;
    box-sizing: border-box;
  }
  .room {
    flex: 1;
    min-width: 930px;
    height: 100%;
    overflow-y: scroll;
    .form-box{
      padding: 10px;
      padding-bottom: 0;
      border-bottom: 1px solid #eeeeee;
      .el-form-item{
        margin-bottom: 10px;
      }
    }
    .bread-box{
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 5px 20px;
      border-bottom: 1px solid #eeeeee;
      margin-bottom: 10px;
      .bread{
        line-height: 40px;
      }
    }
    .building{
      padding: 0px 20px;
      .building-header{
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 5px;
        border-bottom: 1px solid #000;
        margin-bottom: 10px;
      }
      .building-box{
        color: #fff;
        display: flex;
        align-items: center;
        // justify-content: space-between;
        flex-wrap: wrap;
        .building-item{
          box-sizing: border-box;
          width: 200px;
          padding:10px 20px;
          margin-right: 20px;
          margin-bottom: 10px;
          border-radius: 20px;
          background: #0079feb0;
          .box-top{
            display: flex;
            justify-content: space-between;
            align-items: center;
          }
          .box-bottom{
            text-align: center;
            margin-top: 20px;
          }
        }
        
      }
    }

  }
}
:deep(.el-checkbox__label){
  color: #fff;
}
:deep(.el-dialog__header) {
  border-bottom: 1px solid #efefef;
}

:deep(.el-overlay) {
  position: absolute;
  background-color: rgba(0, 0, 0, 0.3);
}

:deep(.el-dialog__footer) {
  text-align: center;
}
:deep(.el-dialog__body){
  padding-bottom: 20px !important;
}
</style>