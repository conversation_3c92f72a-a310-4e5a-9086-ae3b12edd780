<template>
  <kade-route-card>
    <kade-table-filter @search="handleSearch" @reset="handleReset">
        <el-form inline label-width="90px" size="mini">
          <kade-linkage-select :data="linkageData" :value="state.form" @change="linkageChange" />
          <el-form-item label="人员编号">
            <el-input clearable v-model="state.form.userCode" placeholder="请输入">
            </el-input>
          </el-form-item>
          <el-form-item label="人员姓名">
            <el-input clearable v-model="state.form.userName" placeholder="请输入">
            </el-input>
          </el-form-item>
          <el-form-item label="设备类型">
            <el-select clearable v-model="state.form.type" placeholder="全部">
            <el-option v-for="(item,index) in []" :key="index" :label="item.label" :value="item.value"></el-option>
          </el-select>
          </el-form-item>
          <el-form-item label="识别时间">
            <el-date-picker
              v-model="state.requestDate"
              type="datetimerange"
              range-separator="~"
              start-placeholder="请选择开始时间"
              end-placeholder="请选择结束时间"
              :defaultTime="defaultTime"
            >
            </el-date-picker>
          </el-form-item>
        </el-form>
    </kade-table-filter>
    <kade-table-wrap title="考勤原始记录列表">
      <template #extra>
          <el-button class="btn-purple" icon="el-icon-bottom" type="success" size="mini" @click="exportClick()" >导出</el-button>
        </template>
      <el-table border :data="state.data" v-loading="state.loading">
        <el-table-column prop="areaName" label="区域" align="center"></el-table-column>
        <el-table-column  prop="roomString" label="房间" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column prop="userCode" label="人员编号" align="center"></el-table-column>
        <el-table-column prop="userName" label="人员姓名" align="center"></el-table-column>
        <el-table-column prop="deviceType" label="设备类型" align="center"></el-table-column>
        <el-table-column prop="deviceName" label="设备名称" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column prop="attendanceTime" label="识别时间" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column prop="createTime" label="采集时间" align="center" show-overflow-tooltip></el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination
          v-model:currentPage="state.form.currentPage"
          v-model:page-size="state.form.pageSize"
          :page-sizes="[10, 20, 30, 40]"
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total=state.total
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </div>
    </kade-table-wrap>
  </kade-route-card>
</template>

<script>
import {
  ElButton,
  ElForm,
  ElFormItem,
  ElSelect,
  ElOption,
  ElTable,
  ElInput,
  ElTableColumn,
  ElPagination,
  ElDatePicker,
} from "element-plus";
import { reactive } from "@vue/reactivity";
import {timeStr} from "@/utils/date.js";
import { onMounted } from '@vue/runtime-core';
import linkageSelect from '@/applications/eccard-dorm/components/linkageSelect.vue'
import { attendanceRecordList,exportAttendanceRecord } from '@/applications/eccard-dorm/api.js'
const defaultTime=[
  new Date(new Date(new Date().toLocaleDateString()).getTime()),
        new Date(
            new Date(new Date().toLocaleDateString()).getTime() +
            24 * 60 * 60 * 1000 -
            1
        )
]
const linkageData = {
  area: { label: '区域', valueKey: 'areaPath', key: 'areaPath' },
  building: { label: '楼栋', valueKey: 'buildId' },
  unit: { label: '单元', valueKey: 'unitNum' },
  floor: { label: '楼层',valueKey:'floorNum' }
}
export default {
  components: {
    ElButton,
    ElForm,
    ElFormItem,
    ElSelect,
    ElOption,
    ElTable,
    ElInput,
    ElTableColumn,
    ElPagination,
    ElDatePicker,
    'kade-linkage-select':linkageSelect
  },
  setup() {
    const state = reactive({
      form:{
        currentPage:1,
        pageSize:10
      },
      total:0,
      data:[],
      requestDate:[],
      loading:false
    });
    const linkageChange=(val)=>{
      state.form={...state.form,...val}
    }
    const getList=async()=>{
        if(state.requestDate&&state.requestDate.length){
        state.form.beginDate=timeStr(state.requestDate[0])
        state.form.endDate=timeStr(state.requestDate[1])
      }else{
        delete state.form.beginDate
        delete state.form.endDate
      }
      state.loading=true
      try{
        let{data:{list,total}}=await attendanceRecordList(state.form)
        state.data=list
        state.total=total
        state.loading=false
      }
      catch{
        state.loading=false
      }
    };
    const handleSizeChange = (val) => {
      state.form.currentPage=1
      state.form.pageSize=val
      getList()
    };
    const handleCurrentChange = (val) => {
      state.form.currentPage=val
      getList()
    };
    const handleSearch =()=>{
      getList()
    };
    const handleReset =()=>{
      state.form={
        currentPage:1,
        pageSize:10
      }
      state.requestDate=[]
      getList()
    };
    const exportClick=async()=>{
      let res = await exportAttendanceRecord(state.form)
      let url = window.URL.createObjectURL(
        new Blob([res], { type: 'application/vnd.ms-excel' })
      )
      let link = document.createElement('a')
      link.href = url
      link.style.display = 'none'
      link.setAttribute('download','考勤原始记录表.xlsx')
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }
    onMounted(()=>{
      getList();
    })
    return {
      state,
      handleSizeChange,
      handleCurrentChange,
      getList,
      handleSearch,
      linkageChange,
      linkageData,
      handleReset,
      exportClick,
      defaultTime
    };
  },
};
</script>

<style lang="scss" scoped>
:deep(.el-icon-time:before){
    display: none;
}
:deep(.el-input--mini .el-input__inner){
  width: 193px;
}
:deep(.el-pagination .el-select .el-input .el-input__inner){
  width: 100px;
}
:deep(.el-pagination__editor.el-input .el-input__inner){
  width: 46px;
}
</style>