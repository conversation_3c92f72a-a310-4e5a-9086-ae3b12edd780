<template>
  <kade-table-wrap title="用户列表">
    <template #extra>
      <el-button icon="el-icon-edit" size="small" @click="handleBtnClick" class="shop-add">新增商户</el-button>

      <el-upload style="margin:0 10px" :headers="{ Authorization: `bearer ${state.uploadHeader}` }" class="upload-demo" :show-file-list="false" :action="state.uploadUrl" :on-success="uploadSuccess">
        <el-button icon="el-icon-daoru" size="small" class="shop-upload">导入</el-button>
      </el-upload>
      <el-button @click="handleExport" icon="el-icon-daochu" size="small" class="shop-out">导出</el-button>
    </template>
    <el-table style="width: 100%" :data="options.dataList" v-loading="false" highlight-current-row border stripe @current-change="(v) => $emit('on-select-change', v)" @selection-change="(v) => $emit('on-check-change', v)">
      <el-table-column show-overflow-tooltip label="商户编号" prop="merchantNo" align="center"></el-table-column>
      <el-table-column show-overflow-tooltip label="商户名称" prop="merchantName" align="center"></el-table-column>
      <el-table-column show-overflow-tooltip label="商户类型" prop="merchantType" align="center">
        <template #default="scope">
          {{ dictionaryFilter(scope.row.merchantType) }}
        </template>
      </el-table-column>
      <el-table-column show-overflow-tooltip label="所属区域" prop="areaName" align="center"></el-table-column>
      <el-table-column show-overflow-tooltip label="终端总量" prop="deviceTotalCount" align="center"></el-table-column>
      <el-table-column show-overflow-tooltip label="联系人" prop="merchantContacts" align="center"></el-table-column>
      <el-table-column show-overflow-tooltip label="联系电话" prop="merchantTel" align="center"></el-table-column>
      <el-table-column label="操作" align="center" width="240">
        <template #default="scope">
          <el-button type="text" style="margin-right: 10px" @click="handleBtnClick(scope.row, true)" size="mini">查看详情</el-button>
          <el-button type="text" style="margin-right: 10px" @click="handleBtnClick(scope.row)" size="mini">编辑</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="pagination">
      <el-pagination background v-model:current-page="options.currentPage" layout="total, sizes, prev, pager, next, jumper" :page-sizes="[10, 20, 50, 100, 200]" :total="options.total" v-model:page-size="options.pageSize" @current-change="handlePageChange" @size-change="handlePageChange">
      </el-pagination>
    </div>
  </kade-table-wrap>
</template>
<script>
import {ElUpload, ElTable, ElTableColumn, ElButton, ElPagination,ElMessage } from "element-plus";
import { ref, reactive } from "vue";
import { useStore } from "vuex";
import { getToken,downloadXlsx } from "@/utils";
import { getMerchantListToExport } from "@/applications/eccard-finance/api";
export default {
  components: {
    ElUpload,
    "el-table": ElTable,
    "el-button": ElButton,
    "el-table-column": ElTableColumn,
    "el-pagination": ElPagination,
  },
  props: {
    tableOptions: {
      type: Object,
      default: () => {
        return {};
      },
    },
    params:{
      type:Object,
      default:()=>{
        return {}
      }
    },
    loadData: {
      type: Function,
      default: () => {},
    },
    
  },
  setup(props, context) {
    const store = useStore();
    const showCreateModal = ref(false);
    const showAuthModal = ref(false);
    const state = reactive({
      user: {},
      uploadHeader: getToken(),
      uploadUrl: `${CONFIG.BASE_API_PATH}eccard-merchant/Basic/importMerchantList`,
    });
    const uploadSuccess = ({ code, message }) => {
      if (code === 0) {
        ElMessage.success(message);
        props.loadData();
      } else {
        ElMessage.error(message);
      }
    };
    const handleExport=async ()=>{
      let res=await getMerchantListToExport(props.params)
      downloadXlsx(res,'商户列表.xlsx')
    }

    const addUser = () => {
      state.user = {};
      showCreateModal.value = true;
    };
    const editUser = (user) => {
      state.user = user;
      showCreateModal.value = true;
    };
    const handlePageChange = () => {
      context.emit("on-page-change");
      props.loadData();
    };
    const handleBtnClick = (row, isInfo) => {
      let query = {};
      console.log("row", row);
      if (row) {
        query = { id: row.merchantId };
      }
      if (isInfo) {
        query.type = "info";
      }
      store.dispatch("app/addTab", {
        id: `ShopEdit${row?.merchantId || ""}`,
        payload: {
          menuName: "编辑商户",
          menuEnName: "ShopEdit",
          query,
        },
      });
    };
    return {
      showCreateModal,
      showAuthModal,
      state,
      options: props.tableOptions,
      addUser,
      editUser,
      uploadSuccess,
      handleExport,
      handleBtnClick,
      handlePageChange,
    };
  },
};
</script>
<style lang="scss" scoped>
.el-botton {
  color: #fff;
}

.shop-add {
  background: rgb(2, 210, 0, 1);
  color: #fff;
}

.shop-add:hover {
  background: rgba(9, 176, 5, 1);
}

.shop-upload {
  background: rgba(38, 158, 255, 1);
  color: #fff;
}
.shop-upload:hover {
  background: rgba(49, 56, 223, 1);
}

.shop-out {
  background: rgba(84, 95, 255, 1);
  color: #fff;
}
.shop-out:hover {
  background: rgba(49, 56, 223, 1);
}

.el-table {
  border-left: none;
  tr > th:last-child,
  tr > td:last-child {
    border-right: none !important;
  }
  .el-table--border,
  .el-table--group {
    border: none;
  }
  &::after {
    background-color: transparent;
  }
}
</style>