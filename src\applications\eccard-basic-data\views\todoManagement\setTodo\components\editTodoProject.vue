<template>
  <el-dialog :model-value="isShow" :title="(!data?'新增':'编辑')+'待办项目'" width="640px" :before-close="beforeClose">
    <div style="padding-top: 20px">
      <el-form size="mini" label-width="120px" ref="formDom" :rules="state.rules" :model="state.form">
        <el-form-item label="待办类型:" prop="typeId">
          <el-select v-model="state.form.typeId">
            <el-option v-for="(item,index) in todoTypeList" :key="index" :label="item.name" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="待办编码:" prop="code">
          <el-input placeholder="请输入" v-model="state.form.code" :maxlength="20"></el-input>
        </el-form-item>
        <el-form-item label="待办项目名称:" prop="name">
          <el-input placeholder="请输入" v-model="state.form.name" :maxlength="20"></el-input>
        </el-form-item>
        <el-form-item label="处理方式:" prop="mode">
          <el-select v-model="state.form.mode">
            <el-option v-for="(item,index) in handleMode" :key="index" :balel="item.label" :value="item.label"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否启用:">
          <el-switch v-model="state.form.status" active-value="TRUE" inactive-value="FALSE"></el-switch>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="beforeClose" size="mini">取消</el-button>
        <el-button type="primary" @click="submit()" size="mini">提交</el-button>
      </span> 
    </template>
  </el-dialog>
</template>
<script>
import {
  ElDialog,
  ElForm,
  ElFormItem,
  ElInput,
  ElSwitch,
  ElButton,
  ElSelect,
  ElOption,
  ElMessage,
} from "element-plus";
import { reactive, ref,nextTick,watch } from "vue";
import { addTodoProject,editTodoProject } from "@/applications/eccard-basic-data/api";
const handleMode=[
  {label:"超链接处理"},
  {label:"确认处理"},
]
export default {
  components: {
    ElDialog,
    ElForm,
    ElFormItem,
    ElInput,
    ElSwitch,
    ElSelect,
    ElOption,
    ElButton,
  },
  props:{
    isShow:{
      type:Boolean,
      default:false
    },
    type:{
      type:String,
      default:"add"
    },
    todoTypeList:{
      type:Array,
      default:null,
    },
    data:{
      type:Object,
      default:null
    }
  },
  setup(props,context) {
    const formDom = ref(null);
    const state = reactive({
      title: "",
      form: {
        status:'TRUE'
      },
      rules: {
        typeId: [
          {
            required: true,
            message: "请选择待办类型",
            trigger: "change",
          },
        ],
        code: [
          {
            required: true,
            message: "请输入待办编码",
            trigger: "blur",
          },
        ],
        name: [
          {
            required: true,
            message: "请输入待办类型名称",
            trigger: "blur",
          },
        ],
        mode: [
          {
            required: true,
            message: "请选择处理方式",
            trigger: "change",
          },
        ],
      },
    });
    watch(()=>props.isShow,val=>{
      if(!val){
        nextTick(()=>{
          formDom.value.clearValidate()
        })
      }
      if(props.data){
        state.form={...props.data}
      }else{
        state.form={
          status:'TRUE'
        }
      }
    })
    const submit = () => {
      formDom.value.validate(async (valid) => {
        if (valid) {
          let fn=props.data?editTodoProject:addTodoProject
          let {code,message}=await fn(state.form)
          if(code===0){
            ElMessage.success(message)
            context.emit("close",state.form.typeId)
          }
        } else {
          return false;
        }
      });
    };

    const beforeClose = () => {
      context.emit("close",false)
    };
    return {
      handleMode,
      formDom,
      state,
      submit,
      beforeClose,
    };
  },
};
</script>
<style lang="scss" scoped>
  :deep(.el-select){
    width: 100%;
  }

</style>