import "@babel/polyfill";
import { createApp } from 'vue';
import App from '@/components/app';
import installElementPlus from '@/plugins/element';
import ErrorApp from '@/components/error';
import router from './router';
import store from './store';
import { routeAuthentication, initApp } from '@/utils';
import Icon from '@/components/icon';
import '@/assets/styles/style.scss';
import 'element-plus/lib/theme-chalk/display.css';

routeAuthentication({ router, store }).then(() => {
  const app = createApp(App).use(store).use(router);
  app.component(Icon.name, Icon);
  installElementPlus(app);
  initApp(app, store);
  app.mount('#app');
}).catch(e => {
  const errorApp = createApp(ErrorApp, {
    message: e.message,
  });
  errorApp.mount('#app');
});