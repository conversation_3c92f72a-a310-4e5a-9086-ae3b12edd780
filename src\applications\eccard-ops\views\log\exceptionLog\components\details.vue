<template>
  <div class="login-details">
    <el-dialog
      :model-value="dialogVisible"
      title="异常日志详情"
      width="60%"
      :before-close="handleClose"
    >
      <el-form label-width="120px" inline size="mini">
        <el-form-item
          :label="item.label"
          v-for="(item, index) in state.formList"
          :key="index"
        >
          <el-input
            disabled
            :model-value="
              item.isDate
                ? timeStr(selectRow[item.filed])
                : selectRow[item.filed]
            "
          ></el-input>
        </el-form-item>
        <div class="last-input">
          <el-form-item label="请求地址：">
            <el-input disabled :model-value="selectRow['uri']"></el-input>
          </el-form-item>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { reactive } from "vue";
import { ElDialog, ElForm, ElFormItem, ElInput } from "element-plus";
import { timeStr } from "@/utils/date";
export default {
  components: {
    ElDialog,
    ElForm,
    ElFormItem,
    ElInput,
  },
  props: {
    dialogVisible: {
      types: Boolean,
      default: false,
    },
    selectRow: {
      types: Object,
      default: {},
    },
  },
  setup(props, context) {
    const state = reactive({
      formList: [
        {
          label: "日志ID:",
          filed: "id",
        },
        {
          label: "所属应用：",
          filed: "app",
        },
        {
          label: "功能模块：",
          filed: "bizModule",
        },
        {
          label: "操作类型：",
          filed: "operateType",
        },
        {
          label: "请求方式：",
          filed: "requestMethod",
        },
        {
          label: "请求参数：",
          filed: "requestParam",
        },
        {
          label: "返回参数：",
          filed: "result",
        },
        {
          label: "操作员账号：",
          filed: "operateAccount",
        },

        {
          label: "操作员姓名：",
          filed: "operateName",
        },
        {
          label: "客户端IP：",
          filed: "ip",
        },
        {
          label: "操作时间：",
          filed: "operateTime",
          isDate: true,
        },
        {
          label: "客户端系统：",
          filed: "os",
        },

        {
          label: "浏览器名称：",
          filed: "browser",
        },
        {
          label: "响应时间：",
          filed: "spendTime",
        },
      ],
    });

    const handleClose = () => {
      context.emit("close");
    };

    return {
      state,
      timeStr,
      handleClose,
    };
  },
};
</script>
<style lang="scss">
.login-details {
  position: relative;
  z-index: 1000;
  .el-dialog__header {
    border-bottom: 1px solid #ccc;
  }
  .el-form {
    margin-top: 20px;
  }
  .last-input{
      .el-form-item__content{
          width:850px
      }
      .el-input__inner{
          width: 100%;
      }
  }
}
</style>