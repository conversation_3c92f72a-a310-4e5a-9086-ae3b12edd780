<template>
  <div class="padding-box">
    <kade-table-wrap title="时段列表">
      <template #extra>
        <el-button type="success" size="mini" icon="el-icon-plus" @click="handleEdit('add')">新增</el-button>
        <el-button type="primary" size="mini" icon="el-icon-edit" @click="handleEdit('edit')">编辑</el-button>
        <el-button type="danger" size="mini" icon="el-icon-delete-solid" @click="handleDelete()">删除</el-button>
      </template>
      <el-divider></el-divider>
      <div style="padding:10px">
        <el-alert style="margin-bottom:10px" title="提示：1号时段为每天的任意时间有效；0号时段为完全禁止【这两个时段为系统内置，不可新增】" :closable="false" />
        <el-table border height="64vh" ref="tableRef" v-loading="state.loading" :data="state.dataList"
          @row-click="handleChange" highlight-current-row="state.highlight">
          <el-table-column show-overflow-tooltip prop="periodNo" label="时段号" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip prop="monday" label="星期一" align="center">
            <template #default="scope">
              <el-switch disabled :model-value="scope.row.monday" active-color="#13ce66" inactive-color="#ff4949"
                :active-value="1" :inactive-value="0" />
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip label="星期二" align="center">
            <template #default="scope">
              <el-switch disabled :model-value="scope.row.tuesday" active-color="#13ce66" inactive-color="#ff4949"
                :active-value="1" :inactive-value="0" />
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip label="星期三" align="center">
            <template #default="scope">
              <el-switch disabled :model-value="scope.row.wednesday" active-color="#13ce66" inactive-color="#ff4949"
                :active-value="1" :inactive-value="0" />
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip label="星期四" align="center">
            <template #default="scope">
              <el-switch disabled :model-value="scope.row.thursday" active-color="#13ce66" inactive-color="#ff4949"
                :active-value="1" :inactive-value="0" />
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip label="星期五" align="center">
            <template #default="scope">
              <el-switch disabled :model-value="scope.row.friday" active-color="#13ce66" inactive-color="#ff4949"
                :active-value="1" :inactive-value="0" />
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip label="星期六" align="center">
            <template #default="scope">
              <el-switch disabled :model-value="scope.row.saturday" active-color="#13ce66" inactive-color="#ff4949"
                :active-value="1" :inactive-value="0" />
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip label="星期天" align="center">
            <template #default="scope">
              <el-switch disabled :model-value="scope.row.sunday" active-color="#13ce66" inactive-color="#ff4949"
                :active-value="1" :inactive-value="0" />
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip prop="beginTime1" label="时区1起始" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip prop="endTime1" label="时区1终止" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip prop="beginTime2" label="时区2起始" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip prop="endTime2" label="时区2终止" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip prop="beginTime3" label="时区3起始" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip prop="endTime3" label="时区3终止" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip prop="nextPeriodNo" label="下一个链接时段" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip prop="beginDate" label="起始时间" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip prop="endDate" label="截止时间" align="center"></el-table-column>
        </el-table>
        <div class="pagination">
          <el-pagination v-model:currentPage="state.form.currentPage" v-model:page-size="state.form.pageSize"
            :page-sizes="[10, 20, 30, 40]" background layout="total, sizes, prev, pager, next, jumper"
            :total="state.total" @size-change="handleSizeChange" @current-change="handleCurrentChange">
          </el-pagination>
        </div>
      </div>
    </kade-table-wrap>
    <kade-time-edit v-model="state.isEdit" :rowData="state.rowData" @update:modelValue="close" />
  </div>
</template>
<script>
import { onMounted, reactive, ref } from "vue"
import { ElAlert, ElDivider, ElTable, ElTableColumn, ElPagination, ElButton, ElMessage, ElSwitch, ElMessageBox } from "element-plus"
import edit from "./components/edit.vue"
import { getDevicePeriodList, deleteDevicePeriod } from '@/applications/eccard-uac/api.js'
export default {
  components: {
    ElAlert, ElDivider, ElTable, ElTableColumn, ElPagination, ElButton, ElSwitch,
    "kade-time-edit": edit
  },
  setup() {
    const tableRef = ref(null)
    const state = reactive({
      loading: false,
      isEdit: false,
      form: {
        currentPage: 1,
        pageSize: 10
      },
      dataList: [],
      total: 0,
      rowData: {},
      highlight: false
    })
    const handleEdit = (type) => {
      if (type === 'edit' && !state.rowData.id) {
        return ElMessage.error("请选择时段信息！")
      }else if(type ==='add'){        
        state.rowData={}
      }else {
        tableRef.value.setCurrentRow()
      }
      state.isEdit = true
    }
    const handleCurrentChange = val => {
      state.form.currentPage = val
      getList()
    }
    const handleSizeChange = val => {
      state.form.currentPage = 1
      state.form.pageSize = val
      getList()
    }
    const handleChange = (val) => {
      state.rowData = val
      state.highlight = true
      console.log(state.rowData)
    }
    const getList = async () => {
      state.rowData = {}
      state.loading = true
      try {
        let { data: { list, total } } = await getDevicePeriodList(state.form)
        state.dataList = list
        state.total = total
        state.loading = false
      }
      catch {
        state.loading = false
      }
    }
    const handleDelete = () => {
      if (!state.rowData.id) {
        ElMessage.error('请先选择要删除时段信息！')
      } else {
        ElMessageBox.confirm("确认删除?", "提示", {
          confirmButtonText: "确认",
          cancelButtonText: "取消",
          type: "warning",
        }).then(async () => {
          let param = { ...state.rowData }
          let { code, message } = await deleteDevicePeriod(param.id)
          if (code === 0) {
            ElMessage.success(message)
            getList()
          }
        })
      }
    }
    const close = (val) => {
      if (val) {
        getList()
      }
      state.isEdit=false
    }
    onMounted(() => {
      getList()
    })
    return {
      tableRef,
      state,
      close,
      handleEdit,
      handleChange,
      handleDelete,
      handleCurrentChange,
      handleSizeChange
    }
  }
}
</script>
<style scoped lang="scss">
:deep(.el-dialog__footer) {
  text-align: center;
  border-top: none;
  margin-top: 20px;
}

:deep(.el-dialog) {
  padding-bottom: 25px;
  border-radius: 8px;
}

.el-divider--horizontal {
  margin: 0
}

.el-alert {
  background-color: rgba(242, 249, 255, 1);
  color: #3399FF;
}

.el-table {
  border-left: 1px solid #EBEEF5;
  border-right: 1px solid #EBEEF5;
}
</style>