<template>
  <div class="padding-box">
    <div v-if="details" class="basic-info-box">
      <div class="box-item" v-for="(item, index) in list" :key="index">
        <div class="item-label">{{ item.label }}</div>
        <div class="item-value" v-if="item.render">
          {{ item.render(details[item.valueKey]) }}
        </div>
        <div class="item-value" v-else>{{ details[item.valueKey] }}</div>
      </div>
    </div>
    <el-empty v-else description="当前设备参数未设定"></el-empty>
  </div>
</template>
<script>
import { computed } from "vue";
import { useStore } from "vuex";
import { ElEmpty } from "element-plus"

const list = [
  { label: "标题名称", valueKey: "title" },
  /*   { label: "商户编号", valueKey: "num" },
    { label: "商户名称", valueKey: "name" }, */
  { label: "是否打印标题", valueKey: "print_title", render: val => val == 1 ? '是' : '否' },
  { label: "是否打印商户编号", valueKey: "print_num", render: val => val == 1 ? '是' : '否' },
  { label: "是否打印商户名称", valueKey: "print_name", render: val => val == 1 ? '是' : '否' },
  { label: "是否打印操作员号", valueKey: "print_oper_num", render: val => val == 1 ? '是' : '否' },
  { label: "是否打印机号", valueKey: "print_dev_num", render: val => val == 1 ? '是' : '否' },
  { label: "是否打印交易类型", valueKey: "print_deal_type", render: val => val == 1 ? '是' : '否' },
  { label: "是否打印交易金额", valueKey: "print_deal_money", render: val => val == 1 ? '是' : '否' },
  { label: "是否打印余额", valueKey: "print_balance", render: val => val == 1 ? '是' : '否' },
  { label: "是否打印补助余额", valueKey: "print_bzye", render: val => val == 1 ? '是' : '否' },
  { label: "是否打印剩余次数", valueKey: "print_sycs", render: val => val == 1 ? '是' : '否' },
  { label: "是否打印交易序号", valueKey: "print_jyxh", render: val => val == 1 ? '是' : '否' },
  { label: "是否打印交易时间", valueKey: "print_jysj", render: val => val == 1 ? '是' : '否' },
  { label: "是否打印菜单", valueKey: "print_menu", render: val => val == 1 ? '是' : '否' },
  { label: "是否打印用户号", valueKey: "print_usernum", render: val => val == 1 ? '是' : '否' },
  { label: "是否打印用户名", valueKey: "print_username", render: val => val == 1 ? '是' : '否' },
];
export default {
  components:{
    ElEmpty,
  },
  setup() {
    const store = useStore();
    const details = computed(() => {
      let data = "";
      for (let item of store.state.deviceParameters[store.state.app.activeTab].detailsParams) {
        if (item.paramType == "print") {
          data = JSON.parse(item.paramContent);
          break;
        }
      }
      if (!data) {
        data = ""
      }
      console.log(data);
      return data;
    });

    return {
      list,
      details,
    };
  },
};
</script>
<style lang="scss" scoped>
.basic-info-box {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  border-left: 1px solid #ccc;
  border-top: 1px solid #ccc;
  min-width: 900px;
  .box-item {
    box-sizing: border-box;
    width: 33.333%;
    min-width: 300px;
    line-height: 50px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #ccc;
    .item-label {
      width: 50%;
      text-align: center;
      background: #f6f6f6;
      border-right: 1px solid #ccc;
    }
    .item-value {
      text-align: center;
      width: 50%;
      background: #fff;
      border-right: 1px solid #ccc;
    }
  }
}
</style>