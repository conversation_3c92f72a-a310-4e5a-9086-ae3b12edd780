<template>
  <div class="warning-config">
    <el-form label-width="300px" size="mini" ref="formRef" :model="state.form" :rules="state.isEdit&&rules">
      <el-form-item label="设备超期离线预警天数（天）：" prop="offlineWarnDays">
        <el-input v-model="state.form.offlineWarnDays" maxLength="5" :placeholder="state.isEdit?'请输入设备超期离线天数':''" :readonly="!state.isEdit" style="width: 300px;" />
      </el-form-item>
      <el-form-item label="设备超期无交易预警天数（天）：" prop="noTransactionWarnDays">
        <el-input v-model="state.form.noTransactionWarnDays" maxLength="5" :placeholder="state.isEdit?'请输入设备超期无交易预警天数':''" :readonly="!state.isEdit" />
      </el-form-item>
      <el-form-item label="设备大金额消费金额上限预警（元）：" prop="largeAmountWarn">
        <el-input v-model="state.form.largeAmountWarn" maxLength="7" :placeholder="state.isEdit?'请输入设备大金额消费金额上限':''" :readonly="!state.isEdit" />
      </el-form-item>
      <el-form-item label="" prop="roomTypeName">
        <el-button v-if="state.isEdit" size="mini" type="info" @click="handleCancel">取消</el-button>
        <el-button v-if="state.isEdit" size="mini" type="primary" @click="handleSave">保存</el-button>
        <el-button v-if="!state.isEdit" size="mini" type="primary" @click="handleEdit">编辑</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { ElForm, ElFormItem, ElInput, ElButton, ElMessage } from "element-plus"
import { onMounted, reactive, ref } from "vue"
import { warningTypeList } from "@/applications/eccard-water-ops/dict"
import { opsWaterWarnConfig, opsWaterWarnConfigAdd, opsWaterWarnConfigUpdate } from "@/applications/eccard-water-ops/api/warningConfig"
export default {
  components: {
    ElForm, ElFormItem, ElInput, ElButton

  },
  setup() {
    const formRef = ref(null)
    const state = reactive({
      loading: false,
      isEdit: false,
      copyForm: {},
      form: {},
      dataList: []
    })
    const rules = {
      offlineWarnDays: [
        { required: true, message: "请输入设备超期离线天数", trigger: "blur" },
        { pattern: /^[0-9]*$/, message: "请输入数字", },
      ],
      noTransactionWarnDays: [
        { required: true, message: "请输入设备超期无交易预警天数", trigger: "blur" },
        { pattern: /^[0-9]*$/, message: "请输入数字", },

      ],
      largeAmountWarn: [
        { required: true, message: "请输入设备大金额消费金额上限预警", trigger: "blur" },
        { pattern: /^[0-9]*$/, message: "请输入数字", },

      ],
    }
    const getData = async () => {
      let { data, code } = await opsWaterWarnConfig()
      if (code === 0) {
        state.dataList = data
        warningTypeList.forEach(item => {
          state.form[item.key] = data.find(v => v.type == item.value)?.value || ""
        })
      }
    }
    const handleEdit = () => {
      state.copyForm = { ...state.form }
      state.isEdit = true
    }

    const handleSave = () => {
      formRef.value.validate(async (valid) => {
        if (valid) {
          let params = warningTypeList.map(item => {
            let data = {
              type: item.value,
              value: state.form[item.key]
            }
            let p = state.dataList.find(v => v.type == item.value)
            if (p) {
              data.id = p.id
            }
            return data
          })
          let idData = params.find(v => v.id)
          let fn = idData ? opsWaterWarnConfigUpdate : opsWaterWarnConfigAdd
          let { code } = await fn(params)
          if (code === 0) {
            ElMessage.success("保存成功")
            getData()
            state.isEdit = false
          }
        }
      })
    }
    const handleCancel = () => {
      state.form = { ...state.copyForm }
      state.isEdit = false
    }
    onMounted(() => {
      getData()
    })
    return {
      formRef,
      state,
      rules,
      handleEdit,
      handleSave,
      handleCancel
    }
  }
}
</script>

<style lang="scss" scoped>
.warning-config {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  margin-top: 200px;
}
</style>