<template>
  <div class="kade-table-filter">
    <div class="table-filter-head" v-if="!isNoFilter">
      <div class="title">
        <i class="el-icon-search"></i>
        <span class="text">条件筛选</span>
      </div>
      <div class="btns">
        <el-button v-show="state.isShowToggle" @click="handleToggle" :icon="icon" size="small" type="text">{{ text }}
        </el-button>
        <el-button @click="handleReset" icon="el-icon-refresh-right" size="small">重置</el-button>
        <el-button size="small" type="primary" @click="handleSearch" icon="el-icon-search">搜索</el-button>
      </div>
    </div>
    <div class="table-filter-body" :style="collapseStyle">
      <div class="table-filter-body-wrap" ref="wrapRef">
        <slot></slot>
      </div>
    </div>
  </div>
</template>
<script>
// 含筛选条件表格包裹组件
import { ElButton } from 'element-plus';
import { reactive, computed, ref, onMounted, onUnmounted, nextTick } from 'vue';
export default {
  name: 'kade-table-filter',
  emits: ['reset', 'search'],
  components: {
    'el-button': ElButton,
  },
  props: {
    isNoFilter: {
      type: Boolean,
      default: false,
    }
  },
  setup(props, context) {
    const wrapRef = ref(null);
    const state = reactive({
      show: false,
      wrapHeight: '63px',
      isShowToggle: false,
    });
    const text = computed(() => {
      return state.show ? '收起筛选' : '展开筛选';
    });
    const icon = computed(() => {
      return state.show ? 'el-icon-arrow-up' : 'el-icon-arrow-down';
    });
    const initToggleState = () => {

      const h = parseInt(window.getComputedStyle(wrapRef.value).height);
      console.log(h);
      state.isShowToggle = h > 48;
    }
    const handleToggle = () => {
      if (state.show) {
        state.wrapHeight = '63px';
      } else {
        state.wrapHeight = `${parseInt(window.getComputedStyle(wrapRef.value).height) + 15}px`;
      }
      state.show = !state.show;
    }
    const handleReset = () => {
      context.emit('reset');
    }
    const handleSearch = () => {
      context.emit('search');
    }
    const collapseStyle = computed(() => {
      return {
        height: state.wrapHeight,
      }
    });
    const onResize = () => {
      initToggleState();
    }
    onMounted(() => {
      nextTick(() => {
        initToggleState();
      })
      window.addEventListener('resize', onResize);
    });
    onUnmounted(() => {
      window.removeEventListener('resize', onResize);
    });
    return {
      state,
      text,
      icon,
      wrapRef,
      collapseStyle,
      handleToggle,
      handleReset,
      handleSearch,
    }
  }
}
</script>
<style lang="scss">
.kade-table-filter {
  border-radius: 5px;
  border: 1px solid $border-color;
  margin-bottom: 20px;

  .table-filter-head {
    width: 100%;
    height: 60px;
    padding: 0 20px;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid $border-color;

    .title,
    .btns {
      display: flex;
      align-items: center;
    }

    .title {
      .text {
        margin-left: 10px;
      }
    }

    .btns {
      justify-content: flex-end;
    }
  }

  .table-filter-body {
    padding: 15px 15px 0 15px;
    box-sizing: border-box;
    height: 63px;
    transition: height .2s linear;
    overflow-y: hidden;

    .el-form-item--small.el-form-item {
      margin-bottom: 15px !important;
    }
  }
}
</style>