<template>
  <el-dialog :model-value="modelValue" title="参数设置" width="90%" :before-close="beforeClose">
    <el-tabs v-model="state.tab" class="demo-tabs" @tab-click="handleClick" v-if="state.tabs.length">
      <el-tab-pane v-for="(item, index) in state.tabs" :key="index" :label="item.paramsName" :name="item.id">
        <kade-route-card>
          <el-form label-width="120px" size="mini" inline>
            <el-form-item :label="v.paramsName" v-for="(v, i) in item.children" :key="i">
              <el-input v-model="state.form[v.paramsCode]" v-if="v.paramsType == 'INPUT'" placeholder="请输入"></el-input>
              <el-select v-model="state.form[v.paramsCode]" v-if="v.paramsType == 'SELECT'">
                <el-option v-for="(it, idx) in v.paramsValue.split('、')" :key="idx" :label="it" :value="it">
                </el-option>
              </el-select>
            </el-form-item>
          </el-form>
        </kade-route-card>
      </el-tab-pane>
    </el-tabs>
    <el-empty v-else description="当前参数绑定的自定义模板未设置参数值"></el-empty>
    <template #footer  v-if="state.tabs.length">
      <span class="dialog-footer">
        <el-button @click="submit()" type="primary" size="mini">保&nbsp;&nbsp;存</el-button>
        <el-button @click="beforeClose()" size="mini">返&nbsp;&nbsp;回</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script>
import { reactive, watch } from 'vue'
import {
  ElDialog, ElTabs, ElTabPane, ElForm, ElFormItem,
  ElInput, ElSelect, ElOption, ElButton, ElMessage, ElEmpty
} from "element-plus";
import { makeTree } from "@/utils"
export default {
  components: {
    ElDialog, ElTabs, ElTabPane, ElForm, ElFormItem,
    ElInput, ElSelect, ElOption, ElButton, ElEmpty
  },
  props: {
    modelValue: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Object,
      default: null
    },
    type: {
      type: String,
      default: ""
    },
  },
  setup(props, context) {
    const state = reactive({
      tabs: [],
      tab: "",
      tabData: {},
      form: {},
    })
    watch(() => props.modelValue, val => {
      if (val) {
        console.log(props.data, JSON.parse(sessionStorage.getItem('templateList')))
        let data = JSON.parse(sessionStorage.getItem('templateList')).find(item => item.templateName == props.data.paramsTemplate)
        console.log(data)

        state.tabs = makeTree(data.paramsList, "id", "parentId", "children");
        if (state.tabs.length) {
          state.tab = state.tabs[0].id
        }
        handleClick({
          props: {
            name: state.tab
          }
        })
      }
    })
    const handleClick = (val) => {
      state.form = {}
      let a = state.tabs.find(item => item.id == val.props.name)
      state.tabData = a
      if (a&&a.children && a.children.length) {
        a.children.forEach(item => {
          state.form[item.paramsCode] = item.paramsFormat
        })
      }
      let list = JSON.parse(sessionStorage.getItem("customParamsList"))
      let listCutItem = list.find(item => item.id == props.data.id)
      console.log(listCutItem, a);
      if (listCutItem && listCutItem.paramsData) {
        state.form = { ...listCutItem.paramsData[a.paramsCode] }
      }
    }
    const submit = () => {
      console.log(state.tabData);
      let list = JSON.parse(sessionStorage.getItem("customParamsList"))
      let listCutItem = list.find(item => item.id == props.data.id)
      if (listCutItem.paramsData) {
        listCutItem.paramsData[state.tabData.paramsCode] = state.form
      } else {
        listCutItem.paramsData = {}
        listCutItem.paramsData[state.tabData.paramsCode] = state.form
      }
      console.log(list, listCutItem);
      let index = list.findIndex(item => item.id == listCutItem.id)
      list.splice(index, 1, listCutItem)
      sessionStorage.setItem('customParamsList', JSON.stringify(list))
      ElMessage.success('保存成功')
    }
    const beforeClose = () => {
      context.emit("update:modelValue", false);
    };
    return {
      state,
      handleClick,
      submit,
      beforeClose
    }
  }
}
</script>
<style lang="scss" scoped>
.el-tabs {
  margin-top: 20px;
  // margin-bottom: 20px;
  border: 1px solid #eeeeee;

  :deep(.el-tabs__nav-scroll) {
    padding: 0 10px;
  }
}
</style>