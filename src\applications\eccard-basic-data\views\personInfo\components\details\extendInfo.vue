<template>
  <div class="dialog-box">
    <div class="title">教育经历</div>
    <el-divider></el-divider>
    <div class="list-box" v-loading="state.isTeachLoading">
      <div class="teach-item" v-for="(item,index) in state.teachList" :key="index">
        <div class="teach-left">
          <div class="school">
            <span class="school-name">{{item.schoolName}}</span>
            <span class="school-date">{{monthStr(item.beginTime)}}-{{monthStr(item.endTime)}}</span>
            <span v-if="item.whetherHighestEducation=='TRUE'" class="label">最高学历</span>
          </div>
          <div class="education">
            <span style="margin-right:20px">{{item.education}}</span>
            <span style="margin-right:20px">{{item.studyMajor}}</span>
            <span style="margin-right:20px">{{item.learningWays}}</span>
            <span style="margin-right:20px">{{item.academicDegree}}</span>
          </div>
        </div>
        <span class="el-icon-edit icon" @click="teachEdit(item)"></span>
        <span class="el-icon-circle-close icon" @click="teachDel(item)"></span>
      </div>
      <el-empty v-if="!state.teachList.length" description="暂无数据"></el-empty>
    </div>
    <el-button size="mini" icon="el-icon-plus" class="green add" @click="teachEdit('')">增加教育经历</el-button>
  </div>
  <div class="dialog-box">
    <div class="title">资格证书</div>
    <el-divider></el-divider>
    <div class="list-box" v-loading="state.isCertificateLoading">
      <div class="certificate-item" v-for="(item,index) in state.certificateList" :key="index">
        <div class="certificate-left">
          <span class="name">{{item.certificateName}}</span>
          <span class="date">{{monthStr(item.gainTime)}}</span>
        </div>
        <span class="el-icon-edit icon" @click="certificateEdit(item)"></span>
        <span class="el-icon-circle-close icon" @click="certificateDel(item)"></span>
      </div>
      <el-empty v-if="!state.certificateList.length" description="暂无数据"></el-empty>
    </div>
    <el-button size="mini" icon="el-icon-plus" class="green add" @click="certificateEdit('')">增加资格证书</el-button>
  </div>
  <kade-teach-dialog :isShow="state.isTeach" :data="state.teachData" @close="closeTeach" />
  <kade-certificate-dialog :isShow="state.isCertificate" :data="state.certificateData" @close="closeCertificate" />
</template>
<script>
import { reactive } from "@vue/reactivity";
import { useStore } from "vuex";
import {
  ElDivider,
  ElButton,
  ElEmpty,
  ElMessageBox,
  ElMessage,
} from "element-plus";
import { monthStr } from "@/utils/date.js";
import {
  certificate,
  educate,
  delCertificate,
  delTeach,
} from "@/applications/eccard-basic-data/api";
import certificateDialog from "./components/certificateDialog.vue";
import teachDialog from "./components/teachDialog.vue";
import { onMounted } from "@vue/runtime-core";

export default {
  components: {
    ElDivider,
    ElButton,
    ElEmpty,
    "kade-certificate-dialog": certificateDialog,
    "kade-teach-dialog": teachDialog,
  },
  setup() {
    const store = useStore();
    const state = reactive({
      isTeachLoading:false,
      isCertificateLoading:false,
      isTeach: false,
      isCertificate: false,
      teachList: [],
      certificateList: [],
      teachData: "",
      certificateData: "",
    });

    const getTeachList = async () => {
      state.isTeachLoading=true
      let {
        data: { list },
      } = await educate({
        userId: store.state.userInfo.rowData.id,
        pageSize: 1000,
        pageNum: 1,
      });
      state.teachList = list;
      state.isTeachLoading=false
    };
    const getCertificateList = async () => {
      state.isCertificateLoading=true
      let {
        data: { list },
      } = await certificate({
        userId: store.state.userInfo.rowData.id,
        pageSize: 1000,
        pageNum: 1,
      });
      state.certificateList = list;
      state.isCertificateLoading=false
    };

    const teachEdit = (row) => {
      state.teachData = row;
      state.isTeach = true;
    };
    const certificateEdit = (row) => {
      state.certificateData = row;
      state.isCertificate = true;
    };
    const teachDel = (row) => {
      ElMessageBox.confirm(`确认删除?`, {
        type: "warning",
        closeOnPressEscape: false,
        closeOnClickModal: false,
      }).then(async () => {
        try {
          const { message } = await delTeach( row.id);
          ElMessage.success(message);
          getTeachList();
        } catch (e) {
          throw new Error(e.message);
        }
      });
    };
    const certificateDel = (row) => {
      ElMessageBox.confirm(`确认删除?`, {
        type: "warning",
        closeOnPressEscape: false,
        closeOnClickModal: false,
      }).then(async () => {
        try {
          const { message } = await delCertificate(row.id);
          ElMessage.success(message);
          getCertificateList();
        } catch (e) {
          throw new Error(e.message);
        }
      });
    };

    const closeTeach = (val) => {
      if (val) {
        getTeachList();
      }
      state.isTeach = false;
    };

    const closeCertificate = (val) => {
      if (val) {
        getCertificateList();
      }
      state.isCertificate = false;
    };
    onMounted(() => {
      getTeachList();
      getCertificateList();
    });
    return {
      monthStr,
      state,
      teachEdit,
      certificateEdit,
      teachDel,
      certificateDel,
      closeTeach,
      closeCertificate,
    };
  },
};
</script>
<style lang="scss" scoped>
.el-divider--horizontal {
  margin: 0 0 0px;
}
.dialog-box {
  border: 1px solid #dcdfe6;
  border-radius: 5px;
  margin: 10px;
  position: relative;
  .title {
    padding: 10px;
  }
  .list-box {
    padding: 10px;
  }
}
.teach-item {
  border: 1px solid #dcdfe6;
  border-radius: 5px;
  padding: 5px 20px;
  display: flex;
  align-items: center;
  &:nth-child(n + 2) {
    margin-top: 10px;
  }
  .teach-left {
    flex: 1;
    .school {
      margin-bottom: 10px;
      .school-name {
        font: bold 18px arial;
      }
      .school-date {
        font-size: 14px;
        color: #cccccc;
        margin: 0 10px;
      }
      .label {
        padding: 5px;
        background: #ff8726;
        font-size: 12px;
        color: #fff;
        border-radius: 3px;
      }
    }
  }
}
.certificate-item {
  border: 1px solid #dcdfe6;
  border-radius: 5px;
  padding: 5px 20px;
  display: flex;
  align-items: center;
  &:nth-child(n + 2) {
    margin-top: 10px;
  }
  .certificate-left {
    flex: 1;
    .name {
      font: bold 16px arial;
    }
    .date {
      font-size: 14px;
      color: #cccccc;
      margin: 0 10px;
    }
  }
}
.icon {
  width: 30px;
  font: 30px arial;
  color: #3399ff;
  margin-left: 20px;
}
.add {
  position: absolute;
  right: 10px;
  top: 5px;
}
</style>