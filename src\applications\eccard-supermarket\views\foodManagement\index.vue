<template>
  <div class="personnelInformation">
    <kade-route-card>
      <kade-table-filter @search="handleSearch" @reset="handleReset">
        <el-form inline label-width="120px" size="small">
          <el-form-item label="菜品名称">
            <el-input size="small" v-model="state.form.dishName" clearable placeholder="请输入菜品名称" />
          </el-form-item>
          <el-form-item label="菜品标签">
            <el-select v-model="state.form.labelEntityList" value-key="id" multiple collapse-tags-tooltip collapse-tags
              clearable>
              <el-option v-for="(item, index) in state.labelList " :key="index" :label="item.labelName" :value="item">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="菜品类型">
            <el-select v-model="state.form.dishType" clearable>
              <el-option v-for="(item, index) in dishTypeList " :key="index" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </kade-table-filter>
      <kade-table-wrap title="菜品列表">
        <template #extra>
          <el-button icon="el-icon-plus" size="mini" type="success" @click="handleEdit({},'add')">新增菜品</el-button>
        </template>
        <el-table style="width: 100%" height="55vh" :data="state.dataList" v-loading="state.loading" border stripe
          @row-click="rowClick" @selection-change="selectionChange">
          <!-- <el-table-column type="selection" width="55" align="center" /> -->
          <!-- <el-table-column label="菜品编号" prop="userCode" align="center" show-overflow-tooltip></el-table-column> -->
          <el-table-column label="菜品名称" prop="dishName" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column label="价格" width="55" prop="dishPrice" align="center" show-overflow-tooltip>
          </el-table-column>
          <el-table-column label="单位" width="55" prop="dishUnit" align="center" show-overflow-tooltip></el-table-column>
          
          <el-table-column label="菜品类型" width="100" prop="dishPrice" align="center" show-overflow-tooltip>
            <template #default="scope">
              {{dictionaryFilter(scope.row.dishType)}}
            </template>
          </el-table-column>
          <el-table-column label="标签" prop="labelEntityList" align="center" width="500px" show-overflow-tooltip>
            <template #default="scope">
              <el-tag v-for="(item,index) in scope.row.labelEntityList" :key="index" style="margin-right:10px"
                type="primary">{{item.labelName}}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="菜品图片" prop="dishImageUrl" align="center" show-overflow-tooltip>
            <template #default="scope">
              <el-image style="width: 50px; height: 50px" :src="scope.row.dishImageUrl"
                :preview-src-list="[scope.row.dishImageUrl]" fit="cover" />
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="200" show-overflow-tooltip>
            <template #default="scope">
              <el-button size="mini" type="text" @click="handleEdit(scope.row,'details')">详情</el-button>
              <el-button size="mini" type="text" @click="handleEdit(scope.row,'edit')">编辑</el-button>
              <!-- <el-button size="mini" type="text" @click="enable(scope.row)">删除</el-button> -->
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination">
          <el-pagination background v-model:current-page="state.form.currentPage"
            layout="total, sizes, prev, pager, next, jumper" :page-sizes="[10, 20, 50, 100, 200]" :total="state.total"
            v-model:page-size="state.form.pageSize" @current-change="handlePageChange" @size-change="handleSizeChange">
          </el-pagination>
        </div>
      </kade-table-wrap>
    </kade-route-card>
    <kade-food-edit v-model="state.isEdit" :editType="state.editType" :labelList="state.labelList"
      :selectRow="state.selectRow" @update:modelValue="close" />
  </div>
</template>
<script>

import {
  ElTable,
  ElTableColumn,
  ElTag,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElImage,
  ElPagination,
  ElButton,
} from "element-plus";
import { reactive, onMounted } from "vue";
import { useDict } from "@/hooks/useDict.js";

import { dishList, labelList } from "@/applications/eccard-supermarket/api.js";


import foodEdit from "./components/edit.vue"

export default {
  components: {
    "el-table": ElTable,
    "el-table-column": ElTableColumn,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    "el-input": ElInput,
    "el-pagination": ElPagination,
    "el-button": ElButton,
    ElSelect,
    ElOption,
    ElImage,
    ElTag,
    "kade-food-edit": foodEdit
  },
  setup() {
    const dishTypeList = useDict("SYS_DISH_TYPE")
    const state = reactive({
      loading: false,
      isEdit: false,
      editType: "",
      labelList: [],
      form: {
        pageNum: 1,
        pageSize: 10
      },
      dataList: [],
      total: 0,
    });
    const getLabelList = async () => {
      let { data: { list } } = await labelList({})
      state.labelList = list
    }
    const getList = async () => {
      state.loading = true
      try {
        let { data: { list, total } } = await dishList(state.form)
        state.dataList = list
        state.total = total
        state.loading = false
      }
      catch {
        state.loading = false
      }
    }

    const handleEdit = (row, type) => {
      state.editType = type
      state.selectRow = row
      state.isEdit = true
    }
    const handleSearch = () => {
      getList()
    }
    const handleReset = () => {
      state.form = {
        pageNum: 1,
        pageSize: 10
      }
    }
    const handlePageChange = val => {
      state.form.pageNum = val
      getList()
    }
    const handleSizeChange = val => {
      state.form.pageNum = 1
      state.form.pageSize = val
      getList()
    }
    const close = val => {
      if (val) {
        getList()
      }
      state.isEdit = false
    }
    onMounted(() => {
      getList()
      getLabelList()
    })
    return {
      dishTypeList,
      state,
      handleEdit,
      handleSearch,
      handleReset,
      handlePageChange,
      handleSizeChange,
      close
    };
  },
};
</script>
<style lang="scss" scoped>
:deep(.el-dialog__footer) {
  text-align: center;
}
</style>
