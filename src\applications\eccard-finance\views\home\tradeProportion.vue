<template>
    <div class="chart-box">
        <div class="title">交易额类别占比</div>
        <div class="a">
            <el-radio-group v-model="radio1" size="mini" style="margin-top:15px;margin-left:20px">
                <el-radio-button label="全部" />
                <el-radio-button label="线上" />
                <el-radio-button label="线下" />
            </el-radio-group>
            <div id="chartLine" class="echartDiv"></div>
        </div>
    </div>
</template>

<script>
import * as echarts from "echarts";
import { onMounted } from "vue";
import { ElRadioGroup, ElRadioButton } from "element-plus";
export default {
    components: {
        ElRadioGroup,
        ElRadioButton,
    },
    setup() {
        const echartInit = () => {
            var chartDom = document.getElementById("chartLine");
            var myChart = echarts.init(chartDom);
            var option = {
                tooltip: {
                    trigger: "item",
                    // formatter: "{a}<br/>{b}:{c}({d}%)",
                },

                legend: {
                    orient: "vertical",
                    right:"13%",
                    top:"5%",
                    itemGap: 25,
                    itemWidth: 8,
                    itemHeight: 8,
                    icon: "circle",
                    formatter: (val)=>{
                        console.log(val);
                        return `${val}     | 30%             ¥ 4,544`
                    },
                    textStyle:{
                        fontSize:15
                    }
                },
                grid: {
                    containLabel: true,
                    
                },
                graphic: [
                    {
                        type: "text",
                        left: "20%",
                        top: "43%",
                        style: {
                            text: "销售额",
                            textAlign: "center",
                            fill: "#0000006d",
                            fontSize: 14,
                        },
                    },
                    {
                        type: "text",
                        left: "14%",
                        top: "53%",
                        style: {
                            text: "￥123,224",
                            fontSize: 24,
                            fontWeight: 400,
                            align: "center",
                            color: "#1a1917l.",
                        },
                    },
                ],
                color: [
                    "#3ba1ff",
                    "#4fcb74",
                    "#fbd438",
                    "#f04864",
                    "#9860e5",
                    "#37cbcb",
                ],
                series: [
                    {
                        name: "Access From",
                        type: "pie",
                        radius: ["80%", "60%"],
                        avoidLabelOverlap: false,
                        label: {
                            show: false,
                            position: "center",
                        },
                        emphasis: {
                            label: {
                                show: false,
                            },
                        },
                        labelLine: {
                            show: false,
                        },
                        data: [
                            { value: 1300, name: "餐饮" },
                            { value: 500, name: "用水" },
                            { value: 300, name: "用电" },
                            { value: 200, name: "补助" },
                            { value: 100, name: "自助洗衣" },
                            { value: 150, name: "其他" },
                        ],
                        
                        center: ["22%", "50%"],
                        itemStyle: {
                            normal: {
                                borderWidth: 2,
                                borderColor: "#fff",
                            },
                        },
                    },
                ],
            };
            myChart.setOption(option);
        };
        onMounted(() => {
            echartInit();
        });
        return {
            echartInit,
        };
    },
};
</script>

<style lang="scss" scoped>
.chart-box {
    .title {
        padding: 15px 20px;
        font-size: 16px;
        font-weight: 800;
        border-bottom: 1px solid #eeeeee;
    }
    .echartDiv {
        height: 260px;
    }
}
</style>