<template>
  <div class="padding-box">
    <div v-if="details" class="basic-info-box">
      <div class="box-item" v-for="(item, index) in list" :key="index">
        <div class="item-label">{{ item.label }}</div>
        <div class="item-value">{{details[item.valueKey]}}</div>
      </div>
    </div>
    <el-empty v-else description="当前设备参数未设定"></el-empty>
    
  </div>
</template>
<script>
import {computed} from "vue"
import {useStore} from "vuex"
import { ElEmpty } from "element-plus"

const list = [
  { label: "充值机待机时长(秒)", valueKey: "dev_czdj" },
  { label: "同卡充值转账时间间隔(秒)", valueKey: "dev_cardjg" },
  { label: "主钱包最大余额(元)", valueKey: "dev_Mmax" },
  { label: "主钱包单次充值/转账最大金额(元)", valueKey: "dev_Mcmax" },
  { label: "主钱包定值充值金额(元)", valueKey: "dev_Mcdin" },
  { label: "水控钱包最大余额(元)", valueKey: "dev_Smax" },
  { label: "水控包单次充值/转账最大金额(元)", valueKey: "dev_Scmax" },
];
export default {
  components:{
    ElEmpty,
  },
  setup() {
    const store = useStore();
    const details = computed(() => {
      let data = "";
      for (let item of store.state.deviceParameters[store.state.app.activeTab].detailsParams) {
        if (item.paramType == "transe") {
          data = JSON.parse(item.paramContent);
          break;
        }
      }
      if(!data){
        data=''
      }
      return data;
    });
    return {
      list,
      details,
    };
  },
};
</script>
<style lang="scss" scoped>
.basic-info-box {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  border-left: 1px solid #ccc;
  border-top: 1px solid #ccc;
  min-width: 900px;
  .box-item {
    box-sizing: border-box;
    width: 33.333%;
    min-width: 300px;
    line-height: 50px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #ccc;
    .item-label {
      width: 50%;
      min-width: 225px;
      text-align: center;
      background: #f6f6f6;
      border-right: 1px solid #ccc;
    }
    .item-value {
      text-align: center;
      width: 50%;
      background: #fff;
      border-right: 1px solid #ccc;
    }
  }
}
</style>