<template>
  <kade-table-filter @search="handleSearch" @reset="handleReset">
    <el-form inline label-width="100px" size="mini">
      <el-form-item label="门禁区域">
        <kade-area-select-tree style="width: 100%" :value="state.form.areaPath" valueKey="areaPath" :multiple="false"
          @valueChange="(val) => (state.form.areaPath = val.areaPath)" />
      </el-form-item>
      <el-form-item label="门禁分组">
        <el-select clearable v-model="state.form.groupId">
          <el-option v-for="(item, index) in state.groupTypeList" :key="index" :label="item.groupName" :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="设备类型">
        <el-select clearable v-model="state.form.deviceType">
          <el-option v-for="(item, index) in state.deviceTypeList" :key="index" :label="item.cfgValue"
            :value="item.cfgKey"></el-option>
        </el-select>
      </el-form-item>
    </el-form>
  </kade-table-filter>
  <kade-table-wrap title="门禁权限列表">
    <template #extra>
      <el-button type="success" size="mini" icon="el-icon-plus" @click="handleSetAuth('')">设置权限</el-button>
    </template>
    <el-table border height="46vh" ref="tableRef" v-loading="state.loading" @selection-change="selectionChange"
      :data="state.dataList">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column show-overflow-tooltip prop="deviceName" label="设备名称" align="center"></el-table-column>
      <el-table-column show-overflow-tooltip prop="deviceTypeName" label="设备类型" align="center"></el-table-column>
      <el-table-column show-overflow-tooltip prop="areaName" label="门禁区域" align="center"></el-table-column>
      <el-table-column show-overflow-tooltip prop="groupName" label="门禁分组" align="center"></el-table-column>
      <!-- <el-table-column show-overflow-tooltip prop="authStatus" label="是否授权" align="center"></el-table-column> -->
      <el-table-column label="操作" align="center">
        <template #default="scope">
          <el-button type="text" @click="handleSetAuth(scope.row, 'edit')" size="mini">授权</el-button>
          <el-button type="text" @click="handleDetails(scope.row)" size="mini">详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="pagination">
      <el-pagination v-model:currentPage="state.form.pageNum" v-model:page-size="state.form.pageSize"
        :page-sizes="[10, 20, 30, 40]" background layout="total, sizes, prev, pager, next, jumper" :total="state.total"
        @size-change="handleSizeChange" @current-change="handleCurrentChange">
      </el-pagination>
    </div>
  </kade-table-wrap>
  <kade-auth-AC-dialog v-model="state.isShow" :rowList="state.selectList" @update:modelValue="addClose" />
  <kade-details-AC-dialog v-model="state.isDetails" :rowList="state.selectList" @update:modelValue="detailsClose" />
</template>
<script>
import { reactive, ref, onMounted } from "vue"
import { iotCfg } from "@/applications/eccard-iot/api";
import { acsGroupingInfoList, getAuthManageList } from "@/applications/eccard-uac/api";
import { ElForm, ElFormItem, ElSelect, ElOption, ElTable, ElTableColumn, ElPagination, ElButton, ElMessage } from "element-plus"
import authACDialog from "./components/authACDialog.vue"
import detailsACDialog from "./components/detailsACDialog.vue"
import areaSelectTree from "@/components/tree/areaSelectTree.vue";
export default {
  components: {
    ElForm, ElFormItem, ElSelect, ElOption, ElTable, ElTableColumn, ElPagination, ElButton,
    "kade-auth-AC-dialog": authACDialog,
    "kade-details-AC-dialog": detailsACDialog,
    "kade-area-select-tree": areaSelectTree,
  },
  setup() {
    const tableRef = ref(null)
    const state = reactive({
      loading: false,
      isShow: false,
      isDetails: false,
      deviceTypeList: [],
      groupTypeList: [],
      selectList: [],
      form: {
        currentPage: 1,
        pageSize: 10
      },
      dataList: [],
      total: 0,

    })
    const getDeviceTypeList = async () => {
      let { data } = await iotCfg({ cfgType: 'ACCESS_DEVICE_TYPE' });
      state.deviceTypeList = data
    }
    const getGroupList = async () => {
      let { data } = await acsGroupingInfoList({ groupType: "deviceGroup" })
      state.groupTypeList = data
    }
    const getList = async () => {
      state.loading = true
      for (let key in state.form) {
        if (!state.form[key]) {
          delete state.form[key]
        }
      }
      try {
        let { data: { list, total } } = await getAuthManageList(state.form)
        state.dataList = list
        state.total = total
        state.loading = false
      }
      catch {
        state.loading = false
      }
    }
    const selectionChange = val => {
      state.selectList = val
    }
    const handleSetAuth = (val) => {
      if (val) {
        state.selectList = [val]
      } else {
        if (!state.selectList.length) {
          return ElMessage.error("请选择需要授权的门禁！")
        }
      }
      state.isShow = true
    }
    const handleDetails = (val) => {
      state.selectList = [val]
      state.isDetails = true
    }
    const handleSearch = () => {
      getList()
    }
    const handleReset = () => {
      state.form = {
        currentPage: 1,
        pageSize: 10
      }
    }
    const handleCurrentChange = val => {
      state.form.currentPage = val
      getList()
    }
    const handleSizeChange = val => {
      state.form.currentPage = 1
      state.form.pageSize = val
      getList()
    }
    const addClose = val => {
      if (val) {
        getList()
      }
      state.isShow = false
      state.selectList = []
      tableRef.value.clearSelection()
    }
    const detailsClose = () => {
      state.isDetails = false
      state.selectList = []
    }
    onMounted(() => {
      getList()
      getDeviceTypeList()
      getGroupList()
    })
    return {
      tableRef,
      state,
      selectionChange,
      handleSetAuth,
      handleDetails,
      handleSearch,
      handleReset,
      handleCurrentChange,
      handleSizeChange,
      addClose,
      detailsClose
    }
  }
}
</script>