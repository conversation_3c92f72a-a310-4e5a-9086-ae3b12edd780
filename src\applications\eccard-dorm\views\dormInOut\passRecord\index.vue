<template>
  <kade-route-card>
    <kade-table-filter @search="handleSearch" @reset="handleReset">
      <el-form  inline size="mini" label-width="100px">
        <kade-linkage-select :data="linkageData" :value="state.form" @change="linkageChange"></kade-linkage-select>
        <el-form-item label="人员编号">
          <el-input v-model="state.form.userCode" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="人员姓名">
          <el-input v-model="state.form.userName" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="通行类型">
          <el-select v-model="state.form.devicePassType" placeholder="请选择">
            <el-option v-for="(item,index) in devicePassTypeList" :key="index" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="通行时间">
          <el-date-picker v-model="state.requestDate" type="datetimerange" :defaultTime="defaultTime" range-separator="~" start-placeholder="开始时间" end-placeholder="结束时间" />
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="通行记录">
      <template #extra>
        <el-button class="btn-purple" size="mini" icon="el-icon-daochu" @click="handleExport('export')">导出</el-button>
      </template>
      <el-table :data="state.data" border v-loading="state.loading">
        <el-table-column show-overflow-tooltip v-for="(item,index) in column" :key="index" :label="item.label" :prop="item.prop" align="center">
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination v-model:currentPage="state.form.currentPage" v-model:page-size="state.form.pageSize" :page-sizes="[10, 20, 30, 40]"  background layout="total, sizes, prev, pager, next, jumper" :total="state.total" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      </div>
    </kade-table-wrap>
  </kade-route-card>
</template> 

<script>
import { ElForm, ElFormItem, ElInput, ElSelect, ElOption, ElButton, ElDatePicker, ElTable, ElTableColumn,ElPagination } from "element-plus"
import { reactive, onMounted } from 'vue'
import { timeStr } from '@/utils/date.js'
import { useDict } from '@/hooks/useDict.js'
import { getAccessPassRecord,exportAccessPassRecord } from "@/applications/eccard-dorm/api.js"
import linkageSelect from '@/applications/eccard-dorm/components/linkageSelect'
const linkageData={
  area:{label:'区域',valueKey:'areaId',key:'id'},
  building:{label:'楼栋',valueKey:'buildId'},
  unit:{label:'单元',valueKey:'unitNum'},
  floor:{label:'楼层',valueKey:'floorNum'}
}
const column = [
  { label: '区域', prop: 'areaName' },
  { label: '人员编号', prop: 'userCode' },
  { label: '人员姓名', prop: 'userName' },
  { label: '通行类型', prop: 'deviceTypeName' },
  { label: '设备名称', prop: 'deviceName' },
  { label: '通行时间', prop: 'passTime' },
  { label: '采集时间', prop: 'createTime' }
]
const defaultTime=[
    new Date(new Date(new Date().toLocaleDateString()).getTime()),
        new Date(
            new Date(new Date().toLocaleDateString()).getTime()+
    24 * 60 * 60 * 1000 - 1)
  ]
export default {
  components: {
    ElForm,
    ElFormItem,
    ElInput,
    ElSelect,
    ElOption,
    ElButton,
    ElDatePicker,
    ElTable,
    ElTableColumn,
    ElPagination,
    "kade-linkage-select":linkageSelect
  },
  setup() {
    const devicePassTypeList=useDict('DORM_ACCESS_TYPE')//	设备通行类型
    const state = reactive({
      form: {
        pageSize: 10,
        currentPage: 1
      },
      data: [],
      total: 0,
      loading: false,
      requestDate: []
    })
    const linkageChange=(val)=>{
      state.form={...state.form,...val}
    }
    const handleReset=()=>{
      state.form={
        currentPage:1,
        pageSize:10
      }
      state.requestDate=[]
      getList()
    }
    const handleSearch=()=>{
      getList()
    }
    const handleCurrentChange=val=>{
      state.form.currentPage=val
      getList()
    }
    const handleSizeChange=val=>{
      state.form.currentPage=1
      state.form.pageSize=val
    }
    const getList=async()=>{
      if(state.requestDate&&state.requestDate.length){
        state.form.beginTime=timeStr(state.requestDate[0])
        state.form.endTime=timeStr(state.requestDate[1])
      }else{
        delete state.form.beginTime
        delete state.form.endTime
      }
      state.loading=true
      try{
      let {data:{list,total}} = await getAccessPassRecord(state.form)
      state.data=list
      state.total=total
      console.log(state.total,state.data)
      state.loading=false
      }
      catch{
        state.loading=false
      }
    }
    
    const handleExport = async () => {
      let res = await exportAccessPassRecord(state.form)
      let url = window.URL.createObjectURL(
        new Blob([res],{type:'application/vnd.ms-excel'})
      )
      let link = document.createElement('a')
      link.style.display='none'
      link.href = url
      link.download='设备通行记录数据列表.xlsx'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }
    onMounted(()=>{
      getList()
    })
    return {
      state,
      column,
      handleReset,
      handleSearch,
      handleCurrentChange,
      handleSizeChange, 
      defaultTime,
      handleExport,
      linkageData,
      linkageChange,
      devicePassTypeList
    }
  }
}
</script>