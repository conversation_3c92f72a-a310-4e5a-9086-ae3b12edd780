import { getDictionary } from '@/applications/unified_portal/api';
const dictionary = localStorage.getItem(`kade_cache_dictionary`);
const state = {
  dictionary: dictionary ? JSON.parse(dictionary) : [],
};
const mutations = {
	updateState(state, { key, payload }) {
		state[key] = payload;
	},
};
const actions = {
  async loadDictionary({ commit }) {
    const { data } = await getDictionary();
    localStorage.setItem(`kade_cache_dictionary`, JSON.stringify(data));
    commit('updateState', { key: 'dictionary', payload: data });
  },
};
export default {
	namespaced: true,
	state,
	mutations,
	actions
}
