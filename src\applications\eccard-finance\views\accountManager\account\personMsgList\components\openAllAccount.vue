<template>
  <div class="box-dialog">
    <kade-table-wrap title="选择未开户人员">
      <el-divider></el-divider>
      <kade-select-table :isShow="isOpenAllAccount" :value="[]" :reqFnc="getPersonList" :selectCondition="state.selectCondition" :column="column" :params="params" @change="personChange" />
    </kade-table-wrap>
    <kade-table-wrap title="开户信息设置" style="margin-top: 10px">
      <el-divider></el-divider>
      <el-form inline size="small" label-width="100px">
        <el-form-item label="开户策略:">
          <el-select clearable v-model="state.accountStrategy" placeholder="请选择" @change="handleChange" @clear="clear">
            <el-option v-for="(item, index) in accountStrategyList" :key="index" :label="item.strategyName" :value="item.strategyId">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="卡片类型:">
          <el-select clearable v-model="state.form.acctType" placeholder="请选择" :disabled="state.isDisabled">
            <el-option v-for="(item, index) in cardTypeList" :key="index" :label="item.ctName" :value="item.ctCode">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <el-table style="width: 100%" :data="state.form.walletList" v-loading="false" border stripe>
        <el-table-column label="钱包名称" prop="walletName" align="center"></el-table-column>
        <el-table-column label="钱包类型" prop="walletType" align="center">
          <template #default="scope">
            <div>
              {{ dictionaryFilter(scope.row.walletType) }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="是否启用" align="center">
          <template #default="scope">
            <el-switch v-model="scope.row.walletStatus" inline-prompt border-color="#fff" :active-value="'WALLET_NORMAL'" :inactive-value="'WALLET_NOT_ACTIVE'" />
          </template>
        </el-table-column>
        <el-table-column label="有效期（年）" align="center"><template #default="scope">
            <div v-if="scope.row.walletStatus == 'WALLET_NOT_ACTIVE'" class="ash">
              未开启
            </div>
            <el-input v-else class="date-num-input" placeholder="请输入" type="number" :disabled="scope.row.isDisabled" v-model="scope.row.walletValidityDateNum" :min="1" size="mini"></el-input>
          </template></el-table-column>
        <el-table-column label="有效期至" width="300" align="center">
          <template #default="scope">
            <div v-if="scope.row.walletStatus == 'WALLET_NOT_ACTIVE'" class="ash">
              未开启
            </div>

            <div v-else>
              {{
                scope.row.walletValidityDateNum
                  ? timeStrDate(
                      scope.row.walletValidityDateNum * 3600 * 1000 * 24 * 365 +
                        new Date().getTime()
                    )
                  : ""
              }}
            </div>
          </template>
        </el-table-column>
      </el-table>
    </kade-table-wrap>
    <div class="submit-btn">
      <el-button @click="off()" size="mini">取&nbsp;&nbsp;消</el-button>
      <el-button @click="submitForm()" size="mini" type="primary" :laoding="state.loading">确&nbsp;&nbsp;认</el-button>
    </div>
  </div>
</template>
<script>
import {
  ElDivider,
  ElTable,
  ElTableColumn,
  ElForm,
  ElFormItem,
  ElSelect,
  ElOption,
  ElButton,
  ElInput,
  ElMessage,
  ElSwitch,
} from "element-plus";
import { reactive } from "@vue/reactivity";
import { computed, watch, ref } from "@vue/runtime-core";
import { dateStr } from "@/utils/date.js";
import {
  getPersonList,
  getAccountStrategyInfo,
  openPersonAccount,
  getWalletActiveList,
} from "@/applications/eccard-finance/api";
import selectTable from "@/components/table/selectTable.vue";
const column = [
  { label: "用户编号", prop: "userCode", isDict: false, width: "" },
  { label: "姓名", prop: "userName", isDict: false, width: "" },
  { label: "组织机构", prop: "deptName", isDict: false, width: "" },
  { label: "身份类别", prop: "userRoleName", isDict: false, width: "" },
];
const params = {
  currentPageKey: "currentPage",
  pageSizeKey: "pageSize",
  resListKey: "list",
  resTotalKey: "total",
  value: {
    acctStatus: "NO_ACCOUNT",
  },
  tagNameKey: "userName",
  valueKey: "userId",
};
export default {
  components: {
    ElDivider,
    ElTable,
    ElTableColumn,
    ElForm,
    ElFormItem,
    ElButton,
    ElSelect,
    ElOption,
    ElInput,
    ElSwitch,
    "kade-select-table": selectTable,
  },
  props: {
    accountStrategyList: {
      types: Array,
      default: [],
    },
    cardTypeList: {
      types: Array,
      default: [],
    },
    roleList: {
      types: Array,
      default: [],
    },
    isOpenAllAccount: {
      types: Boolean,
      default: false,
    },
    allWalletList: {
      types: Array,
      default: [],
    },
  },
  setup(props, context) {
    let dataTable = ref(null);
    const state = reactive({
      loading:false,
      selectCondition: [
        {
          label: "关键字",
          valueKey: "keyWord",
          placeholder: "姓名或编号关键字搜索",
          isSelect: false,
        },
        {
          label: "组织机构",
          valueKey: "deptPath",
          dataKey: "deptPath",
          placeholder: "请选择",
          isSelect: false,
          isTree: "dept",
        },
        {
          label: "身份类别",
          valueKey: "userRoleId",
          placeholder: "请选择",
          isSelect: true,
          select: {
            list: props.roleList,
            option: {
              label: "roleName",
              value: "id",
            },
          },
        },
      ],
      form: {
        acctType: null,
        queryCondition: {
          deptPath: null,
          keyWord: null,
          userRoleId: null,
        },
        selectAll: false,
        userIds: [],
        walletList: [],
      },
      isDisabled: false,
      personList: [],
      getPersonLoading: false,
    });

    //日期格式
    const timeStrDate = computed(() => {
      return dateStr;
    });

    watch(
      () => props.allWalletList,
      (val) => {
        state.form.walletList = val;
      }
    );
    const handleChange = (val) => {
      if (!val) return
      getAccountStrategyInfo({ strategyId: val }).then((res) => {
        state.form.acctType = res.data.cardType;
        state.isDisabled = true;
        state.form.walletList = res.data.strategyWallets.map((item) => ({
          isDisabled: true,
          walletName: item.walletName,
          walletCode: item.walletCode,
          walletStatus: "WALLET_NOT_ACTIVE",
          walletType: item.walletType,
          walletValidityDate: "",
          walletValidityDateNum: item.termMonth / 12,
        }));
      });
    };

    //获取钱包列表
    const queryWalletActiveList = () => {
      getWalletActiveList().then((res) => {
        state.form.walletList = res.data.map((item) => {
          return {
            isDisabled: false,
            walletCode: item.walletCode,
            walletName: item.walletName,
            walletStatus: "WALLET_NOT_ACTIVE",
            walletType: item.walletType,
            walletValidityDate: "",
            walletValidityDateNum: 5,
          };
        });
      });
    };
    const personChange = (val) => {
      state.form.userIds = val.list.map((item) => item.userId);
      let { deptPath, keyWord, userRoleId } = val.params;
      state.form.queryCondition = { deptPath, keyWord, userRoleId };
      state.form.selectAll = val.isSelectAll;
      delete state.form.queryCondition.currentPage;
      delete state.form.queryCondition.pageSize;
    };
    const clear = () => {
      state.accountStrategy = "";
      state.form.acctType = "";
      state.isDisabled = false;
      queryWalletActiveList();
    };
    const off = () => {
      context.emit("off", false);
      state.form = {
        acctType: null,
        queryCondition: {
          deptPath: null,
          keyWord: null,
          userRoleId: null,
        },
        selectAll: false,
        userIds: [],
        walletList: [],
      };
      state.accountStrategy = "";
    };
    const submitForm = () => {
      let params = { ...state.form };
      if (!params.selectAll) {
        if (!params.userIds.length) {
          return ElMessage.error("请选择开户人员！");
        }
      }
      if (!params.acctType) {
        return ElMessage.error("请选择卡片类型！");
      }

      for (let item of params.walletList) {
        if (!item.walletStatus) {
          ElMessage.error("请选择钱包状态！");
          return false;
        }
        if (!item.walletValidityDateNum) {
          ElMessage.error("请输入钱包有效期！");
          return false;
        }
        item.walletValidityDate = dateStr(
          item.walletValidityDateNum * 3600 * 1000 * 24 * 365 +
          new Date().getTime()
        );
      }
      state.loading=true
      openPersonAccount(params).then(({ code, message }) => {
        if (code === 0) {
          ElMessage.success(message);
          context.emit("success", true);
          state.form = {
            acctType: null,
            queryCondition: {
              deptPath: null,
              keyWord: null,
              userRoleId: null,
            },
            selectAll: false,
            userIds: [],
            walletList: [],
          };
        }
      state.loading=false
      }).catch(()=>{
        state.loading=false
      });
    };
    return {
      getPersonList,
      column,
      params,
      dataTable,
      state,
      timeStrDate,
      clear,
      personChange,
      handleChange,
      queryWalletActiveList,
      off,
      submitForm,
    };
  },
};
</script>
<style lang="scss" scoped>
.box-dialog {
  padding: 10px 0;
  .el-table__row {
    height: 30px !important;
  }
  .account-tips {
    text-align: center;
    margin-bottom: 10px;
    color: #f00;
  }
}
.table-box {
  padding: 10px;
}
.submit-btn {
  text-align: center;
  margin: 10px auto;
}
.pagination {
  display: flex;
  justify-content: space-between;
  margin-left: 20px;
}
.el-divider--horizontal {
  margin: 0 0 10px;
}
</style>