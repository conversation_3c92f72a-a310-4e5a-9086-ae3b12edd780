import { ElMessage, ElPopover, ElLoading, ElIcon, ElScrollbar, ElInfiniteScroll } from 'element-plus';
import lang from 'element-plus/lib/locale/lang/zh-cn';
import 'dayjs/locale/zh-cn';
import locale from 'element-plus/lib/locale';
import RouteCard from '@/components/routeCard';
import TableFilter from '@/components/tableFilter';
import TableWrap from '@/components/tableWrap';
import Table from '@/components/table';
import TabWrap from '@/components/tabWrap';
import Status from '@/components/status';

export default (app) => {
  locale.use(lang);
  app.component('kade-route-card', RouteCard);
  app.component('kade-table-filter', TableFilter);
  app.component('kade-table-wrap', TableWrap);
  app.component('kade-table', Table);
  app.component('kade-tab-wrap', TabWrap);
  app.component('kade-switch', Status);
  app.use(ElMessage)
    .use(ElLoading)
    .use(ElIcon)
    .use(ElScrollbar)
    .use(ElInfiniteScroll)
    .use(ElPopover);
}
