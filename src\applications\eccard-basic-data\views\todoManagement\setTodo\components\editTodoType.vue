<template>
  <el-dialog :model-value="isShow" :title="(!data?'新增':'编辑')+'待办类型'" width="640px" :before-close="beforeClose">
    <div style="padding-top: 20px">
      <el-form size="mini" label-width="120px" ref="formDom" :rules="state.rules" :model="state.form">
        <el-form-item label="待办图标:" prop="logo">
          <kade-single-image-upload :action="uploadApplyLogo" icon="iconplus" v-model="state.form.logo" />
            <el-alert
              style="margin-top: 10px;width: 300px"
              center
              show-icon
              type="warning"
              title="格式：SVG、PNG"
              :closable="false"
            />
        </el-form-item>
        <el-form-item label="待办编码:" prop="code">
          <el-input placeholder="请输入" v-model="state.form.code" :maxlength="20"></el-input>
        </el-form-item>
        <el-form-item label="待办类型名称:" prop="name">
          <el-input placeholder="请输入" v-model="state.form.name" :maxlength="20"></el-input>
        </el-form-item>
        <el-form-item label="是否启用:">
          <el-switch v-model="state.form.status" active-value="TRUE" inactive-value="FALSE"></el-switch>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="beforeClose" size="mini">取消</el-button>
        <el-button type="primary" @click="submit()" size="mini">提交</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script>
import {
  ElAlert,
  ElDialog,
  ElForm,
  ElFormItem,
  ElInput,
  ElSwitch,
  ElButton,
  ElMessage,
} from "element-plus";
import { reactive,watch, ref ,nextTick} from "vue";
import { uploadApplyLogo } from '@/applications/unified_portal/api';
import { addTodoType,editTodoType } from "@/applications/eccard-basic-data/api";
import SingleImageUpload from '@/components/singleImageUpload';
export default {
  components: {
    ElAlert,
    ElDialog,
    ElForm,
    ElFormItem,
    ElInput,
    ElSwitch,
    ElButton,
    'kade-single-image-upload': SingleImageUpload,
  },
  props:{
    isShow:{
      type:Boolean,
      default:false
    },
    type:{
      type:String,
      default:"add"
    },
    data:{
      type:Object,
      default:null
    }
  },
  setup(props,context) {
    const formDom = ref(null);
    const state = reactive({
      title: "",
      form: {
        status:'TRUE'
      },
      rules: {
        logo: [
          {
            required: true,
            message: "请上传图标",
            trigger: "blur",
          },
        ],
        code: [
          {
            required: true,
            message: "请输入待办编码",
            trigger: "blur",
          },
        ],
        name: [
          {
            required: true,
            message: "请输入待办类型名称",
            trigger: "blur",
          },
        ],
      },
    });

    watch(()=>props.isShow,val=>{
      if(!val){
        nextTick(()=>{
          formDom.value.clearValidate()
        })
      }
      if(props.data){
        state.form={...props.data}
        state.form.typeId=state.form.id
      }else{
        state.form={
          status:'TRUE'
        }
      }
    })

    const submit = () => {
      formDom.value.validate(async (valid) => {
        if (valid) {
          let fn=props.data?editTodoType:addTodoType
          let {code,message}=await fn(state.form)
          if(code===0){
            ElMessage.success(message)
            context.emit("close",true)
          }
        } else {
          return false;
        }
      });
    };

    const beforeClose = () => {
      context.emit("close",false)
    };
    return {
      uploadApplyLogo,
      formDom,
      state,
      submit,
      beforeClose,
    };
  },
};
</script>
<style lang="scss" scoped>

  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: var(--el-transition-duration-fast);
  }
  .avatar-uploader .el-upload:hover {
    border-color: var(--el-color-primary);
  }
  .el-icon.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    text-align: center;
  }
  .avatar {
    width: 178px;
    height: 178px;
    display: block;
  }

</style>