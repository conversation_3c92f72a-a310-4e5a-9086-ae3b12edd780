<template>
  <kade-modal v-bind="attrs" :modelValue="modelValue" @update:modelValue="update">
    <el-form :label-width="labelWidth" :rules="rules" :model="model" size="small">
      <el-form-item label="名称" prop="name">
        <el-input placeholder="请输入" v-model="model.name" />
      </el-form-item>
      <el-form-item label="备注" prop="desc">
        <el-input show-word-limit maxlength="140" type="textarea" placeholder="请输入" v-model="model.desc" />
      </el-form-item>      
    </el-form>
    <template #footer>
      <el-button icon="el-icon-circle-close" @click="cancel" size="small">取消</el-button>
      <el-button icon="el-icon-circle-check" @click="submit" size="small" type="primary">保存</el-button>
    </template>
  </kade-modal>
</template>
<script>
import Modal from '@/components/modal';
import { computed, reactive, ref, onUpdated } from 'vue';
import { ElButton, ElForm, ElFormItem, ElInput } from 'element-plus';
export default {
  emits: ['update:modelValue'],
  props: {
    title: {
      type: String,
      default: ''
    },
    modelValue: {
      type: Boolean,
      default: false,
    },
  },
  components: {
    'kade-modal': Modal,
    'el-button': ElButton,
    'el-form': ElForm,
    'el-form-item': ElFormItem,
    'el-input': ElInput,
  },
  setup(props, context) {
    const formRef = ref(null);
    const state = reactive({
      model: {
        name: '',
        desc: ''
      }
    });
    const rules = {
      name: [
        { required: true, message: '请输入状态名称' },
        { max: 20, message: '名字不能超过20个字符' }
      ],
      desc: [
        { max: 140, message: '备注不能超过140个字符' }
      ]
    };
    const cancel = () => {
      formRef.value?.resetFields();
      context.emit('update:modelValue', false);
    }
    const submit = () => {
      formRef.value?.validate(valid => {
        if(valid) {
          console.log(valid);
          context.emit('updated:change');
        }
      });      
    }
    const attrs = computed(() => {
      // eslint-disable-next-line no-unused-vars
      const { modelValue, ...attrs } = props;
      return attrs;
    });
    const update = (v) => {
      context.emit('update:modelValue', v);
    }
    onUpdated(() => {
      console.log(1)
    });
    return {
      attrs,
      update,
      formRef,
      cancel,
      submit,
      rules,
      labelWidth: THEMEVARS.formLabelWidth,
      ...state,
    }
  }
}
</script>