<template>
  <el-dialog :model-value="modelValue" :title="rowData.id ? '编辑时段' : '新增时段'" width="1130px" :before-close="handleClose"
    :close-on-click-modal="false">
    <div class="padding-box">
      <el-form size="mini" label-width="130px" :model="state.form" :rules="rules" ref="formRef">
        <el-row>
          <el-col :span="8">
            <el-form-item label="时段号：" prop="periodNo">
              <el-input-number v-model="state.form.periodNo" :min="2" :max="254" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item label="描述：" prop="remarks">
              <el-input v-model="state.form.remarks" maxlength="200" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="下一个链接时段：">
              <el-select v-model="state.form.nextPeriodNo" placeholder="请输入">
                <el-option v-for="(item, index) in state.periodNoList" :key="index" :label="item.periodNo"
                  :value="item.periodNo"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="起始日期：" prop="beginDate">
              <el-date-picker v-model="state.form.beginDate" type="date" placeholder="请选择" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="截止日期：" prop="endDate">
              <el-date-picker v-model="state.form.endDate" type="date" placeholder="请选择" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="有效星期选项：">
              <el-checkbox v-for="(item, index) in state.weekList" :key="index" v-model="item.checked">{{ item.label }}
              </el-checkbox>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="每日有效时区1：" prop="requestTime1">
              <el-time-picker v-model="state.form.requestTime1" is-range range-separator="~" start-placeholder="请选择时间"
                end-placeholder="请选择时间" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="每日有效时区2：" prop="requestTime2">
              <el-time-picker v-model="state.form.requestTime2" is-range range-separator="~" start-placeholder="请选择时间"
                end-placeholder="请选择时间" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="每日有效时区3：" prop="requestTime3">
              <el-time-picker v-model="state.form.requestTime3" is-range range-separator="~" start-placeholder="请选择时间"
                end-placeholder="请选择时间" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button type="primary" @click="submit()" size="mini">保存</el-button>
        <el-button @click="handleClose" size="mini">返回</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script>
import { ElDialog, ElRow, ElCol, ElButton, ElForm, ElFormItem, ElInput, ElSelect, ElOption, ElInputNumber, ElDatePicker, ElCheckbox, ElTimePicker, ElMessage, } from "element-plus"
import { reactive } from '@vue/reactivity'
import { nextTick, watch, ref } from 'vue'
import { hourStr, dateStr, time_to_sec } from '@/utils/date.js'
import { addDevicePeriod, editDevicePeriod, getDevicePeriod } from '@/applications/eccard-uac/api.js'
const rules = {
  periodNo: [{ required: true, message: '请输入时段号', trigger: 'blur' }],
  remarks: [{ required: false, max: 200, message: '描述信息不能超过200' }],
  beginDate: [{ required: true, message: '请选择起始日期', trigger: 'change' }],
  endDate: [{ required: true, message: '请选择截止日期', trigger: 'change' }],
  requestTime1: [{ required: true, message: '请选择时区1', trigger: 'change' }],
  requestTime2: [{ required: true, message: '请选择时区2', trigger: 'change' }],
  requestTime3: [{ required: true, message: '请选择时区3', trigger: 'change' }],

}
export default {
  components: {
    ElDialog, ElRow, ElCol, ElButton, ElForm, ElFormItem, ElInput, ElSelect, ElOption, ElInputNumber, ElDatePicker, ElCheckbox, ElTimePicker
  },
  emits: ["update:modelValue"],
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    rowData: {
      type: Object,
      default: null
    }
  },
  setup(props, context) {
    const formRef = ref(null)
    const state = reactive({
      form: {
        requestTime1: [],
        requestTime2: [],
        requestTime3: [],
      },
      periodNoList: {},

      weekList: [
        { label: '星期一', checked: false, filed: 'monday' },
        { label: '星期二', checked: false, filed: 'tuesday' },
        { label: '星期三', checked: false, filed: 'wednesday' },
        { label: '星期四', checked: false, filed: 'thursday' },
        { label: '星期五', checked: false, filed: 'friday' },
        { label: '星期六', checked: false, filed: 'saturday' },
        { label: '星期日', checked: false, filed: 'sunday' }
      ]
    })
    const handleClose = () => {
      context.emit("update:modelValue", false)
    }
    const getPeriodNoList = () => {
      getDevicePeriod().then((res) => {
        console.log(res)
        state.periodNoList = res.data.map(item => {
          return {
            periodNo: item.periodNo
          }
        })
        console.log(state.periodNoList)
      })
    }
    watch(() => props.modelValue, val => {
      if (val) {
        getPeriodNoList()
        if (props.rowData.id) {
          let { id, periodNo, remarks, nextPeriodNo, beginDate, endDate } = { ...props.rowData }
          state.form = {
            id, periodNo: Number(periodNo), remarks, nextPeriodNo, beginDate: dateStr(beginDate), endDate: dateStr(endDate),
            requestTime1: [],
            requestTime2: [],
            requestTime3: [],
          }

          console.log(state.form, 1)
          state.form.requestTime1[0] = new Date("2000-01-01 00:00:00").getTime() + time_to_sec({ ...props.rowData }.beginTime1)
          state.form.requestTime1[1] = new Date("2000-01-01 00:00:00").getTime() + time_to_sec({ ...props.rowData }.endTime1)
          state.form.requestTime2[0] = new Date("2000-01-01 00:00:00").getTime() + time_to_sec({ ...props.rowData }.beginTime2)
          state.form.requestTime2[1] = new Date("2000-01-01 00:00:00").getTime() + time_to_sec({ ...props.rowData }.endTime2)
          state.form.requestTime3[0] = new Date("2000-01-01 00:00:00").getTime() + time_to_sec({ ...props.rowData }.beginTime3)
          state.form.requestTime3[1] = new Date("2000-01-01 00:00:00").getTime() + time_to_sec({ ...props.rowData }.endTime3)
          state.weekList.forEach((item) => {
            item.checked = { ...props.rowData }[item.filed] === 1 ? true : false
          })
        } else {
          state.form = {}
          state.form.requestTime1 = []
          state.form.requestTime2 = []
          state.form.requestTime3 = []
          state.weekList.forEach((item) => {
            item.checked = false
          })
        }
        nextTick(() => {
          formRef.value.clearValidate();
        })
      }
    })
    const submit = () => {
      formRef.value.validate(async (valid) => {
        if (valid) {
          let param = { ...state.form }
          delete param.requestTime1
          delete param.requestTime2
          delete param.requestTime3
          param.beginDate = dateStr(param.beginDate)
          param.endDate = dateStr(param.endDate)
          state.weekList.forEach((item) => {
            param[item.filed] = item.checked ? 1 : 0
          })
          param.beginTime1 = hourStr(state.form.requestTime1[0])
          param.endTime1 = hourStr(state.form.requestTime1[1])
          param.beginTime2 = hourStr(state.form.requestTime2[0])
          param.endTime2 = hourStr(state.form.requestTime2[1])
          param.beginTime3 = hourStr(state.form.requestTime3[0])
          param.endTime3 = hourStr(state.form.requestTime3[1])
          console.log(param)
          let fn = props.rowData.id ? editDevicePeriod : addDevicePeriod
          console.log(fn)
          let { code, message } = await fn(param)
          if (code === 0) {
            ElMessage.success(message)
            context.emit('update:modelValue', true)
          }
        }

      })
    }
    return {
      state,
      formRef,
      rules,
      submit,
      handleClose
    }
  }
}
</script>
<style lang="scss" scoped>
:deep(.kade-table-wrap) {
  margin-bottom: 20px;
  box-sizing: border-box;
}

.padding-box {
  padding: 20px 20px 0
}

.time-select {
  margin-bottom: 10px;

  .time-label {
    margin-right: 10px;
  }
}

:deep(.el-input-number) {
  width: 198px
}
</style>