<template>
  <div v-if="details" class="padding-box">
    <div class="tip">可使用卡类合计：32 个，单位：元/计费单位</div>
    <div class="table-header">
      <div class="header-item" v-for="(item,index) in 4" :key="index">
        <div class="header-card">卡类</div>
        <div class="header-rate">费率</div>
      </div>
    </div>
    <div class="table-body">
      <div class="body-item" v-for="(item,index) in formList" :key="index">
        <div class="body-card">{{item.label}}</div>
        <div class="body-rate">{{details[item.valueKey]}}</div>
      </div>
    </div>
  </div>
  <el-empty v-else description="当前设备参数未设定"></el-empty>
</template>
<script>
import { computed } from "@vue/runtime-core";
import { useStore } from "vuex";
import { ElEmpty } from "element-plus"
const formList = [
  { label: "卡类1", valueKey: "cardType1" },
  { label: "卡类2", valueKey: "cardType2" },
  { label: "卡类3", valueKey: "cardType3" },
  { label: "卡类4", valueKey: "cardType4" },
  { label: "卡类5", valueKey: "cardType5" },
  { label: "卡类6", valueKey: "cardType6" },
  { label: "卡类7", valueKey: "cardType7" },
  { label: "卡类8", valueKey: "cardType8" },
  { label: "卡类9", valueKey: "cardType9" },
  { label: "卡类10", valueKey: "cardType10" },
  { label: "卡类11", valueKey: "cardType11" },
  { label: "卡类12", valueKey: "cardType12" },
  { label: "卡类13", valueKey: "cardType13" },
  { label: "卡类14", valueKey: "cardType14" },
  { label: "卡类15", valueKey: "cardType15" },
  { label: "卡类16", valueKey: "cardType16" },
  { label: "卡类17", valueKey: "cardType17" },
  { label: "卡类18", valueKey: "cardType18" },
  { label: "卡类19", valueKey: "cardType19" },
  { label: "卡类20", valueKey: "cardType20" },
  { label: "卡类21", valueKey: "cardType21" },
  { label: "卡类22", valueKey: "cardType22" },
  { label: "卡类23", valueKey: "cardType23" },
  { label: "卡类24", valueKey: "cardType24" },
  { label: "卡类25", valueKey: "cardType25" },
  { label: "卡类26", valueKey: "cardType26" },
  { label: "卡类27", valueKey: "cardType27" },
  { label: "卡类28", valueKey: "cardType28" },
  { label: "卡类29", valueKey: "cardType29" },
  { label: "卡类30", valueKey: "cardType30" },
  { label: "卡类31", valueKey: "cardType31" },
  { label: "卡类32", valueKey: "cardType32" },
]
export default {
  components: {
    ElEmpty,
  },
  setup() {
    const store = useStore();
    const details = computed(() => {
      let data = "";
      for (let item of store.state.deviceParameters[store.state.app.activeTab].detailsParams) {
        if (item.paramType == "CARDMODE") {
          data = JSON.parse(item.paramContent);
          break;
        }
      }
      if (!data) {
        data = ""
      }
      return data;
    });
    return {
      formList,
      details,
    };
  },
};
</script>
<style lang="scss" scoped>
.tip {
  margin-bottom: 10px;
}
.table-header {
  display: flex;
  align-items: center;
  border: 1px solid #eeeeee;
  line-height: 50px;
  .header-item {
    width: 25%;
    display: flex;
    justify-content: space-between;
    border-right: 1px solid #eeeeee;
    background: #f6f6f6;
    .header-card {
      text-align: center;
      width: 50%;
      border-right: 1px solid #eeeeee;
    }
    .header-rate {
      text-align: center;
      width: 50%;
    }
    &:last-child {
      border-right: 0;
    }
  }
}
.table-body {
  box-sizing: border-box;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  border-right: 1px solid #eeeeee;
  border-top: 0;
  line-height: 50px;
  .body-item {
    width: 25%;
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #eeeeee;
    .body-card {
      text-align: center;
      width: 50%;
      border-left: 1px solid #eeeeee;
    }
    .body-rate {
      text-align: center;
      width: 50%;
      border-left: 1px solid #eeeeee;
    }
  }
}
</style>