<template>
  <el-dialog
    :model-value="dialogVisible"
    :title="title"
    width="380px"
    :before-close="handleClose"
  >
    <el-form
      style="margin-top: 20px"
      ref="form"
      :model="state.form"
      :rules="rules"
      label-width="150px"
      size="mini"
    >
      <el-form-item label="统一收费类型名称：" prop="uctName">
        <el-input
          :readonly="isDisabled"
          v-model="state.form.uctName"
        ></el-input>
      </el-form-item>
      <el-form-item label="是否启用：">
        <el-switch
          :disabled="isDisabled"
          v-model="state.form.uctStatus"
          active-value="ENABLE_TRUE"
          inactive-value="ENABLE_FALSE"
        />
      </el-form-item>
      <el-form-item label="排序：" prop="uctSort">
        <el-input-number
          :disabled="isDisabled"
          v-model="state.form.uctSort"
          :min="1"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose" size="mini">关闭</el-button>
        <el-button
          type="primary"
          v-if="title === '统一收费类型详情'"
          @click="edit"
          size="mini"
          >编辑</el-button
        >
        <el-button type="primary" v-else @click="save" size="mini"
          >保存</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>
<script>
import {
  ElDialog,
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElInputNumber,
  ElSwitch,
  ElMessage,
} from "element-plus";
import { useDict } from "@/hooks/useDict";
import { reactive, ref } from "@vue/reactivity";
import { computed, watch } from "@vue/runtime-core";
import {
  addUnifiedChargeType,
  updateUnifiedChargeType,
} from "@/applications/eccard-finance/api";

const rules = {
  uctName: [
    {
      required: true,
      message: "请输入统一收费类型名称",
      trigger: "blur",
    },
  ],
  uctSort: [
    {
      required: true,
      message: "请输入排序",
      trigger: "blur",
    },
  ],
};

export default {
  components: {
    ElDialog,
    ElButton,
    ElForm,
    ElFormItem,
    ElInput,
    ElInputNumber,
    ElSwitch,
  },
  props: {
    dialogVisible: {
      types: Boolean,
      default: false,
    },
    title: {
      types: String,
      default: "",
    },
    data: {
      types: Object,
      default: {},
    },
  },
  setup(prop, context) {
    const form = ref(null);
    const enable = useDict("SYS_ENABLE");
    const state = reactive({
      form: {
        uctStatus: "ENABLE_FALSE",
      },
    });

    const isDisabled = computed(() => {
      return prop.title === "统一收费类型详情" ? true : false;
    });

    watch(
      () => prop.data,
      (val) => {
        state.form = { ...val };
      }
    );

    const edit = () => {
      context.emit("edit");
    };

    const save = () => {
      form.value.validate(async (valid) => {
        if (valid) {
          console.log(state.form);
          let fn = state.form.id
            ? updateUnifiedChargeType
            : addUnifiedChargeType;
          let data;
          if (state.form.id) {
            data = {
              uctId: { ...state.form }.id,
              uctName: { ...state.form }.uctName,
              uctStatus: { ...state.form }.uctStatus,
              uctSort: { ...state.form }.uctSort,
            };
          }else{
            data = {
              uctName: { ...state.form }.uctName,
              uctStatus: { ...state.form }.uctStatus,
              uctSort: { ...state.form }.uctSort,
            };
          }

          let { message, code } = await fn(data);
          if (code === 0) {
            ElMessage.success(message);
            context.emit("close", true);
          }
        } else {
          return false;
        }
      });
    };

    const handleClose = () => {
      form.value.clearValidate();
      context.emit("close", false);
      state.form = {
        uctStatus: "ENABLE_FALSE",
      };
    };

    return {
      rules,
      enable,
      form,
      state,
      isDisabled,
      edit,
      save,
      handleClose,
    };
  },
};
</script>
<style lang="scss" scoped>
</style>