<template>
  <kade-route-card>
    <el-row justify="space-between">
      <el-col :span="12">
        <kade-table-wrap title="菜单列表" style="margin-right:20px">
          <el-table ref="menuTable" :data="state.menuTreeList" style="width: 100%" height="75vh" row-key="id" @row-click="rowClick" highlight-current-row border>
            <el-table-column prop="menuName" label="菜单名称" />
            <el-table-column prop="name" label="状态" align="center">
              <template #default="scope">
                <el-switch v-model="scope.row.status" active-value="ENABLE_TRUE" inactive-value="ENABLE_FALSE" @change="statusChange(scope.row)"/>
              </template>
            </el-table-column>
            <el-table-column prop="name" label="操作" align="center" width="200px">
              <template #default="scope">
                <el-button @click="edit(scope.row,'add')"  type="text" size="mini">新增</el-button>
                <el-button @click="edit(scope.row,'edit')"  type="text" size="mini">修改</el-button>
              </template>
            </el-table-column>
          </el-table>
        </kade-table-wrap>
      </el-col>
      <el-col :span="12">
        <kade-table-wrap title="权限列表">
          <template #extra>
             <el-button @click="authEdit({},'add')" :disabled="!state.menu.id" type="success" size="mini">新增</el-button>
          </template>
          <el-table v-loading="state.authLoading" :data="state.authList" style="width: 100%"  border>
            <el-table-column prop="permName" label="权限名称" align="center" />
            <el-table-column prop="btnPerm" label="按钮权限" align="center"></el-table-column>
            <el-table-column prop="name" label="操作" align="center" width="200px">
              <template #default="scope">

                <el-button @click="authEdit(scope.row,'edit')"  type="text" size="mini">修改</el-button>
                <el-button @click="authDel(scope.row)"  type="text" size="mini">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <div class="pagination">
            <el-pagination
              background
              :current-page="state.authForm.pageNum"
              layout="total, sizes, prev, pager, next, jumper"
              :page-sizes="[10, 20, 50, 100, 200]"        
              :total="state.total"
              :page-size="state.authForm.pageSize"
              @current-change="() => loadData()"
              @size-change="() => loadData()"
            >
            </el-pagination>             
          </div>
        </kade-table-wrap>
      </el-col>
    </el-row>
      
    <kade-menu-edit :menuList="state.menuTreeList" @save="queryMenuList()" :menu="state.menu" :type="state.type" :title="state.type=='edit' ? '编辑' : '新增'" v-model="state.isEdit"/>
    <kade-menu-auth-edit :appList="state.appList" @save="getBtnAuthList()"  :menu="state.menu" :authMenu="state.authMenu" :type="state.authType" :title="state.type=='edit' ? '编辑' : '新增'" v-model="state.isAuthEdit"/>
  </kade-route-card>
</template>
<script>
import {ElCol,ElRow, ElTable, ElTableColumn,ElButton,ElSwitch,ElPagination, ElMessage,ElMessageBox } from "element-plus";
import { getMenuList,editMenuStatus,getMenuAuthList,getUserAppPermission,delMenuAuth } from '@/applications/eccard-ops/api';
import { reactive ,onMounted, watch, ref, nextTick} from 'vue';
import edit from "./components/edit"
import authEdit from "./components/authEdit"
export default {
  components: {
    ElCol,
    ElRow,
    ElTable,
    ElTableColumn,
    ElButton,
    ElSwitch,
    ElPagination,
    "kade-menu-edit":edit,
    "kade-menu-auth-edit":authEdit,
  },
  setup(){
    const menuTable=ref(null)
    const state=reactive({
      authLoading:false,
      isEdit:false,
      isAuthEdit:false,
      menu:{},
      authMenu:{},
      type:"",
      authType:"",
      menuTreeList:[],
      appList:[],
      authForm:{
        pageNum:1,
        pageSize:10
      },
      authList:[],
      total:0
    })
    watch(()=>state.isAuthEdit,val=>{
      if(!val){
        state.authMenu={}
      }
    })

    watch(()=>state.isEdit,val=>{
      if(!val){
        state.menu={}
        nextTick(()=>{
          menuTable.value.setCurrentRow()
        })
      }
    })

    const queryMenuList=async ()=>{
      let {data}=await getMenuList()
      state.menuTreeList=data
    }
    const getAppList=async ()=>{
      let {data}=await getUserAppPermission()
      state.appList=data
    }
    const getBtnAuthList=async ()=>{
      state.authLoading=true
      let params={
        menuId:state.menu.id,
        form:state.authForm
      }
      let {data:{total,list}}=await getMenuAuthList(params)
      state.authList=list
      state.total=total
      state.authLoading=false
    }

    const edit=(row,type)=>{
      state.menu=row
      state.type=type
      state.isEdit=true
    }

    const authEdit=(row,type)=>{
      state.authMenu=row
      state.authType=type
      state.isAuthEdit=true
    }
    
    const authDel=async row=>{
      ElMessageBox.confirm(
        '确认删除?',
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      )
        .then(async () => {
          let {code,message}=await delMenuAuth({menuId:state.menu.id,permissionIds:row.id})
          if(code===0){
            ElMessage.success(message)
            getBtnAuthList()
          }
        })
        .catch(() => {
          ElMessage({
            type: 'info',
            message: '已取消',
          })
        })
      
    }

    const statusChange=async row=>{
      console.log(row);
      if(row.id){
        let params={
        id: row.id,
        status: row.status
        }
        let {code,message}=await editMenuStatus(params)
        if(code===0){
          ElMessage.success(message)
        }
        queryMenuList()
      }
    }

    const rowClick=row=>{
      state.menu=row
      getBtnAuthList()
    }

    onMounted(()=>{
      queryMenuList()
      getAppList()
    })
    return {
      menuTable,
      state,
      queryMenuList,
      getBtnAuthList,
      edit,
      authEdit,
      authDel,
      statusChange,
      rowClick
    }
  }
};
</script>