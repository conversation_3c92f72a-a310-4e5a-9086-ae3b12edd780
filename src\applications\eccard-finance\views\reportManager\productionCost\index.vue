<template>
  <!-- 工本费明细 -->
  <div class="cashRecharge" v-loading="state.loading">
    <kade-route-card style="height: auto">
      <kade-table-filter @search="getList()" @reset="reset()">
        <el-form inline size="mini" label-width="100px">
          <el-form-item label="用户编号:">
            <el-input placeholder="编号关键字搜索" v-model="state.form.userCode"></el-input>
          </el-form-item>
          <el-form-item label="用户姓名:">
            <el-input placeholder="姓名关键字搜索" v-model="state.form.userName"></el-input>
          </el-form-item>
          <el-form-item label="组织机构:">
            <kade-dept-select-tree style="width: 100%" :value="state.form.deptPath" valueKey="deptPath" :multiple="false" @valueChange="(val) => (state.form.deptPath = val.deptPath)" />
          </el-form-item>
          <el-form-item label="操作员:">
            <el-select clearable filterable allow-create multiple collapse-tags v-model="state.form.operatorId" placeholder="请选择">
              <el-option v-for="(item, index) in state.systemUserList" :key="index" :label="item.userName" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="交款时间:">
            <el-date-picker v-model="state.requestDate" type="datetimerange" unlink-panels :default-time="state.defaultTime" range-separator="~" start-placeholder="请选择日期" end-placeholder="请选择日期" @change="changeDate">
            </el-date-picker>
          </el-form-item>
        </el-form>
      </kade-table-filter>
      <kade-table-wrap title="工本费明细报表">
        <template #extra>
          <el-button @click="exportClick()" icon="el-icon-daoru" size="mini" class="btn-blue">导出查询明细</el-button>
        </template>
        <el-table style="width: 100%" :data="state.detailList" ref="multipleTable" height="55vh" highlight-current-row border stripe>
          <el-table-column v-for="(item,index) in column" :key="index" :width="item.width" :label="item.label" :prop="item.prop" align="center" show-overflow-tooltip>
            <template v-if="item.render" #default="scope">
              {{item.render(scope.row[item.prop])}}
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination">
          <el-pagination background :current-page="state.form.currentPage" :page-size="state.form.pageSize" layout="total, sizes, prev, pager, next, jumper" :page-sizes="[6, 10, 20, 50, 100]" :total="state.total" @current-change="handlePageChange" @size-change="handleSizeChange">
          </el-pagination>
        </div>
      </kade-table-wrap>
    </kade-route-card>
  </div>
</template>
<script>
import {
  ElButton,
  ElForm,
  ElFormItem,
  ElSelect,
  ElInput,
  ElOption,
  ElTable,
  ElTableColumn,
  ElPagination,
  ElDatePicker,
} from "element-plus";
import { timeStr } from "@/utils/date.js";
import { reactive } from "@vue/reactivity";
import {
  getFlatCostList,
  getSystemUser,
  flatCostExport,
  tradeSource,
  tradeMode,
} from "@/applications/eccard-finance/api";
import { onMounted, watch } from "@vue/runtime-core";
import deptSelectTree from "@/components/tree/deptSelectTree.vue";
const column = [
  { label: "用户编号", prop: "userCode", width: "" },
  { label: "用户姓名", prop: "userName", width: "" },
  { label: "组织机构", prop: "deptName", width: "" },
  { label: "工本费金额", prop: "tradeAmount", width: "" },
  { label: "交款时间", prop: "tradeDate", width: "170", render: (val) => val && timeStr(val) },
  { label: "交易来源", prop: "tradeSourceName", width: "" },
  { label: "交易方式", prop: "tradeModeName", width: "" },
  { label: "操作员", prop: "operatorName", width: "" },
]
export default {
  components: {
    "el-button": ElButton,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    "el-select": ElSelect,
    "el-option": ElOption,
    "el-table": ElTable,
    "el-table-column": ElTableColumn,
    "el-pagination": ElPagination,
    ElDatePicker,
    ElInput,
    "kade-dept-select-tree": deptSelectTree,
  },
  setup() {
    const state = reactive({
      loading: false,
      form: {
        pageNum: 1,
        pageSize: 6,
      },
      detailList: [],
      total: 0,
      requestDate: "",
      systemUserList: [],
      defaultTime: [
        new Date(new Date(new Date().toLocaleDateString()).getTime()),
        new Date(
          new Date(new Date().toLocaleDateString()).getTime() +
          24 * 60 * 60 * 1000 -
          1
        ),
      ],
    });
    watch(
      () => state.requestDate,
      (val) => {
        if (!val) {
          delete state.form.startDate;
          delete state.form.endDate;
        }
      }
    );
    //获取操作员
    const querySystemUser = () => {
      getSystemUser().then((res) => {
        state.systemUserList = res.data;
      });
    };
    //获取交易来源
    const getTradeSource = () => {
      tradeSource().then((res) => {
        state.tradeSourceList = res.data.filter((item) => item.tradeCode != 4);
      });
    };
    //获取交易方式
    const getTradeModeList = () => {
      tradeMode({ costType: 101 }).then((res) => {
        state.tradeModeList = res.data.filter(
          (item) => item.code != 8 && item.code != 7
        );
      });
    };
    const getList = async () => {
      state.loading = true;
      try {
        let { code, data } = await getFlatCostList(state.form);
        if (code === 0) {
          let {
            pageInfo: { list, total },
            totalAmount,
          } = data;
          state.detailList = list;

          if (state.detailList.length) {
            state.detailList.push({
              userCode: "合计",
              tradeAmount: totalAmount,
            });
          }
          state.total = total;
        }
        state.loading = false;
      }
      catch {
        state.loading = false;
      }
    };
    const changeDate = (val) => {
      if (val && val.length) {
        state.form.startDate = timeStr(val[0]);
        state.form.endDate = timeStr(val[1]);
      } else {
        delete state.form.startDate;
        delete state.form.endDate;
      }
    };

    const exportClick = async () => {
      state.loading = true
      try {
        let res = await flatCostExport(state.form);
        let url = window.URL.createObjectURL(
          new Blob([res], { type: "application/vnd.ms-excel" })
        );
        let link = document.createElement("a");
        link.style.display = "none";
        link.href = url;
        link.setAttribute("download", "工本费明细报表.xlsx");
        document.body.appendChild(link);
        link.click();
        state.loading = false
      }
      catch {
        state.loading = false
      }
    };

    const handlePageChange = (val) => {
      state.form.pageNum = val;
      getList();
    };
    const handleSizeChange = (val) => {
      state.form.pageNum = 1;
      state.form.pageSize = val;
      getList();
    };

    const reset = () => {
      state.form = {
        pageNum: 1,
        pageSize: 6,
      };
      state.requestDate = "";
    };
    onMounted(() => {
      getList();
      querySystemUser();
      getTradeSource();
      getTradeModeList();
    });
    return {
      column,
      state,
      timeStr,
      exportClick,
      getList,
      changeDate,
      handlePageChange,
      handleSizeChange,
      reset,
    };
  },
};
</script>
<style lang="scss" scoped>
:deep(.el-divider--horizontal) {
  margin: 0 0 10px 0;
}
.cashRecharge {
  :deep(.search-box) {
    .el-input__inner {
      width: 193px;
    }
    .el-date-editor {
      width: 400px;
    }
  }
}
</style>