<template>
  <div class="padding-form-box setBasicParam" v-loading="state.loading">
    <el-form inline size="small" label-width="200px" :model="state.form.paramContent" :rules="rules">
      <el-form-item label="计费模式:">
        <el-select v-model="state.form.paramContent.billModel">
          <el-option v-for="(item, index) in dictListFnc().billModel" :key="index" :label="item.label"
            :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="最小计费单位(1秒/次):">
        <el-input-number :min="0" v-model="state.form.paramContent.minbillUnit"></el-input-number>
      </el-form-item>
      <el-form-item label="预扣费金额(元):">
        <el-input-number :min="0" v-model="state.form.paramContent.billNum"></el-input-number>
      </el-form-item>
    </el-form>
    <div style="text-align: center">
      <el-button size="mini" @click="beforeClose()">取消</el-button>
      <el-button size="mini" type="primary" :loading="state.loading" @click="saveClick()">保存</el-button>
    </div>
  </div>
</template>
<script>
import {
  ElButton,
  ElForm,
  ElFormItem,
  ElInputNumber,
  ElOption,
  ElSelect,
  ElMessage,
} from "element-plus";
import { onMounted, reactive } from "vue";
import { useStore } from "vuex";
const defaultParamsFnc = () => {
  return {
    billModel: "REAL_TIME_TIMING",	
    minbillUnit: 1,
    billNum: 0,
  };
};

export default {
  components: {
    ElInputNumber,
    ElForm,
    ElFormItem,
    ElSelect,
    ElOption,
    ElButton,
  },
  setup() {
    const store = useStore();
    const state = reactive({
      loading: false,
      form: {
        paramContent: defaultParamsFnc(),
        id: "",
        paramId: "",
        paramType: "BASE",
      },
    });
    const rules = {
      a: [

      ]
    }
    const dictListFnc = () => {
      return store.state.deviceParameters[store.state.app.activeTab].dict
    }
    const saveClick = async () => {
      state.loading = true;
      let storeData = store.state.deviceParameters[store.state.app.activeTab]
      state.form.paramId = storeData.selectRow.id;
      let fn = state.form.id ? storeData.TabModule.detailsEditFnc : storeData.TabModule.detailsAddFnc

      let params = { ...state.form }
      console.log(params.paramContent);
      params.paramContent = JSON.stringify(params.paramContent)
      let { message, code } = await fn(params);
      if (code === 0) {
        setTimeout(async () => {
          ElMessage.success(message);
          await store.dispatch("deviceParameters/getParams", store.state.deviceParameters[store.state.app.activeTab].selectRow.id);
          init()
          state.loading = false;
        }, 1500);
      } else {
        state.loading = false;
      }
    };

    const beforeClose = () => {
      store.commit("deviceParameters/updateState", {
        key: store.state.app.activeTab,
        childKey: "isSetParams",
        payload: false,
      });
    };
    const init = () => {
      let val = store.state.deviceParameters[store.state.app.activeTab].detailsParams
      if (val && val.length) {
        let baseList = val.filter((item) => item.paramType == "BASE");
        if (baseList.length) {
          state.form.id = baseList[0].id;
          for (let key in state.form.paramContent) {
            state.form.paramContent[key] = JSON.parse(
              baseList[0].paramContent
            )[key];
          }
        } else {
          state.form.id = "";
          state.form.paramContent = defaultParamsFnc();
        }
      } else {
        //设置默认选项
        state.form.id = "";
        state.form.paramContent = defaultParamsFnc();
      }
    }
    onMounted(() => {
      init()
    });
    return {
      state,
      rules,
      dictListFnc,
      saveClick,
      beforeClose,
    };
  },
};
</script>

<style lang="scss" scoped>

:deep(.el-form-item__content) {
  width: 200px;

  .el-input-number {
    width: 100%;
  }

  .el-select {
    width: 100% !important;
  }
}
</style>
