<template>
  <div class="dialog-box">
    <div class="title">设备基本信息</div>
    <el-divider></el-divider>
    <div class="padding-form-box">
      <el-form label-width="120px" inline size="mini">
        <el-form-item :label="item.label" v-for="(item,index) in filedList" :key="index">
          <el-input readonly v-if="item.isDict" :model-value="dictionaryFilter(data[item.filed])"></el-input>
          <el-input readonly v-else :model-value="data[item.filed]"></el-input>
        </el-form-item>
      </el-form>
      <el-form label-width="120px" size="mini">
        <el-form-item label="设备位置：">
          <el-input readonly class="device-position-width" :model-value="data.devicePosition"></el-input>
        </el-form-item>
        <el-form-item label="备注：">
          <el-input readonly type="textarea" :model-value="data.deviceRemarks"></el-input>
        </el-form-item>
      </el-form>
    </div>
  </div>
  <div class="dialog-box">
    <div class="title">设备扩展信息</div>
    <el-divider></el-divider>
    <div class="padding-form-box">
      <el-form label-width="120px" inline size="mini">
        <el-form-item label="所属商户：">
          <el-input readonly :model-value="data.merchantName"></el-input>
        </el-form-item>
        <el-form-item label="远传水表类型：">
          <el-input readonly :model-value="data.watermeterTypeName"></el-input>
        </el-form-item>
        <el-form-item label="集中器编号：">
          <el-input readonly :model-value="data.watermeterNum"></el-input>
        </el-form-item>
        <el-form-item label="采集器编号：">
          <el-input readonly :model-value="data.channelNum"></el-input>
        </el-form-item>
        <el-form-item label="条码：" >
          <el-input readonly :model-value="data.barCode"></el-input>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>
<script>
import { ElDivider, ElForm, ElFormItem, ElInput } from "element-plus"
const filedList = [
  { label: "设备厂家：", filed: "factoryName" },
  { label: "终端型号：", filed: "deviceModel" },
  { label: "所属区域：", filed: "areaName" },
  { label: "设备机号：", filed: "deviceNo" },
  { label: "设备名称：", filed: "deviceName" },
  { label: "所属网关：", filed: "gatewayName" },
  { label: "设备使用状态：", filed: "deviceStatus", isDict: true },
  { label: "所属工作站：", filed: "workstationName" },
  { label: "连接类型：", filed: "deviceConnectType", isDict: true },
  { label: "设备IP：", filed: "deviceIp" },
  { label: "固件版本：", filed: "deviceVersion" },
  // {label:"参数：",filed:""},
  { label: "设备SN号：", filed: "deviceSn" },
  { label: "设备添加日期：", filed: "createTime" },
]
export default {
  components: {
    ElDivider,
    ElForm,
    ElFormItem,
    ElInput
  },
  props: {
    data: {
      type: Object,
      default: null
    }
  },
  setup() {
    return {
      filedList
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-box {
  border: 1px solid #dcdfe6;
  border-radius: 5px;
  margin: 10px;

  .title {
    padding: 10px;
  }
}
.el-divider--horizontal {
  margin: 0;
}
.device-position-width {
  :deep(.el-input__inner) {
    width: 100% !important;
  }
}
</style>