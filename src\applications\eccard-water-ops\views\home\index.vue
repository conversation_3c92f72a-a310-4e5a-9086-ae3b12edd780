<template>
  <div class="padding-box">
    <el-row :gutter="20" style="margin-bottom: 20px;">
      <el-col :span="4">
        <el-select v-model="state.form.type" placeholder="Select">
          <el-option v-for="item in typeList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-col>
      <el-col :span="4">
        <el-date-picker
          v-model="state.form.date"
          :type="state.form.type === '1' ? 'daterange' : 'monthrange'"
          range-separator="至"
          :start-placeholder="state.form.type === '1' ? '开始日期' : '开始月份'"
          :end-placeholder="state.form.type === '1' ? '结束日期' : '结束月份'"
          :format="state.form.type === '1' ? 'YYYY-MM-DD' : 'YYYY-MM'"
          :value-format="state.form.type === '1' ? 'YYYY-MM-DD' : 'YYYY-MM'"
          :disabled-date="disabledDate" />
      </el-col>
      <el-col :span="4">
        <el-button type="primary" @click="fetchSummaryData" :loading="state.loading">查询</el-button>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :span="12" :xl="8" :lg="12" :md="24">
        <div class="card-box">
          <div class="summary-box" v-loading="state.loading">
            <img src="@/assets/water_ops_img/summary.png" alt="">
            <div class="summary-content" style="width: 100px;">
              <div class="summary-title">总消费金额</div>
              <div class="summary-value">¥{{ state.summaryData.totalAmount || 0 }}</div>
            </div>
          </div>
        </div>
        <div class="flex-box">
          <div class="card-box" style="flex: 1;margin-right: 20px;">
            <div class="summary-box">
              <img src="@/assets/water_ops_img/boy.png" alt="">
              <div class="summary-content">
                <div class="summary-title">男生消费金额</div>
                <div class="summary-value">¥{{ state.summaryData.maleAmount || 0 }}</div>
              </div>
            </div>
          </div>
          <div class="card-box" style="flex: 1;">
            <div class="summary-box">
              <img src="@/assets/water_ops_img/girl.png" alt="">
              <div class="summary-content">
                <div class="summary-title">女生消费金额</div>
                <div class="summary-value">¥{{ state.summaryData.femaleAmount || 0 }}</div>
              </div>
            </div>
          </div>
        </div>
      </el-col>
      <el-col :span="12" :xl="16" :lg="12" :md="24">
        <div class="card-box">
          <boy-girl-trade-trend :queryDate="queryDate"></boy-girl-trade-trend>
        </div>
      </el-col>
      <el-col :span="12">
        <div class="card-box">
          <operational-change-trend :queryDate="queryDate"></operational-change-trend>
        </div>
      </el-col>
      <el-col :span="12">
        <div class="card-box">
          <user-consume-trend :queryDate="queryDate"></user-consume-trend>
        </div>
      </el-col>
      <el-col :span="24">
        <div class="card-box">
          <month-cost-trend :queryDate="queryDate"></month-cost-trend>
        </div>
      </el-col>
      <el-col :span="24">
        <div class="card-box">
          <year-cost-trend ></year-cost-trend>
        </div>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import { ElRow, ElCol, ElSelect, ElOption, ElDatePicker, ElButton } from 'element-plus';
import boyGirlTradeTrend from './boyGirlTradeTrend.vue'
import operationalChangeTrend from './operationalChangeTrend.vue'
import userConsumeTrend from './userConsumeTrend.vue'
import monthCostTrend from './monthCostTrend.vue'
import yearCostTrend from './yearCostTrend.vue'

export default {
  components: {
    ElRow,
    ElCol,
    ElSelect,
    ElOption,
    ElDatePicker,
    ElButton,
    boyGirlTradeTrend,
    operationalChangeTrend,
    userConsumeTrend,
    monthCostTrend,
    yearCostTrend
  }
}
</script>
<script setup>
import { ref,reactive, onMounted, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { getOpsWaterMainPageTotalNum } from '../../api/home.js';
import { hasDateTimeRange } from '@/utils/date.js';
const typeList = [
  { label: '按天', value: '1' },
  { label: '按月', value: '2' }
]


// 获取默认日期范围（最近一个月）
const getDefaultDateRange = (type = '1') => {
  const today = new Date();
  const oneMonthAgo = new Date();
  oneMonthAgo.setMonth(today.getMonth() - 1);

  if (type === '2') {
    // 按月选择时，返回最近一年的月份范围
    const oneYearAgo = new Date();
    oneYearAgo.setFullYear(today.getFullYear() - 1);
    return [oneYearAgo, today];
  } else {
    // 按天选择时，返回最近一个月的日期范围
    return [oneMonthAgo, today];
  }
};

const state = reactive({
  form: {
    type: '1',
    date: getDefaultDateRange('1')
  },
  summaryData: {
    totalAmount: 0,
    maleAmount: 0,
    femaleAmount: 0
  },
  loading: false
})
const queryDate = ref()
// 格式化日期，根据类型返回不同格式
const formatDate = (date, type = '1') => {
  if (!date) return '';
  const d = new Date(date);
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');

  if (type === '2') {
    // 按月选择时，返回 YYYY-MM 格式
    return `${year}-${month}`;
  } else {
    // 按天选择时，返回 YYYY-MM-DD 格式
    const day = String(d.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }
};

// 验证日期范围是否超出限制
const validateDateRange = (dateArray, type) => {
  if (!dateArray || dateArray.length !== 2) {
    return true; // 如果没有选择完整的日期范围，不进行验证
  }

  if (type === '1') {
    // 按天选择：检查是否超过一个月
    if (hasDateTimeRange(dateArray, 1)) {
      ElMessage.warning('按天查询时间范围不能超过一个月');
      return false;
    }
  } else if (type === '2') {
    // 按月选择：检查是否超过一年
    if (hasDateTimeRange(dateArray, 12)) {
      ElMessage.warning('按月查询时间范围不能超过一年');
      return false;
    }
  }

  return true;
};

// 日期选择限制函数
const disabledDate = (time) => {
  const currentDate = new Date();
  const selectedDate = new Date(time);

  // 如果还没有选择日期，只限制不能选择未来日期
  if (!state.form.date || state.form.date.length === 0) {
    return selectedDate > currentDate;
  }

  // 如果已经选择了开始日期
  if (state.form.date.length === 1) {
    const startDate = new Date(state.form.date[0]);

    if (state.form.type === '1') {
      // 按天选择：限制范围为一个月
      const oneMonthLater = new Date(startDate);
      oneMonthLater.setMonth(oneMonthLater.getMonth() + 1);

      const oneMonthBefore = new Date(startDate);
      oneMonthBefore.setMonth(oneMonthBefore.getMonth() - 1);

      return selectedDate > oneMonthLater || selectedDate < oneMonthBefore || selectedDate > currentDate;
    } else {
      // 按月选择：限制范围为一年
      const oneYearLater = new Date(startDate);
      oneYearLater.setFullYear(oneYearLater.getFullYear() + 1);

      const oneYearBefore = new Date(startDate);
      oneYearBefore.setFullYear(oneYearBefore.getFullYear() - 1);

      return selectedDate > oneYearLater || selectedDate < oneYearBefore || selectedDate > currentDate;
    }
  }

  // 默认不能选择未来日期
  return selectedDate > currentDate;
};


// 获取统计数据
const fetchSummaryData = async () => {
  try {
    state.loading = true;
    // 构建请求参数
    queryDate.value = {
      startTime: state.form.date.length > 0 ? formatDate(state.form.date[0], state.form.type) : '',
      endTime: state.form.date.length > 1 ? formatDate(state.form.date[1], state.form.type) : '',
      type: state.form.type
    };
    console.log('构建请求参数', queryDate.value);
    const response = await getOpsWaterMainPageTotalNum(queryDate.value);
    console.log('总消费金额', response);
    if (response && response.code === 0) {
      state.summaryData = {
        totalAmount: response.data?.total || 0,
        maleAmount: response.data?.male || 0,
        femaleAmount: response.data?.female || 0
      };
    }
  } catch (error) {
    console.error('获取统计数据失败:', error);
  } finally {
    state.loading = false;
  }
};
// 监听类型变化，设置对应的默认日期范围
watch(() => state.form.type, (newType, oldType) => {
  if (newType !== oldType) {
    // 切换类型时设置对应的默认日期范围
    state.form.date = getDefaultDateRange(newType);
  }
});

// 监听日期变化，进行范围验证
watch(() => state.form.date, (newDate) => {
  if (newDate && newDate.length === 2) {
    // 验证日期范围
    if (!validateDateRange(newDate, state.form.type)) {
      // 如果验证失败，重置为默认日期范围
      setTimeout(() => {
        state.form.date = getDefaultDateRange(state.form.type);
      }, 100);
      return;
    }
  }
}, { deep: true });

// 监听表单变化，重新获取数据
watch(() => [state.form.type, state.form.date], () => {
  // 只有在日期范围有效时才获取数据
  if (state.form.date && state.form.date.length === 2) {
    if (validateDateRange(state.form.date, state.form.type)) {
      fetchSummaryData();
    }
  } else if (state.form.date && state.form.date.length === 0) {
    // 如果日期为空，也获取数据（使用默认参数）
    fetchSummaryData();
  }
}, { deep: true });

// 组件挂载时获取数据
onMounted(() => {
  fetchSummaryData();
});

</script>

<style lang="scss" scoped>
.card-box {
  border: 1px solid #e4e7ed;
  border-radius: 10px;
  padding: 20px;
  margin-bottom: 20px;
}

.flex-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.summary-box {
  padding: 40px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
  width: 100%;

  img {
    width: 60px;
    height: 60px;
    margin-right: 20px;
  }

  .summary-content {
    .summary-title {
      font-size: 14px;
      font-weight: 500;
      color: #666;
      margin-bottom: 10px;
      width: 100px;
    }

    .summary-value {
      font-size: 28px;
      font-weight: 500;
      color: #000;
      font-weight: bold;
    }
  }
}
</style>