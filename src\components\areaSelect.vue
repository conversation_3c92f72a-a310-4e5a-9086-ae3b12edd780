<template>
    <kade-dropdown-select-tree
      style="width: 100%"
      :action="areaAction"
      idKey="areaId"
      queryKey="areaParentId"
      :opts="areaTreeOpts"
      :text="text"
      @change="handleAreaChange"
    />
</template>
<script>
// 区域下拉树选择
import DropdownSelectTree from '@/components/dropdownSelectTree';
import { getAreaMenuList } from '@/applications/eccard-basic-data/api';
export default {
  emits: ['change'],
  components: {
    'kade-dropdown-select-tree': DropdownSelectTree,
  },
  props: {
    text: {
      type: String,
    }
  },
  setup(props, context) {
    const areaAction = async (params) => {
      const { data: { areaMenuList } } = await getAreaMenuList(params);
      return areaMenuList;
    }
    const handleAreaChange = (area) => {
      context.emit('change', area);
    }
    return {
      areaAction,
      handleAreaChange,
      areaTreeOpts: {
        props: {
          children: 'children',
          label: 'areaName',
          isLeaf: (data) => {
            if(data['isLeaf'] === undefined) {
                return true;
            }
            return !!data.isLeaf;                        
          }
        },
      },      
    }
  }
}
</script>