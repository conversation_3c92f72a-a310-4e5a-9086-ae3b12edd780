<template>
  <div class="padding-form-box setBasicParam" v-loading="state.loading">
    <el-form inline size="small" label-width="200px" :model="state.form.paramContent" :rules="rules">
      <el-form-item label="倍率:">
        <el-input-number :min="0" :max="100" v-model="state.form.paramContent.ratio"></el-input-number>
      </el-form-item>
      <el-form-item label="最大值:">
        <el-input-number :min="0" v-model="state.form.paramContent.maxValue"></el-input-number>
      </el-form-item>
      <el-form-item label="是否需要告警:">
        <el-select v-model="state.form.paramContent.alarm">
          <el-option v-for="(item,index) in dictListFnc().alarm" :key="index" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="告警上限:">
        <el-input-number :min="0" v-model="state.form.paramContent.alarmLimit"></el-input-number>
      </el-form-item>
      <el-form-item label="告警下限:">
        <el-input-number :min="0" v-model="state.form.paramContent.alarmThreshold"></el-input-number>
      </el-form-item>
    </el-form>
  </div>
  <div style="text-align: center">
    <el-button size="mini" @click="beforeClose()">取消</el-button>
    <el-button size="mini" type="primary" @click="saveClick()">保存</el-button>
  </div>
</template>

<script>
import {
  ElButton,
  ElForm,
  ElFormItem,
  ElInputNumber,
  ElOption,
  ElSelect,
  ElMessage,
} from "element-plus";
import { onMounted, reactive } from "vue";
import { useStore } from "vuex";
const defaultParamsFnc = () => {
  return {
    alarm: "YES",	//是否需要告警	string	
    alarmLimit: 0,//告警上限	integer(int32)	
    alarmThreshold: 0,	//告警下限	integer(int32)	
    maxValue: 0,	//最大值	integer(int32)	
    ratio: 0,	//倍率	integer(int32)
  };
};

export default {
  components: {
    ElInputNumber,
    ElForm,
    ElFormItem,
    ElSelect,
    ElOption,
    ElButton,
  },
  setup() {
    const store = useStore();
    const state = reactive({
      loading: false,
      form: {
        paramContent: defaultParamsFnc(),
        id: "",
        paramId: "",
        paramType: "BASE",
      },
    });
    const rules = {
      a: [

      ]
    }
    const dictListFnc = () => {
      return store.state.deviceParameters[store.state.app.activeTab].dict
    }
    const saveClick = async () => {
      state.loading = true;
      let storeData = store.state.deviceParameters[store.state.app.activeTab]
      state.form.paramId = storeData.selectRow.id;
      let fn = state.form.id ? storeData.TabModule.detailsEditFnc : storeData.TabModule.detailsAddFnc

      let params = { ...state.form }
      console.log(params.paramContent);
      params.paramContent = JSON.stringify(params.paramContent)
      let { message, code } = await fn(params);
      if (code === 0) {
        setTimeout(() => {
          ElMessage.success(message);
          store.dispatch("deviceParameters/getParams", store.state.deviceParameters[store.state.app.activeTab].selectRow.id);
          state.loading = false;
        }, 1500);
      }
    };

    const beforeClose = () => {
      store.commit("deviceParameters/updateState", {
        key: store.state.app.activeTab,
        childKey: "isSetParams",
        payload: false,
      });
    };
    onMounted(() => {
      // store.dispatch("waterParameters/getParams");
      let val = store.state.deviceParameters[store.state.app.activeTab].detailsParams
      if (val && val.length) {
        let baseList = val.filter((item) => item.paramType == "BASE");
        if (baseList.length) {
          state.form.id = baseList[0].id;
          for (let key in state.form.paramContent) {
            state.form.paramContent[key] = JSON.parse(
              baseList[0].paramContent
            )[key];
          }
        } else {
          state.form.id = "";
          state.form.paramContent = defaultParamsFnc();
        }
      } else {
        //设置默认选项
        state.form.id = "";
        state.form.paramContent = defaultParamsFnc();
      }
    });
    return {
      state,
      rules,
      dictListFnc,
      saveClick,
      beforeClose,
    };
  },
};
</script>

<style lang="scss" scoped>
.setBasicParam {
  border: 1px solid #dcdfe6;
  border-radius: 5px;
  margin-bottom: 10px;
}
:deep(.el-form-item__content) {
  width: 200px;
  .el-input-number {
    width: 100%;
  }
  .el-select {
    width: 100% !important;
  }
}
</style>
