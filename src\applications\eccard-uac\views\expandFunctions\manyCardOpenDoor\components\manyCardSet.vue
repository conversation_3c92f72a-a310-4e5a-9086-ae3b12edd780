<template>
  <el-dialog :model-value="modelValue" title="多卡开门设置" width="1700px" :before-close="handleClose"
    :close-on-click-modal="false">
    <div style="padding:20px 0 0">
      <kade-table-wrap title="多卡开门" icon="none"  style="margin-bottom:20px">
        <el-divider></el-divider>
        <div class="padding-box">
          <el-checkbox v-model="state.form.checked" size="large" style="margin:10px 0">启用多卡开门</el-checkbox>
          <el-row :gutter="20">
            <el-col :span="6">
              <kade-table-wrap title="开始" icon="none" style=" padding: 0;">
                <el-divider></el-divider>
                <div class="padding-box">
                  <el-form size="mini" label-width="100px">
                    <el-form-item label="">
                      <el-checkbox-group v-model="state.form.checkList">
                        <el-checkbox v-for="(item, index) in accessList" :key="index" :label="item.value">{{ item.label }}
                        </el-checkbox>
                      </el-checkbox-group>
                    </el-form-item>
                    <el-form-item label="刷卡人数：">
                      <el-input-number v-model="state.form.num" placeholder="请输入" style="width:180px" />
                    </el-form-item>
                  </el-form>
                </div>
              </kade-table-wrap>
            </el-col>
            <el-col :span="18">
              <kade-table-wrap title="人数设置" icon="none" style=" padding: 0;height: 100%;">
                <el-divider></el-divider>
                <div class="padding-box">
                  <el-form size="mini" label-width="100px" inline>
                    <el-form-item label="第一组人数：" v-for="(item, index) in 8" :key="index">
                      <el-input-number v-model="state.form.num" placeholder="请输入" style="width:180px" />
                    </el-form-item>
                  </el-form>
                </div>
              </kade-table-wrap>
            </el-col>
          </el-row>
        </div>
      </kade-table-wrap>
      <kade-table-wrap title="多卡用户选择" icon="none"  style="margin-bottom:20px">
        <el-divider></el-divider>
        <div class="padding-box">
          <kade-shuttle-data :titleData="titleData" :listData="state.listData" :columnData="columnData"
            @oneCurrentChange="oneCurrentChange" @twoCurrentChange="twoCurrentChange" @toAllRight="toAllRight"
            @toRight="toRight" @toLeft="toLeft" @toAllLeft="toAllLeft">
            <template #firstSearch>
              <el-form label-width="80px" size="mini" inline>
                <el-form-item label="关键字">
                  <el-input v-model="state.form.b" placeholder="输入编号或姓名"></el-input>
                </el-form-item>
                <el-button type="primary" @click="firstSearch" size="mini">搜索</el-button>
              </el-form>
            </template>
            <template #secondSearch>
              <el-form label-width="80px" size="mini" inline>
                <el-form-item label="关键字">
                  <el-input v-model="state.form.b" placeholder="输入编号或姓名"></el-input>
                </el-form-item>
                <el-button type="primary" @click="firstSearch" size="mini">搜索</el-button>
              </el-form>
            </template>
          </kade-shuttle-data>
        </div>
      </kade-table-wrap>

    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose" size="mini">取消</el-button>
        <el-button type="primary" v-if="type != 'details'" @click="submit" size="mini">保存</el-button>
        <el-button type="primary" v-else @click="$emit('edit')" size="mini">编辑</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script>
import { ElDialog, ElDivider, ElRow, ElCol, ElButton, ElForm, ElFormItem, ElInput, ElCheckboxGroup, ElCheckbox,ElInputNumber } from "element-plus"
import { reactive } from '@vue/reactivity'
import shuttleData from "@/components/shuttleData.vue"
const accessList = [
  { label: '进门要求多卡', value: 1 },
  { label: '出门要求多卡', value: 2 },
]

const titleData = {
  oneTitle: '待选列表',
  twoTitle: '已选列表'
}
const columnData = {
  oneList: [
    { label: "组织机构", prop: "", },
    { label: "身份类别", prop: "", },
    { label: "用户编号", prop: "", },
    { label: "用户名称", prop: "", },
  ],
  twoList: [
    { label: "组织机构", prop: "", },
    { label: "身份类别", prop: "", },
    { label: "用户编号", prop: "", },
    { label: "用户名称", prop: "", },
  ],
}
export default {
  components: {
    ElDialog, ElDivider, ElRow, ElCol, ElButton, ElForm, ElFormItem, ElInput, ElCheckboxGroup, ElCheckbox,ElInputNumber,
    'kade-shuttle-data': shuttleData
  },
  emits: ["update:modelValue"],
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
  },
  setup(props, context) {
    const state = reactive({
      form: {
        checkList: []
      },
      listData: {
        oneList: [],
        oneTotal: 100,
        twoList: [],
        twoTotal: 100
      }
    })
    const handleClose = () => {
      context.emit("update:modelValue", false)
    }
    return {
      accessList,
      titleData,
      columnData,
      state,
      handleClose
    }
  }
}
</script>
<style lang="scss" scoped>

.padding-box {
  padding: 10px 10px 0
}

.el-divider--horizontal {
  margin: 0
}

.week-box {
  height: 140px;
  display: flex;
  // justify-content: center;
  align-items: center;
}

:deep(.box) {
  padding: 10px 10px 0;

  .el-form {
    .el-select {
      width: 150px;
    }

    .el-input {
      width: 150px
    }
  }
}

:deep(.el-input) {
  width: 100%;
}

:deep(.el-select) {
  width: 100%;
}

.el-table {
  border-left: 1px solid #EBEEF5;
  border-right: 1px solid #EBEEF5;
}
</style>