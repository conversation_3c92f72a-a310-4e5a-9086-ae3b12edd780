<template>
  <el-dialog v-loading="state.loading" :model-value="isShow" :title="data.id?'编辑教育经历':'新增教育经历'" width="500px" :before-close="beforeClose" :append-to-body="true">
    <div style="padding-top: 20px">
      <el-form size="mini" label-width="120px" ref="formDom" :rules="state.rules" :model="state.form">
        <el-form-item label="学校名称:" prop="schoolName">
          <el-input placeholder="请输入" v-model="state.form.schoolName" :maxlength="20"></el-input>
        </el-form-item>
        <el-form-item label="学历:" prop="education">
          <el-input placeholder="请输入" v-model="state.form.education" :maxlength="20"></el-input>
        </el-form-item>
        <el-form-item label="专业:" prop="studyMajor">
          <el-input placeholder="请输入" v-model="state.form.studyMajor" :maxlength="20"></el-input>
        </el-form-item>
        <el-form-item label="教学方式:" prop="learningWays">
          <el-input placeholder="请输入" v-model="state.form.learningWays" :maxlength="20"></el-input>
        </el-form-item>
        <el-form-item label="学位:" prop="academicDegree">
          <el-input placeholder="请输入" v-model="state.form.academicDegree" :maxlength="20"></el-input>
        </el-form-item>
        <el-form-item label="入学时间:" prop="beginTime">
          <el-date-picker v-model="state.form.beginTime" type="month" placeholder="请选择" />
        </el-form-item>
        <el-form-item label="毕业时间:" prop="endTime">
          <el-date-picker v-model="state.form.endTime" type="month" placeholder="请选择" />
        </el-form-item>
        <el-form-item label="是否为最高学历:">
          <el-switch v-model="state.form.whetherHighestEducation" active-value="TRUE" inactive-value="FALSE"></el-switch>
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="beforeClose" size="mini">取消</el-button>
        <el-button type="primary" @click="submit()" size="mini">提交</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script>
import {
  ElDialog,
  ElForm,
  ElFormItem,
  ElInput,
  ElSwitch,
  ElDatePicker,
  ElButton,
  ElMessage,
} from "element-plus";
import { reactive, ref } from "@vue/reactivity";
import { nextTick, watch } from "@vue/runtime-core";
import { useStore } from "vuex";
import { timeStr } from "@/utils/date.js";
import { addTeach, editTeach } from "@/applications/eccard-basic-data/api";
export default {
  components: {
    ElDialog,
    ElForm,
    ElFormItem,
    ElInput,
    ElSwitch,
    ElDatePicker,
    ElButton,
  },
  props: {
    isShow: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Object,
      default: null,
    },
  },
  setup(props, context) {
    const store = useStore();
    const formDom = ref(null);
    const state = reactive({
      loading:false,
      title: "",
      form: {
        whetherHighestEducation: "FALSE",
      },
      rules: {
        schoolName: [
          {
            required: true,
            message: "请输入学校名称",
            trigger: "blur",
          },
        ],
        education: [
          {
            required: true,
            message: "请输入学历",
            trigger: "blur",
          },
        ],
        studyMajor: [
          {
            required: true,
            message: "请输入专业",
            trigger: "blur",
          },
        ],
        learningWays: [
          {
            required: true,
            message: "请输入教学方式",
            trigger: "blur",
          },
        ],
        academicDegree: [
          {
            required: true,
            message: "请输入学位",
            trigger: "blur",
          },
        ],
        beginTime: [
          {
            required: true,
            message: "请选择入学时间",
            trigger: "change",
          },
        ],
        endTime: [
          {
            required: true,
            message: "请选择毕业时间",
            trigger: "change",
          },
        ],
      },
      studyDate: [],
    });
    watch(
      () => props.isShow,
      (val) => {
        if (val) {
          state.form = props.data.id ? { ...props.data } : {};
        }
        nextTick(() => {
          formDom.value.clearValidate();
        });
      }
    );
    const submit = () => {
      formDom.value.validate(async (valid) => {
        if (valid) {
          state.loading=true
          let fn = props.data.id ? editTeach : addTeach;
          let params={...state.form}
          params.beginTime=timeStr(params.beginTime)
          params.endTime=timeStr(params.endTime)
          params.userId=store.state.userInfo.rowData.id
          let { code, message } = await fn(params);
          if (code === 0) {
            ElMessage.success(message);
            context.emit("close", true);
          }
          state.loading=false
        } else {
          return false;
        }
      });
    };

    const beforeClose = () => {
      context.emit("close", false);
      nextTick(() => {
        formDom.value.clearValidate();
      });
    };
    return {
      formDom,
      state,
      submit,
      beforeClose,
    };
  },
};
</script>
<style lang="scss" scoped>
:deep(.el-form-item__content) {
  .el-select {
    width: 100% !important;
  }
  .el-input{
    width: 100%;
  }
  .el-input__inner {
    width: 100% !important;
  }
}
</style>