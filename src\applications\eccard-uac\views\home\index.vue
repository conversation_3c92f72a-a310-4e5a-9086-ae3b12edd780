<template>
  <div class="home padding-box">
    <div class="summary-box">
      <div class="summary-item" v-for="(item, index) in list" :key="index">
        <div class="left" :style="{ backgroundColor: item.background }">
          <img :src="item.icon" alt="">
        </div>
        <div class="right">
          <div class="value" :style="{ color: item.background }">{{ item.value }}</div>
          <div class="label">{{ item.label }}</div>
        </div>
      </div>
    </div>
    <el-row :gutter="20">
      <el-col :span="8" style="margin-bottom:20px">
        <kade-online-device />
      </el-col>
      <el-col :span="8" style="margin-bottom:20px">
        <kade-lock-monitor />
      </el-col>
      <el-col :span="8" style="margin-bottom:20px">
        <kade-pass-person-num />
      </el-col>
      <el-col :span="16">
        <kade-pass-person-curve />
      </el-col>
      <el-col :span="8">
        <kade-abnormal />
      </el-col>
    </el-row>
  </div>
</template>
<script>
import { reactive } from '@vue/reactivity'
import { ElCol, ElRow } from "element-plus"
import acIcon from "@/assets/uac_img/ac.svg"
import ccIcon from "@/assets/uac_img/cc.svg"
import faceIcon from "@/assets/uac_img/face.svg"
import wirelessIcon from "@/assets/uac_img/wireless.svg"
import plateIcon from "@/assets/uac_img/plate.svg"
import onlineDevice from "@/applications/eccard-uac/views/home/<USER>"
import lockMonitor from "@/applications/eccard-uac/views/home/<USER>"
import passPersonNum from "@/applications/eccard-uac/views/home/<USER>"
import passPersonCurve from "@/applications/eccard-uac/views/home/<USER>"
import abnormal from "@/applications/eccard-uac/views/home/<USER>"

export default {
  components: {
    ElCol, ElRow,
    "kade-online-device": onlineDevice,
    "kade-lock-monitor": lockMonitor,
    "kade-pass-person-num": passPersonNum,
    "kade-pass-person-curve": passPersonCurve,
    "kade-abnormal": abnormal,

  },
  setup() {
    const list = [
      { label: "门禁控制器", value: 6542, icon: acIcon, background: "#3399ff" },
      { label: "通道控制器", value: 6542, icon: ccIcon, background: "#e64444" },
      { label: "人脸识别智能终端", value: 6542, icon: faceIcon, background: "#3c6" },
      { label: "无线联网智能门锁", value: 6542, icon: wirelessIcon, background: "#ff8726 " },
      { label: "高清车牌识别一体机", value: 6542, icon: plateIcon, background: "#935cff" },
    ]
    const state = reactive({

    })
    return {
      state,
      list,
    }
  }
}
</script>
<style lang="scss" scoped>
.home {
  .summary-box {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;

    .summary-item {
      box-sizing: border-box;

      width: 19%;
      border-radius: 5px;
      box-shadow: 1px 3px 5px rgb(0 0 0 / 12%);
      overflow: hidden;
      display: flex;

      .left {
        width: 100px;
        // height: 100px;
        display: flex;
        justify-content: center;
        align-items: center;

        img {
          width: 40px;
          height: 40px;
        }
      }

      .right {
        flex: 1;
        margin-left: 20px;
        padding: 20px 0;

        .value {
          font: 36px arial;
          // height: 60px;
        }

        .label {
          font-size: 14px;
          color: #999999;
        }
      }
    }
  }
}

:deep(.kade-route-card) {
  height: auto;
}

:deep(.el-card__header) {
  padding: 0;

  .header {
    display: flex;
    justify-content: space-between;
    // align-items: center;

    .header-title {
      display: inline-block;
      padding: 16px;
      border-bottom: 2px solid #3399ff;
      font-size: 16px;
      color: #1890FF;
    }

    .header-right {
      padding-top: 32px;
      margin-right: 20px;
    }
  }

}
</style>