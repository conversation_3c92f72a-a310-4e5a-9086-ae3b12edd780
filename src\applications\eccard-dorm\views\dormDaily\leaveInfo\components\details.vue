<template xmlns:el-form-item="http://www.w3.org/1999/html">
  <el-dialog
    :model-value="dialogVisible"
    title="学生请假记录详情"
    width="40%"
    :before-close="handleClose"
  >
    <el-form style="margin-top:20px" inline label-width="100px" size="mini">
      <el-form-item :label="item.label" v-for="(item,index) in state.dialogList" :key="index">
        <el-input readonly :model-value="data[item.filed]"></el-input>
      </el-form-item>
    </el-form>
    <el-form label-width="100px" size="mini" style="padding-bottom:20px">
      <el-form-item label="请假时间">
        <el-input readonly :model-value="data.leavePeriod"></el-input>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script>
import {ElDialog, ElForm, ElFormItem, ElInput} from "element-plus";
import {reactive} from "@vue/reactivity";

export default {
  components: {
    ElDialog,
    ElForm,
    ElFormItem,
    ElInput,
  },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    data: {
      types: Object,
      default: {}
    }
  },
  setup(prop, context) {
    const state = reactive({
      dialogList: [
        {label: "人员编号：", filed: "userId",},
        {label: "人员姓名：", filed: "userName"},
        {label: "组织机构：", filed: "operateAccount"},
        {label: "请假原因：", filed: "reason"},
        {label: "楼栋：", filed: "ip"},
        {label: "单元号：", filed: "operateTime"},
        {label: "楼层号：", filed: "requestParam"},
        {label: "房间号：", filed: "roomId"}
      ],
      leavePeriod: {label: "请假时间：", filed: "leavePeriod"},
    });
    const handleClose = () => {
      context.emit("close", false);
    };
    return {
      state,
      handleClose,
    };
  },
};
</script>

<style lang="scss" scoped>

</style>
