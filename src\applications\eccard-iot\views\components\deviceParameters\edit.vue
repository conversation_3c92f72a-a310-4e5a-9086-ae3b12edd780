<template>
  <el-dialog :model-value="modelValue" v-loading="state.loading" :title="state.title" width="400px" :before-close="beforeClose">
    <div style="padding-top: 20px">
      <el-form size="mini" label-width="120px" ref="formDom" :rules="state.rules" :model="state.form">
        <el-form-item label="参数名称:" prop="name">
          <el-input placeholder="请输入" v-model="state.form.name" :maxlength="20"></el-input>
        </el-form-item>
        <el-form-item label="参数编号:" prop="code">
          <el-input placeholder="请输入" v-model="state.form.code" :maxlength="20"></el-input>
        </el-form-item>
        <el-form-item label="是否启用:">
          <el-switch v-model="state.form.useStatus" active-value="TRUE" inactive-value="FALSE"></el-switch>
        </el-form-item>
        <el-form-item label="备注:">
          <el-input placeholder="请输入备注" v-model="state.form.remarks" :maxlength="200"></el-input>
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="beforeClose" size="mini">取消</el-button>
        <el-button type="primary" @click="submit()" size="mini">提交</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script>
import {
  ElDialog,
  ElForm,
  ElFormItem,
  ElInput,
  ElSwitch,
  ElButton,
  ElMessage,
} from "element-plus";
import { reactive, ref } from "@vue/reactivity";
import { useStore } from "vuex";
import { watch } from "@vue/runtime-core";
export default {
  components: {
    ElDialog,
    ElForm,
    ElFormItem,
    ElInput,
    ElSwitch,
    ElButton,
  },
  props: {
    modelValue: {
      type: Boolean,
      default: false,
    },
    listResquest: {
      type: Function,
      default: null,
    },
    TabModule: {
      type: Function,
      default: null,
    }
  },
  setup(props, context) {
    const store = useStore();
    const formDom = ref(null);
    const state = reactive({
      loading: false,
      title: "",
      form: {},
      rules: {
        name: [
          {
            required: true,
            message: "请输入参数名称",
            trigger: "blur",
          },
        ],
        code: [
          {
            required: true,
            message: "请输入参数编号",
            trigger: "blur",
          },
        ],
      },
    });

    watch(
      () => props.modelValue,
      (val) => {
        if (val) {
          if (store.state.deviceParameters[store.state.app.activeTab].selectRow.id) {
            state.title = "修改参数";
            let { name, code, remarks, useStatus, id, paramType } =
              store.state.deviceParameters[store.state.app.activeTab].selectRow
            state.form = { name, code, remarks, useStatus, id, paramType };
          } else {
            state.title = "新增参数";
            state.form = {};
          }
        }
      }
    );
    const submit = () => {
      state.form.paramType = props.TabModule().title
      let fnc = store.state.deviceParameters[store.state.app.activeTab].selectRow.id
        ? props.TabModule().editFnc
        : props.TabModule().addFnc;
        console.log(props.TabModule());
      formDom.value.validate(async (valid) => {
        if (valid) {
          state.loading = true
          try {
            let { code, message } = await fnc(state.form);
            if (code === 0) {
              ElMessage.success(message);
              context.emit("update:modelValue", false)
              props.listResquest()
              state.loading = false
            }
          }
          catch {
            state.loading = false
          }
        } else {
          return false;
        }
      });
    };

    const beforeClose = () => {
      context.emit("update:modelValue", false)
    };
    return {
      formDom,
      state,
      submit,
      beforeClose,
    };
  },
};
</script>
<style lang="scss" scoped>
</style>