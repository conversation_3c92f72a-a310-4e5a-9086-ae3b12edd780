import {
  getRechargeRecord,
  getRightingList
} from "@/applications/eccard-finance/api";
import {
  ElMessage
} from "element-plus";
const state = {
  isOff: false,
  selectPerson: "",
  RechargeRecord: [],
  RechargeRecordTotal: 0,
  RightingList: [],
  RightingListTotal: 0
};
const mutations = {
  updateState(state, { key, payload }) {
    state[key] = payload;
  },
};
const actions = {
  queryRechargeRecord({ commit }, data) {
    getRechargeRecord(data).then(res => {
      commit("updateState", { key: "RechargeRecord", payload: res.data.list })
      commit("updateState", { key: "RechargeRecordTotal", payload: res.data.total })
    })
  },
  queryRightingList({ commit }, data) {
    if (state.selectPerson) {
      getRightingList(data).then(res => {
        commit("updateState", { key: "RightingList", payload: res.data.list })
        commit("updateState", { key: "RightingListTotal", payload: res.data.total })
      })
    } else {
      ElMessage.error("请选择人员！")
    }
  }

};
export default {
  namespaced: true,
  state,
  mutations,
  actions
}