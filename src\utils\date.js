/*-- js逻辑部分 --*/

//格式化--时间
export function timeStr(dataStr) {
  var date = new Date(dataStr);
  var y = date.getFullYear();

  var m = date.getMonth() + 1;
  m = m < 10 ? ('0' + m) : m;

  var d = date.getDate();
  d = d < 10 ? ('0' + d) : d;

  var h = date.getHours();
  h = h < 10 ? ('0' + h) : h;

  //获得分
  var mm = date.getMinutes()
  mm = mm < 10 ? ('0' + mm) : mm;

  //获得秒
  var ss = date.getSeconds()
  ss = ss < 10 ? ('0' + ss) : ss;

  // console.log(y+'-'+m+'-'+d+' '+h+':'+mm+':'+ss)

  return y + '-' + m + '-' + d + ' ' + h + ':' + mm + ':' + ss
}

export function dateStr(dataStr) {
  var date = new Date(dataStr);
  var y = date.getFullYear();

  var m = date.getMonth() + 1;
  m = m < 10 ? ('0' + m) : m;

  var d = date.getDate();
  d = d < 10 ? ('0' + d) : d;

  // console.log(y+'-'+m+'-'+d+' '+h+':'+mm+':'+ss)

  return y + '-' + m + '-' + d
}

export function monthStr(dataStr) {
  var date = new Date(dataStr);
  var y = date.getFullYear();

  var m = date.getMonth() + 1;
  m = m < 10 ? ('0' + m) : m;
  // console.log(y+'-'+m+'-'+d+' '+h+':'+mm+':'+ss)

  return y + '-' + m
}


export function dayStr(dataStr) {
  var date = new Date(dataStr);

  var m = date.getMonth() + 1;
  m = m < 10 ? ('0' + m) : m;

  var d = date.getDate();
  d = d < 10 ? ('0' + d) : d;

  // console.log(y+'-'+m+'-'+d+' '+h+':'+mm+':'+ss)

  return  m + '-' + d
}



export function hourStr(dataStr) {
  var date = new Date(dataStr);
  var h = date.getHours();
  h = h < 10 ? ('0' + h) : h;

  //获得分
  var mm = date.getMinutes()
  mm = mm < 10 ? ('0' + mm) : mm;

  //获得秒
  var ss = date.getSeconds()
  ss = ss < 10 ? ('0' + ss) : ss;

  // console.log(y+'-'+m+'-'+d+' '+h+':'+mm+':'+ss)

  return h + ':' + mm + ':' + ss
}

export function hourMinStr(dataStr) {
  var date = new Date(dataStr);
  var h = date.getHours();
  h = h < 10 ? ('0' + h) : h;

  //获得分
  var mm = date.getMinutes()
  mm = mm < 10 ? ('0' + mm) : mm;

  return h + ':' + mm
}

//时分秒转换为时间戳
export function time_to_sec(time) {
  if (time) {
    var s = "";
    var hour = time.split(":")[0];
    var min = time.split(":")[1];
    var sec = time.split(":")[2];
    s = Number(hour * 3600 * 1000) + Number(min * 60 * 1000) + Number(sec * 1000);
    return s;
  }
}

//时分转换为时间戳
export function hour_and_min_to_sec(time) {
  if (time) {
    var s = "";
    var hour = time.split(":")[0];
    var min = time.split(":")[1];
    s = Number(hour * 3600 * 1000) + Number(min * 60 * 1000)
    return s;
  }
}
// 判断日期时间区间是否超过设定的月份
export function hasDateTimeRange(dateList, monthNum = 1) {
  let time1 = new Date(dateList[0]).getTime();
  let time2 = new Date(dateList[1]).getTime();
  return time2 - time1 > 1000 * 60 * 60 * 24 * 31 * monthNum;
}
