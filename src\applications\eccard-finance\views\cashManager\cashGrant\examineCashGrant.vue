<template>
  <div class="income-detail border-box" v-loading="state.loading">
    <div class="padding-form-box">
      <el-form inline size="small" label-width="100px">
        <el-form-item label="充值项目名称:">
          <el-input placeholder="关键字搜索" v-model="state.form.projectName" :maxlength="20"></el-input>
        </el-form-item>
        <!-- <el-form-item label="状态:">
          <el-select
            clearable
            v-model="state.form.auditStatus"
            placeholder="请选择"
          >
            <el-option
              v-for="(item, index) in state.auditStatusList"
              :key="index"
              :label="item.dictValue"
              :value="item.dictCode"
            >
            </el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item label="选择日期:">
          <el-col :span="24">
            <el-date-picker v-model="state.requestDate" type="datetimerange" :default-time="state.defaultTime"
              range-separator="~" start-placeholder="请选择日期" end-placeholder="请选择日期" unlink-panels @change="changeDate">
            </el-date-picker>
          </el-col>
        </el-form-item>
        <el-button @click="search()" size="small" type="primary" icon="el-icon-search">查询</el-button>
        <el-button icon="el-icon-refresh-right" @click="reset()" size="small">重置</el-button>
        <el-form-item :label="'&nbsp;'">
          <el-button icon="el-icon-s-check" @click="examine()" class="btn-deep-blue" size="small">审核</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-table style="width: 100%" :data="state.dataList" v-loading="false" @row-click="rowClick" highlight-current-row
      border stripe>
      <el-table-column width="150" label="充值项目名称" prop="projectName" align="center"></el-table-column>
      <el-table-column width="100" prop="auditStatus" label="状态" align="center">
        <template #default="scope">
          <div>
            {{
                filterDictionary(scope.row.auditStatus, state.auditStatusList)
            }}
          </div>
        </template>
      </el-table-column>

      <el-table-column label="交易类型" prop="tradeType" align="center"></el-table-column>
      <el-table-column label="充值钱包" prop="walletName" align="center"></el-table-column>
      <el-table-column label="充值总金额(元)" prop="totalAmount" align="center"></el-table-column>
      <el-table-column label="充值总人数" prop="totalPersonCount" align="center"></el-table-column>
      <el-table-column label="人员清单" align="center">
        <template #default="scope">
          <el-button type="text" @click="handleBtnClick(scope.row)" size="mini">查看清单</el-button>
        </template>
      </el-table-column>
      <el-table-column label="导入人员" prop="createUserName" align="center"></el-table-column>
      <el-table-column label="审核人员" prop="auditPerson" align="center"></el-table-column>
      <el-table-column label="创建时间" align="center" width="160">
        <template #default="scope">
          <div>
            {{ timeStr(scope.row.createTime) }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="200px">
        <template #default="scope">
          <el-button type="text" @click="listDetailsClick(scope.row)" size="mini">查看详情</el-button>
          <el-button type="text" @click="edit(scope.row)" size="mini">编辑</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="pagination">
      <el-pagination background :current-page="state.form.currentPage" :page-size="state.form.pageSize"
        layout="total, sizes, prev, pager, next, jumper" :page-sizes="[6, 10, 20, 50, 100]" :total="state.dataListTotal"
        @current-change="handlePageChange" @size-change="handleSizeChange">
      </el-pagination>
    </div>
    <kade-examine-cash :dialogVisible="state.dialogVisible" @off="offExamine" />
  </div>
</template>
<script>
import { onMounted, reactive } from "vue";
import { useStore } from "vuex";
import {
  ElCol,
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElDatePicker,
  ElTable,
  ElTableColumn,
  ElPagination,
  ElMessage,
} from "element-plus";

import { timeStr } from "@/utils/date.js";
import { requestDate, requestDefaultTime } from "@/utils/reqDefaultDate.js";
import { getCashGrantListByPage } from "@/applications/eccard-finance/api";
import examineCash from "./components/examineCash.vue";
export default {
  components: {
    "el-button": ElButton,
    "el-input": ElInput,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    "el-date-picker": ElDatePicker,
    "el-col": ElCol,
    /*     "el-select": ElSelect,
        "el-option": ElOption, */
    "el-table": ElTable,
    "el-table-column": ElTableColumn,
    "el-pagination": ElPagination,
    "kade-examine-cash": examineCash,
  },
  setup() {
    const store = useStore();
    const state = reactive({
      loading: false,
      isShow: false,
      dialogVisible: false,
      dataList: [],
      dataListTotal: 0,
      personListData: "",
      projectId: "",
      form: {
        beginDate: requestDate()[0],
        endDate: requestDate()[1],
        currentPage: 1,
        pageSize: 6,
        auditStatus: "SYS_NO_AUDIT",
        projectName: null,
        walletCode: null,
        tenantId: null,
      },
      requestDate: requestDate(),
      defaultTime: requestDefaultTime(),
      auditStatusList: JSON.parse(
        localStorage.getItem("kade_cache_dictionary")
      ).filter((item) => item.dictType == "SYS_AUDIT_STATUS"), //状态类型
    });

    const filterDictionary = (val, dictionaryList) => {
      for (let i of dictionaryList) {
        if (val == i.dictCode) {
          return i.dictValue;
        }
      }
    };
    const getList = () => {
      state.loading = true;
      getCashGrantListByPage(state.form).then((res) => {
        store.commit("cashData/updateState", {
          key: "selectRow",
          payload: "",
        });
        state.dataList = res.data.list;
        state.dataListTotal = res.data.total;
        state.loading = false;
      }).catch(() => {
        state.loading = false;
      });
    };

    const rowClick = (row) => {
      store.commit("cashData/updateState", {
        key: "selectRow",
        payload: row,
      });
    };

    const changeDate = (val) => {
      state.form.beginDate = timeStr(val[0]);
      state.form.endDate = timeStr(val[1]);
    };

    const edit = (row) => {
      store.commit("cashData/updateState", {
        key: "selectRow",
        payload: row,
      });
      store.commit("cashData/updateState", {
        key: "isEdit",
        payload: true,
      });
    };

    const search = () => {
      getList();
    };
    const reset = () => {
      state.form = {
        beginDate: requestDate()[0],
        endDate: requestDate()[1],
        currentPage: 1,
        pageSize: 6,
        auditStatus: null,
        projectName: null,
        walletCode: null,
        tenantId: null,
      };
      state.requestDate = requestDate()
    };

    const handleBtnClick = (row) => {
      store.commit("cashData/updateState", {
        key: "selectRow",
        payload: row,
      });
      store.commit("cashData/updateState", {
        key: "isDetailsList",
        payload: true,
      });
    };
    const listDetailsClick = (row) => {
      store.commit("cashData/updateState", {
        key: "selectRow",
        payload: row,
      });
      store.commit("cashData/updateState", {
        key: "isDetails",
        payload: true,
      });
    };

    const examine = () => {
      if (store.state.cashData.selectRow) {
        state.dialogVisible = true;
      } else {
        ElMessage.error("请选择发放项目！");
      }
    };
    const offExamine = (val) => {
      val && getList();
      state.dialogVisible = false;
    };
    const handlePageChange = (val) => {
      state.form.currentPage = val;
      getList();
    };
    const handleSizeChange = (val) => {
      state.form.currentPage = 1;
      state.form.pageSize = val;
      getList();
    };

    onMounted(() => {
      getList();
    });
    return {
      state,
      timeStr,
      search,
      reset,
      edit,
      filterDictionary,
      rowClick,
      changeDate,
      examine,
      offExamine,
      handlePageChange,
      handleSizeChange,
      handleBtnClick,
      listDetailsClick,
    };
  },
};
</script>
<style lang="scss" scoped>
.income-detail {
  min-height: 680px;
}
</style>
