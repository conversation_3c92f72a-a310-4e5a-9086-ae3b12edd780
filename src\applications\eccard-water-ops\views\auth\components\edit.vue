<template>
  <el-dialog v-loading="state.loading" :model-value="modelValue" title="成本录入" width="600px" :before-close="handleClose">
    <el-form label-width="100px" size="mini" ref="formRef" :model="state.form" :rules="rules">
      <el-row>
        <el-col :span="12">
          <el-form-item label="类型：" prop="type">
            <el-select v-model="state.form.type" placeholder="请选择" style="width: 100%;">
              <el-option v-for="(item,index) in typeList" :key="index" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="菜单名称：" prop="name">
            <el-input v-model="state.form.name" placeholder="请输入菜单名称" maxLength="20"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="图标：" prop="icon">
            <el-input v-model="state.form.icon" placeholder="请输入图标名称" maxLength="200"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="路由：" prop="url">
            <el-input v-model="state.form.url" placeholder="请输入路由" maxLength="200"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="权限：" prop="roles"><!-- 加载系统人员角色权限 多选-->
            <el-select v-model="state.form.roles" placeholder="请选择" style="width: 100%;" multiple>
              <el-option v-for="(item,index) in roleList" :key="index" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-col>

      </el-row>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button type="primary" @click="handleSave" size="mini">保存</el-button>
        <el-button @click="handleClose" size="mini">返回</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { ElDialog, ElButton, ElRow, ElCol, ElForm, ElFormItem, ElInput, ElSelect, ElOption, ElMessage } from "element-plus"
import { reactive, ref, nextTick } from 'vue'
import { watch } from '@vue/runtime-core';
import { authTypeList } from "@/applications/eccard-water-ops/dict"
import { opsWaterAuthAdd, opsWaterAuthEdit } from "@/applications/eccard-water-ops/api/auth"
export default {
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    roleList: {
      type: Array,
      default: () => []
    },
    rowData: Object,
  },
  components: {
    ElDialog,
    ElButton,
    ElRow,
    ElCol,
    ElForm,
    ElFormItem,
    ElInput,
    ElSelect,
    ElOption,
  },
  setup(props, context) {
    const formRef = ref(null)
    const state = reactive({
      loading: false,
      form: {}
    });
    watch(() => props.modelValue, val => {
      if (val) {
        state.form = { ...props.rowData, roles: props.rowData.id ? props.rowData.roles.split(',') : [] }
        nextTick(() => {
          formRef.value.clearValidate()
        })
      }
    })
    const rules = {
      name: [
        {
          required: true,
          message: "请输入类型名称",
        },
        {
          max: 20,
          message: "类型名称长度不能超过20字符",
        },
        {
          pattern: /^\S.*\S$|(^\S{0,1}\S$)/,
          message: "类型名称首尾不能包含空格",
        },
      ],
      type: [
        {
          required: true,
          message: "请选择菜单类型",
          trigger: "change",
        },
      ],
      icon: [
        {
          required: true,
          message: "请输入图标名称",
        },
      ],
      url: [
        {
          required: true,
          message: "请输入路由地址",
        },
      ],
      roles: [
        {
          required: true,
          message: "请选择角色权限",
          trigger: "change",
        },
      ],
    }
    const handleSave = () => {
      formRef.value.validate(async valid => {
        if (valid) {
          let params = {
            ...state.form,
            roles: state.form.roles.join(',')
          }
          let fn = state.form.id ? opsWaterAuthEdit : opsWaterAuthAdd
          state.loading = true
          try {
            let { code, message } = await fn(params)
            if (code === 0) {
              ElMessage.success(message)
              context.emit("update:modelValue", false)
              context.emit("success")
            }
            state.loading = false
          }
          catch {
            state.loading = false
          }
        } else {
          return false
        }
      })
    }
    const handleClose = () => {
      context.emit("update:modelValue", false)
    };
    return {
      authTypeList,
      formRef,
      state,
      rules,
      handleSave,
      handleClose,
    }
  }
}
</script>
   
<style lang="scss" scoped>
.el-form {
  margin-top: 20px;
}
</style>