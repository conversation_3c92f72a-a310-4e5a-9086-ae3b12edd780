<template>
  <el-dropdown
    trigger="click"
    ref="dropdown"
    placement="bottom-end"
    :max-height="300"
  >
    <el-input
      ref="inputRef"
      :model-value="deptName"
      readonly
      type="text"
      placeholder="请选择"
      style="cursor: pointer"
      suffix-icon="el-icon-arrow-down"
      class="dropdown-select-tree-input"
    >
      <template #suffix v-if="deptName">
        <i class="el-icon-circle-close" @click.stop="handleClear()"></i>
      </template>
    </el-input>
    <template #dropdown>
      <div class="dropdown-select-tree">
        <el-tree
          ref="treeRef"
          v-bind="attrs"
          :props="state.props"
          default-expand-all
          :expand-on-click-node="false"
          :data="state.departCheckList"
          @node-click="handleNodeClick"
          @check-change="handleCheckChange"
        />
      </div>
    </template>
  </el-dropdown>
</template>
<script>
import { ref,onMounted, reactive } from "vue";
import { getDepartCheckList } from "@/applications/eccard-finance/api";
import { makeTree } from "@/utils/index.js";
import { ElDropdown, ElInput, ElTree } from "element-plus";
export default {
  components: {
    // ElCascader,
    ElDropdown,
    ElInput,
    ElTree,
  },
  props: {
    deptName: {
      types: String,
      default: "",
    },
  },
  setup(props,context) {
        const treeRef = ref(null);
    const inputRef = ref(null);
    const dropdown = ref(null);
    const state = reactive({
      departCheckList: [],
      props: {
        children: "children",
        label: "deptName",
        isLeaf: "isLeaf",
      },
    });
    const queryDepartCheckList = () => {
      getDepartCheckList().then((res) => {
        let arr = makeTree(res.data, "deptId", "deptParentId", "children");
        state.departCheckList = arr;
      });
    };
    const handleNodeClick=(val)=>{
      context.emit("deptChange",val)
      dropdown.value.visible = false;
    }

    const handleClear=()=>{
      context.emit("deptChange","")
    }


    onMounted(() => {
      queryDepartCheckList();
    });
    return {
      treeRef,
      inputRef,
      dropdown,
      state,
      handleNodeClick,
      handleClear
    };
  },
};
</script>
<style lang="scss" scoped>
.dropdown-select-tree {
  padding: 20px;
  box-sizing: border-box;
  width: 300px;
  overflow-x: auto;
  .el-tree {
    width: auto;
    display: inline-block;
    min-width: 100%;
  }
}
.dropdown-select-tree-input {
  .el-icon-circle-close {
    display: none;
  }
  &:hover {
    .el-icon-circle-close {
      display: inline-block;
    }
  }
}
</style>