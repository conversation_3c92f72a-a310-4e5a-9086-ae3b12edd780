<template>
  <div class="padding-form-box setBasicParam" v-loading="state.loading">
    <el-form inline size="small" label-width="200px">
      <el-form-item label="超时断开(n*6秒):">
        <el-input-number :min="0" :max="1000" v-model="state.form.paramContent.timeoutdisconnection"></el-input-number>
      </el-form-item>
    </el-form>
    <div style="text-align: center">
      <el-button size="mini" @click="beforeClose()">取消</el-button>
      <el-button size="mini" type="primary" :loading="state.loading" @click="saveClick()">保存</el-button>
    </div>
  </div>
</template>

<script>
import {
  ElButton,
  ElForm,
  ElFormItem,
  ElInputNumber,
  ElMessage,
} from "element-plus";
import { onMounted, reactive } from "vue";
import { useStore } from "vuex";
const defaultParamsFnc = () => {
  return {
    timeoutdisconnection: 0,
  };
};

export default {
  components: {
    ElForm,
    ElFormItem,
    ElInputNumber,
    ElButton,
  },
  setup() {
    const store = useStore();
    const state = reactive({
      loading: false,
      form: {
        paramContent: defaultParamsFnc(),
        id: "",
        paramId: "",
        paramType: "SENIOR",
      },
    });
    const dictListFnc = () => {
      return store.state.deviceParameters[store.state.app.activeTab].dict
    }
    const saveClick = async () => {
      state.loading = true;
      let storeData = store.state.deviceParameters[store.state.app.activeTab]
      state.form.paramId = storeData.selectRow.id;
      let fn = state.form.id ? storeData.TabModule.detailsEditFnc : storeData.TabModule.detailsAddFnc

      let params = { ...state.form }
      console.log(params.paramContent);
      params.paramContent = JSON.stringify(params.paramContent)
      let { message, code } = await fn(params);
      if (code === 0) {
        setTimeout(async () => {
          ElMessage.success(message);
          await store.dispatch("deviceParameters/getParams", store.state.deviceParameters[store.state.app.activeTab].selectRow.id);
          init()
          state.loading = false;
        }, 1500);
      } else {
        state.loading = false;
      }
    };

    const beforeClose = () => {
      store.commit("deviceParameters/updateState", {
        key: store.state.app.activeTab,
        childKey: "isSetParams",
        payload: false,
      });
    };

    const init = () => {
      let val = store.state.deviceParameters[store.state.app.activeTab].detailsParams
      let arr = val.filter(item => item.paramType == 'SENIOR')
      if (arr && arr.length) {
        state.form = { ...arr[0] }
        state.form.paramContent = JSON.parse(state.form.paramContent)
      } else {
        //设置默认选项
        state.form.id = "";
        state.form.paramContent = defaultParamsFnc();
      }
    }
    onMounted(() => {
      init()
    });
    return {
      state,
      dictListFnc,
      saveClick,
      beforeClose,
    };
  },
  name: "basicParam",
};
</script>

<style lang="scss" scoped>
// .setBasicParam {
//   border: 1px solid #dcdfe6;
//   border-radius: 5px;
//   margin-bottom: 10px;
// }
:deep(.el-form-item__content) {
  width: 200px;

  .el-input-number {
    width: 100%;
  }

  .el-select {
    width: 100% !important;
  }
}
</style>
