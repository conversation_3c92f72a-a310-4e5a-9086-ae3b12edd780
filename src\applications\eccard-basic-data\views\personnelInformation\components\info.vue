<template>
  <kade-modal v-bind="attrs" title="用户信息" :modelValue="modelValue" @update:modelValue="update">
    <el-form label-width="120px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="用户名">
            {{ user.userName }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="创建时间">
            {{ user.createTime }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="创建人">
            {{ user.createUser }}
          </el-form-item>
        </el-col>            
        <el-col :span="12">
          <el-form-item label="最近修改人">
            {{ user.lastModifyUser }}
          </el-form-item>
        </el-col>          
        <el-col :span="12">
          <el-form-item label="最近修改时间">
            {{ user.lastModifyTime }}
          </el-form-item>
        </el-col> 
        <el-col :span="12">
          <el-form-item label="账户状态">
            {{ user.userAccountState }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="地址">
            {{ user.userAddress }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="物理卡号">
            {{ user.userBankNo }}
          </el-form-item>
        </el-col> 
        <el-col :span="12">
          <el-form-item label="生日">
            {{ user.userBirthday }}
          </el-form-item>
        </el-col> 
        <el-col :span="12">
          <el-form-item label="编号">
            {{ user.userCode }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="组织机构">
            {{ user.userDept }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="userIdNo">
            {{ user.userIdNo }}
          </el-form-item>
        </el-col> 
        <el-col :span="12">
          <el-form-item label="userIdType">
            {{ user.userIdType }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="角色">
            {{ user.userRole }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="性别">
            {{ user.userSex }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="来源">
            {{ user.userSource }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="状态">
            {{ user.userState }}
          </el-form-item>
        </el-col> 
        <el-col :span="12">
          <el-form-item label="电话">
            {{ user.userTel }}
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="头像">
            <kade-avatar :size="appImageSize" icon="iconyingyong" :is-app-image="true" :src="user.userPhoto" />
          </el-form-item>
        </el-col>                                                                                                                                                            
      </el-row>
    </el-form>
  </kade-modal>
</template>
<script>
import Modal from '@/components/modal';
import { computed } from 'vue';
import { ElRow, ElCol, ElForm, ElFormItem } from 'element-plus';
import Avatar from '@/components/avatar';
export default {
  emits: ['update:modelValue'],
  props: {
    modelValue: {
      type: Boolean,
      default: false,
    },
    user: {
      type: Object,
      default: () => ({})
    },
  },
  components: {
    'kade-modal': Modal,
    'el-row': ElRow,
    'el-col': ElCol,
    'el-form': ElForm,
    'el-form-item': ElFormItem,
    'kade-avatar': Avatar,
  },
  setup(props, context) {
    const attrs = computed(() => {
      // eslint-disable-next-line no-unused-vars
      const { modelValue, user, ...attrs } = props;
      return attrs;
    });
    const update = (v) => {
      context.emit('update:modelValue', v);
    };
    return {
      attrs,
      appImageSize: THEMEVARS.appImageSize,
      update,
    }
  }
}
</script>