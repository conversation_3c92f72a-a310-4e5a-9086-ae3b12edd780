<template>
  <div class="padding-form-box setBasicParam" v-loading="state.loading">
    <div class="params">
      <div class="title">网络参数</div>
      <el-form inline size="small" label-width="200px" ref="formRef1" :rules="rules" :model="state.form.paramContent">
        <el-form-item label="设备IP:" prop="deviceIP">
          <el-input v-model="state.form.paramContent.deviceIP" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="子网掩码:" prop="subnetMask">
          <el-input v-model="state.form.paramContent.subnetMask" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="网关:" prop="gateway">
          <el-input v-model="state.form.paramContent.gateway" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="DNS:" prop="dns">
          <el-input v-model="state.form.paramContent.dns" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="DHCP:" prop="dhcp">
          <el-select v-model="state.form.paramContent.dhcp">
            <el-option v-for="item in dictListFnc().dhcp" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <div class="params">
      <div class="title">wifi参数</div>
      <el-form inline size="small" label-width="200px" ref="formRef2" :rules="rules" :model="state.form.paramContent">
        <el-form-item label="wifi名称:" prop="wifiName">
          <el-input v-model="state.form.paramContent.wifiName" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="wifi密码:" prop="wifiPwd">
          <el-input v-model="state.form.paramContent.wifiPwd" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="IP:" prop="wifiIP">
          <el-input v-model="state.form.paramContent.wifiIP" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="网关:" prop="wifiGateway">
          <el-input v-model="state.form.paramContent.wifiGateway" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="DNS:" prop="wifiDNS">
          <el-input v-model="state.form.paramContent.wifiDNS" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="DHCP:" prop="wifiDHCP">
          <el-select v-model="state.form.paramContent.wifiDHCP">
            <el-option v-for="item in dictListFnc().wifiDHCP" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </div>
  </div>
  <div style="text-align: center">
    <el-button size="mini" @click="beforeClose()">取消</el-button>
    <el-button size="mini" type="primary" @click="saveClick()">保存</el-button>
  </div>
</template>

<script>
import {
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElOption,
  ElSelect,
  ElMessage,
} from "element-plus";
import { onMounted, reactive, ref } from "vue";
import { useStore } from "vuex";
import { rules } from "../validate.js"
const option = [
  {
    value: '1',
    label: "是",
  },
  {
    value: '0',
    label: "否",
  },
];

const defaultParamsFnc = () => {
  return {
    deviceIP: "",		//设备IP	string	
    dhcp: '1',		//DHCP	string	
    dns: "",		//DNS	string	
    gateway: "",		//网关	string	
    subnetMask: "",		//子网掩码	string	
    wifiDHCP: '1',		//wifi DHCP	string	
    wifiDNS: "",		//wifi DNS	string	
    wifiGateway: "",		//wifi网关	string	
    wifiIP: "",		//wifiIP	string	
    wifiName: "",		//wifi名称	string	
    wifiPwd: "",		//wifi秘密	string
  };
};

export default {
  components: {
    ElForm,
    ElFormItem,
    ElInput,
    ElSelect,
    ElOption,
    ElButton,
  },
  setup() {
    const formRef1 = ref(null)
    const formRef2 = ref(null)
    const store = useStore();
    const state = reactive({
      loading: false,
      form: {
        paramContent: defaultParamsFnc(),
        id: "",
        paramId: "",
        paramType: "netWork",
      },
    });
    const dictListFnc = () => {
      return store.state.deviceParameters[store.state.app.activeTab].dict
    }
    const saveClick = () => {
      formRef1.value.validate((valid => {
        if (valid) {
          formRef2.value.validate(async (valid2) => {
            if (valid2) {
              state.loading = true;
              let storeData = store.state.deviceParameters[store.state.app.activeTab]
              state.form.paramId = storeData.selectRow.id;
              let fn = state.form.id ? storeData.TabModule.detailsEditFnc : storeData.TabModule.detailsAddFnc
              let params = { ...state.form }
              params.paramContent = JSON.stringify(params.paramContent)
              let { message, code } = await fn(params);
              if (code === 0) {
                setTimeout(() => {
                  ElMessage.success(message);
                  store.dispatch("deviceParameters/getParams", store.state.deviceParameters[store.state.app.activeTab].selectRow.id);
                  state.loading = false;
                }, 1500);
              }
            }
          })
        }
      }))

    };

    const beforeClose = () => {
      store.commit("deviceParameters/updateState", {
        key: store.state.app.activeTab,
        childKey: "isSetParams",
        payload: false,
      });
    };
    onMounted(() => {
      let val = store.state.deviceParameters[store.state.app.activeTab].detailsParams

      if (val) {
        if (val.netWork && val.netWork.id) {
          state.form = { ...val.netWork }
          state.form.paramContent = JSON.parse(state.form.paramContent)
        } else {
          state.form.id = "";
          state.form.paramContent = defaultParamsFnc();
        }
      } else {
        //设置默认选项
        state.form.id = "";
        state.form.paramContent = defaultParamsFnc();
      }
    });
    return {
      rules,
      option,
      formRef1,
      formRef2,
      state,
      dictListFnc,
      saveClick,
      beforeClose,
    };
  },
  name: "basicParam",
};
</script>

<style lang="scss" scoped>
.setBasicParam {
  border: 1px solid #dcdfe6;
  border-radius: 5px;
  margin-bottom: 10px;

  .params {
    margin-bottom: 10px;

    .title {
      margin-left: 100px;
      margin-bottom: 10px;
    }
  }
}

:deep(.el-form-item__content) {
  width: 215px;

  .el-select {
    width: 100% !important;
  }
}
</style>
