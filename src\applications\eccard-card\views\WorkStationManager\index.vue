<template>
  <div class="personnelInformation">
    <kade-route-card>
      <kade-table-filter @search="search" @reset="reset">
        <el-form inline label-width="120px" size="small">
          <el-form-item label="工作站名称">
            <el-input size="small" v-model="state.queryListForm.wstName" placeholder="请输入工作站名称"/>
          </el-form-item>
          <el-form-item label="IP地址">
            <el-input size="small" v-model="state.queryListForm.wstIp" placeholder="请输入IP地址"/>
          </el-form-item>
          <el-form-item label="MAC地址">
            <el-input v-model="state.queryListForm.wstMac" placeholder="请输入MAC地址"/>
          </el-form-item>
          <el-form-item label="工作站状态">
            <el-select clearable v-model="state.queryListForm.wstStatus" placeholder="请选择">
              <el-option
                v-for="(item, index) in workstationStatusList"
                :key="index"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </kade-table-filter>
      <kade-table-wrap>
        <el-table style="width: 100%" :data="state.dataList" border stripe>
          <el-table-column label="工作站名称" prop="wstName" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column label="MAC地址" prop="wstMac" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column label="IP地址" prop="wstIp" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column label="注册时间" prop="createTime" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column label="有效期" prop="expiryDate" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column label="工作站状态" prop="wstStatus" align="center" show-overflow-tooltip>
            <template #default="scope">
              <div v-if="scope.row.wstStatus=='WST_NOT_ACTIVE'">{{ dictionaryFilter(scope.row.wstStatus) }}</div>
              <el-switch v-else :model-value="scope.row.wstStatus" disabled active-value="WST_NORMAL" inactive-value="WST_FROZEN" />
            </template>
          </el-table-column>
          <el-table-column label="状态更新时间" prop="lastModifyTime" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column label="操作" align="center" fixed="right" width="120">
            <template #default="scope">
              <el-button @click="handleActive(scope.row.wstId)" type="text" size="mini">激活</el-button>
              <el-button @click="showInfo(scope.row)" type="text" size="mini">修改</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination">
          <el-pagination
            background
            v-model:current-page="state.queryListForm.currentPage"
            layout="total, sizes, prev, pager, next, jumper"
            :page-sizes="[10, 20, 50, 100, 200]"
            :total="state.dataListTotal"
            v-model:page-size="state.queryListForm.pageSize"
            @current-change="handlePageChange"
            @size-change="handleSizeChange"
          >
          </el-pagination>
        </div>
      </kade-table-wrap>
    </kade-route-card>
  </div>
  <el-dialog v-model="state.dialogVisible" title="修改工作站" width="30%">
    <el-form inline label-width="120px" size="mini">
      <el-form-item label="工作站名称:">
        <el-input v-model="state.editForm.wstName" :maxlength="20"></el-input>
      </el-form-item>
      <el-form-item label="有效期:">
        <el-date-picker v-model="state.editForm.expiryDate" type="datetime"></el-date-picker>
      </el-form-item>
      <el-form-item label="状态:">
        <el-radio-group v-model="state.editForm.wstStatus">
          <el-radio :label="item.value" v-for="(item,index) in workstationStatusList" :key="index">{{item.label}}</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="state.dialogVisible = false" size="mini">取消</el-button>
        <el-button type="primary" @click="update()"  size="mini"
        >提交</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>
<script>
import {
  ElTable,
  ElTableColumn,
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElPagination,
  ElSelect, ElMessage, ElMessageBox, ElOption, ElDialog, ElDatePicker,ElRadioGroup,ElRadio,ElSwitch
} from 'element-plus';
import {onMounted, reactive} from "vue";
import {getWorkStationListByPage, workStationActive, workStationUpdate} from "@/applications/eccard-card/api";
import {timeStr} from "@/utils/date";
import {useDict} from "@/hooks/useDict"
export default {
  components: {
    'el-table': ElTable,
    'el-button': ElButton,
    'el-table-column': ElTableColumn,
    'el-form': ElForm,
    'el-form-item': ElFormItem,
    'el-input': ElInput,
    'el-pagination': ElPagination,
    'el-select': ElSelect,
    'el-option': ElOption,
    'el-dialog': ElDialog,
    'el-date-picker': ElDatePicker,
    ElRadioGroup,
    ElRadio,
    ElSwitch
  },
  setup() {
    const workstationStatusList=useDict("SYS_WORKSTATION_STATUS")
    const state = reactive({
      dialogVisible: false,
      dataList: [],
      dataListTotal: 0,
      selectRow: "",
      queryListForm: {
        currentPage: 1,
        pageSize: 10,
        wstIp: "",
        wstMac: "",
        wstName: "",
        wstStatus: "",
      },
      editForm: {
        wstName: "",
        expiryDate: "",
        wstId: "",
      },
      activeForm: {
        wstId: 0,
      },
    });
    const getList = () => {
      getWorkStationListByPage(state.queryListForm).then((res) => {
        state.dataList = res.data.list;
        state.dataListTotal = res.data.total;
      });
    };
    const rowClick = (row) => {
      state.selectRow = row
    }
    const showInfo = (row) => {
      state.editForm.wstName = row.wstName
      state.editForm.expiryDate = row.expiryDate
      state.editForm.wstId = row.wstId
      state.dialogVisible = true
    }

    const update = () => {
      state.editForm.expiryDate = timeStr(state.editForm.expiryDate)
      workStationUpdate(state.editForm).then((res) => {
        ElMessage.info(res.message)
        state.dialogVisible=false;
        getList()
      });
    }

    const search = () => {
      getList();
      state.selectRow = ""
    };
    const reset = () => {
      state.queryListForm = {
        currentPage: 1,
        pageSize: 10,
      };
    };
    const handlePageChange = (val) => {
      state.form.currentPage = val;
      getList();
    };
    const handleSizeChange = (val) => {
      state.form.currentPage = 1;
      state.form.pageSize = val;
      getList();
    };
    const handleActive = (val) => {
      ElMessageBox.confirm(`确定要激活此工作站?`, {
        type: 'warning',
        closeOnPressEscape: false,
        closeOnClickModal: false,
      }).then(async () => {
        try {
          state.activeForm.wstId = val
          const {message} = await workStationActive(state.activeForm);
          ElMessage.success(message);
          getList();
        } catch (e) {
          throw new Error(e.message);
        }
      });
    }
    onMounted(() => {
      getList();
    });
    return {
      workstationStatusList,
      state,
      rowClick,
      search,
      reset,
      showInfo,
      update,
      handleActive,
      handlePageChange,
      handleSizeChange,
    };
  },
}
</script>
<style lang="scss" scoped>
.personnelInformation {
  width: 100%;
  height: 100%;
}
</style>
