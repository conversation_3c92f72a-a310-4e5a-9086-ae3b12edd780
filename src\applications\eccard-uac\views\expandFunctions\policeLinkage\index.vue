<template>
  <kade-route-card style="height: 100%">
    <div class="deviceRealTimeMonitor">
      <kade-table-wrap title="区域" style="width: 300px; height: 100%;margin-right: 20px; ">
        <el-divider></el-divider>
        <div class="search-box">
          <el-input v-model="state.areaKeyword" placeholder="请输入关键字搜索" size="small" clearable>
            <template #append>
              <el-button icon="el-icon-sousuo" @click="areaSearch()">查询</el-button>
            </template>
          </el-input>
          <el-tree class="area-tree" :data="state.areaCheckTreeList" :props="defaultProps" default-expand-all
            @node-click="handleNodeClick">
            <template #default="{ node }">
              <div class="custom-tree-node hasbtn">
                <span class="left">
                  <i class="el-icon-location-information"></i>
                  <span style="margin-left: 5px; color: #333">{{
                      node.label
                  }}</span>
                </span>
              </div>
            </template>
          </el-tree>
        </div>
      </kade-table-wrap>
      <div style="height: 100%;flex:1">
        <kade-table-filter @search="handleSearch" @reset="handleReset">
          <el-form inline label-width="120px" size="mini">
            <el-form-item label="控制器编号">
              <el-input placeholder="请输入" v-model="state.form.keyWord"></el-input>
            </el-form-item>
            <el-form-item label="产品序列SN号">
              <el-input placeholder="请输入" v-model="state.form.keyWord"></el-input>
            </el-form-item>
            <el-form-item label="门名称">
              <el-input placeholder="请输入" v-model="state.form.keyWord"></el-input>
            </el-form-item>
          </el-form>
        </kade-table-filter>
        <kade-table-wrap title="门禁通道控制器列表">
          <template #extra>
            <el-button @click="handleOutputSet" class="btn-yellow" icon="el-icon-edit" size="mini">输出设置</el-button>
            <el-button @click="handleEditPwd" class="btn-purple" icon="el-icon-nested" size="mini">更改胁迫密码</el-button>
          </template>
          <el-table :data="state.dataList" @selection-change="handleSelectChange" border height="55vh">
            <el-table-column show-overflow-tooltip prop="userCode" label="控制器编号" align="center"></el-table-column>
            <el-table-column show-overflow-tooltip prop="userName" label="产品序列SN号" align="center"></el-table-column>
            <el-table-column show-overflow-tooltip prop="userName" label="胁迫密码" align="center"></el-table-column>
            <el-table-column show-overflow-tooltip prop="userName" label="门长时间未关报警(合法开门后)" align="center">
            </el-table-column>
            <el-table-column show-overflow-tooltip prop="userName" label="强行闯入" align="center"></el-table-column>
            <el-table-column show-overflow-tooltip prop="userName" label="无效刷门" align="center"></el-table-column>
            <el-table-column show-overflow-tooltip prop="userName" label="所控制的门" align="center"></el-table-column>
          </el-table>
          <div class="pagination">
            <el-pagination v-model:currentPage="state.form.currentPage" v-model:page-size="state.form.pageSize"
              :page-sizes="[10, 20, 30, 40]" :small="small" :disabled="disabled" background
              layout="total, sizes, prev, pager, next, jumper" :total="state.total" @size-change="handleSizeChange"
              @current-change="handleCurrentChange" />
          </div>
        </kade-table-wrap>
        <kade-output-set v-model="state.isOutputSet" @update:modelValue="state.isOutputSet=false"/>
        <kade-edit-pwd v-model="state.isEditPwd" @update:modelValue="state.isEditPwd=false"/>
      </div>
    </div>
  </kade-route-card>
</template>
<script>
import { onMounted, reactive } from "vue";
import {
  ElDivider,
  ElInput,
  ElTree,
  ElButton,
  ElForm,
  ElFormItem,
  ElTable, ElTableColumn, ElPagination
} from "element-plus";
import { getUserAreaPurview } from "@/applications/eccard-basic-data/api";
import { makeTree, filterDictionary } from "@/utils/index.js";
import outputSet from "./components/outputSet.vue"
import editPwd from "./components/editPwd.vue"
const defaultProps = {
  children: "children",
  label: "areaName",
};
export default {
  components: {
    ElDivider,
    ElInput,
    ElTree,
    ElForm,
    ElFormItem,
    ElButton, ElTable, ElTableColumn, ElPagination,
    "kade-output-set":outputSet,
    "kade-edit-pwd":editPwd,
  },
  setup() {
    const state = reactive({
      activeIndex: 0,
      areaKeyword: "",
      areaCheckTreeList_copy: '',
      loading: false,
      isOutputSet:false,
      isEditPwd:false,
      form: {},
      dataList: [],
    });
    const queryAreaCheckList = () => {
      getUserAreaPurview().then((res) => {
        let arr = makeTree(res.data, "id", "areaParentId", "children");
        state.areaCheckTreeList = [...arr];
        state.areaCheckTreeList_copy = [...arr];
      });
    };
    const mapTree = (value, arr) => {
      let newarr = [];
      arr.forEach((element) => {
        if (element.areaName.indexOf(value) > -1) {
          // 判断条件
          newarr.push(element);
        } else {
          if (element.children && element.children.length > 0) {
            let redata = mapTree(value, element.children);
            if (redata && redata.length > 0) {
              let obj = {
                ...element,
                children: redata,
              };
              newarr.push(obj);
            }
          }
        }
      });
      return newarr;
    };
    const areaSearch = () => {
      state.areaCheckTreeList = mapTree(state.areaKeyword, state.areaCheckTreeList_copy)
    }
    const handleOutputSet=()=>{
      state.isOutputSet=true
    }
    const handleEditPwd=()=>{
      state.isEditPwd=true

    }

    onMounted(() => {
      queryAreaCheckList();
    });
    return {
      filterDictionary,
      defaultProps,
      state,
      handleOutputSet,
      handleEditPwd,
      areaSearch,
    };
  },
};
</script>
<style lang="scss" scoped>
.deviceRealTimeMonitor {
  height: 100%;
  display: flex;
  justify-content: space-between;

  .search-box {
    box-sizing: border-box;
    padding: 10px;
    height: calc(100% - 40px);
  }

  .area-tree {
    margin-top: 20px;
    height: calc(100% - 50px);
    overflow-y: auto;
  }

  .img {
    width: 100%;
    box-sizing: border-box;
    padding: 0px;
    height: calc(100% - 40px);

    img {
      width: 100%;
      height: 100%;
    }
  }

}

:deep(.el-divider--horizontal) {
  margin: 0;
}
:deep(.el-dialog__footer) {
  text-align: center;
}
</style>