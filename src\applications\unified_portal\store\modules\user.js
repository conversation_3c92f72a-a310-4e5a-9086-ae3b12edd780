import { removeToken, getCacheUser, removeCacheUser, removeRefreshToken } from '@/utils';
import { loginOuted } from '@/service';
const state = {
	userMenus: [{
		label: '门户首页',
		action: 'Home'
	},/* {
		label: '我的消息',
		action: 'MyMessage'
	}, */{
		label: '个人信息',
		action: 'MyInfo'
	}/* ,{
		label: '修改密码',
		action: 'ChangePass'
	} */],
	userInfo: getCacheUser(),
	isInitRoute: false,
};
const mutations = {
	updateState(state, { key, payload }) {
		state[key] = payload;
	},
};
const actions = {
	async loginout() {
		await loginOuted();
		removeToken();
		removeRefreshToken();
		removeCacheUser();
		localStorage.removeItem('kade_cache_dictionary');
		sessionStorage.clear();
		window.location.href = `https://saas.sicnu.edu.cn/Login`;
		/* window.opener = null;
		window.open("about:blank", "_top").close()
		console.log(123); */
	},
};
export default {
	namespaced: true,
	state,
	mutations,
	actions
}
