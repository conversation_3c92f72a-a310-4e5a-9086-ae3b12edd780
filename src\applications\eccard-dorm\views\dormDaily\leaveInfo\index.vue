<template>
  <div class="login-log">
    <div class="search-box">
      <div class="top-box">
        <div class="top-left-box">
          <i class="el-icon-search"></i>
          <span>条件筛选</span>
        </div>
        <div class="top-right-box">
          <el-button icon="el-icon-refresh-right" type="" size="mini" @click="reset">重置</el-button>
          <el-button icon="el-icon-search" type="primary" size="mini" @click="search">搜索</el-button>
        </div>
      </div>
      <el-divider></el-divider>
      <div class="bottom-box">
        <el-form inline size="mini" label-width="90px">
          <el-form-item label="关键字">
            <el-input
              v-model="state.form.operateAccount"
              placeholder="输入人员编号或人员姓名搜索"
            ></el-input>
          </el-form-item>
          <el-form-item label="组织机构">
            <kade-dropdown-select-tree
              :action="departAction"
              style="width: 200px"
              idKey="deptId"
              queryKey="deptParentId"
              :opts="treeOpts"
              :text="deptName"
              @change="handleDepartChange"
            />
          </el-form-item>
          <el-form-item label="所属区域">
            <el-input
              v-model="state.form.spendTime"
              placeholder="输入数值搜索"
            ></el-input>
          </el-form-item>
          <el-form-item label="楼栋">
            <el-input
              v-model="state.form.spendTime"
              placeholder="输入数值搜索"
            ></el-input>
          </el-form-item>
          <el-form-item label="单元">
            <el-input
              v-model="state.form.spendTime"
              placeholder="输入数值搜索"
            ></el-input>
          </el-form-item>
          <el-form-item label="楼层">
            <el-input
              v-model="state.form.spendTime"
              placeholder="输入数值搜索"
            ></el-input>
          </el-form-item>
          <el-form-item label="请假原因">
            <el-input
              v-model="state.form.spendTime"
              placeholder="输入数值搜索"
            ></el-input>
          </el-form-item>
          <el-form-item label="请假时间">
            <el-date-picker
              style="width: 200px"
              v-model="state.loginTime"
              type="daterange"
              range-separator="~"
              @change="timeChange"
              unlink-panels
            >
            </el-date-picker>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="table-box">
      <div class="title-box">
        <div class="title-left-box">
          <i class="el-icon-s-fold"></i>
          <span>学生请假记录列表</span>
        </div>
        <div class="title-right-box">
          <el-button class="btn-blue" icon="el-icon-daoru" size="mini" @click="exportClick()"
          >导出
          </el-button
          >
        </div>
      </div>
      <el-table border :data="state.data">
        <el-table-column
          prop="userCode"
          label="人员编号"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="userName"
          label="人员姓名"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="ip"
          label="组织机构"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="leavePeriod"
          label="请假时间"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="reason"
          label="请假原因"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="os"
          label="所属区域"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="roomId"
          label="房间"
          align="center"
        ></el-table-column>
        <el-table-column label="操作" align="center">
          <template #default="scope">
            <el-button type="text" @click="details(scope.row)"
            >详情
            </el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination
          background
          v-model:currentPage="state.form.currentPage"
          v-model:page-size="state.form.pageSize"
          :page-sizes="[5, 10, 20, 30]"
          layout="total,sizes, prev, pager, next, jumper"
          :total="state.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </div>
    </div>
    <kade-LeaveInfo-details
      :data="state.selectRow"
      :dialogVisible="state.dialogVisible"
      @close="close"
    ></kade-LeaveInfo-details>
  </div>
</template>

<script>
import {
  ElButton,
  ElDivider,
  ElForm,
  ElFormItem,
  ElInput,
  ElTable,
  ElTableColumn,
  ElPagination,
  ElDatePicker,
} from "element-plus";
import {getLeaveInfoList} from "@/applications/eccard-dorm/api";
import {dateStr} from "@/utils/date.js"
import {reactive, onMounted, ref} from "vue";
import details from "./components/details.vue";
import {getDepartTree} from "@/applications/eccard-basic-data/api";
import DropdownSelectTree from "@/components/dropdownSelectTree";
import {getRechargeListByExport} from "@/applications/eccard-finance/api";

const resultList = [
  {label: "成功", value: 0},
  {label: "失败", value: 1},
]

export default {
  components: {
    ElButton,
    ElDivider,
    ElForm,
    ElFormItem,
    ElInput,
    ElTable,
    ElTableColumn,
    ElPagination,
    ElDatePicker,
    "kade-LeaveInfo-details": details,
    "kade-dropdown-select-tree": DropdownSelectTree,
  },
  setup() {
    const deptName = ref(null);
    const state = reactive({
      dialogVisible: false,
      loginTime: [],
      data: [],
      total: 0,
      form: {
        userDept: "",
        currentPage: 1,
        pageSize: 10,
      },
      selectRow: "",
    });
    const getList = () => {
      getLeaveInfoList(state.form).then((res) => {
        console.log(res);
        state.data = res.data.list
        state.total = res.data.total
      });
    };

    const search = () => {
      getList();
    };
    const reset = () => {
      state.form = {
        currentPage: 1,
        pageSize: 10,
      }
      state.loginTime = []
    };
    const timeChange = (val) => {
      if (val) {
        (state.form.operateStartTime = dateStr(val[0])),
          (state.form.operateEndtTime = dateStr(val[1]));
      } else {
        delete state.form.operateStartTime,
          delete state.form.operateEndtTime;
      }
    };
    const exportClick = () => {
      getRechargeListByExport(state.form).then((res) => {
        let url = window.URL.createObjectURL(new Blob([res]));
        let link = document.createElement("a");
        link.style.display = "none";
        link.href = url;
        link.setAttribute("download", "学生请假信息表.xlsx");
        document.body.appendChild(link);
        link.click();
      });
    };
    const departAction = async (params) => {
      const {
        data: {deptMenuList},
      } = await getDepartTree(params);
      return deptMenuList;
    };
    const details = (row) => {
      console.log(row);
      state.selectRow = row
      state.dialogVisible = true;
    };
    const close = () => {
      state.dialogVisible = false;
    };
    const handleDepartChange = (data) => {
      state.form.userDept = data?.deptId || "";
      deptName.value = data?.deptName || "";
    };
    const handleSizeChange = (val) => {
      console.log(val);
      state.form.pageSize = val
      getList();
    };
    const handleCurrentChange = (val) => {
      console.log(val);
      state.form.currentPage = val
      getList();
    };
    onMounted(() => {
      getList();
    });
    return {
      resultList,
      state,
      getList,
      details,
      reset,
      search,
      close,
      departAction,
      handleSizeChange,
      handleCurrentChange,
      timeChange,
      deptName,
      handleDepartChange,
      exportClick,
      treeOpts: {
        props: {
          children: "children",
          label: "deptName",
          isLeaf: (data) => {
            if (data["isLeaf"] === undefined) {
              return true;
            }
            return !!data.isLeaf;
          },
        },
      },
    };
  },
};
</script>

<style lang="scss" scoped>
.login-log {
  width: 100%;
  padding: 20px;
  box-sizing: border-box;

  .search-box {
    border: 1px solid #eeeeee;
    border-radius: 4px;

    .top-box {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 15px;

      .el-icon-search {
        margin-right: 10px;
      }
    }

    .el-form {
      margin-top: 15px;
      margin-left: 15px;
    }
  }

  .table-box {
    margin-top: 20px;
    border: 1px solid #eeeeee;
    border-radius: 4px;

    .title-box {
      padding: 12px 15px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .el-icon-s-fold {
        margin-right: 10px;
      }
    }

    .pagination {
      margin: 20px 15px;
    }
  }


}

:deep(.el-divider--horizontal) {
  margin: 0;
}

:deep(.el-date-editor .el-range__icon) {
  display: none;
}

:deep(.el-dialog__header) {
  border-bottom: 1px solid #eeeeee;
}

:deep(.el-dialog__body) {
  padding-bottom: 20px;
}
</style>
