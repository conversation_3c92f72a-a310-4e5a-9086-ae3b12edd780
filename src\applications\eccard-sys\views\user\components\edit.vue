<template>
  <kade-modal v-bind="attrs" :modelValue="modelValue" @update:modelValue="update">
    <el-form ref="formRef" :label-width="labelWidth" :rules="rules" :model="state.model" size="small">
      <el-form-item label="用户账号" prop="userAccount">
        <el-input placeholder="请输入" v-model="state.model.userAccount" />
      </el-form-item>
      <el-form-item label="邮箱" prop="userEmail">
        <el-input placeholder="请输入" v-model="state.model.userEmail" />
      </el-form-item>
      <el-form-item label="用户名称" prop="userName">
        <el-input placeholder="请输入" v-model="state.model.userName" />
      </el-form-item>
      <el-form-item label="密码" prop="userPwd" v-if="!state.model.id">
        <el-input placeholder="请输入" v-model="state.model.userPwd" />
      </el-form-item>
      <el-form-item label="电话" prop="userTel">
        <el-input placeholder="请输入" v-model="state.model.userTel" />
      </el-form-item>
      <el-form-item label="头像" prop="userHeadUrl">
        <kade-single-image-upload :action="uploadApplyLogo" icon="iconuser" v-model="state.model.userHeadUrl" />
        <div style="color: #f00">建议图片格式jpg/png，大小小于100kb</div>
      </el-form-item>
      <el-form-item label="性别" prop="userSex">
        <el-radio-group v-model="state.model.userSex">
          <el-radio v-for="item in sexList" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="state.model.status">
          <el-radio v-for="item in statusList" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="角色" prop="roleIds">
        <el-select multiple v-model="state.model.roleIds">
          <el-option :label="item.roleName" :value="item.id" v-for="(item, index) in state.roleList" :key="index">
          </el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button icon="el-icon-circle-close" size="small" @click="cancel">取消</el-button>
      <el-button icon="el-icon-circle-check" size="small" :loading="loading" @click="submit" type="primary">保存
      </el-button>
    </template>
  </kade-modal>
</template>
<script>
import Modal from "@/components/modal";
import { computed, onMounted, reactive, ref, watch } from "vue";
import {
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElMessage,
  ElRadio,
  ElRadioGroup,
  ElSelect,
  ElOption,
} from "element-plus";
import { addSysUser, editSysUser, getRole } from "@/applications/eccard-sys/api";
import SingleImageUpload from "@/components/singleImageUpload";
import { uploadApplyLogo } from "@/applications/unified_portal/api";
import { useDict } from "@/hooks/useDict";
import { validateEmail, validateMobileAndFixTel } from "@/utils/validate";
const getDefaultModel = () => ({
  userAccount: "",
  userEmail: "",
  userHeadUrl: "",
  userName: "",
  userPwd: "",
  userSex: "SEX_MALE",
  userTel: "",
  status: "ENABLE_TRUE",
});
export default {
  emits: ["update:modelValue", "change"],
  props: {
    title: {
      type: String,
      default: "",
    },
    modelValue: {
      type: Boolean,
      default: false,
    },
    user: {
      type: Object,
      default: null,
    },
  },
  components: {
    "kade-modal": Modal,
    "el-button": ElButton,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    "el-input": ElInput,
    "kade-single-image-upload": SingleImageUpload,
    "el-radio-group": ElRadioGroup,
    "el-radio": ElRadio,
    ElSelect,
    ElOption,
  },
  setup(props, context) {
    const formRef = ref(null);
    const loading = ref(false);
    const sexList = useDict("SYS_SEX");
    const userSources = useDict("SYS_DATA_SOURCE");
    const userTypes = useDict("SYS_USER_LEVEL");
    const statusList = useDict("SYS_ENABLE");
    const state = reactive({
      model: getDefaultModel(),
      roleList: [],
    });
    const rules = {
      userAccount: [
        { required: true, message: "请输入账号" },
        { max: 20, message: "账号不能超过20个字符" },
        { pattern: /^[a-zA-Z0-9]+$/gim, message: "账号只能输入字母或者数字" },
      ],
      userEmail: [
        {
          required: true,
          message: "请输入邮箱",
        },
        {
          max: 20,
          message: "邮箱地址不能超过20字符",
        },
        {
          validator: validateEmail,
        },
      ],
      userName: [
        {
          required: true,
          message: "请输入用户名称",
        },
        {
          max: 20,
          message: "用户名称不能超过20字符",
        },
      ],
      userPwd: [
        {
          required: true,
          message: "请输入密码",
        },
        {
          pattern: /^[^\u4E00-\u9FA5]{6,18}$/gim,
          message: "密码格式为6~18位的非中文字符",
        },
      ],
      userTel: [
        {
          required: true,
          message: "请输入电话",
        },
        {
          validator: validateMobileAndFixTel,
        },
      ],
      userType: [
        {
          required: true,
          message: "请选择用户类型",
          trigger: "change",
        },
      ],

      userSex: [
        {
          required: true,
          message: "请选择性别",
          trigger: "change",
        },
      ],
      roleIds: [
        {
          required: true,
          message: "请选择角色",
          trigger: "change",
        },
      ],
    };

    const isDisabled = computed(() => {
      console.log(props.user);
      if (props.user.userType === "UT_SUPER_ADMIN") {
        return true;
      } else {
        return false;
      }
    });

    const cancel = () => {
      formRef.value?.resetFields();
      context.emit("update:modelValue", false);
    };
    const submit = () => {
      formRef.value?.validate(async (valid) => {
        if (valid) {
          let fn = props.user.id ? editSysUser : addSysUser;
          try {
            loading.value = true;
            const { message } = await fn(state.model);
            ElMessage.success(message);
            context.emit("change");
            context.emit("update:modelValue", false);
          } catch (e) {
            throw new Error(e.message);
          } finally {
            loading.value = false;
          }
        }
      });
    };
    const attrs = computed(() => {
      // eslint-disable-next-line no-unused-vars
      const { modelValue, user, ...attrs } = props;
      return attrs;
    });
    const update = (v) => {
      context.emit("update:modelValue", v);
    };

    onMounted(async () => {
      let { data } = await getRole({status:'ENABLE_TRUE'})
      state.roleList = data
    })

    watch(
      () => props.modelValue,
      (v) => {
        if (v) {
          let params = { ...props.user }
          if (params.roleIds) {
            params.roleIds = params.roleIds.split(",")
          }
          state.model = Object.assign(getDefaultModel(), params);
        }
      }
    );
    return {
      attrs,
      update,
      formRef,
      cancel,
      submit,
      rules,
      labelWidth: THEMEVARS.formLabelWidth,
      state,
      isDisabled,
      loading,
      uploadApplyLogo,
      sexList,
      userSources,
      userTypes,
      statusList,
    };
  },
};
</script>