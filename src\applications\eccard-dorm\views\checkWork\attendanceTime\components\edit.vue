<template>
  <el-dialog :model-value="dialogVisible" title="编辑时段" width="800px" :before-close="handleClose">
    <el-form inline label-width="150px" size="mini">
      <el-form-item label="时段号：">
        <el-input-number v-model="state.form.code" @change="handleChange" />
      </el-form-item>
      <el-form-item label="时段名称：">
        <el-input v-model="state.form.name" disabled placeholder="晚归寝考勤"></el-input>
      </el-form-item>
      <el-form-item label="正常签到开始时间：">
        <el-time-picker v-model="state.form.beginTime" placeholder="请选择"></el-time-picker>
      </el-form-item>
      <el-form-item label="正常签到结束时间：">
        <el-time-picker v-model="state.form.endTime" placeholder="请选择"></el-time-picker>
      </el-form-item>
      <el-form-item label="终止签到时间：">
        <el-time-picker v-model="state.form.terminateTime" placeholder="请选择"></el-time-picker>
      </el-form-item>
      <el-form-item label="启用状态：">
        <el-select v-model="state.form.status" clearable placeholder="全部">
          <el-option v-for="(item, index) in statusList" :key="index" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="备注：">
        <el-input v-model="state.form.remarks" type="textarea" style="width: 534px" maxlength="200"></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button type="primary" @click="save()" size="mini">保存</el-button>
        <el-button @click="handleClose" size="mini">返回</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import {
  ElDialog,
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElInputNumber,
  ElSelect,
  ElOption,
  ElTimePicker,
  ElMessage,
} from "element-plus";
import { reactive } from "@vue/reactivity";
import { watch } from "@vue/runtime-core";
import { hour_and_min_to_sec,hourMinStr } from "@/utils/date.js"
import { useDict } from "../../../../../../hooks/useDict";
import { attendancePeriodEdit } from '@/applications/eccard-dorm/api.js'
export default {
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    selectRow: {
      type: Object,
      default: null,
    },
  },
  components: {
    ElDialog,
    ElButton,
    ElForm,
    ElFormItem,
    ElInput,
    ElInputNumber,
    ElSelect,
    ElOption,
    ElTimePicker,
  },
  setup(props, context) {
    const statusList = useDict('SYS_ENABLE')
    const state = reactive({
      form: {},
    });
    watch(
      () => props.dialogVisible,
      (val) => {
        if (val) {
          state.form = {
            ...props.selectRow,
            beginTime: new Date("1770/01/01").getTime() + hour_and_min_to_sec(props.selectRow.beginTime),
            endTime: new Date("1770/01/01").getTime() + hour_and_min_to_sec(props.selectRow.endTime),
            terminateTime: new Date("1770/01/01").getTime() + hour_and_min_to_sec(props.selectRow.terminateTime),
          }
          state.form.code = Number(state.form.code)
        }
      }
    );
    const handleClose = () => {
      context.emit("close", false);
    };
    const save = async () => {
      let param = { ...state.form };
      param.beginTime=hourMinStr(param.beginTime)
      param.endTime=hourMinStr(param.endTime)
      param.terminateTime=hourMinStr(param.terminateTime)
      console.log(param)
      let { code, message } = await attendancePeriodEdit(param)
      if (code === 0) {
        ElMessage.success(message)
        context.emit('close', true)
      }
    };
    return {
      state,
      save,
      statusList,
      handleClose,
    };
  },
};
</script>

<style lang="scss" scoped>
.el-form {
  margin-top: 20px;
}

:deep(.el-input-number__increase) {
  left: 152px;
}
</style>