<template>
    <div class="percentage">
        <div :style="{width:state.num+'%'}"  class="progress"></div>
    </div>
    <div class="online-box">
        <div class="yoy-box">
            <div class="title">周同比</div>
            <div class="icon"></div>
            <div>12%</div>
        </div>
        <div class="day-box">
            <div class="title">日环比</div>
            <div class="icon"></div>
            <div>11%</div>
        </div>
    </div>
</template>

<script>
import { onMounted, reactive } from "vue";

export default {
    setup() {
        const state=reactive({
            num:78,
        })
        onMounted(() => {

        });

        return {
            state
        };
    },
};
</script>


<style lang="scss" scoped>
.percentage{
    position: relative;
    height: 8px;
    width: 100%;
    line-height: 25px;
    align-items: center;
    background-color: #f0f2f5;
    min-width: 328px;
    margin: 10px 0 20px 0;
    .progress{
        position: absolute;
        background-color: #1890ff;
        height: 8px;
    }
}
.online-box{
    display: flex;
    width: 100%;
    color: #0000006d;
    align-items: center;
    border-top: 1px solid #eeeeee;
    padding: 5px 0 0 5px;
    .yoy-box{
        display: flex;
        font-size: 10px;
        align-items: center;
        margin-right: 80px;
        .icon{
            margin: 5px;
            width: 0;
            height: 0;
            border-right: 7px solid #01a855;
            border-bottom: 7px solid #fff;
            transform: rotate(-45deg);
        }
    }
    .day-box{
        display: flex;
        font-size: 10px;
        align-items: center;
        .icon{
            margin: 5px;
            width: 0;
            height: 0;
            border-left: 7px solid #f00;
            border-top: 7px solid #fff;
            transform: rotate(-45deg);
        }
    }
}
</style>