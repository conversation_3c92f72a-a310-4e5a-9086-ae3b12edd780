<template>
  <div class="padding-box">
    <el-table style="width: 100%" :data="state.list" v-loading="state.loading" border stripe>
      <el-table-column label="设备机号" prop="deviceNo" align="center" show-overflow-tooltip></el-table-column>
      <el-table-column label="设备名称" align="center" prop="deviceName" show-overflow-tooltip>
      </el-table-column>
      <el-table-column label="所属区域" align="center" show-overflow-tooltip prop="deviceAddress">
      </el-table-column>
      <el-table-column label="设备类型" align="center" show-overflow-tooltip prop="deviceType">
        <template #default="scope">
          {{ dictionaryFilter(scope.row.deviceType) }}
        </template>
      </el-table-column>
      <el-table-column label="绑定时间" align="center" show-overflow-tooltip prop="createTime">
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template #default="scope">
          <el-button type="text" @click="cancel(scope.row)" class="green" size="mini">取消绑定</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="pagination">
      <el-pagination background v-model:current-page="state.params.currentPage"
        layout="total, sizes, prev, pager, next, jumper" :page-sizes="[10, 20, 50, 100, 200]" :total="state.total"
        v-model:page-size="state.params.pageSize" @current-change="currentChange" @size-change="sizeChange">
      </el-pagination>
    </div>
    <kade-bind-equipment-modal :model-value="isShow" :id="id || ''" width="1000" @change="showBindModalChange" />
  </div>
</template>
<script>
import {
  // ElButton,
  ElPagination,
  ElTable,
  ElTableColumn,
  ElMessageBox,
  ElMessage
} from "element-plus";
import { reactive, watch } from "vue";
// import { usePagination } from "@/hooks/usePagination";
import { getDeviceListToPage, deleteMerchantDevice } from "@/applications/eccard-finance/api";
import BindEquipmentModal from "./bindEquipmentModal";
export default {
  components: {
    // 'el-button': ElButton,
    "el-table": ElTable,
    "el-table-column": ElTableColumn,
    "el-pagination": ElPagination,
    "kade-bind-equipment-modal": BindEquipmentModal,
  },
  props: {
    id: {
      type: [String, Number],
    },
    isShow: {
      type: Boolean,
      default: false,
    },
  },
  setup(props, context) {
    /* const { options, loadData, querys } = usePagination(getDeviceListToPage, {}, {},
      { currentPage: 'currentPage', pageSize: 'pageSize' }, true,
    ); */
    const state = reactive({
      loading: false,
      params: {
        currentPage: 1,
        pageSize: 10,
      },
    });
    watch(
      () => props.id,
      () => {
        if (props.id) {
          state.params = {
            currentPage: 1,
            pageSize: 10,
          };
          state.params.merchantId = props.id;

          getList();
        }
      }
    );
    const getList = async () => {
      state.loading = true;
      try {
        let { data, code } = await getDeviceListToPage(state.params);
        if (code === 0) {
          state.total = data.total;
          state.list = data.list;
        }
      } finally {
        state.loading = false;
      }
    };
    const cancel = (row) => {
      ElMessageBox.confirm(`确认删除?`, {
        type: "warning",
        closeOnPressEscape: false,
        closeOnClickModal: false,
      }).then(async () => {
        try {
          const { message } = await deleteMerchantDevice({ merchantDeviceId: row.merchantDeviceId, merchantId: props.id });
          ElMessage.success(message);
          getList();
        } catch (e) {
          throw new Error(e.message);
        }
      });
    };
    const showBindModalChange = (val) => {
      if (val) {
        getList();
      }
      context.emit("close", val);
    }
    const currentChange = (val) => {
      state.params.currentPage = val;
      getList();
    };
    const sizeChange = (val) => {
      state.params.pageSize = val;
      getList();
    };

    return {
      state,
      cancel,
      showBindModalChange,
      currentChange,
      sizeChange,
    };
  },
};
</script>
<style lang="scss" scoped>

</style>