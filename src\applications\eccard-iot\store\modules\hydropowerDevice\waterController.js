import { saveWaterParamDetails } from "@/applications/eccard-iot/api";

const state = {
	isRecord: {
		type: "",
		isShow: false,
		rowData: {},
	},
	params: [],
	dict:{},
};
const mutations = {
	updateState(state, { key, payload }) {
		state[key] = payload;
	},
};
const actions = {
	async getParams({ commit }, params) {
		if (!params) return
		let { data } = await saveWaterParamDetails(params)
		commit("updateState", { key: "params", payload: data })
	}
};
export default {
	namespaced: true,
	state,
	mutations,
	actions
}