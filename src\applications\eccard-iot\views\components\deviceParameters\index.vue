<template>
  <div class="income-detail border-box">
    <kade-route-card style="height: auto">
      <kade-table-filter @search="search()" @reset="reset()">
        <el-form inline size="small" label-width="100px">
          <el-form-item label="参数名称:">
            <el-input v-model="state.form.name" placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="参数编号:">
            <el-input v-model="state.form.code" placeholder="请输入"></el-input>
          </el-form-item>
        </el-form>
      </kade-table-filter>
      <kade-table-wrap :title="TabModule().title+'参数列表'">
        <template #extra>
          <el-button icon="el-icon-daoru" size="small" type="success" @click="edit('')">新增</el-button>
          <el-button icon="el-icon-daoru" size="small" class="btn-yellow" @click="setParam()">参数设置</el-button>
          <el-button icon="el-icon-daoru" size="small" class="btn-purple" @click="bindDevice()">绑定设备</el-button>
        </template>
        <el-table ref="tableRef" style="width: 100%" :data="state.dataList" @row-click="rowClick" v-loading="state.loading" height="55vh" highlight-current-row border stripe>
          <el-table-column label="参数名称" prop="name" align="center"></el-table-column>
          <el-table-column label="参数编号" prop="code" align="center"></el-table-column>
          <el-table-column label="是否启用" property="useStatus" align="center">
            <template #default="scope">
              <el-switch v-model="scope.row.useStatus" active-value="TRUE" inactive-value="FALSE" disabled></el-switch>
            </template>
          </el-table-column>
          <el-table-column label="添加时间" prop="createTime" align="center"></el-table-column>
          <el-table-column label="操作" align="center" label-width="100px">
            <template #default="scope">
              <el-button size="mini" class="green" @click="details(scope.row)" type="text">详情</el-button>
              <el-button size="mini" class="green" @click="edit(scope.row)" type="text">编辑</el-button>
              <el-button size="mini" class="green" @click="handleDel(scope.row)" type="text">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination">
          <el-pagination background :current-page="state.form.currentPage" :page-size="state.form.pageSize" layout="total, sizes, prev, pager, next, jumper" :page-sizes="[5, 10, 20, 50, 100]" :total="state.dataListTotal" @current-change="handlePageChange" @size-change="handleSizeChange">
          </el-pagination>
        </div>
      </kade-table-wrap>
    </kade-route-card>
    <kade-params-edit v-model:modelValue="state.isEdit" :listResquest="getList" :TabModule="TabModule" @update:modelValue="state.isEdit=false" />
    <kade-set-params :listResquest="getList" :TabModule="TabModule" @update:modelValue="state.isSetParams=false" />
    <kade-bind-device v-model:modelValue="state.isBindDevice" :listResquest="getList" :TabModule="TabModule" @update:modelValue="state.isBindDevice=false" />
    <kade-details v-model:modelValue="state.isDetails" :TabModule="TabModule" @update:modelValue="state.isDetails=false" />
  </div>
</template>

<script>
import { onMounted, reactive, ref } from "vue";
import {
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElTable,
  ElSwitch,
  ElTableColumn,
  ElPagination,
  ElMessage,
  ElMessageBox,
} from "element-plus";
import {
  deviceGetDict,
  saveWateretDict,
  watermeterDict,
  getIotDeviceParamPage,
  saveIotDeviceParam,
  updateIotDeviceParam,
  deleteIotDeviceParam,
  bindInfo,
  unbindDevice,
  ParamBindDevice,
  devicePage,
  getdeviceParamDetail,
  saveBase,
  faceParamList,
  faceParamAdd,
  faceParamEdit,
  faceParamDel,
  bindFaceDevice,
  faceParamDetails,
  faceParamDetailAdd,
  faceParamDetailEdit,
  faceDeviceList,
  bindFaceDeviceList,
  faceBindCancel,
  saveWaterParamList,
  saveWaterParamAdd,
  saveWaterParamEdit,
  saveWaterParamDel,
  watercontrollerdeviceNoBind,
  bindSaveWaterDevice,
  bindSaveWaterDeviceList,
  saveWaterBindCancel,
  saveWaterParamDetailAdd,
  // waterParamDetailEdit,
  saveWaterParamDetails,

  watermeterParamList,
  watermeterParamAdd,
  watermeterParamEdit,
  watermeterParamDel,
  bindWatermeterDevice,
  bindWatermeterDeviceList,
  remoteWaterDeviceNoBind,
  watermeterBindCancel,
  watermeterParamDetailAdd,
  watermeterParamDetails,
  infiniteWaterParamList,
  infiniteWaterParamAdd,
  infiniteWaterParamEdit,
  infiniteWaterParamDel,
  infiniteWaterDeviceNoBind,
  bindInfiniteWaterDeviceList,
  bindInfiniteWaterDevice,
  infiniteWaterParamDetails,
  infiniteWaterParamDetailAdd,
  infiniteWaterBindCancel,
  infiniteWaterDict,
} from "@/applications/eccard-iot/api";
import { useStore } from "vuex";
import edit from "./edit.vue";
import bindDevice from "./bindDevice.vue";
import setParams from "./setParams";
import details from "./details";
export default {
  components: {
    "el-button": ElButton,
    "el-input": ElInput,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    "el-table": ElTable,
    "el-table-column": ElTableColumn,
    "el-pagination": ElPagination,
    "el-switch": ElSwitch,
    "kade-params-edit": edit,
    "kade-bind-device": bindDevice,
    "kade-set-params": setParams,
    "kade-details": details,
  },
  setup() {
    const tableRef = ref(null)
    const store = useStore();
    const state = reactive({
      loading: false,
      isEdit: false,
      isBindDevice: false,
      isDetails: false,
      form: {
        pageNum: 1,
        pageSize: 10,

      },
      dataList: [],
      dataListTotal: 0,
    });
    /** Function TabModule
     * return
     * title 标题
     * listFnc 参数列表
     * addFnc 新增参数
     * editFnc 修改参数
     * delFnc 删除参数
     * onBindDeviceListFnc 未绑定参数设备列表
     * bindDeviceListFnc 绑定参数设备列表
     * cancelBindFnc 设备取消绑定
     * bindDeviceFnc 参数绑定设备
     * detailsFnc 参数信息
     * detailsAddFnc 新增参数信息
     * detailsEditFnc 编辑参数信息
     * dictListFnc  参数详情编辑
     */
    const TabModule = () => {
      let type = store.state.app.activeTab;
      if (type === "machineParameters") {
        return {
          deviceType: "SMART_CARD_CONSUMPTION_TERMINAL",
          title: "智能卡消费终端",
          listFnc: getIotDeviceParamPage,
          addFnc: saveIotDeviceParam,
          editFnc: updateIotDeviceParam,
          delFnc: deleteIotDeviceParam,
          onBindDeviceListFnc: devicePage,
          bindDeviceListFnc: bindInfo,
          cancelBindFnc: unbindDevice,
          bindDeviceFnc: ParamBindDevice,
          detailsFnc: getdeviceParamDetail,
          detailsAddFnc: saveBase,
          detailsEditFnc: saveBase,
        }
      } else if (type === "terminalDeviceParameters") {
        return {
          deviceType: "SMART_CONSUMPTION_TERMINAL",
          title: "智能消费终端",
          listFnc: getIotDeviceParamPage,
          addFnc: saveIotDeviceParam,
          editFnc: updateIotDeviceParam,
          delFnc: deleteIotDeviceParam,
          onBindDeviceListFnc: devicePage,
          bindDeviceListFnc: bindInfo,
          cancelBindFnc: unbindDevice,
          bindDeviceFnc: ParamBindDevice,
          detailsFnc: getdeviceParamDetail,
          detailsAddFnc: saveBase,
          detailsEditFnc: saveBase,
        }
      } else if (type === "waterParameters") {
        return {
          deviceType: "REMOTE_WATER",
          title: "远传水表",
          listFnc: watermeterParamList,
          addFnc: watermeterParamAdd,
          editFnc: watermeterParamEdit,
          delFnc: watermeterParamDel,
          onBindDeviceListFnc: remoteWaterDeviceNoBind,
          bindDeviceListFnc: bindWatermeterDeviceList,
          bindDeviceFnc: bindWatermeterDevice,
          detailsFnc: watermeterParamDetails,
          detailsAddFnc: watermeterParamDetailAdd,
          detailsEditFnc: watermeterParamDetailAdd,
          cancelBindFnc: watermeterBindCancel,
          dictListFnc: watermeterDict,
        }
      } else if (type === "faceParameters") {
        return {
          deviceType: "FACE_RECOGNITION_ANALYSIS_TERMINAL",
          title: "人脸识别终端设备",
          listFnc: faceParamList,
          addFnc: faceParamAdd,
          editFnc: faceParamEdit,
          delFnc: faceParamDel,
          onBindDeviceListFnc: faceDeviceList,
          bindDeviceListFnc: bindFaceDeviceList,
          bindDeviceFnc: bindFaceDevice,
          detailsFnc: faceParamDetails,
          detailsAddFnc: faceParamDetailAdd,
          detailsEditFnc: faceParamDetailEdit,
          cancelBindFnc: faceBindCancel,
          dictListFnc: deviceGetDict,

        }
      } else if (type === "saveWaterParameters") {
        return {
          deviceType: "WATER_CONTROLLER",
          title: "节水控制器",
          listFnc: saveWaterParamList,
          addFnc: saveWaterParamAdd,
          editFnc: saveWaterParamEdit,
          delFnc: saveWaterParamDel,
          onBindDeviceListFnc: watercontrollerdeviceNoBind,
          bindDeviceListFnc: bindSaveWaterDeviceList,
          bindDeviceFnc: bindSaveWaterDevice,
          detailsFnc: saveWaterParamDetails,
          detailsAddFnc: saveWaterParamDetailAdd,
          detailsEditFnc: saveWaterParamDetailAdd,
          cancelBindFnc: saveWaterBindCancel,
          dictListFnc: saveWateretDict,
        }
      } else if (type === "infiniteWaterParams") {
        return {
          deviceType: "REMOTE_WATER",
          title: "无线联网水控",
          listFnc: infiniteWaterParamList,
          addFnc: infiniteWaterParamAdd,
          editFnc: infiniteWaterParamEdit,
          delFnc: infiniteWaterParamDel,
          onBindDeviceListFnc: infiniteWaterDeviceNoBind,
          bindDeviceListFnc: bindInfiniteWaterDeviceList,
          bindDeviceFnc: bindInfiniteWaterDevice,
          detailsFnc: infiniteWaterParamDetails,
          detailsAddFnc: infiniteWaterParamDetailAdd,
          detailsEditFnc: infiniteWaterParamDetailAdd,
          cancelBindFnc: infiniteWaterBindCancel,
          dictListFnc: infiniteWaterDict,
        }
      } else {
        return {
          title: "设备"
        }
      }
    }
    const getLictList = async () => {
      if (!TabModule().dictListFnc) return
      let { data } = await TabModule().dictListFnc()
      let dict = {}
      for (let key in data) {
        let dictList = []
        for (let k in data[key]) {
          dictList.push({ label: data[key][k], value: String(k) })
        }
        dict[key] = dictList
      }
      store.commit("deviceParameters/updateState", {
        key: store.state.app.activeTab,
        childKey: "dict",
        payload: dict,
      });
    }
    store.commit("deviceParameters/updateState", {
      key: store.state.app.activeTab,
      childKey: "TabModule",
      payload: TabModule(),
    });
    const getList = () => {
      store.commit("deviceParameters/updateState", {
        key: store.state.app.activeTab,
        childKey: "selectRow",
        payload: "",
      });
      tableRef.value.setCurrentRow()
      state.loading = true
      let data = TabModule()
      state.form.paramType = data.title
      data.listFnc(state.form).then((res) => {
        state.dataList = res.data.list;
        state.dataListTotal = res.data.total;
        state.loading = false
      }).catch(() => {
        state.loading = false
      });
    };

    const rowClick = (row) => {
      store.commit("deviceParameters/updateState", {
        key: store.state.app.activeTab,
        childKey: "selectRow",
        payload: row,
      });
      // store.dispatch("deviceParameters/getParams");
    };

    const details = async () => {
      await getLictList()
      state.isDetails = true
    };

    const edit = (row) => {
      store.commit("deviceParameters/updateState", {
        key: store.state.app.activeTab,
        childKey: "selectRow",
        payload: row,
      });
      tableRef.value.setCurrentRow()
      state.isEdit = true
    };
    const handleDel = (row) => {
      ElMessageBox.confirm("确认删除?", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        let { code, message } = await TabModule().delFnc(row.id);
        if (code === 0) {
          ElMessage.success(message);
          getList();
        }
      });
    };
    const setParam = async () => {
      if (store.state.deviceParameters[store.state.app.activeTab].selectRow) {
        await getLictList()
        await store.dispatch("deviceParameters/getParams", store.state.deviceParameters[store.state.app.activeTab].selectRow.id);
        store.commit("deviceParameters/updateState", {
          key: store.state.app.activeTab,
          childKey: "isSetParams",
          payload: true,
        });
      } else {
        ElMessage.error("请先选择参数！");
      }
    };

    const bindDevice = () => {
      if (store.state.deviceParameters[store.state.app.activeTab].selectRow) {
        state.isBindDevice = true
      } else {
        ElMessage.error("请先选择参数！");
      }
    };
    const handlePageChange = (val) => {
      state.form.pageNum = val;
      getList();
    };
    const handleSizeChange = (val) => {
      state.form.pageNum = 1;
      state.form.pageSize = val;
      getList();
    };

    const search = () => {
      getList();
    };

    const reset = () => {
      state.form = {
        pageNum: 1,
        pageSize: 10,
      };
    };

    onMounted(() => {
      getList();
    });
    return {
      tableRef,
      state,
      TabModule,
      getList,
      rowClick,
      details,
      edit,
      handleDel,
      setParam,
      bindDevice,
      handlePageChange,
      handleSizeChange,
      search,
      reset,
    };
  },
};
</script>
<style lang="scss" scoped>
.operation {
  color: #1abc9c;
}

:deep(.el-divider--horizontal) {
  margin: 0;
}

:deep(.el-dialog__header) {
  border-bottom: 1px solid #efefef;
}

:deep(.el-overlay) {
  position: absolute;
  background-color: rgba(0, 0, 0, 0.3);
}

:deep(.el-dialog__footer) {
  text-align: center;
}

:deep(.el-form-item__content) {
  .el-select {
    width: 178px;
  }

  .el-textarea {
    width: 100%;

    .el-textarea__inner {
      width: 100%;
    }
  }
}

.dialog-box {
  border: 1px solid #dcdfe6;
  border-radius: 5px;
  margin: 10px;

  .title {
    padding: 10px;
  }
}
</style>
