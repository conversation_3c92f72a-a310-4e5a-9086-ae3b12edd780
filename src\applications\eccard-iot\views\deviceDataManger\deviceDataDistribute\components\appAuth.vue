<template>
  <div v-loading="state.loading">
    <div class="app-box" v-if="state.dataList.length">
      <div class="box-title">已授权应用</div>
      <div class="app-list">
        <div class="app-item" v-for="(item,index) in state.dataList " :key="index">
          <el-checkbox v-if="state.isEdit" v-model="item.checked" :label="item.sysName" size="large" />
          <div v-else class="app-name">{{item.sysName}}</div>
        </div>
      </div>
      <div class="btn-box">
        <el-button v-if="!state.isEdit" icon="el-icon-edit" size="small" type="primary" @click="handleEdit">修改</el-button>
        <el-button v-if="state.isEdit" icon="el-icon-close" size="small" type="info" @click="handleCancel">取消</el-button>
        <el-button v-if="state.isEdit" icon="el-icon-circle-check" size="small" type="success" @click="handleSave">保存</el-button>
      </div>
    </div>
    <div v-else style="text-align: center;">
      <el-empty description="暂无授权应用"></el-empty>
      <el-button size="small" type="primary" @click="goAuth">去授权</el-button>
    </div>
  </div>
</template>
<script>
import { onMounted, reactive, watch } from 'vue'
import { ElCheckbox, ElButton, ElEmpty, ElMessage } from "element-plus"
import { appAuthList, dataDistributeAppAdd } from "@/applications/eccard-iot/api";
export default {
  components: {
    ElCheckbox,
    ElButton,
    ElEmpty
  },
  props: {
    rowData: {
      type: Object,
      default: null
    },
    appList: {
      type: Array,
      default: null
    }
  },
  setup(props) {
    const state = reactive({
      loading: false,
      checked: true,
      isEdit: false,
      authList: [],
      dataList: [],

    })
    watch(() => props.rowData, val => {
      if (val.id) {
        state.isEdit = false
        getList()
      }
    })

    const getList = async () => {
      state.loading = true
      try {
        let { data } = await appAuthList({ RDistributeId: props.rowData.id })
        state.dataList = data.map(item => {
          return {
            ...item,
            checked: true
          }
        })
        state.authList = state.dataList
        state.loading = false
      }
      catch {
        state.loading = false
      }
    }

    const goAuth = () => {
      state.dataList = props.appList.map(item => {
        return {
          ...item,
          checked: false,
        }
      })
      state.isEdit = true
    }

    const handleEdit = () => {
      let arr = state.authList.map(item => item.rsysId)
      state.dataList = props.appList.map(item => {
        return arr.includes(item.rsysId) ? { ...item, checked: true } : { ...item, checked: false }
      })
      state.isEdit = true
    }

    const handleSave = async () => {
      let params = {
        rdistributeId: props.rowData.id,
        appList: state.dataList.filter(item => item.checked).map(item => {
          return {
            ...item,
            rdistributeId: props.rowData.id
          }
        })
      }
      state.loading = true
      try {
        let { code, message } = await dataDistributeAppAdd(params)
        if (code === 0) {
          ElMessage.success(message)
          getList()
          state.isEdit = false
        }
        state.loading = false
      }
      catch {
        state.loading = false
      }
    }
    const handleCancel = () => {
      state.dataList = state.authList
      state.isEdit = false
    }
    onMounted(() => {
      if (props.rowData.id) {
        getList()
      }
    })
    return {
      state,
      goAuth,
      handleEdit,
      handleSave,
      handleCancel
    }
  }
}
</script>
<style lang="scss" scoped>
.app-box {
  .box-title {
    padding: 15px;
    background: #409eff;
    color: #fff;
    border-radius: 10px 10px 0 0;
  }
  .app-list {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    border-left: 1px solid #ddd;
    .app-item {
      width: 20%;
      display: flex;
      align-items: center;
      // justify-content: center;
      box-sizing: border-box;
      padding: 15px;
      border-bottom: 1px solid #ddd;
      border-right: 1px solid #ddd;
      .app-name {
        margin-left: 10px;
      }
    }
  }
  .btn-box {
    margin-top: 100px;
    text-align: center;
  }
}
</style>