<template>
  <div class="chart-container" v-loading="state.loading">
    <div id="mychart1" class="echartDiv"></div>
  </div>
</template>
<script>
import * as echarts from "echarts";
import { onMounted, reactive, watch } from "vue";
import { genderTrend } from "../../api/home.js";
export default {
  props: {
    queryDate: {
      type: Object,
      default: () => ({
        startTime: '',
        endTime: '',
        type: '1' // 按天
      })
    }
  },
  setup(props) {
    // 状态管理
    const state = reactive({
      chartData: {
        xAxisData: [],
        maleData: [],
        femaleData: []
      },
      loading: false
    });


    // 获取图表数据
    const fetchChartData = async () => {
      state.loading = true;
      // 使用父组件传递的参数
      console.log('父组件传递的参数:', props.queryDate);
      const params = props.queryDate;
      const response = await genderTrend(params);
      console.log('男女生消费趋势数据:', response);
      if (response && response.code === 0) {
        // 处理接口返回的数据结构
        const maleRes = response.data?.maleRes || [];
        const femaleRes = response.data?.femaleRes || [];
        // 提取日期作为X轴数据（使用男生数据的日期，假设男女数据日期一致）
        const xAxisData = maleRes.map(item => item.date);
        // 提取男生消费数据
        const maleData = maleRes.map(item => item.num);
        // 提取女生消费数据
        const femaleData = femaleRes.map(item => item.num);
        state.chartData = {
          xAxisData: xAxisData,
          maleData: maleData,
          femaleData: femaleData
        };

        // 重新初始化图表
        echartInit();
      }
      state.loading = false;
    };

    const echartInit = () => {
      var chartDom = document.getElementById("mychart1");
      var myChart = echarts.init(chartDom);
      var option = {
        title: {
          text: '男女生消费趋势'
        },
        tooltip: {
          trigger: "axis",
        },
        grid: {
          top: "20%",
          left: "0%",
          right: "3%",
          bottom: "0%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          boundaryGap: false,
          data: state.chartData.xAxisData,
          axisLine: {
            lineStyle: {
              color: "#9498b0s",
            },
          },
          axisLabel: {
            margin: 18,
            textStyle: {
              fontSize: 12,
            },
          },
        },
        yAxis: {
          type: "value",
          splitLine: {
            show: true,
            lineStyle: {
              type: "dashed",
            },
          },
          axisLabel: {
            margin: 20,
            textStyle: {
              fontSize: 12,
            },
          },
        },
        series: [
          {
            smooth: true,
            symbol: "circle",
            name: "男生消费金额:",
            data: state.chartData.maleData,
            type: "line",
            color: "#1890ff",
          },
          {
            smooth: true,
            symbol: "circle",
            name: "女生消费金额:",
            data: state.chartData.femaleData,
            type: "line",
            color: "#f16dab",
          },

        ],
      };
      myChart.setOption(option);
    };


    // 监听父组件传递的参数变化
    watch(() => props.queryDate, () => {
      if (props.queryDate) {
        fetchChartData();
      }
    }, { deep: true });

    //挂载
    onMounted(() => {
      // fetchChartData();
    });

    return {
      echartInit,
      fetchChartData,
      state
    };
  },
};
</script>
<style lang="scss" scoped>
.chart-container {
  position: relative;
  height: 354px;
}

.echartDiv {
  height: 354px;
}
</style>
