<template>
  <el-tabs
    class="menutab"
    type="border-card"
    :closable="tabs.length > 1"
    :model-value="activeTab"
    @tab-remove="handleTabRemove"
    @tab-click="handleTabClick"
  >
    <template v-for="item in tabs" :key="item.id">
      <el-tab-pane :label="item.options.menuName" :name="item.id" lazy>
        <component :is="getMenuComponent(item.options.menuEnName)"></component>
      </el-tab-pane>
    </template>
  </el-tabs>
</template>
<script>
// Tab标签控制组件
import { computed } from 'vue';
import { useStore } from 'vuex';
import { ElTabs, ElTabPane } from 'element-plus';
export default {
  components: {
    'el-tabs': ElTabs,
    'el-tab-pane': ElTabPane,    
  },
  setup() {
    const store = useStore();
    const handleTabRemove = (targetName) => {
      store.dispatch('app/removeTab', targetName);
    }
    const handleTabClick = (t) => {
      store.commit('app/updateState', {
        key: 'activeTab',
        payload: t.props.name,
      });
    }
    const tabs = computed(() => {
      return store.state.app.tabs;
    });
    const activeTab = computed(() => {
      return store.state.app.activeTab;
    });
    return {
      tabs,
      activeTab,
      handleTabRemove,
      handleTabClick,
    }
  }
}
</script>
<style lang="scss">
.menutab{
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  & > .el-tabs__content{
    width: 100%;
    height: calc(100% - 40px);
    box-sizing: border-box;
    padding: 0;
    & > .el-tab-pane{
      height: 100%;
      overflow-x: hidden;
      overflow-y: auto;
    }
  }
}
</style>