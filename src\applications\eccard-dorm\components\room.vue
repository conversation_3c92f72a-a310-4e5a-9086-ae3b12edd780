<template>
  <el-popover v-if="data.isPopover" effect="dark" placement="bottom" width="500px" trigger="click" :hide-after="50"
    @show="handleShow">
    <el-table :data="state.dataList" border height="380px">
      <el-table-column show-overflow-tooltip width="70px" align="center" label="床位号" prop="bedNum"></el-table-column>
      <el-table-column show-overflow-tooltip width="120px" align="center" label="人员编号" prop="userCode"></el-table-column>
      <el-table-column show-overflow-tooltip width="120px" align="center" label="人员姓名" prop="userName"></el-table-column>
      <el-table-column show-overflow-tooltip align="center" label="组织机构" prop="deptName"></el-table-column>
    </el-table>
    <template #reference>
      <div class="room-box" :style="{ background: numFnc(data.checkPersonCount, data.bedCount).bgColor }">
        <div class="room-no">{{ data.roomName }}</div>
        <img class="room-icon" :src="data.isSelect ? selectIcon : roomIcon" alt="">
        <div class="room-person-num" :style="{ color: numFnc(data.checkPersonCount, data.bedCount).color }">
          {{ numFnc(data.checkPersonCount, data.bedCount).text }}</div>
      </div>
    </template>

  </el-popover>
  <div class="room-box" :style="{ background: numFnc(data.checkPersonCount, data.bedCount).bgColor }" v-else>
    <div class="room-no">{{ data.roomName }}</div>
    <img class="room-icon" :src="data.isSelect ? selectIcon : roomIcon" alt="">
    <div class="room-person-num" :style="{ color: numFnc(data.checkPersonCount, data.bedCount).color }">
      {{ numFnc(data.checkPersonCount, data.bedCount).text }}</div>
  </div>
</template>
<script>

import { ElPopover, ElTable, ElTableColumn } from "element-plus"
import { reactive } from '@vue/reactivity'
import { getRoomStayInfo } from "@/applications/eccard-dorm/api";

import roomIcon from "@/assets/room.png";
import selectIcon from "@/assets/paySuccess.png";
export default {
  components: {
    ElPopover, ElTable, ElTableColumn
  },
  emits: [],
  props: {
    data: {
      type: Object,
      default: null
    }
  },
  setup(props) {
    const state = reactive({
      dataList: []
    })
    const numFnc = (afterNum, totalNum) => {
      if (afterNum == totalNum) {
        return {
          color: "red",
          text: "已满",
          bgColor: "#409eff"
        }
      } else if (afterNum == 0) {
        return {
          color: "#70b603",
          text: `${afterNum}/${totalNum}`,
          bgColor: "#aaaaaa"
        }
      } else {
        return {
          color: "black",
          text: `${afterNum}/${totalNum}`,
          bgColor: "#409eff"
        }
      }
    }
    const handleShow = () => {
      console.log("show", props);
      let arr = []
      for (let i = 0; i < props.data.bedCount; i++) {
        arr.push({
          bedNum: i + 1,
          deptName: "--",
          userCode: "--",
          userName: "--",
        })
      }
      getRoomStayInfo({ roomId: props.data.roomId }).then(({ data }) => {
        data.forEach(item => {
          arr[item.bedNum - 1] = item
        })
        state.dataList = arr
      });
    }
    return {
      roomIcon,
      selectIcon,
      state,
      numFnc,
      handleShow,
    }
  }
}
</script>
<style lang="scss" scoped>
.room-box {
  display: inline-block;
  width: 82px;
  text-align: center;
  background: #409eff;
  padding: 5px 3px;
  font-size: 12px;
  box-sizing: border-box;
  border-radius: 5px;
  margin-right: 10px;
  margin-bottom: 10px;

  .room-no {
    color: #fff;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .room-icon {
    width: 33px;
    height: 33px;
  }

  .room-person-num {
    background: #f5f5f5;
  }
}

:deep(.el-table) {

  th,
  td {
    padding: 5px 0 !important;
  }
}
</style>