<template>
  <el-dialog :model-value="modelValue" :title="title" width="750px" :before-close="handleClose">
    <el-form v-loading="state.loading" :model="state.form" inline label-width="120px" size="mini" ref="formRef"
      :rules="type !== 'details' && rules">
      <el-form-item v-if="type == 'details'" label="所属区域：">
        <el-input :model-value="state.form.areaName" readonly />
      </el-form-item>
      <el-form-item v-if="type == 'details'" label="楼栋：">
        <el-input :model-value="state.form.buildName" readonly />
      </el-form-item>
      <el-form-item v-if="type == 'details'" label="单元：">
        <el-input :model-value="state.form.unitName" readonly />
      </el-form-item>
      <el-form-item v-if="type == 'details'" label="楼层：">
        <el-input :model-value="state.form.floorName" readonly />
      </el-form-item>
      <el-form-item v-if="type == 'details'" label="房间：">
        <el-input :model-value="state.form.roomName" readonly />
      </el-form-item>
      <kade-linkage-select v-if="type !== 'details'" :isEdit="modelValue" :data="linkageData"
        :value="state.form" @change="linkageChange" />
      <el-form-item label="物品所有人：" prop="userId">
        <el-input v-if="type == 'details'" :model-value="state.form.goodsOwnerName" readonly />
        <el-select clearable v-else v-model="state.form.userId" placeholder="请选择人员">
          <el-option v-for="(item, index) in state.ownerList" :key="index" :label="item.userName" :value="item.userId">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="出入时间：" prop="inoutTime">
        <el-input v-if="type == 'details'" :model-value="state.form.inoutTime" readonly />
        <el-date-picker v-else v-model="state.form.inoutTime" type="datetime" placeholder="请选择" />
      </el-form-item>
      <el-form-item label="物品信息：" prop="goodsInfo">
        <el-input v-if="type == 'details'" :model-value="state.form.goodsInfo" readonly />
        <el-input v-else v-model="state.form.goodsInfo" placeholder="请输入" type="textarea" :rows="3"></el-input>
      </el-form-item>
      <el-form-item label="人员照片：" class="person-img">
        <el-image v-if="type == 'details'" style="width: 100px; height: 100px" :src="state.form.userPhoto"
          :preview-src-list="[state.form.userPhoto]" :initial-index="0" fit="cover"></el-image>
        <kade-single-image-upload v-else v-model="state.form.userPhoto" :action="uploadApplyLogo" icon="el-icon-plus" />
      </el-form-item>
      <el-form-item label="物品照片：">
        <el-image v-if="type == 'details'" style="width: 100px; height: 100px" :src="state.form.goodsPhoto"
          :preview-src-list="[state.form.goodsPhoto]" :initial-index="0" fit="cover"></el-image>
        <kade-single-image-upload v-else v-model="state.form.goodsPhoto" :action="uploadApplyLogo"
          icon="el-icon-plus" />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer" v-if="type !== 'details'">
        <el-button @click="handleClose" size="mini">取消</el-button>
        <el-button type="primary" @click="submit" size="mini">确认</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { reactive, computed, watch, nextTick, ref } from 'vue'
import { timeStr } from "@/utils/date.js"
import { addGoodsInOut, editGoodsInOut, getRoomStayInfo } from "@/applications/eccard-dorm/api.js"
import linkageSelect from '@/applications/eccard-dorm/components/linkageSelect'
import singleImageUpload from '@/components/singleImageUpload'
import { uploadApplyLogo } from "@/applications/unified_portal/api";
import { ElDialog, ElButton, ElForm, ElFormItem, ElSelect, ElOption, ElDatePicker, ElInput, ElImage, ElMessage } from "element-plus"
const linkageData = {
  area: { label: '所属区域：', valueKey: 'areaId', key: 'id' },
  building: { label: '楼栋：', valueKey: 'buildId' },
  unit: { label: '单元：', valueKey: 'unitNum' },
  floor: { label: '楼层：', valueKey: 'floorNum' },
  room: { label: '房间：', valueKey: 'roomId' }
}
const rules = {
  roomId: [{ required: true, message: "请选择楼房间", trigger: "change" }],
  userId: [{ required: true, message: "请选择人员", trigger: "change" }],
  inoutTime: [{ required: true, message: "请选择时间", trigger: "change" }],
  goodsInfo: [{ required: true, message: "请输入物品信息", trigger: "blur" }, { max: 200, message: '不能超过200字符' }]
}
export default {
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: ''
    },
    rowData: {
      type: String,
      default: ''
    }
  },
  components: {
    ElDialog, ElButton, ElForm, ElFormItem, ElSelect, ElOption, ElDatePicker, ElInput, ElImage,
    "kade-linkage-select": linkageSelect,
    "kade-single-image-upload": singleImageUpload
  },
  setup(props, context) {
    const formRef = ref(null)
    const state = reactive({
      form: {
        // resourceUrl: ''
        userPhoto: '',
        goodsPhoto: ''
      },
      ownerList: [],
      loading: false,
    })
    //获取房间人员
    const queryPerson = (roomId) => {
      getRoomStayInfo({ roomId }).then((res) => {
        console.log(res)
        state.ownerList = res.data
      })
    }
    const title = computed(() => {
      if (props.type == 'add') {
        return '新增贵重物品出入登记'
      } else if (props.type == 'edit') {
        return '编辑贵重物品出入登记'
      } else {
        return '贵重物品出入登记详情'
      }
    });
    watch(() => props.modelValue, val => {
      if (val) {
        if (props.type == 'add') {
          state.form = {
            userPhoto: '',
            goodsPhoto: '',
          }
          state.ownerList = []
        } else {
          let { id, userId, areaId, areaName, buildId, goodsOwnerName, buildName, unitNum, unitName, floorNum, floorName, roomId, roomName, inoutTime, goodsInfo, userPhoto, goodsPhoto, tenantId, userName } = JSON.parse(JSON.stringify(props.rowData))
          state.form = { id, userId, areaId, areaName, goodsOwnerName, buildId, buildName, unitNum, unitName, floorNum, floorName, roomId, roomName, inoutTime, goodsInfo, userPhoto, goodsPhoto, tenantId, userName }
          console.log(state.form)
          queryPerson(roomId)
        }
        nextTick(() => {
          formRef.value.clearValidate()
        })
      }
    })
    const linkageChange = (val) => {
      state.form = { ...state.form, ...val }
      if (val.roomId) {
        delete state.form.userId
        queryPerson(val.roomId)
      } else {
        state.ownerList = []
        delete state.form.userId
      }
    }
    const handleClose = () => {
      context.emit('update:modelValue', false)
    }
    const submit = () => {
      formRef.value.validate(async (valid) => {
        if (valid) {
          let param = { ...state.form }
          param.inoutTime = timeStr(param.inoutTime)
          let fn = props.type == 'add' ? addGoodsInOut : editGoodsInOut
          let { code, message } = await fn(param)
          if (code === 0) {
            ElMessage.success(message)
            context.emit('update:modelValue', true)
          }
        }
      })
    }
    return {
      state,
      title,
      formRef,
      rules,
      uploadApplyLogo,
      submit,
      handleClose,
      linkageData,
      linkageChange,

    }
  }
}
</script>

<style lang="scss" scoped>
.person-img {
  margin-right: 108px;
}

:deep(.el-date-editor.el-input, .el-date-editor.el-input__inner) {
  width: 198px !important;
}
</style>