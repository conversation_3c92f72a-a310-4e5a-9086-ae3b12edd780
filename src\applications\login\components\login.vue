<template>
  <div class="login">
    <img class="login-bg" :src="bgImage" alt="" />
    <!-- <img class="login-logo" :src="logo" alt="卡德智能" /> -->
    <div class="login-logo">四川师范大学后勤服务管理中心</div>
    <div class="login-content">
      <div class="login-form">
        <div class="form-box">
          <div class="form-h">
            <img :src="logo2" alt="" />
            <div class="form-h-text">
              <span class="logo-text">统一身份认证系统</span>
              <span class="logo-desc">UNIFIED IDENTITY SYSTEM</span>
            </div>
          </div>
          <el-form v-if="state.loginType == 'password'" ref="form" :rules="rules" :model="state.fields"
            @keyup.enter="handleSubmit">
            <!-- <el-form-item prop="tenantId" :style="{ marginBottom: '25px' }">
              <el-autocomplete
                v-model="state.tenantData.tenantName"
                :fetch-suggestions="querySearchAsync"
                placeholder="请输入"
                debounce
                value-key="tenantName"
                @select="handleSelect"
              ></el-autocomplete>
            </el-form-item> -->
            <el-form-item prop="username" :style="{ marginBottom: '25px' }">
              <el-input prefix-icon="el-icon-user" v-model="state.fields.username" type="text" placeholder="账号">
              </el-input>
            </el-form-item>
            <el-form-item prop="password" :style="{ marginBottom: '15px' }">
              <el-input prefix-icon="el-icon-lock" v-model="state.fields.password" type="password" placeholder="密码"
                show-password></el-input>
            </el-form-item>
            <el-form-item :style="{ marginBottom: '10px' }">
              <el-row>
                <el-col :span="24">
                  <el-checkbox v-model="state.remember">记住用户名和密码</el-checkbox>
                </el-col>
              </el-row>
            </el-form-item>
            <el-button type="primary" :loading="state.loading" @click="handleSubmit" style="width: 100%">登录</el-button>
          </el-form>
          <el-form v-if="state.loginType == 'sms'" ref="form" :rules="rules1" :model="state.form"
            @keyup.enter="handleSubmit">
            <el-form-item prop="phone" :style="{ marginBottom: '25px' }">
              <el-input prefix-icon="el-icon-user" v-model="state.form.phone" type="text" placeholder="手机号">
              </el-input>
            </el-form-item>
            <el-form-item prop="sms" :style="{ marginBottom: '35px' }">
              <el-input prefix-icon="el-icon-lock" v-model="state.form.sms" type="text" placeholder="验证码">
                <template #append>
                  <el-button @click="getSmsCode" :disabled="state.time !== 0 ">{{ state.time === 0 ? `获取验证码` :
                      `获取验证码(${state.time})`
                  }}
                  </el-button>
                </template>
              </el-input>
            </el-form-item>
            <el-button type="primary" :loading="state.loading" @click="handleSubmit" style="width: 100%">登录</el-button>
          </el-form>
        </div>
        <!-- <div class="other" @click="handleLoginType">{{ state.loginType == 'sms' ? '账号密码' : '手机号验证' }}登录＞</div> -->
      </div>
    </div>
    <div class="copyright">
      成都卡德智能科技有限公司 Copyright @ 2021-2022 All right Reserved
    </div>
  </div>
</template>
<script>
import { ref, reactive, onMounted, onUnmounted } from "vue";
import Logo from "@/assets/login_images/logo.png";
import Logo2 from "@/assets/login_images/<EMAIL>";
import bgImage from "@/assets/login_images/<EMAIL>";
import {
  ElButton,
  ElCheckbox,
  ElCol,
  ElForm,
  ElFormItem,
  // ElAutocomplete,
  ElInput,
  ElMessage,
  ElRow,
} from "element-plus";
import { getAgentTenant, login } from "@/applications/login/api";
import { testSms } from "@/applications/eccard-ops/api";
import { setToken, setRefreshToken, getQueryString } from "@/utils";
export default {
  components: {
    "el-button": ElButton,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    "el-input": ElInput,
    "el-checkbox": ElCheckbox,
    "el-row": ElRow,
    "el-col": ElCol,
    // ElAutocomplete,
  },
  setup() {
    const hideForm = ref(null);
    const form = ref(null);
    let state = reactive({
      tenantData: {},
      loading: false,
      remember: false,
      loginType: 'password',
      fields: {
        // tenantId: null,
        username: "",
        password: "",
      },
      form: {
        phone: "",
        sms: ""
      },
      time: 0,
      timer: null
    });
    const rules = {
      /*       tenantId: [
              {
                required: true,
                message: "请选择租户",
              },
            ], */
      username: [
        {
          required: true,
          message: "请输入账号",
        },
      ],
      password: [
        {
          required: true,
          message: "请输入密码",
        },
      ],
    };
    const rules1 = {
      phone: [
        {
          required: true,
          message: "请输入手机号",
        },
      ],
      sms: [
        {
          required: true,
          message: "请输入验证码",
        },
      ],
    };
    /*     const loadTenant = () => {
      state.tenantData = [];
      if (!state.tenantData.length) {
        getAgentTenant()
          .then((res) => {
            state.tenantData = res.data;
          })
          .catch((err) => {
            throw new Error(err.message);
          });
      }
    }; */
    const handleLoginType = () => {
      if (state.loginType === 'password') {
        state.loginType = 'sms';
      } else {
        state.loginType = 'password';
      }
    }
    const getSmsCode = () => {
      if (!state.form.phone) {
        ElMessage.error('请输入手机号');
        return;
      }
      testSms({
        sendMobile: state.form.phone,
      }).then(() => {
        ElMessage.success('验证码已发送');
        state.time = 60;
        state.timer = setInterval(() => {
          state.time--;
          if (state.time <= 0) {
            clearInterval(state.timer);
          }
        }, 1000);
      }).catch(err => {
        ElMessage.error(err.message);
      })
    }
    const querySearchAsync = async (val, cb) => {
      cb([]);
      console.log(val);
      if (val) {
        let { data } = await getAgentTenant(val);
        console.log(data);
        cb(data);
      } else {
        state.tenantData = {};
        delete state.fields.tenantId
      }
    };

    const handleSelect = (val) => {
      state.fields.tenantId = val.tenantId;
    };

    const handleSubmit = () => {
      form.value.validate(async (valid) => {
        if (valid) {
          if (state.remember) {
            localStorage.setItem(
              "kade-common-cache-user",
              JSON.stringify({
                // tenant: btoa(state.fields.tenantId),
                username: btoa(state.fields.username),
                password: btoa(state.fields.password),
                remember: state.remember,
              })
            );
          } else {
            localStorage.removeItem("kade-common-cache-user");
          }
          state.loading = true;
          // const { data,code,message } = await login(state.fields);
          login(state.fields)
            .then(({ data, code, message }) => {
              if (code === 0) {
                sessionStorage.removeItem("kade-common-rediect-times");
                setToken(data.access_token);
                setRefreshToken(data.refresh_token);
                state.loading = false;
                window.location.href =
                  getQueryString("rediectUrl") || "unified_portal";
              } else {
                state.loading = false;
                ElMessage.error(message);
              }
            })
            .catch(() => {
              state.loading = false;
              // ElMessage.error(err.message);
            });
        }
      });
    };
    onMounted(() => {
      // loadTenant();
      if (window.errorMessage) {
        ElMessage.error(window.errorMessage);
      }
      let cache = localStorage.getItem("kade-common-cache-user");
      if (cache) {
        cache = JSON.parse(cache);
        state.fields = {
          // tenantId: Number(atob(cache.tenant)),
          username: atob(cache.username),
          password: atob(cache.password),
        };
        state.remember = cache.remember;
      }
    });
    onUnmounted(() => {
      if (state.timer) {
        clearInterval(state.timer);
      }
    })
    return {
      form,
      hideForm,
      state,
      rules,
      rules1,
      getSmsCode,
      handleLoginType,
      querySearchAsync,
      handleSelect,
      handleSubmit,
      logo: Logo,
      logo2: Logo2,
      bgImage,
    };
  },
  methods: {
    changeData(val) {
      console.log(this.state.tenantData, val);
    },
  },
};
</script>
<style lang="scss">
.login {
  width: 100%;
  height: 100%;
  position: relative;
  min-width: 1200px;
  overflow-x: auto;
  padding: 160px 20px;
  box-sizing: border-box;

  .login-bg {
    width: 100%;
    height: 100%;
    object-fit: cover contain;
    position: absolute;
    left: 0;
    top: 0;
  }

  .login-logo {
    position: absolute;
    left: 80px;
    top: 64px;
    width: 400px;

    font-size: 29px;
    font-family: "blacktitle";
    font-weight: 400;
    color: #215493; 
  }

  .login-content {
    position: relative;
    width: 100%;
    height: 100%;
    max-width: 1200px;
    margin: 0 auto;
    background-image: url(../../../assets/login_images/<EMAIL>);
    background-repeat: no-repeat;
    background-position: left center;
    background-size: 50% auto;

    .login-form {
      position: absolute;
      right: 0;
      top: 0;
      bottom: 0;
      margin: auto;
      width: 450px;
      height: 370px;
      padding: 30px 50px;
      border-radius: 16px;
      filter: blur(0);
      background: #fff;
      margin-left: 148px;
      box-sizing: border-box;

      .form-h {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 25px;

        img {
          width: 40px;
          margin-right: 20px;
        }

        .form-h-text {
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: flex-start;

          .logo-text {
            font-size: 24px;
          }

          .logo-desc {
            font-size: 12px;
            color: #999;
            margin-top: 5px;
          }
        }
      }
    }

    .el-form-item {
      margin-bottom: 25px;
    }
  }

  .copyright {
    color: #999;
    font-size: 14px;
    width: 100%;
    bottom: 20px;
    left: 0;
    text-align: center;
    position: absolute;
  }

  .other {
    position: absolute;
    right: 10px;
    bottom: 10px;
    color: #409EFF;
    cursor: pointer;
  }
}

.el-autocomplete {
  width: 100%;
}
</style>
