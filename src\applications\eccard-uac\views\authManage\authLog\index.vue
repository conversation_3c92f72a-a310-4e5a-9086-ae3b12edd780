<template>
  <div class="padding-box">
    <kade-table-filter @search="handleSearch" @reset="handleReset">
      <el-form inline label-width="100px" size="mini">
        <el-form-item label="所属区域">
          <kade-area-select-tree style="width: 100%" :value="state.form.areaPath" valueKey="areaPath" :multiple="false"
            @valueChange="(val) => (state.form.areaPath = val.areaPath)" />
        </el-form-item>
        <el-form-item label="设备类型">
          <el-select clearable v-model="state.form.deviceType">
            <el-option v-for="(item, index) in state.deviceTypeList" :key="index" :label="item.cfgValue"
              :value="item.cfgKey"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="设备名称">
          <el-input clearable v-model="state.form.deviceName" placeholder="请输入设备名称"></el-input>
        </el-form-item>
        <el-form-item label="授权人员">
          <el-input clearable v-model="state.form.keyWord" placeholder="请输入编号或姓名关键字"></el-input>
        </el-form-item>
        <el-form-item label="组织机构">
          <kade-dept-select-tree style="width: 100%" :value="state.form.deptPath" valueKey="deptPath" :multiple="false"
            @valueChange="(val) => (state.form.deptPath = val.deptPath)" />
        </el-form-item>
        <el-form-item label="权限来源">
          <el-select clearable v-model="state.form.authSourceType">
            <el-option v-for="(item, index) in authSourceTypeList" :key="index" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="操作类型">
          <el-select clearable v-model="state.form.operateType">
            <el-option v-for="(item, index) in operateTypeList" :key="index" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="权限列表">
      <template #extra>
        <el-button class="btn-purple" size="mini" icon="el-icon-daochu" @click="handleExport">导出</el-button>
      </template>
      <el-table border height="55vh" v-loading="state.loading" :data="state.dataList">
        <el-table-column show-overflow-tooltip prop="areaName" label="授权门禁" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="deviceTypeName" label="设备类型" align="center">
          <template #default="scope">
            {{ state.deviceTypeList.find(item => item.cfgKey == scope.row.deviceType)?.cfgValue }}
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip prop="deviceName" label="设备名称" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="userCode" label="人员编号" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="userName" label="人员姓名" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="deptName" label="组织机构" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="authSourceType" label="权限来源" align="center">
          <template #default="scope">
            {{ dictionaryFilter(scope.row.authSourceType) }}
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip prop="beginTime" label="授权时间" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="endTime" label="过期时间" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="operateType" label="操作类型" align="center">
          <template #default="scope">
            {{ dictionaryFilter(scope.row.operateType) }}
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip prop="operatePerson" label="操作员" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="operateTime" label="操作时间" align="center"></el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination v-model:currentPage="state.form.currentPage" v-model:page-size="state.form.pageSize"
          :page-sizes="[10, 20, 30, 40]" background layout="total, sizes, prev, pager, next, jumper"
          :total="state.total" @size-change="handleSizeChange" @current-change="handleCurrentChange">
        </el-pagination>
      </div>
    </kade-table-wrap>
  </div>
</template>
<script>
import { onMounted, reactive } from "vue"
import areaSelectTree from "@/components/tree/areaSelectTree.vue";
import deptSelectTree from '@/components/tree/deptSelectTree';
import { useDict } from '@/hooks/useDict.js'
import { getOperateLogList, exportOperateLog } from '@/applications/eccard-uac/api.js'
import { iotCfg } from "@/applications/eccard-iot/api";
import { ElForm, ElFormItem, ElSelect, ElOption, ElInput, ElTable, ElTableColumn, ElPagination, ElButton } from "element-plus"
export default {
  components: {
    ElForm, ElFormItem, ElSelect, ElOption, ElInput, ElTable, ElTableColumn, ElPagination, ElButton,
    "kade-area-select-tree": areaSelectTree,
    "kade-dept-select-tree": deptSelectTree,
  },
  setup() {
    const authSourceTypeList = useDict('ACS_AUTHORIZE_SOURCE')
    const operateTypeList = useDict('ACS_AUTHORIZE_OPERATE')
    const state = reactive({
      loading: false,
      isDetails: false,
      form: {
        currentPage: 1,
        pageSize: 10
      },
      dataList: [{ id: 1 }],
      total: 0,
      deviceTypeList: [],

    })
    const getDeviceTypeList = async () => {
      let { data } = await iotCfg({ cfgType: 'ACCESS_DEVICE_TYPE' });
      state.deviceTypeList = data
    }
    const handleExport = async () => {
      let res = await exportOperateLog(state.form)
      let link = document.createElement('a')
      link.href = window.URL.createObjectURL(
        new Blob([res], { type: 'application/vnd.ms-excel' })
      )
      link.style.display = 'none'
      link.setAttribute('download', '权限操作日志表.xlsx')
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }
    const handleCurrentChange = val => {
      state.form.currentPage = val
      getList()
    }
    const handleSizeChange = val => {
      state.form.currentPage = 1
      state.form.pageSize = val
      getList()
    }
    const handleSearch = () => {
      getList()
    }
    const handleReset = () => {
      state.form = {
        currentPage: 1,
        pageSize: 10
      }
      getList()
    }
    const getList = async () => {
      state.loading = true
      try {
        let { data: { list, total } } = await getOperateLogList(state.form)
        state.dataList = list
        state.total = total
        state.loading = false
      }
      catch {
        state.loading = false
      }
    }
    onMounted(() => {
      getList()
      getDeviceTypeList()
    })
    return {
      state,
      authSourceTypeList,
      operateTypeList,
      handleExport,
      handleSearch,
      handleReset,
      handleCurrentChange,
      handleSizeChange
    }
  }
}
</script>
<style scoped lang="scss">
:deep(.el-dialog__footer) {
  text-align: center;
}
</style>