<template>
  <div class="padding-box">
    <el-table style="width: 100%" ref="multipleTable" :data="details" border stripe>
      <el-table-column type="expand">
        <template #default="scope">
          <el-table style="width: 100%" :data="scope.row.listTime" border stripe>
            <el-table-column v-for="(item, index) in tableChildItemList" :key="index" :label="item.label" :prop="item.prop" align="center">
              <template v-if="item.rander" #default="scope">
                {{ item.rander(scope.row[item.prop]) }}
              </template>
            </el-table-column>
          </el-table>
        </template>
      </el-table-column>
      <el-table-column v-for="(item, index) in tableItemList" :key="index" :label="item.label" :prop="item.prop" align="center">
        <template v-if="item.rander" #default="scope">
          {{ item.rander(scope.row[item.prop],state.cardTypeList) }}
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<script>
import { ElTable, ElTableColumn } from "element-plus";
import { computed, onMounted, reactive } from "vue";
import { useStore } from "vuex";
import { getCardTypeList } from "@/applications/eccard-iot/api";
const tableItemList = [
  {
    prop: "card_num",
    label: "卡类",
    rander: function (val, list) {
      let arr = list.filter((item) => val == item.ctCode);
      if (arr.length) {
        return arr[0].ctName;
      }
    },
  },
  { prop: "card_discount", label: "卡类折扣率(%)" },
  { prop: "card_single_quota", label: "卡类单次限额(元)" },
  { prop: "card_day_quota", label: "卡类日限额(元)" },
  { prop: "card_day_xianci", label: "卡类日限次" },
];
const tableChildItemList = [
  { prop: "label", label: "餐(时段)" },
  {
    prop: "card_is_trade",
    label: "是否允许交易",
    rander: (val) => (val === 0 ? "否" : "是"),
  },
  { prop: "card_dzcs", label: "次数定值" },
  { prop: "card_jedz", label: "金额定值(第一次)" },
  { prop: "card_jedz1", label: "金额定值(第二次)" },
  { prop: "card_jedz2", label: "金额定值(第三次)" },
  { prop: "card_is_free", label: "免费使用次数" },
  { prop: "card_dzjycs", label: "次数交易限次" },
  { prop: "card_jyxe", label: "交易限额" },
  { prop: "card_jyxc", label: "餐交易总限次" },
];
export default {
  components: {
    ElTable,
    ElTableColumn,
  },
  setup() {
    const store = useStore();
    const state = reactive({
      cardTypeList: [],
    });
    const details = computed(() => {
      let data = "";
      for (let item of store.state.deviceParameters[store.state.app.activeTab].detailsParams) {
        if (item.paramType == "card") {
          data = JSON.parse(item.paramContent);
          break;
        }
      }
      if (!data) {
        data = [];
      }
      return data;
    });

    //获取卡片类型
    const queryCardTypeList = () => {
      getCardTypeList().then((res) => {
        state.cardTypeList = res.data;
      });
    };
    onMounted(() => {
      queryCardTypeList();
    });
    return {
      tableItemList,
      tableChildItemList,
      state,
      details,
    };
  },
};
</script>
<style lang="scss" scoped>
</style>