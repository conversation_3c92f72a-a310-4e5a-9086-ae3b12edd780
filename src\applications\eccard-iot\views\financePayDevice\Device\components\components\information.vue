<template>
  <div class="device-information">
    <div class="dialog-box">
      <div class="title">设备基本信息</div>
      <el-divider></el-divider>
      <div class="padding-form-box">
        <el-form
          inline
          size="mini"
          ref="ruleForm"
          :model="state.form"
          label-width="120px"
          :rules="state.rules"
        >
          <el-form-item label="终端型号：" prop="deviceModel">
            <el-input disabled v-model="details.deviceModel"></el-input>
          </el-form-item>
          <el-form-item label="所属区域：" prop="areaId">
            <el-input disabled v-model="details.areaName"></el-input>
          </el-form-item>

          <el-form-item label="设备机号：" prop="deviceNo">
            <el-input disabled v-model="details.deviceNo"></el-input>
          </el-form-item>
          <el-form-item label="设备名称：" prop="deviceName">
            <el-input disabled v-model="details.deviceName"></el-input>
          </el-form-item>
          <el-form-item label="设备使用状态：" prop="deviceStatus">
            <el-input
              disabled
              :model-value="
                filterDictionary(details.deviceStatus, state.deviceStaticList)
              "
            ></el-input>
          </el-form-item>
          <el-form-item label="所属工作站：">
            <el-input disabled v-model="details.workstationId"></el-input>
          </el-form-item>
          <el-form-item label="连接类型：">
            <el-input
              disabled
              :model-value="
                filterDictionary(
                  details.deviceConnectType,
                  state.deviceConnectTypeList
                )
              "
            ></el-input>
          </el-form-item>
          <el-form-item label="设备IP：">
            <el-input disabled v-model="details.deviceIp"></el-input
            >
          </el-form-item>
          <el-form-item label="固件版本：">
            <el-input disabled v-model="details.deviceVersion"></el-input>
          </el-form-item>
          <el-form-item label="参数：">
            <el-input disabled v-model="details.paramName"></el-input>
          </el-form-item>
          <el-form-item label="设备SN号：">
            <el-input disabled v-model="details.deviceSn"></el-input>
          </el-form-item>
        </el-form>
        <el-form size="mini" label-width="120px">
          <el-form-item label="设备位置：">
            <el-input disabled v-model="details.devicePosition"></el-input>
          </el-form-item>
          <el-form-item label="备注：">
            <el-input
              disabled
              type="textarea"
              v-model="details.deviceRemarks"
            ></el-input>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="dialog-box">
      <div class="title">设备个性化信息</div>
      <el-divider></el-divider>
      <div class="padding-form-box">
        <el-form inline size="mini" label-width="120px">
          <el-form-item label="所属商户：">
            <el-input disabled v-model="details.merchantName"></el-input>
          </el-form-item>
          <el-form-item label="终端类型：">
            <el-input disabled v-model="details.deviceType"></el-input>
          </el-form-item>
          <el-form-item label="设备验证码：">
            <el-input disabled v-model="details.verifyCode"></el-input>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>
<script>
import {computed, reactive, ref} from "vue";
import {ElForm, ElFormItem, ElInput, ElDivider} from "element-plus";
import {useStore} from "vuex";

export default {
  components: {
    "el-input": ElInput,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    ElDivider,
  },
  props: {
    title: {
      types: String,
      default: "",
    },
    isShow: {
      types: Boolean,
      default: false,
    },
    listData: {
      types: Object,
      default: {},
    },
  },
  setup(props, context) {
    const store = useStore();
    let ruleForm = ref(null);
    const state = reactive({
      form: {},
      deviceStaticList: JSON.parse(
        localStorage.getItem("kade_cache_dictionary")
      ).filter((item) => item.dictType == "SYS_DEVICE_STATICE"), //设备使用状态
      deviceConnectTypeList: JSON.parse(
        localStorage.getItem("kade_cache_dictionary")
      ).filter((item) => item.dictType == "SYS_DEVICE_CONNECT_TYPE"), //连接类型
    });
    /*    watch(
      () => props.isShow,
      (val) => {
        if (val) {

        }
      }
    ); */
    const filterDictionary = (val, dictionaryList) => {
      for (let i of dictionaryList) {
        if (val == i.dictCode) {
          return i.dictValue;
        }
      }
    };
    const details = computed(() => {
      return store.state.deviceData.deviceDetail;
    });

    const beforeClose = () => {
      context.emit("off", false);
      state.form = {};
    };

    return {
      state,
      ruleForm,
      details,
      filterDictionary,
      beforeClose,
    };
  },
};
</script>
<style lang="scss" scoped>
.dialog-box {
  border: 1px solid #dcdfe6;
  border-radius: 5px;
  margin: 10px;

  .title {
    padding: 10px;
  }
}
.el-divider--horizontal {
  margin: 0 0 10px;
}
</style>
