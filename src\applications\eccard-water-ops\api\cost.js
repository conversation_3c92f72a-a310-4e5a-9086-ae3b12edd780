import request from '@/service';

// 获取成本列表
export function getCostList(params) {
  return request.post('/eccard-ops/opsWaterCost/page', params);
}

// 添加成本信息
export function addCost(params) {
  return request.post('/eccard-ops/opsWaterCost', params);
}

// 更新成本信息
export function updateCost(params) {
  return request.put('/eccard-ops/opsWaterCost', params);
}

// 删除成本信息
export function deleteCost(id) {
    const tenantId = JSON.parse(sessionStorage.getItem("kade_cache_userinfo"))
      .tenantId;
  return request.delete(`/eccard-ops/opsWaterCost/${tenantId}/${id}`);
}
