<template>
  <el-dialog :model-value="modelValue" title="权限申请详情" width="1700px" :before-close="handleClose"
    :close-on-click-modal="false">
    <div style="padding:20px 0 10px">
      <el-row :gutter="20">
        <el-col :span="12">
          <kade-table-wrap title="申请信息" icon="none">
            <el-divider></el-divider>
            <div class="padding-box">
              <el-form label-width="100px" size="mini">
                <el-form-item label="申请人员：">
                  <el-input :model-value="rowData.userName" readonly></el-input>
                </el-form-item>
                <el-form-item label="开始时间：">
                  <el-input :model-value="rowData.beginTime" readonly></el-input>
                </el-form-item>
                <el-form-item label="结束时间：">
                  <el-input :model-value="rowData.endTime" readonly></el-input>
                </el-form-item>
                <el-form-item label="申请原因：">
                  <el-input :model-value="rowData.reason" type="textarea" readonly></el-input>
                </el-form-item>
              </el-form>
            </div>
          </kade-table-wrap>
          <kade-table-wrap title="审核信息" icon="none" style="margin-bottom:0">
            <el-divider></el-divider>
            <div class="padding-box">
              <el-form label-width="100px" size="mini">
                <el-form-item label="审核状态：">
                  <el-radio-group :model-value="rowData.auditStatus">
                    <el-radio :label="item.value" v-for="(item, index) in auditStatusList" :key="index">{{ item.label }}
                    </el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="审核时间：">
                  <el-input :model-value="rowData.lastModifyTime" readonly></el-input>
                </el-form-item>
                <el-form-item label="审核备注：">
                  <el-input :model-value="rowData.auditRemarks" type="textarea" readonly></el-input>
                </el-form-item>
              </el-form>
            </div>
          </kade-table-wrap>
        </el-col>
        <el-col :span="12">
          <kade-table-wrap title="申请门禁" icon="none">
            <el-divider></el-divider>
            <div style="padding:10px 10px 0">
              <el-table height="438px" :data="rowData.acsApplyAuthorizeDeviceList" border>
                <el-table-column show-overflow-tooltip label="区域" align="center">
                  <template #default="{ row }">
                    {{ `${row.areaName?row.areaName+'>':''}${row.buildName ? row.buildName +
                        '>' : ''}${row.unitNum ? row.unitNum + '单元>' : ''}${row.floorNum ? row.floorNum + '楼>'
                          : ''}${row.roomName ? '>' + row.roomName : ''}`
                    }}
                  </template>
                </el-table-column>
              </el-table>
              <div class="pagination">
                <el-pagination background :total="state.total" layout="prev, pager, next"
                  @current-change="handleCurrentChange" />
              </div>
            </div>
          </kade-table-wrap>
        </el-col>
      </el-row>
    </div>
  </el-dialog>
</template>
<script>
import { ElDialog, ElRow, ElCol, ElDivider, ElForm, ElFormItem, ElInput, ElRadioGroup, ElRadio, ElTable, ElTableColumn, ElPagination } from "element-plus"
import { reactive } from '@vue/reactivity'
import { useDict } from "@/hooks/useDict.js";
export default {
  components: {
    ElDialog, ElRow, ElCol, ElDivider, ElForm, ElFormItem, ElInput, ElRadioGroup, ElRadio, ElTable, ElTableColumn, ElPagination
  },
  emits: ["update:modelValue"],
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    rowData: {
      type: Object,
      default: null
    }
  },
  setup(props, context) {
    const auditStatusList = useDict("AUDIT_STATUS")

    const state = reactive({
      form: {},
      dataList: [],
      total: 0
    })
    const handleClose = () => {
      context.emit("update:modelValue", false)
    }
    return {
      auditStatusList,
      state,
      handleClose
    }
  }
}
</script>
<style lang="scss" scoped>
:deep(.kade-table-wrap) {
  margin-bottom: 20px;
  box-sizing: border-box;
}

.padding-box {
  padding: 20px 20px 0
}

.el-divider--horizontal {
  margin: 0
}

.el-table {
  border-left: 1px solid #EBEEF5;
  border-right: 1px solid #EBEEF5;
}
</style>