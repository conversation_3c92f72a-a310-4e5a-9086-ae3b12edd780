<template>
  <el-dialog :model-value="modelValue" :title="rowData.id?'编辑物品':'新增物品'" width="800px" :before-close="handleClose">
    <el-form label-width="150px" size="mini" ref="formRef" :model="state.form" :rules="rules">
      <el-row>
        <el-col :span="12">
          <el-form-item label="物品名称:" prop="goodsName">
            <el-input placeholder="请输入" v-model="state.form.goodsName"></el-input>
          </el-form-item>
          <el-form-item label="物品规格:">
            <el-input placeholder="请输入" v-model="state.form.goodsSpecs" maxlength="20"></el-input>
          </el-form-item>
          <el-form-item label="数量:" prop="goodsCount">
            <el-input-number v-model="state.form.goodsCount" :min="0" :max="10000" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="物品图片:">
            <kade-single-image-upload v-model="state.form.resourceUrl" :action="uploadApplyLogo" icon="el-icon-plus" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="备注:">
            <el-input style="width:100%" show-word-limit maxlength="200" type="textarea" :rows="5" placeholder="请输入" v-model="state.form.goodsRemark"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer v-if="editType!=='details'">
      <span class="dialog-footer">
        <el-button type="primary" @click="handleSave" size="mini">保存</el-button>
        <el-button @click="handleClose" size="mini">返回</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { ElDialog, ElButton, ElCol, ElRow, ElForm, ElFormItem, ElInput, ElInputNumber, ElMessage } from "element-plus"
import { reactive, watch, ref, nextTick } from 'vue'
import { uploadApplyLogo } from "@/applications/unified_portal/api";
import { addRoomGoodsAdd, addRoomGoodsEdit } from "@/applications/eccard-uac/api";
import SingleImageUpload from "@/components/singleImageUpload";
export default {
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    selectRow: {
      types: Object,
      default: null
    },
    rowData: {
      types: Object,
      default: null
    },
  },
  components: {
    ElDialog,
    ElButton,
    ElCol,
    ElRow,
    ElForm,
    ElFormItem,
    ElInput,
    ElInputNumber,
    "kade-single-image-upload": SingleImageUpload,

  },
  setup(props, context) {
    const formRef = ref(null)
    const state = reactive({
      form: {
        resourceUrl: ""
      }
    });
    watch(() => props.modelValue, val => {
      if (val) {
        if (props.rowData.id) {
          state.form = { ...props.rowData }
        } else {
          state.form = {
            resourceUrl: ""
          }
        }
        nextTick(() => {
          formRef.value.clearValidate()
        })
      }
    })
    const rules = {
      goodsName: [
        {
          required: true,
          message: "请输入物品名称",
        },
        {
          max: 20,
          message: "物品名称长度不能超过20字符",
        },
      ],
      goodsCount: [
        {
          required: true,
          message: "请输入物品数量",
        },
      ]
    }
    const handleSave = () => {
      formRef.value.validate(async valid => {
        if (valid) {
          let fn = state.form.id ? addRoomGoodsEdit : addRoomGoodsAdd
          
          let { roomGoodsId } = { ...state.form }
          let params = {
            id: roomGoodsId,
            roomTypeId:props.selectRow.id,
            ...state.form
          }
          state.loading = true
          try {
            let { code, message } = await fn(params)
            if (code === 0) {
              ElMessage.success(message)
              context.emit("update:modelValue", true)
            }
            state.loading = false
          }
          catch {
            state.loading = false
          }
        } else {
          return false
        }
      })
    }
    const handleClose = () => {
      context.emit("update:modelValue", false)
    };
    return {
      uploadApplyLogo,
      formRef,
      state,
      rules,
      handleSave,
      handleClose,
    }
  }
}
</script>

<style lang="scss" scoped>
.el-form {
  margin-top: 20px;
}
:deep(.el-input-number) {
  width: 192px;
}
:deep(.el-input) {
  width: 192px;
}
:deep(.single-image-uploader) {
  width: 100px;
  height: 100px;
  .el-upload {
    width: 100px;
    height: 100px;
  }
  .element-icons {
    font-size: 40px !important;
  }
}
</style>