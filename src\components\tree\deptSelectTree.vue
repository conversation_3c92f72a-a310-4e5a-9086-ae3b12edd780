<template>
  <kade-select-tree :value="value" :valueKey="valueKey" :dataTreeList="state.departCheckTreeList" :list="state.list"
    :props="state.props" :multiple="multiple" @valueChange="valueChange" />
</template>
<script>
import { onMounted, reactive } from "vue";

import { getUserOrgPurviewTree, getUserOrgPurview } from '@/applications/eccard-basic-data/api';
import { makeTree } from "@/utils/index.js";
import selectTree from "./selectTree";

export default {
  components: {
    "kade-select-tree": selectTree,
  },
  props: {
    multiple: {
      types: Boolean,
      default: false,
    },
    value: {
      types: String || Array,
      default: "",
    },
    valueKey: {
      types: String,
      default: "id",
    },
  },
  setup(prop, context) {
    const state = reactive({
      departCheckTreeList: [],
      list: [],
      props: {
        children: "children",
        label: "deptName",
        disabled: (data) => {
          return !data.checked
        }
      },
    });


    const queryDepartCheckList = () => {
      getUserOrgPurview().then((res) => {
        state.list = res.data
        getUserOrgPurviewTree().then((res) => {
          let arr = makeTree(res.data, "id", "deptParentId", "children");
          state.departCheckTreeList = [...arr];
        });
      });
    };
    const valueChange = (val) => {
      context.emit("valueChange", val);
    };

    onMounted(() => {
      queryDepartCheckList();
    });

    return {
      state,
      valueChange,
    };
  },
};
</script>
<style lang="scss" scoped>

</style>