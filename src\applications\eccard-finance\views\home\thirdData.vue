<template>
    <div class="title-box">数据标题</div>
    <div id="chartsline" class="chartline"></div>
</template>

<script>
import * as echarts from "echarts";
import { onMounted } from "vue";

export default {
    setup() {
        const echartInit = () => {
            var chartDom = document.getElementById("chartsline");
            var myChart = echarts.init(chartDom);
            var option = {
                tooltip: {
                    axisPointer: {
                        type: "shadow",
                    },
                },
                legend: {
                    orient: "horizontal",
                    bottom: "0%",
                    left: "5%",
                    itemGap: 25,
                    itemWidth: 7,
                    itemHeight: 7,
                    icon: "circle",
                },
                grid: {
                    top: "15%",
                    left: "5%",
                    right: "10%",
                    bottom: "20%",
                    containLabel: true,
                },
                xAxis: {
                    axisLine: {
                        lineStyle: {
                            color: "#9498b0s",
                        },
                    },
                    axisLabel: {
                        margin: 10,
                        textStyle: {
                            fontSize: 12,
                        },
                    },
                    splitLine: {
                        show: false,
                    },
                },
                yAxis: {
                    axisLine: {
                        show: false,
                    },
                    axisLabel: {
                        margin: 20,
                        textStyle: {
                            fontSize: 13,
                        },
                    },
                    axisTick: {
                        show: false,
                    },
                    splitLine: {
                        lineStyle: {
                            type: "dashed",
                        },
                    },
                },
                color: "#1890ff",
                series: [
                    {
                        name: "汽车销售",
                        symbolSize: 5,
                        data: [
                            [10.0, 8.04],
                            [8.07, 6.95],
                            [13.0, 7.58],
                            [9.05, 8.81],
                            [11.0, 8.33],
                            [14.0, 7.66],
                            [13.4, 6.81],
                            [10.0, 6.33],
                            [14.0, 8.96],
                            [12.5, 6.82],
                            [9.15, 7.2],
                            [11.5, 7.2],
                            [3.03, 4.23],
                            [12.2, 7.83],
                            [2.02, 4.47],
                            [1.05, 3.33],
                            [4.05, 4.96],
                            [6.03, 7.24],
                            [12.0, 6.26],
                            [12.0, 8.84],
                            [7.08, 5.82],
                            [5.02, 5.68],
                        ],
                        type: "scatter",
                    },
                ],
            };
            myChart.setOption(option);
        };
        onMounted(() => {
            echartInit();
        });
        return {
            echartInit,
        };
    },
};
</script>

<style lang="scss" scoped>
.title-box {
    font-size: 16px;
    font-weight: 600;
    border-bottom: 1px solid #eeeeee;
    padding: 0 0 10px 10px;
    margin-right: 10px;
}

.chartline {
    height: 260px;
    width: 100%;
}
</style>