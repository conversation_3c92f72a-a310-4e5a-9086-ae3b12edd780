import request from '@/service';

// 总消费金额
export function getOpsWaterMainPageTotalNum(params) {
  return request.post('/eccard-ops/opsWaterMainPage/totalNum', params);
}
// 男女消费趋势
export function genderTrend(params) {
  return request.post('/eccard-ops/opsWaterMainPage/genderTrend', params);
}

// 平台运营变化趋势
export function incomeTrend(params) {
  return request.post('/eccard-ops/opsWaterMainPage/incomeTrend', params);
}
// 用户消费变化趋势
export function getWaterMainPageTrend(params) {
  return request.post('/eccard-ops/opsWaterMainPage/trend', params);
}
// 月度运营成本对比
export function getMonthCost(params) {
  return request.post('/eccard-ops/opsWaterMainPage/monthCost', params);
}
// 年度运营成本对比
export function getYearCost(params) {
  return request.post('/eccard-ops/opsWaterMainPage/yearCost', params);
}

