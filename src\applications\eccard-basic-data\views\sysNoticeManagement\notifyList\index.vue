<template>
  <kade-route-card style="height: auto">
    <kade-table-filter @search="getList()" @reset="reset()">
      <el-form inline size="mini" label-width="100px">
        <el-form-item label="关键字">
          <el-input v-model="state.form.keyWord" placeholder="通知标题关键字搜索"></el-input>
        </el-form-item>
        <el-form-item label="消息类型">
          <el-select v-model="state.form.msgType">
            <el-option v-for="item in messTypeList" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="阅读状态">
          <el-select v-model="state.form.status">
            <el-option v-for="(item,index) in 10" :key="index" :label="item" :value="item"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="发布时间">
          <el-date-picker
            v-model="state.requestDate"
            type="daterange"
            unlink-panels
            range-separator="~"
            start-placeholder="请选择日期"
            end-placeholder="请选择日期"
            @change="changeDate"
          >
          </el-date-picker>
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="系统通知列表">
      <template #extra>
        <el-button size="mini" type="primary" icon="el-icon-brush" @click="handleClick(scope.row)">一键已读</el-button>
        <el-button size="mini" type="danger" icon="el-icon-circle-close" @click="handleClick(scope.row)">删除</el-button>
      </template>
      <el-table style="width: 100%" :data="state.dataList" ref="multipleTable" v-loading="state.loading" height="55vh" border stripe>
        <el-table-column v-for="(item,index) in column" :key="index" :width="item.width" :label="item.label" :prop="item.prop" align="center" show-overflow-tooltip>
          <template v-if="item.render" #default="scope">
            {{dictionaryFilter(scope.row[item.prop])}}
          </template>  
        </el-table-column>
        <el-table-column label="操作" align="center">
          <template #default="scope">
            <el-button size="mini" type="text" @click="handleClick(scope.row)">查看详情</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination
          background
          :current-page="state.form.pageNum"
          :page-size="state.form.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :page-sizes="[6, 10, 20, 50, 100]"
          :total="state.total"
          @current-change="handlePageChange"
          @size-change="handleSizeChange"
        >
        </el-pagination>
      </div>
    </kade-table-wrap>
    <kade-notify-details :isShow="state.isDetails" :details="state.rowData" @close="state.isDetails=false"/>
  </kade-route-card>
</template>
<script>
import { reactive } from "@vue/reactivity";
import {timeStr} from "@/utils/date"
import { useDict } from "@/hooks/useDict";
import {ElForm,ElFormItem,ElInput,ElSelect,ElOption,ElDatePicker,ElTable,ElTableColumn,ElPagination,ElButton} from "element-plus"
import { getSysMessageListPage } from "@/applications/eccard-basic-data/api";
import { onMounted } from '@vue/runtime-core';
import details from "./components/details.vue"
const column=[
  {label:"消息类型",prop:"messType",render(){
    return true
  }},
  {label:"阅读状态",prop:"status",width:"",render(){
    return true
  }},
  {label:"消息标题",prop:"messTitle",width:""},
  {label:"发布人",prop:"publishUser",width:""},
  {label:"发布时间",prop:"publishTime",width:""},
  {label:"阅读时间",prop:"receiveTime",width:""},
]
export default {
  components: {
    ElForm,ElFormItem,ElInput,ElSelect,ElOption,ElDatePicker,ElTable,ElTableColumn,ElPagination,ElButton,
    "kade-notify-details":details
  },
  setup() {
    const messTypeList = useDict("SYS_MESSAGE_TYPE"); //消息类型
    const state = reactive({
      loading: false,
      isDetails:false,
      form:{
        pageNum:1,
        pageSize:10
      },
      requestDate:[],
      dataList:[],
      total:0,
      rowData:{},
    });
    const getList=async ()=>{
      state.loading=true
      let {data}=await getSysMessageListPage(state.form)
      state.dataList=data.list
      state.total=data.total
      state.loading=false
    }
    const changeDate=(val)=>{
      if (val && val.length) {
        state.form.beginDate = timeStr(val[0]);
        state.form.endDate = timeStr(val[1]);
      } else {
        delete state.form.beginDate;
        delete state.form.endDate;
      }
    }

    const handleClick=(row)=>{
      state.rowData=row
      state.isDetails=true
    }

    const handlePageChange=(val)=>{
      state.form.pageNum=val
      getList()
    }
    const handleSizeChange=(val)=>{
      state.form.pageSize=val
      getList()
    }
    onMounted(()=>{
      getList()
    })
    return {
      column,
      messTypeList,
      state,
      changeDate,
      handleClick,
      handlePageChange,
      handleSizeChange
    };
  },
};
</script>
<style lang="scss" scoped>

</style>