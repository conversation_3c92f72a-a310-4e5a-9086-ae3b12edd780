<template>
  <el-dialog :model-value="modelValue" title="个人得分数据导入" width="1030px" :before-close="handleClose">
    <div class="button-box">
      <el-button class="button-item" type="text" @click="exportModel">导入模板下载</el-button>
      <div>
        <el-upload class="upload-demo" :action="state.action" :headers="state.headers" :on-preview="handlePreview"
        :on-success="handleSuccess" :on-error="handleError" :show-file-list="false">
        <el-button icon="el-icon-daoru" type="success" size="mini">导入数据</el-button>
      </el-upload>
      </div>
      <el-button icon="el-icon-circle-check" type="primary" @click="submit" :disabled="!state.data.length" size="mini">保存</el-button>
    </div>
    <el-table :data="state.data" border v-loading="state.loading">
      <el-table-column show-overflow-tooltip v-for="(item, index) in column" :key="index" :prop="item.prop"
        :label="item.label" align="center"></el-table-column>
      <el-table-column show-overflow-tooltip prop="resultMsg" label="校验结果" align="center" width="200px">
        <template #default="scope">
          <div :style="{color:scope.row.resultMsg=='校验通过'?'green':'red'}" >
            {{scope.row.resultMsg}}
          </div>
        </template>
      </el-table-column>
    </el-table>
    <div class="pagination">
      <el-pagination v-model:currentPage="state.form.currentPage" v-model:page-size="state.form.pageSize"
        :page-sizes="[10, 20, 30, 40]" background layout="total, sizes, prev, pager, next, jumper" :total="state.total"
        @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </div>
  </el-dialog>
</template>

<script>
import { reactive } from "@vue/reactivity";
import {
  ElDialog,
  ElButton,
  ElTable,
  ElTableColumn,
  ElPagination,
  ElMessage,
  ElUpload,
} from "element-plus";
import { getToken } from '@/utils'
import { watch } from "@vue/runtime-core";
import { HygieneCheckImportTemplate, getImportHygieneCheckScore, saveImportHygieneCheckScore } from "@/applications/eccard-dorm/api.js"
const column = [
  { label: "所属区域", prop: "areaName" },
  { label: "所属楼栋", prop: "buildName" },
  { label: "所属单元", prop: "unitName" },
  { label: "所属楼层", prop: "floorName" },
  { label: "房间号", prop: "roomNo" },
  { label: "人员编号", prop: "userCode" },
  { label: "人员姓名", prop: "userName" },
  { label: "检查日期", prop: "checkTime" },
  { label: "检查得分", prop: "checkScore" },
];
export default {
  props: {
    modelValue: {
      type: Boolean,
      default: false,
    },
  },
  components: {
    ElDialog,
    ElButton,
    ElTable,
    ElTableColumn,
    ElPagination,
    ElUpload,
  },
  setup(props, context) {
    const state = reactive({
      action: `${CONFIG.BASE_API_PATH}eccard-dorm/HygieneCheck/importHygieneCheckScore`,
      headers: { 
        Authorization: "bearer "+getToken()
      },
      form: {
        currentPage: 1,
        pageSize: 10,
      },
      total: 0,
      data: [],
      loading: false,
      ImportRoomAllocRes:{}
    });
    const getList = async () => {
      state.loading=true
      try {
        let { data:{page:{totalCount,dataList},errorCount,projectNo } } = await getImportHygieneCheckScore(state.form)
        state.total=totalCount
        state.data=dataList
        state.ImportRoomAllocRes={
          errorCount,
          projectNo
        }
        state.loading = false
      } catch {
        state.loading = false
      }
    };
    const handlePreview = () => {
      state.loading = true
    }
    const handleSuccess = ({code,message,data}) => {
      if(code===0){
        state.form.projectNo=data.projectNo
        getList()
      }else{
      ElMessage.error(message)
      }
      state.loading = false
    }
    const handleError = () => {
      state.loading = false
    }
    const handleSizeChange = (val) => {
      (state.form.currentPage = 1), (state.form.pageSize = val);
      getList();
    };
    const handleCurrentChange = (val) => {
      state.form.currentPage = val;
      getList();
    };
    const exportModel = async () => {
      let res = await HygieneCheckImportTemplate(state.form)
      let url = window.URL.createObjectURL(
        new Blob([res], { type: 'application/vnd.ms-excel' })
      )
      let link = document.createElement('a')
      link.href = url
      link.style.display = 'none'
      link.download = "个人得分数据导入列表.xlsx"
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    };
    const submit = async () => {
      if(!state.data.length){
        return ElMessage.error("导入列表为空，请重新导入！")
      }
      if(state.ImportRoomAllocRes.errorCount!==0){
        return ElMessage.error('当前导入个人得分数据有错误信息，请检查后重新上传！')
      }
      state.loading = true;
      try {
        let { code, message } = await saveImportHygieneCheckScore(state.form.projectNo)
        if (code === 0) {
          ElMessage.success(message);
          context.emit("update:modelValue", true);
        }
        state.loading = false;
      } catch {
        state.loading = false;
      }
    };
    const handleClose = () => {
      context.emit("update:modelValue", false);
    };
    watch(
      () => props.modelValue,
      (val) => {
        if (val) {
          state.data = [];
          state.total = 0;
          state.ImportRoomAllocRes={}
        }
      }
    );
    return {
      state,
      column,
      submit,
      handlePreview,
      handleSuccess,
      handleError,
      handleClose,
      exportModel,
      handleSizeChange,
      handleCurrentChange,
    };
  },
};
</script>

<style lang="scss" scoped>
.button-box {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .button-item {
    text-decoration: underline;
    margin: 20px 700px 15px 0;
  }
}

.pagination {
  border: 1px solid #eeeeee;
  border-top: none;
  padding: 10px;
  border-radius: 0 0 8px 8px;
}

:deep(.el-pagination .el-select .el-input .el-input__inner) {
  width: 100px;
}
</style>