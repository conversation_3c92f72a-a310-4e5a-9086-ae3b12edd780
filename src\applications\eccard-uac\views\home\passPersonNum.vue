<template>
  <kade-route-card>
    <template #header>
      <div class="header">
        <span class="header-title">近一周门禁通行人次</span>
        <span class="header-right">更多>></span>
      </div>
    </template>
    <div class="data-list">
      <div class="list-item" v-for="(item,index) in state.dataList" :key="index">
        <div class="item-index" :style="{color:index<3?'#fff':'',backgroundColor:index<3?'#314659':''}">{{index+1}}</div>
        <div class="item-area">{{item.area}}</div>
        <div class="item-num">{{item.num}}次</div>
      </div>
    </div>
  </kade-route-card>
</template>
<script>
import { onMounted, reactive } from "vue";
export default {
  setup() {
    const state=reactive({
      dataList:[
        {area:"天府校区A区南大门",num:348901},
        {area:"天府校区A区南大门",num:348901},
        {area:"天府校区A区南大门",num:348901},
        {area:"天府校区A区南大门",num:348901},
        {area:"天府校区A区南大门",num:348901},
        {area:"天府校区A区南大门",num:348901},
        {area:"天府校区A区南大门",num:348901},
      ]
    })
    onMounted(() => {
    });
    return {
      state
    };
  }
}
</script>
<style scoped lang="scss">
.header-right{
  text-decoration: underline;
}
.data-list{
  height: 260px;
  padding: 0 30px;
  .list-item{
    display: flex;
    margin-bottom: 20px;
    .item-index{
      width:20px;
      line-height: 20px;
      border-radius: 50%;
      margin: 0 20px;
      text-align: center;
    }
    .item-area{
      flex: 1;
    }
    .item-num{
      width:100px;
    }
  }
}
</style>