<template>
  <div class="padding-box" v-if="details">
    <div class="basic-info-box">
      <div class="box-item" v-for="(item, index) in list" :key="index">
        <div class="item-label">{{ item.label }}</div>
        <div class="item-value" v-if="item.render">{{item.render(details[item.valueKey])}}</div>
        <div class="item-value" v-else>{{details[item.valueKey]}}</div>
      </div>
    </div>
  </div>
  <el-empty v-else description="当前设备参数未设定"></el-empty>
</template>
<script>
import { computed } from "@vue/runtime-core";
import { useStore } from "vuex";
import { hourStr } from "@/utils/date.js"
import { ElEmpty } from "element-plus"
export default {
  components: {
    ElEmpty
  },
  setup() {
    const store = useStore();
    const details = computed(() => {
      let data = {}
      if (store.state['identityDevice/faceDistingDevice'].detailsParams.base) {
        data = JSON.parse(store.state['identityDevice/faceDistingDevice'].detailsParams.base.paramContent);
      } else {
        data = ''
      }
      return data;
    });
    const dictListFnc = () => {
      return store.state['identityDevice/faceDistingDevice'].faceDict
    }
    const list = [
      { label: "语音模式", valueKey: "speechPatterns", render: val => dictListFnc().speechPatterns.filter(item => item.value == val)[0]?.label },
      { label: "识别距离", valueKey: "readRange", render: val => dictListFnc().readRange.filter(item => item.value == val)[0]?.label },
      { label: "语音自定义", valueKey: "voiceCustomization" },
      { label: "识别分数", valueKey: "recognitionScores" },
      { label: "显示模式", valueKey: "displayMode", render: val => dictListFnc().displayMode.filter(item => item.value == val)[0]?.label },
      { label: "识别间隔", valueKey: "identifyInterval" },
      { label: "显示自定义", valueKey: "displayCustomization" },
      { label: "陌生人开关", valueKey: "strangerSwitch", render: val => dictListFnc().strangerSwitch.filter(item => item.value == val)[0]?.label },
      { label: "韦根输出", valueKey: "wigginsOutput", render: val => dictListFnc().wigginsOutput.filter(item => item.value == val)[0]?.label },
      { label: "补光灯类型", valueKey: "supplementaryLight", render: val => dictListFnc().supplementaryLight.filter(item => item.value == val)[0]?.label },
      { label: "补光灯开始时间", valueKey: "fillingStartTime", render: val => val ? hourStr(val) : "" },
      { label: "补光灯结束时间", valueKey: "fillingEndTime", render: val => val ? hourStr(val) : "" },
    ];
    return {
      list,
      details,
    };
  },
};
</script>
<style lang="scss" scoped>
.basic-info-box {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  border-left: 1px solid #ccc;
  border-top: 1px solid #ccc;
  min-width: 900px;
  .box-item {
    box-sizing: border-box;
    width: 33.333%;
    min-width: 300px;
    line-height: 50px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #ccc;
    .item-label {
      width: 50%;
      text-align: center;
      background: #f6f6f6;
      border-right: 1px solid #ccc;
    }
    .item-value {
      text-align: center;
      width: 50%;
      height: 50px;
      background: #fff;
      border-right: 1px solid #ccc;
    }
  }
}
</style>