import "@babel/polyfill";
import { createApp } from 'vue';
import App from '@/components/app';
import installElementPlus from '@/plugins/element';
import ErrorApp from '@/components/error';
import router from './router';
import store from './store';
import { routeAuthentication, initApp } from '@/utils';
import Icon from '@/components/icon';
import '@/assets/styles/style.scss';
import 'element-plus/lib/theme-chalk/display.css';

routeAuthentication({ router, store }).then(() => {
  const app = createApp(App).use(store).use(router);
  app.component(Icon.name, Icon);
  installElementPlus(app);
  initApp(app, store);
  app.mount('#app');
  const list1 = [
    { id: 1, paramsName: "智能卡消费终端参数1", paramsCode: "001", deviceType: "智能卡消费终端", paramsTemplate: "智能卡消费终端自定义参数", status: true, dateTime: new Date('2022/6/7 16:39:07') },
  ]
  if (!sessionStorage.getItem("customParamsList")) {
    sessionStorage.setItem("customParamsList", JSON.stringify(list1))
  }
  const list2 = [
    { id: 1, templateName: "智能卡消费终端自定义参数", deviceType: "智能卡消费终端", status: true, dateTime: new Date('2022/5/26 09:21:31'), remarks: "--", paramsList: [] },
  ]
  if (!sessionStorage.getItem("templateList")) {
    sessionStorage.setItem("templateList", JSON.stringify(list2))
  }

}).catch(e => {
  const errorApp = createApp(ErrorApp, {
    message: e.message,
  });
  errorApp.mount('#app');
});