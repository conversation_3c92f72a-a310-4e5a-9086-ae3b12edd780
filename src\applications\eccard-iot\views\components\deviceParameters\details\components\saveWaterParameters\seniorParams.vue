<template>
  <div class="padding-box">
    <div v-if="details" class="basic-info-box">
      <div class="box-item" v-for="(item, index) in list" :key="index">
        <div class="item-label">{{ item.label }}</div>
        <div class="item-value" v-if="item.select">{{dictListFnc()[item.select].filter(val => val.value == details[item.valueKey])[0]?.label}}</div>
        <div class="item-value" v-else>{{details[item.valueKey]}}</div>
      </div>
    </div>
    <el-empty v-else description="当前设备参数未设定"></el-empty>
  </div>
</template>
<script>
import { computed } from "@vue/runtime-core";
import { useStore } from "vuex";
import { ElEmpty } from "element-plus"
const list = [
  { label: "一阶段比例(%)", valueKey: "oneStageRatio" },
  { label: "二阶段比例(%)", valueKey: "twoStageRatio" },
  { label: "三阶段比例(%)", valueKey: "thirdStageRatio" },
  { label: "一阶段消费总用水量(升)", valueKey: "oneStageTotalConsumerWater" },
  { label: "二阶段消费总用水量(升)", valueKey: "twoStageTotalConsumerWater", },
  { label: "防盗水时间间隔(秒)", valueKey: "waterTheftTimeInterval", },
  { label: "计量脉冲数", valueKey: "meteringPulseNumber" },
  { label: "当天最大消费次数", valueKey: "maxConsumerCount" },
  { label: "当天消费最大使用时间", valueKey: "maxConsumerTime" },
  { label: "两次消费时间间隔", valueKey: "consumerInterval" },
  { label: "卡片最大余额(元)", valueKey: "cardMaxBalance" },
  { label: "防盗水脉冲数", valueKey: "antiTheftWaterPulseNumber" },
  { label: "地址锁卡模式", valueKey: "addressLockCardMode", select: 'addressLockCardMode' },
  { label: "红外控制功能", valueKey: "infraredControlFunction", select: 'infraredControlFunction' },
  { label: "是否允许使用补助", valueKey: "whetherSubsidiesAllowed", select: 'whetherSubsidiesAllowed' },
];

export default {
  components: {
    ElEmpty,
  },
  setup() {
    const store = useStore();
    const dictListFnc = () => {
      return store.state.deviceParameters[store.state.app.activeTab].dict
    }
    const details = computed(() => {
      let data = "";
      for (let item of store.state.deviceParameters[store.state.app.activeTab].detailsParams) {
        if (item.paramType == "SENIOR") {
          data = JSON.parse(item.paramContent);
          break;
        }
      }
      if (!data) {
        data = ""
      }
      return data;
    });
    return {
      list,
      dictListFnc,
      details,
    };
  },
};
</script>
<style lang="scss" scoped>
.basic-info-box {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  border-left: 1px solid #ccc;
  border-top: 1px solid #ccc;
  min-width: 900px;
  .box-item {
    box-sizing: border-box;
    width: 33.333%;
    min-width: 300px;
    line-height: 50px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #ccc;
    .item-label {
      width: 50%;
      text-align: center;
      background: #f6f6f6;
      border-right: 1px solid #ccc;
    }
    .item-value {
      text-align: center;
      width: 50%;
      background: #fff;
      border-right: 1px solid #ccc;
    }
  }
}
</style>