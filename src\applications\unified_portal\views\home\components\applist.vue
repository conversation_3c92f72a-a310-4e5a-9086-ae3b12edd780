<template>
  <div class="applist" v-loading="loading">
    <template v-if="dataList.length">
      <template v-for="item in dataList" :key="item.id">
        <div class="appitem" @click="handleClick(item)" v-if="item.state == 1">
          <kade-avatar :size="appImageSize" icon="iconyingyong" :is-app-image="true" :src="item.image" />
          <div class="label">{{ item.label }}</div>
        </div>
      </template>
    </template>
    <el-empty v-else description="暂无应用" />
  </div>
</template>
<script>  
import { ref, onMounted } from 'vue';
import { getUserApplyList } from '@/applications/unified_portal/api';
import Avatar from '@/components/avatar';
import { ElEmpty, ElMessage } from 'element-plus';
import { getToken } from '@/utils';
export default {
  components: {
    'el-empty': ElEmpty,
    'kade-avatar': Avatar,
  },
  setup() {
    const loading = ref(false);
    const dataList = ref([]);
    const load = async () => {
      try {
        loading.value = true;
        const { data } = await getUserApplyList({ status: 'ENABLE_TRUE'});
        dataList.value = data.map(it => {
          return {
            ...it,
            label: it.applyName,
            image: it.applyLogo,
          }
        });
      } catch(e){
          throw new Error(e.message);
      } finally {
          loading.value = false;
      }
    }    
    const handleClick = (item) => {
      if(!item.applyUrl) {
        ElMessage.warning('applyUrl 为空');
      } else {
        window.open(`${item.applyUrl}?token=${getToken()}`, item.openMode === 'AOS_CURRENT_WINDOW' ? '_self' : '_blank');
      }
    }
    onMounted(() => {
        load();
    });    
    return {
      dataList,
      loading,
      handleClick,
      appImageSize: THEMEVARS.appImageSize,
    }
  }
}
</script>
<style lang="scss" scoped>
.applist{
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  flex-wrap: wrap;
  padding: 20px;
  box-sizing: border-box;
  .appitem{
    width: 110px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    margin-bottom: 30px;
    cursor: pointer;
    .label{
      margin-top: 5px;
      color: $font-sub-color;
    }
  }
}
</style>