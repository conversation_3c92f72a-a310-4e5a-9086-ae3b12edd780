<template>
  <kade-route-card style="height: auto">
    <kade-table-wrap title="用户列表">
      <template #extra>
        <el-button :disabled="!state.selected" icon="el-icon-edit" size="small" @click="handleCreateVoucher" class="shop-add">生成结算凭证</el-button>
      </template>
      <el-table style="width: 100%" :data="options.dataList" v-loading="false" highlight-current-row border stripe @current-change="
          (v) => {
            $emit('on-select-change', v);
            state.selected = v;
          }
        " @selection-change="(v) => $emit('on-check-change', v)">
        <el-table-column label="商户编号" prop="merchantNo" align="center"></el-table-column>
        <el-table-column label="商户名称" prop="merchantName" align="center"></el-table-column>

        <el-table-column label="商户类型" prop="merchantType" align="center">
          <template #default="scope">
            {{ filterDictionary(scope.row.merchantType, state.merchantTypes) }}
          </template>
        </el-table-column>
        <el-table-column label="所属区域" prop="areaName" align="center"></el-table-column>
        <el-table-column label="终端总量" prop="deviceTotalCount" align="center"></el-table-column>
        <el-table-column label="结余金额" prop="merchantBalance" align="center"></el-table-column>
        <el-table-column label="联系人" prop="merchantContacts" align="center"></el-table-column>
        <el-table-column label="联系电话" prop="merchantTel" align="center"></el-table-column>
        <el-table-column label="操作" align="center" width="240">
          <template #default="scope">
            <el-button type="text" style="margin-right: 10px" @click="handleBtnClick(scope.row, true)" size="mini">查看详情</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination background v-model:current-page="options.currentPage" layout="total, sizes, prev, pager, next, jumper" :page-sizes="[10, 20, 50, 100, 200]" :total="options.total" v-model:page-size="options.pageSize" @current-change="handlePageChange" @size-change="handlePageChange">
        </el-pagination>
      </div>
    </kade-table-wrap>
    <kade-create-settlement-voucher :selected="state.selected" :isShow="state.isVoucher" @close="close"/>
  </kade-route-card>
</template>
<script>
import { ElTable, ElTableColumn, ElButton, ElPagination, ElMessage } from "element-plus";
import { ref, reactive } from "vue";
import { useStore } from "vuex";
import createSettlementVoucher from "./createSettlementVoucher.vue"
export default {
  components: {
    "el-table": ElTable,
    "el-button": ElButton,
    "el-table-column": ElTableColumn,
    "el-pagination": ElPagination,
    "kade-create-settlement-voucher":createSettlementVoucher
  },
  props: {
    tableOptions: {
      type: Object,
      default: () => {
        return {};
      },
    },
    loadData: {
      type: Function,
      default: () => {},
    },
  },
  setup(props, context) {
    const store = useStore();
    const showCreateModal = ref(false);
    const showAuthModal = ref(false);
    const state = reactive({
      isVoucher:false,
      user: {},
      selected: null,
      merchantTypes: JSON.parse(
        localStorage.getItem("kade_cache_dictionary")
      ).filter((item) => item.dictType == "MERCHANT_TYPE"), //商户类型
    });
    const filterDictionary = (val, dictionaryList) => {
      for (let i of dictionaryList) {
        if (val == i.dictCode) {
          return i.dictValue;
        }
      }
    };

    const handlePageChange = () => {
      context.emit("on-page-change");
      props.loadData();
    };
    const handleBtnClick = (row, isInfo) => {
      let query = {};
      if (row) {
        query = { id: row.merchantId };
      }
      if (isInfo) {
        query.type = "info";
      }
      store.dispatch("app/addTab", {
        id: `ShopEdit${row?.merchantId || ""}`,
        payload: {
          menuName: "编辑商户",
          menuEnName: "ShopEdit",
          query,
        },
      });
    };

    const handleCreateVoucher = () => {
      if(state.selected.merchantBalance<0||state.selected.merchantBalance==0){
        return ElMessage.error("当前商户无可结余金额")
      }
      state.isVoucher=true
    };
    const close=(val)=>{
      if(val){
        console.log(1);
      }
      state.isVoucher=false
    }

    return {
      showCreateModal,
      showAuthModal,
      state,
      options: props.tableOptions,
      filterDictionary,
      handleBtnClick,
      handlePageChange,
      handleCreateVoucher,
      close
    };
  },
};
</script>
<style lang="scss" scoped>
.el-table {
  border-left: none;
  tr > th:last-child,
  tr > td:last-child {
    border-right: none !important;
  }
  .el-table--border,
  .el-table--group {
    border: none;
  }
  &::after {
    background-color: transparent;
  }
}
</style>