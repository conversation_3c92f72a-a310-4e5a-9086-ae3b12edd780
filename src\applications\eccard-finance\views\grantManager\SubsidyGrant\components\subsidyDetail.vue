<template>
  <div>
    <el-dialog :model-value="isShow" width="90%" :before-close="beforeClose" :close-on-click-modal="false">
      <template #title>
        <div class="dialog-title">
          <div class="detail-title">补助发放详情</div>
          <div class="detail-status" :style="{
            color: statusColorFnc && statusColorFnc.color,
            background: statusColorFnc && statusColorFnc.background,
          }">
            当前状态：{{ statusColorFnc && statusColorFnc.label }}
          </div>
        </div>
      </template>
      <div>
        <kade-table-wrap title="审核信息" v-if="
          subsidyDetailData.auditStatus == 'SYS_AUDIT_PASSED' ||
          subsidyDetailData.auditStatus == 'SYS_AUDIT_FAIL'
        ">
          <el-divider></el-divider>
          <div class="padding-form-box">
            <el-form inline size="small" label-width="120px">
              <el-form-item label="审核人员：">
                <el-input :model-value="subsidyDetailData.auditPerson" readonly></el-input>
              </el-form-item>
              <el-form-item label="审核时间：">
                <el-input :model-value="timeStr(subsidyDetailData.auditTime)" readonly></el-input>
              </el-form-item>
            </el-form>
            <el-form size="small" label-width="120px">
              <el-form-item label="备注：:">
                <el-input type="textarea" readonly :model-value="subsidyDetailData.auditRemark"></el-input>
              </el-form-item>
            </el-form>
          </div>
        </kade-table-wrap>
        <kade-table-wrap title="补助信息">
          <el-divider></el-divider>
          <div class="padding-form-box">
            <el-form inline size="small" label-width="120px">
              <el-form-item label="项目名称：">
                <el-input :model-value="subsidyDetailData.projectName" readonly></el-input>
              </el-form-item>
              <el-form-item label="补助类型：">
                <el-input :model-value="subsidyDetailData.subsidyTypeName" readonly></el-input>
              </el-form-item>
              <el-form-item label="交易类型：">
                <el-input :model-value="subsidyDetailData.costTypeName" readonly></el-input>
              </el-form-item>
              <el-form-item label="所属年月：">
                <el-input :model-value="timeStr(subsidyDetailData.subsidyMonth)" readonly></el-input>
              </el-form-item>
              <el-form-item label="导入人员：">
                <el-input :model-value="subsidyDetailData.createUserName" readonly></el-input>
              </el-form-item>
              <el-form-item label="发放方式：">
                <el-input :model-value="subsidyDetailData.subsidyTypeName" readonly></el-input>
              </el-form-item>
 <!--              <el-form-item label="生效时间：">
                <el-input :model-value="timeStr(subsidyDetailData.takeEffectTime)" readonly></el-input>
              </el-form-item>
              <el-form-item label="有效期至：">
                <el-input :model-value="timeStr(subsidyDetailData.invalidTime)" readonly></el-input>
              </el-form-item>
              <el-form-item label="每人补助金额：">
                <el-input :model-value="
                  (subsidyDetailData.totalAmount /
                    subsidyDetailData.totalPerson).toFixed(2)
                " readonly></el-input>
              </el-form-item> -->
              <el-form-item label="总补助人数：">
                <el-input :model-value="subsidyDetailData.totalPerson" readonly></el-input>
              </el-form-item>
              <el-form-item label="总金额：">
                <el-input :model-value="subsidyDetailData.totalAmount" readonly></el-input>
              </el-form-item>
              <el-form-item label="是否已发放：">
                <el-input :model-value="subsidyDetailData.trade" readonly></el-input>
              </el-form-item>
              <el-form-item label="未到账人数：">
                <el-input :model-value="subsidyDetailData.tradeCount" readonly></el-input>
              </el-form-item>
            </el-form>
            <el-form size="small" label-width="120px">
              <el-form-item label="发放说明:">
                <el-input placeholder="请输入" type="textarea" readonly :model-value="subsidyDetailData.grantRemark">
                </el-input>
              </el-form-item>
            </el-form>
          </div>
        </kade-table-wrap>
        <kade-table-wrap title="人员清单">
          <el-table style="width: 100%" :data="state.personList" v-loading="false" highlight-current-row border stripe>
            <el-table-column show-overflow-tooltip label="用户编号" prop="userCode" align="center"></el-table-column>
            <el-table-column show-overflow-tooltip label="姓名" prop="userName" align="center"></el-table-column>
            <el-table-column show-overflow-tooltip label="组织结构" prop="deptName" align="center"></el-table-column>
            <el-table-column show-overflow-tooltip label="身份类别" prop="roleName" align="center"></el-table-column>
            <el-table-column show-overflow-tooltip label="补助金额" prop="subsidyAmount" align="center"></el-table-column>
            <el-table-column show-overflow-tooltip label="生效时间" prop="takeEffectTime" align="center"></el-table-column>
            <el-table-column show-overflow-tooltip label="过期时间" prop="invalidTime" align="center"></el-table-column>
            <el-table-column show-overflow-tooltip label="到账状态" prop="" align="center"></el-table-column>
          </el-table>
          <div class="pagination">
            <el-pagination background :current-page="state.form.currentPage" :page-size="state.form.pageSize"
              layout="total, sizes, prev, pager, next, jumper" :page-sizes="[6, 10, 20, 50, 100]" :total="state.total"
              @current-change="handlePageChange" @size-change="handleSizeChange">
            </el-pagination>
          </div>
        </kade-table-wrap>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="off()" size="mini">关&nbsp;&nbsp;闭</el-button>
          <el-button @click="edit()" v-if="subsidyDetailData.auditStatus !== 'SYS_AUDIT_PASSED'" type="primary"
            size="mini">编&nbsp;&nbsp;辑</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script>
import { useStore } from "vuex";
import { computed, reactive, watch } from "vue";
import { getSubsidyListByPage } from "@/applications/eccard-finance/api";
import { statusColor } from "../styleData";
import { timeStr } from "@/utils/date.js";
import {
  ElDialog,
  ElDivider,
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElTable,
  ElTableColumn,
  ElPagination,
} from "element-plus";
export default {
  components: {
    "el-dialog": ElDialog,
    "el-button": ElButton,
    "el-input": ElInput,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    "el-table": ElTable,
    "el-table-column": ElTableColumn,
    "el-pagination": ElPagination,
    ElDivider,
  },
  props: {
    isShow: {
      types: Boolean,
      default: false,
    },
    subsidyDetailData: {
      types: Object,
      default: {},
    },
    projectId: {
      types: String,
      default: "",
    },
    personListData: {
      types: Array,
      default: [],
    },
  },
  setup(props, context) {
    const store = useStore();
    const state = reactive({
      form: {
        currentPage: 1,
        pageSize: 6,
        projectId: props.projectId,
      },
      personList: props.personListData.list,
      total: props.personListData.total,
    });
    const statusColorFnc = computed(() => {
      return statusColor(props.subsidyDetailData.auditStatus);
    });

    watch(
      () => props.isShow,
      (val) => {
        if (val) {
          state.form.projectId = props.projectId;
          getDataList();
        }
      }
    );

    const getDataList = () => {
      getSubsidyListByPage(state.form).then((res) => {
        state.personList = res.data.pageInfo.list;
        state.total = res.data.pageInfo.total;
      });
    };
    const edit = () => {
      store.commit("subsidyData/updateState", {
        key: "selectRow",
        payload: props.subsidyDetailData,
      });
      store.commit("subsidyData/updateState", { key: "isEdit", payload: true });
      context.emit("offDetail", false);
    };
    const off = () => {
      context.emit("offDetail", false);
    };
    const beforeClose = () => {
      context.emit("offDetail", false);
    };
    const handlePageChange = (val) => {
      state.form.currentPage = val;
      getDataList();
    };
    const handleSizeChange = (val) => {
      state.form.currentPage = 1;
      state.form.pageSize = val;
      getDataList();
    };
    return {
      state,
      timeStr,
      statusColorFnc,
      getDataList,
      edit,
      off,
      beforeClose,
      handlePageChange,
      handleSizeChange,
    };
  },
};
</script>
<style lang="scss" scoped>
:deep(.el-dialog__header) {
  border-bottom: 1px solid #efefef;
}

:deep(.el-overlay) {
  position: absolute;
  background-color: rgba(0, 0, 0, 0);
}

:deep(.el-dialog__footer) {
  text-align: center;
}

.dialog-title {
  display: flex;
  align-items: center;

  .detail-status {
    border-radius: 4px;
    margin-left: 20px;
    padding: 10px;
    color: #f15a1f;
    background: #ffb89c;
  }
}

.el-divider--horizontal {
  margin: 5px 0;
}

.kade-table-wrap {
  margin: 10px 0;
}
</style>