<template>
  <div class="box">
    <kade-table-wrap :title="titleData.oneTitle" style="flex:1" icon="none">
      <el-divider></el-divider>
      <div class="padding-box">
        <slot name="firstSearch"></slot>
        <el-table height="40vh" :data="listData.oneList" @selection-change="handleSelectionChange1" border>
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column v-for="(item, index) in columnData.oneList" :key="index" :label="item.label"
            :prop="item.prop" align="center">
            <template v-if="item.isDict" #default="scope">
              {{ dictionaryFilter(scope.row[item.prop]) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" v-if="delData && delData.isLeft">
            <template #default="scope">
              <el-button size="mini" type="text" @click="handleDel(scope.row)">{{ delData.label }}</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination">
          <el-pagination background :total="listData.oneTotal" layout="prev, pager, next"
            @current-change="handleCurrentChange1" />
        </div>
      </div>
    </kade-table-wrap>
    <div class="move">
      <el-button size="mini" class="move-btn" type="primary" icon="el-icon-d-arrow-right" @click="toAllRight"
        :disabled="!listData.oneList.length"></el-button>
      <el-button size="mini" class="move-btn" type="primary" icon="el-icon-arrow-right" @click="toRight"
        :disabled="!listData.oneList.length || !state.oneCheckedList.length"></el-button>
      <el-button size="mini" class="move-btn" type="primary" icon="el-icon-arrow-left" @click="toLeft"
        :disabled="!listData.twoList.length || !state.twoCheckedList.length"></el-button>
      <el-button size="mini" class="move-btn" type="primary" icon="el-icon-d-arrow-left" @click="toAllLeft"
        :disabled="!listData.twoList.length"></el-button>
    </div>
    <kade-table-wrap :title="titleData.twoTitle" style="flex:1" icon="none">
      <el-divider></el-divider>
      <div class="padding-box">
        <slot name="secondSearch"></slot>
        <el-table height="40vh" :data="listData.twoList" @selection-change="handleSelectionChange2" border>
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column v-for="(item, index) in columnData.twoList" :key="index" :label="item.label"
            :prop="item.prop" align="center">
            <template v-if="item.isDict" #default="scope">
              {{ dictionaryFilter(scope.row[item.prop]) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" v-if="delData && delData.isRight">
            <template #default="scope">
              <el-button size="mini" type="text" @click="handleDel(scope.row)">{{ delData.label }}</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination">
          <el-pagination background :total="listData.twoTotal" layout="prev, pager, next"
            @current-change="handleCurrentChange" />
        </div>
      </div>
    </kade-table-wrap>
  </div>
</template>
<script>
import { reactive } from '@vue/reactivity'
import { ElButton, ElDivider, ElTable, ElTableColumn, ElPagination } from "element-plus"
export default {
  components: {
    ElButton, ElDivider, ElTable, ElTableColumn, ElPagination
  },
  emits: ["toAllRight", "toRight", "toLeft", "toAllLeft", "oneCurrentChange", "twoCurrentChange"],
  props: {
    titleData: {
      type: Object,
      default: null
    },
    listData: {
      type: Object,
      default: null
    },
    columnData: {
      type: Object,
      default: null
    },
    delData: {
      type: Object,
      default: null
    }
  },
  setup(props, context) {
    const state = reactive({
      oneCheckedList: [],
      twoCheckedList: [],
    })
    const handleSelectionChange1 = (val) => {
      state.oneCheckedList = val
    }
    const handleSelectionChange2 = (val) => {
      state.twoCheckedList = val
    }

    //左侧全部向右移动
    const toAllRight = () => {
      context.emit("toAllRight")
    }
    //左侧向右移动选择元素
    const toRight = () => {
      context.emit("toRight", state.oneCheckedList)
    }
    //右侧向左移动选择元素
    const toLeft = () => {
      context.emit("toLeft", state.twoCheckedList)
    }
    //右侧全部向左移动
    const toAllLeft = () => {
      context.emit("toAllLeft")
    }
    //删除元素
    const handleDel = (row) => {
      context.emit("handleDel", row)
    }
    //左侧列表页数改变
    const handleCurrentChange1 = val => {
      context.emit("oneCurrentChange", val)
    }
    //右侧列表页数改变
    const handleCurrentChange2 = val => {
      context.emit("twoCurrentChange", val)
    }
    return {
      state,
      handleSelectionChange1,
      handleSelectionChange2,
      toAllRight,
      toRight,
      toLeft,
      toAllLeft,
      handleDel,
      handleCurrentChange1,
      handleCurrentChange2
    }
  }
}
</script>
<style lang="scss" scoped>
.box {
  box-sizing: border-box;
  padding: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .move {
    width: 40px;
    text-align: center;
    margin: 0 10px;

    .move-btn {
      width: 30px;
      margin: 0 0 10px 0;
      padding: 5px;
    }
  }
}

.el-divider--horizontal {
  margin: 0
}

.el-table {
  border-left: 1px solid #EBEEF5;
  border-right: 1px solid #EBEEF5;
}

.padding-box {
  padding: 10px
}
</style>