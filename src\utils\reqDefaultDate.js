import { timeStr, dateStr } from "@/utils/date.js"

export const defaultDateList = [{
    label: "今天",
    value: () => {
        const end = new Date(
            new Date(new Date().toLocaleDateString()).getTime() +
            24 * 60 * 60 * 1000 -
            1
        );
        const start = new Date(
            new Date(new Date().toLocaleDateString()).getTime()
        );
        return [timeStr(start), timeStr(end)];
    },
},
{
    label: "昨天",
    value: () => {
        const end = new Date(
            new Date(new Date().toLocaleDateString()).getTime() - 1
        );
        const start = new Date(end - 24 * 60 * 60 * 1000 + 1);
        return [timeStr(start), timeStr(end)];
    },
},
{
    label: "近七天",
    value: () => {
        const end = new Date(
            new Date(new Date().toLocaleDateString()).getTime() +
            24 * 60 * 60 * 1000 -
            1
        )
        const start = new Date(
            new Date(new Date().toLocaleDateString()).getTime()
        );
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
        return [timeStr(start), timeStr(end)];
    },
},
{
    label: "近三十天",
    value: () => {
        const end = new Date(
            new Date(new Date().toLocaleDateString()).getTime() +
            24 * 60 * 60 * 1000 -
            1
        )
        const start = new Date(
            new Date(new Date().toLocaleDateString()).getTime()
        );
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
        return [timeStr(start), timeStr(end)];
    },
},
]





export const requestDate = function () {
    const start = new Date(
        new Date(new Date().toLocaleDateString()).getTime()
    );
    start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
    return [
        timeStr(
            start
        ),
        timeStr(new Date(
            new Date(new Date().toLocaleDateString()).getTime() +
            24 * 60 * 60 * 1000 -
            1
        )),
    ]
}

export const requestDefaultDate = function () {
    const start = new Date(
        new Date(new Date().toLocaleDateString()).getTime()
    );
    start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
    return [
        dateStr(
            start
        ),
        dateStr(new Date()),
    ]
}

export const requestDefaultTime = function () {
    return [
        new Date(new Date(new Date().toLocaleDateString()).getTime()),
        new Date(
            new Date(new Date().toLocaleDateString()).getTime() +
            24 * 60 * 60 * 1000 -
            1
        )
    ]
}

export const defaultTime = [
  new Date(new Date(new Date().toLocaleDateString()).getTime()),
  new Date(
    new Date(new Date().toLocaleDateString()).getTime() +
      24 * 60 * 60 * 1000 -
      1
  )
];