<template>
    <el-form
      :model="state.model"
      :rules="rules"
      ref="formRef"
      :label-width="labelWidth"
      size="small"
      v-loading="state.loadLoading"
      @keyup.enter="handleSubmit"
    >
      <el-row>
        <el-col v-bind="layout.one">
          <el-form-item label="证件照" prop="userPhoto">
            <kade-single-image-upload :action="uploadApplyLogo" icon="iconuser" v-model="state.model.userPhoto" />
            <el-alert
              style="margin-top: 10px;width: 300px"
              center
              show-icon
              type="warning"
              title="请上传分辨率为295*413的一寸照片"
              :closable="false"
            />
          </el-form-item>
        </el-col>
        <el-col v-bind="layout.normal">
          <el-form-item label="用户编号" prop="userCode" >
            <el-input placeholder="请输入" :disabled="store.getters['app/query'].id" v-model="state.model.userCode" />
          </el-form-item>
        </el-col>
        <el-col v-bind="layout.normal">
          <el-form-item label="用户姓名" prop="userName">
            <el-input placeholder="请输入" v-model="state.model.userName" />
          </el-form-item>
        </el-col>
        <el-col v-bind="layout.normal">
          <el-form-item label="性别" prop="userSex">
            <el-radio-group v-model="state.model.userSex">
              <el-radio v-for="item in sexList" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col v-bind="layout.normal">
          <el-form-item label="生日" prop="userBirthday">
            <el-date-picker
              style="width: 100%"
              v-model="state.model.userBirthday"
              placeholder="请选择日期"
              :disabledDate="disabledDate"
            />
          </el-form-item>
        </el-col>
        <el-col v-bind="layout.normal">
          <el-form-item label="来源" prop="userSource">
            <el-select clearable style="width:100%" placeholder="请选择" v-model="state.model.userSource">
              <el-option v-for="item in sources" :key="item.value" :value="item.value" :label="item.label"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col v-bind="layout.normal">
          <el-form-item label="身份类别" prop="userRole">
            <kade-origin-select
              :pageKeys="{ currentPage: 'currPage', pageSize: 'pageSize' }"
              :action="getRolelistByPage()"
              :columns="[{ prop: 'roleName', label: '类别名称', align: 'center' }]"
              :text="state.roleName"
              @change="selectRoleChange"
            />
          </el-form-item>
        </el-col>
        <el-col v-bind="layout.normal">
          <el-form-item label="组织机构" prop="userDept">
            <kade-dept-select-tree style="width:100%" :value="state.model.userDept " valueKey="id" :multiple="false" @valueChange="val=>state.model.userDept=val.id" />
          </el-form-item>
        </el-col>
        <el-col v-bind="layout.normal">
          <el-form-item label="人员状态" prop="userState">
            <el-select clearable style="width:100%" placeholder="请选择" v-model="state.model.userState">
              <el-option v-for="item in userStates" :key="item.value" :value="item.value" :label="item.label"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col v-bind="layout.normal" v-if="state.model.userState === 'STATE_LEAVE'">
          <el-form-item label="离校时间" prop="userLeaveTime">
            <el-date-picker
              style="width: 100%"
              v-model="state.model.userLeaveTime"
              placeholder="请选择日期时间"
            />
          </el-form-item>
        </el-col>
        <el-col v-bind="layout.normal">
          <el-form-item label="证件类型" prop="userIdType">
            <el-select clearable style="width:100%" placeholder="请选择" v-model="state.model.userIdType">
              <el-option v-for="item in cardTypes" :key="item.value" :value="item.value" :label="item.label"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col v-bind="layout.normal">
        <el-form-item label="证件号码" prop="userIdNo">
          <el-input placeholder="请输入" v-model="state.model.userIdNo" />
        </el-form-item>
        </el-col>
        <el-col v-bind="layout.normal">
        <el-form-item label="联系电话" prop="userTel">
          <el-input placeholder="请输入" v-model="state.model.userTel" />
        </el-form-item>
        </el-col>
        <el-col v-bind="layout.normal">
        <el-form-item label="是否住校" prop="userIsBoarders">
          <el-radio-group v-model="state.model.userIsBoarders">
            <el-radio v-for="item in boarders" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
          </el-radio-group>
        </el-form-item>
        </el-col>
        <el-col v-bind="layout.normal">
        <el-form-item label="银行卡号" prop="userBankNo">
          <el-input placeholder="请输入" v-model="state.model.userBankNo" />
        </el-form-item>
        </el-col>
<!--         <el-col v-bind="layout.normal">
        <el-form-item label="交易密码" :prop="state.model.id ? '' : 'userTransPwd'">
          <el-input placeholder="请输入" :type="state.model.id ? 'password' : 'text'" :disabled="!!state.model.id" v-model="state.model.userTransPwd">
            <template v-if="state.model.id" #append>
              <el-button type="primary" :loading="state.resetLoading" @click="resetPass">重置密码</el-button>
            </template>
          </el-input>
        </el-form-item>
        </el-col> -->
        <el-col v-bind="layout.one">
        <el-form-item label="通信地址" prop="userAddress">
          <el-input :rows="3" type="textarea" show-word-limit maxlength="100" placeholder="请输入" v-model="state.model.userAddress" />
        </el-form-item>
        </el-col>
        <el-col v-bind="layout.one">
        <el-form-item label="备注" prop="userRemark">
          <el-input maxlength="100" show-word-limit type="textarea" :rows="5" placeholder="请输入" v-model="state.model.userRemark" />
        </el-form-item>
        </el-col>
        <el-col v-bind="layout.one">
        <el-form-item label="启用账户" prop="userAccountState">
          <el-switch
            v-model="state.model.userAccountState"
            :active-color="themes.primaryColor"
            :inactive-color="themes.dangerColor"
            active-value="ENABLE_TRUE"
            inactive-value="ENABLE_FALSE"
          >
          </el-switch>
        </el-form-item>
        </el-col>
        <el-col v-bind="layout.one">
        <el-form-item>
          <el-button icon="el-icon-circle-close" @click="handleBack">关闭</el-button>
          <el-button icon="el-icon-circle-check" :loading="state.loading" type="primary" @click="handleSubmit">保存</el-button>
        </el-form-item>
        </el-col>
      </el-row>
    </el-form>
</template>
<script>
import {
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElOption,
  ElRow,
  ElCol,
  ElSelect,
  ElSwitch,
  ElRadio,
  ElRadioGroup,
  ElMessage,
  ElAlert,
  ElMessageBox,
  ElDatePicker,
} from 'element-plus';
import { reactive, ref, onMounted, nextTick } from 'vue';
import SingleImageUpload from '@/components/singleImageUpload';
import { uploadApplyLogo } from '@/applications/unified_portal/api';
import {
  addUser,
  updateUserInfo,
  getRolelistByPage,
  getDepartTree,
  resetPwdById,
  getUserInfoById,
} from '@/applications/eccard-basic-data/api';
import { useDict } from '@/hooks/useDict';
import { useStore } from 'vuex';
import { useEvent } from '@/hooks';
import OriginSelect from '@/components/originSelect';
import deptSelectTree from "@/components/tree/deptSelectTree.vue"
import { fillData } from '@/utils';
import { checkIdCard, checkPassport, validateMobileAndFixTel } from '@/utils/validate';
import moment from 'moment';
const getDefaultModel = () => ({
  userAddress: '',
  userBankNo: '',
  userBirthday: '',
  userCode: '',
  userDept: '',
  userIdNo: '',
  userIdType: '',
  userIsBoarders: 'TRUE',
  userLeaveTime: '',
  userName: '',
  userPhoto: '',
  userRemark: '',
  userRole: '',
  userSex: 'SEX_MALE',
  userSource: '',
  userTel: '',
  userAccountState: 'ENABLE_TRUE',
  userState: '',
  userTransPwd: '000000',
  userDeptName: '',
  id: 0,
  createUser: 1
});
export default {
  components: {
    'el-form': ElForm,
    'el-form-item': ElFormItem,
    'el-input': ElInput,
    'el-select': ElSelect,
    'el-option': ElOption,
    'el-switch': ElSwitch,
    'el-button': ElButton,
    'el-radio': ElRadio,
    'el-radio-group': ElRadioGroup,
    'el-row': ElRow,
    'el-col': ElCol,
    'el-alert': ElAlert,
    'kade-single-image-upload': SingleImageUpload,
    'kade-origin-select': OriginSelect,
    'kade-dept-select-tree': deptSelectTree,
    'el-date-picker': ElDatePicker,
  },
  setup() {
    const formRef = ref(null);
    const sexList = useDict('SYS_SEX');
    const userStates = useDict('BASE_USER_STATE');
    const boarders = useDict('SYS_BOOL_STRING');
    const cardTypes = useDict('BASE_ID_TYPE');
    const sources = useDict('SYS_DATA_SOURCE');
    const store = useStore();
    const { $emit } = useEvent();
    const state = reactive({
      model: getDefaultModel(),
      loading: false,
      roleName: '',
      resetLoading: false,
      loadLoading: false,
    });

    const validateLeaveTime = (rule, value, callback) => {
      if(state.model.userIsBoarders === 'FALSE' && !value) {
        callback(new Error('请选择离校时间'));
      } else {
        callback();
      }
    }

    const validateCardNumber = (rule, value, callback) => {
      if (state.model.userIdType === 'ID_CARD') {
        if(!checkIdCard(value)) {
          callback(new Error('身份证号码格式错误'));
          return;
        }
      } else if (state.model.userIdType === 'ID_PASSPORT') {
        if(!checkPassport(value)) {
          callback(new Error('护照号码格式错误'));
          return;
        }
      }
      callback();
    }

    const rules = {
      userCode: [{
        required: true,
        message: '请输入用户编号'
      },{
        pattern: /^[0-9a-zA-Z]+$/,
        message: '请输入字母+数字',
      },{
        max: 20,
        message: '用户编号长度不能超过20字符'
      }],
      userDept: [{
        required: true,
        message: '请选择组织机构',
        trigger: 'change'
      }],
      userIdNo: [{
        required: true,
        message: '请输入证件号',
      },{
        validator: validateCardNumber,
      }],
      userIdType: [{
        required: true,
        message: '请选择证件类型',
        trigger: 'change'
      }],
      userSex: [{
        required: true,
        message: '请选择性别',
        trigger: 'change'
      }],
      userIsBoarders: [{
        required: true,
        message: '请选择是否住校',
        trigger: 'change'
      }],
      userBankNo: [{
        required: true,
        message: '请输入银行卡号'
      },{
        pattern: /^([1-9]{1})(\d{14}|\d{18})$/,
        message: '银行卡号格式错误'
      }],
      userName: [{
        required: true,
        message: '请输入用户姓名'
      },{
        max: 20,
        message: '用户姓名长度不能超过20字符'
      },{
        pattern: /^\S.*\S$|(^\S{0,1}\S$)/,
        message: '用户姓名首尾不能包含空格'
      }],
      userRole: [{
        required: true,
        message: '请选择身份类别',
        trigger: 'change'
      }],
      userSource: [{
        required: true,
        message: '请选择来源',
        trigger: 'change'
      }],
      userTel: [{
        required: true,
        message: '请输入联系电话'
      },{
        validator: validateMobileAndFixTel,
      }],
      userBirthday: [{
        required: true,
        message: '清选择生日',
        trigger: 'change',
      }],
      userLeaveTime: [{
        validator: validateLeaveTime,
      }],
      userTransPwd: [{
        required: true,
        message: '请输入交易密码'
      },{
        pattern: /\d{6}/gim,
        message: '请输入六位数字',
      }]
    };

    const selectRoleChange = ({ roleName, id }) => {
      state.roleName = roleName;
      state.model.userRole = id;
    }

    const handleSubmit = () => {
      formRef.value.validate(async (valid) => {
        if (valid) {
          try {
            state.loading = true;
            // eslint-disable-next-line no-unused-vars
            const { userLeaveTime, userBirthday, userTransPwd, userDeptName, ...fields } = state.model;
            Object.assign(fields, {
              userBirthday: moment(userBirthday).format('YYYY-MM-DD'),
            });
            if (fields.userState === 'STATE_LEAVE') {
              Object.assign(fields, {
                userLeaveTime: moment(userLeaveTime).format('YYYY-MM-DD HH:mm:ss'),
              });
            }
            if (!fields.id) {
              fields.userTransPwd = userTransPwd;
            }
            const fn = fields.id ? updateUserInfo : addUser;
            const { message ,code} = await fn(fields);
            if(code===0){
              ElMessage.success(message);
              store.dispatch('app/closeCurrent');
            }
            
            $emit('personal/refresh');
          } catch(e) {
            throw new Error(e);
          } finally {
            state.loading = false;
          }
        }
      });
    }
    const resetPass = () => {
      ElMessageBox.confirm(`将交易密码重置为"000000"?`, {
          type: 'warning',
          closeOnPressEscape: false,
          closeOnClickModal: false,
      }).then(async () => {
          try {
              state.resetLoading = true;
              const { message } = await resetPwdById({ id: state.model.id });
              ElMessage.success(message);
          } catch(e) {
              throw new Error(e.message);
          } finally {
            state.resetLoading = false;
          }
      });
    }

    const loadData = async () => {
      try {
        state.loadLoading = true;
        const { data } = await getUserInfoById({ userId: store.getters['app/query'].id });
        const target = getDefaultModel();
        fillData(target, data);
        state.model = {
          ...target,
          userDeptName: data.deptName,
        };
        state.roleName = data.roleName; 
        nextTick(() => {
          formRef.value.clearValidate();
        });
      } catch(e) {
        throw new Error(e.message);
      } finally {
        state.loadLoading = false;
      }
    }

    const handleBack = () => {
      store.dispatch('app/closeCurrent');
    }

    const disabledDate = (date) => {
      const curr = new Date();
      return date.getTime() > curr.getTime();
    }

    onMounted(() => {
      if(store.getters['app/query'].id) {
        loadData();
      }
    });

    return {
      store,
      state,
      rules,
      formRef,
      labelWidth: THEMEVARS.formLabelWidth,
      handleSubmit,
      handleBack,
      uploadApplyLogo,
      sexList,
      userStates,
      boarders,
      cardTypes,
      themes: THEMEVARS,
      getRolelistByPage: () => {
        return (args) => {
          return getRolelistByPage({ ...args, status: 'ENABLE_TRUE' });
        }
      },
      selectRoleChange,
      resetPass,
      sources,
      disabledDate,
      selectDeptAction: async (params) => {
        const { data: { deptMenuList } } = await getDepartTree(params);
        return deptMenuList;
      },
      layout: {
        normal: {
          xl: 6,
          lg: 8,
          md: 12,
          sm: 24,
        },
        one: {
          span: 24
        }
      }
    }
  }
}
</script>
