<template>
  <kade-route-card style="height: auto">
    <kade-table-wrap title="明细列表">
      <template #extra>
        <el-button
          icon="el-icon-zuhuzu"
          size="small"
          class="shop-add"
          >下载查询明细</el-button
        >
        <el-button icon="el-icon-daoru" size="small" class="shop-upload"
          >打印</el-button
        >
        <el-button icon="el-icon-zuhuzu" size="small" class="shop-add">下载查询明细</el-button>
        <el-button icon="el-icon-daoru" size="small" class="shop-upload">打印</el-button>
      </template>
      <el-table style="width: 100%" :data="options.dataList" v-loading="false" highlight-current-row border stripe>
        <el-table-column type="selection" width="55" align="center"></el-table-column>
        <el-table-column label="交易编号" prop="jybh12" align="center"></el-table-column>
        <el-table-column label="交易时间" prop="jybh11" align="center"></el-table-column>

        <el-table-column label="对方信息" prop="jybh9" align="center"></el-table-column>
        <el-table-column label="交易钱包" prop="jybh8" align="center"></el-table-column>
        <el-table-column label="收支类型" prop="jybh7" align="center"></el-table-column>
        <el-table-column label="交易类型" prop="jybh6" align="center"></el-table-column>
        <el-table-column label="交易来源" prop="jybh5" align="center"></el-table-column>

        <el-table-column label="设备所在区域" prop="jybh4" align="center"></el-table-column>
        <el-table-column label="设备名称" prop="jybh3" align="center"></el-table-column>
        <el-table-column label="收入金额(元)" prop="jybh2" align="center"></el-table-column>
      </el-table>
      <div class="pagination" v-if="options.total<1">
        <el-pagination background v-model:current-page="options.currentPage" layout="total, sizes, prev, pager, next, jumper" :page-sizes="[10, 20, 50, 100, 200]" :total="options.total" v-model:page-size="options.pageSize" @current-change="handlePageChange" @size-change="handlePageChange">
        </el-pagination>
      </div>
    </kade-table-wrap>
  </kade-route-card>
</template>
<script>
import { ElTable, ElTableColumn, ElButton, ElPagination } from "element-plus";
import { ref, reactive } from "vue";
import { useStore } from "vuex";
export default {
  components: {
    "el-table": ElTable,
    "el-button": ElButton,
    "el-table-column": ElTableColumn,
    "el-pagination": ElPagination,
  },
  props: {
    tableOptions: {
      type: Object,
      default: () => {
        return {};
      },
    },
    loadData: {
      type: Function,
      default: () => {},
    },
  },
  setup(props, context) {
    const store = useStore();
    const showCreateModal = ref(false);
    const showAuthModal = ref(false);
    const state = reactive({
      user: {},
    });

    const addUser = () => {
      state.user = {};
      showCreateModal.value = true;
    };
    const editUser = (user) => {
      state.user = user;
      showCreateModal.value = true;
    };
    const handlePageChange = () => {
      context.emit("on-page-change");
      props.loadData();
    };
    const handleBtnClick = (row, isInfo) => {
      let query = {};
      console.log("row", row);
      if (row) {
        query = { id: row.merchantId };
      }
      if (isInfo) {
        query.type = "info";
      }
      store.dispatch("app/addTab", {
        id: `ShopEdit${row?.merchantId || ""}`,
        payload: {
          menuName: "编辑商户",
          menuEnName: "ShopEdit",
          query,
        },
      });
    };
    return {
      showCreateModal,
      showAuthModal,
      state,
      options: props.tableOptions,
      addUser,
      editUser,
      handleBtnClick,
      handlePageChange,
    };
  },
};
</script>
<style lang="scss" scoped>
.el-botton {
  color: #fff;
}

.shop-add {
  background: rgb(2, 210, 0, 1);
  color: #fff;
}

.shop-add:hover {
  background: rgba(9, 176, 5, 1);
  color: #fff;
}

.shop-upload {
  background: rgba(38, 158, 255, 1);
  color: #fff;
}
.shop-upload:hover {
  background: rgba(49, 56, 223, 1);
  color: #fff;
}

.shop-out {
  background: rgba(84, 95, 255, 1);
  color: #fff;
}
.shop-out:hover {
  background: rgba(49, 56, 223, 1);
  color: #fff;
}

.el-table {
  border-left: none;
  tr > th:last-child,
  tr > td:last-child {
    border-right: none !important;
  }
  .el-table--border,
  .el-table--group {
    border: none;
  }
  &::after {
    background-color: transparent;
  }
}
</style>