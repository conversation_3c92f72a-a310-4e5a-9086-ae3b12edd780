<template>
  <div class="personnelInformation">
    <kade-route-card style="height: auto">
      <kade-table-filter @search="handleSearch" @reset="handleReset">
        <el-form inline size="small" label-width="100px">
          <el-form-item label="所属区域">
            <kade-area-select-tree style="width: 100%" :value="state.form.areaId" valueKey="id" :multiple="false"
              @valueChange="(val) => (state.form.areaId = val.id)" />
          </el-form-item>
          <el-form-item label="机号">
            <el-input placeholder="请输入" v-model="state.form.deviceNo"></el-input>
          </el-form-item>
          <el-form-item label="设备名称">
            <el-input placeholder="请输入" v-model="state.form.deviceName"></el-input>
          </el-form-item>
          <el-form-item label="设备IP">
            <el-input placeholder="请输入" v-model="state.form.deviceIp"></el-input>
          </el-form-item>
        </el-form>
      </kade-table-filter>
      <kade-table-wrap title="智能金融消费设备列表">
        <!--         <template #extra>
          <el-button @click="state.isShowAdd = true" icon="el-icon-daoru" size="small" class="btn-yellow">新增</el-button>
        </template> -->
        <el-table style="width: 100%" :data="state.dataList" ref="multipleTable" v-loading="state.loading" height="55vh"
          highlight-current-row border stripe>
          <!--           <el-table-column
            type="selection"
            width="55"
            align="center"
          ></el-table-column> -->
          <el-table-column show-overflow-tooltip label="设备类型" prop="deviceType" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip label="设备机号" prop="deviceNo" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip label="设备名称" prop="deviceName" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip label="终端型号" prop="deviceModel" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip label="所属区域" prop="areaName" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip label="设备状态" prop="deviceStatus" align="center">
            <template #default="scope">
              {{dictionaryFilter(scope.row.deviceStatus)}}
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip label="连接类型" prop="deviceConnectType" align="center">
            <template #default="scope">
              {{dictionaryFilter(scope.row.deviceConnectType)}}
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip label="设备IP" prop="deviceIp" align="center"></el-table-column>
          <el-table-column label="操作" align="center">
            <template #default="scope">
              <el-button size="mini" type="text" @click="handleBindFood(scope.row)">绑定菜品</el-button>
              <el-button size="mini" type="text" @click="handleDetails(scope.row)">详情</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination">
          <el-pagination background :current-page="state.form.pageNum" :page-size="state.form.pageSize"
            layout="total, sizes, prev, pager, next, jumper" :page-sizes="[6, 10, 20, 50, 100]" :total="state.total"
            @current-change="handlePageChange" @size-change="handleSizeChange">
          </el-pagination>
        </div>
      </kade-table-wrap>
    </kade-route-card>
    <kade-food-bind v-model="state.isBind" :rowData="state.rowData" @update:modelValue="state.isBind=false" />
    <kade-device-details v-model="state.isDetails" :rowData="state.rowData"
      @update:modelValue="state.isDetails=false" />
  </div>
</template>
<script>
import {
  ElTable,
  ElTableColumn,
  ElForm,
  ElFormItem,
  ElInput,
  ElPagination,
  ElButton,
} from "element-plus";
import { reactive, onMounted } from "vue";
import {
  devicePage,
} from "@/applications/eccard-iot/api";
import areaSelectTree from "@/components/tree/areaSelectTree.vue";
import bind from "./components/bind.vue"
import details from "./components/details.vue"
export default {
  components: {
    "el-table": ElTable,
    "el-table-column": ElTableColumn,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    "el-input": ElInput,
    "el-pagination": ElPagination,
    "el-button": ElButton,
    "kade-area-select-tree": areaSelectTree,
    "kade-food-bind": bind,
    "kade-device-details": details
  },
  setup() {
    const state = reactive({
      isBind: false,
      isDetails: false,
      form: {
        pageSize: 10,
        pageNum: 1,
      },
      dataList: [],
      total: 0,
      rowData: {},
    });
    const getList = async () => {
      state.loading = true;
      for (let key in state.form) {
        if (!state.form[key]) {
          delete state.form[key];
        }
      }
      let { data } = await devicePage(state.form);
      state.loading = false;
      state.dataList = data.list;
      state.total = data.total;
    };

    const handleBindFood = (row) => {
      state.rowData = row
      state.isBind = true
    }
    const handleDetails = (row) => {
      state.rowData = row
      state.isDetails = true
    }
    const handleSearch = () => {
      getList();
    }
    const handleReset = () => {
      state.form = {
        pageSize: 10,
        pageNum: 1,
      }
    }
    const handlePageChange = val => {
      state.form.pageNum = val
      getList();
    }
    const handleSizeChange = val => {
      state.form.pageNum = 1
      state.form.pageSize = val
      getList();
    }
    onMounted(() => {
      getList();
    });
    return {
      state,
      handleBindFood,
      handleDetails,
      handleSearch,
      handleReset,
      handlePageChange,
      handleSizeChange,
    };
  },
};
</script>
<style lang="scss" scoped>
:deep(.el-dialog__body) {
  margin-top: 20px;
}

:deep(.el-dialog__footer) {
  text-align: center;
}
</style>
