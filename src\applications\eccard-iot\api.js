import request from "@/service";

//系统配置表信息
export function iotCfg(params) {
  return request.get(`/eccard-iot/sysCfg/detail`, { params });
}

//消费机设备信息表分页列表
export function devicePage(params) {
  return request.get("/eccard-iot/consumeDevice/listDevice", { params });
}

//导出消费机设备信息表分页列表
export function exportList(params) {
  return request.post("/eccard-iot/consumeDevice/exportList", params, {
    responseType: "blob"
  });
}

//获取终端型号
export function getModel(params) {
  /*   let data = new URLSearchParams()
    data.append("type", params.type) */
  return request.get("/eccard-iot/sysproductMode/list", { params });
}
//获取终端类型-模拟数据
export function getType(params) {
  return request.post("/eccard-iot/consumeDevice/getType", params, {
    headers: {
      "content-type": "application/x-www-form-urlencoded",
      Accept: "*/*"
    }
  });
}

//获取区域选择列表
export function getTgetAreaCheckListype(params) {
  return request.get("/eccard-basic-data/area/getAreaCheckList", { params });
}

//获取设备参数列表
export function getDeviceParamList(params) {
  return request.post("/eccard-iot/consume/device/param/list", params);
}

//获取设备参数详情
export function getDeviceParamDetails(params) {
  return request.get(
    `/eccard-iot/consume/device/param/detail/info/${params.type}/${params.paramId}`
  );
}

//获取商户列表
export function getMerchantList(params) {
  return request.post("/eccard-merchant/Basic/getMerchantList", { params });
}

//获取固件版本
export function getDeviceVersion(params) {
  let data = new URLSearchParams();
  data.append("deviceModelCode", params.deviceModelCode);
  return request.post("/eccard-iot/consumeDevice/getDeviceVersion", data, {
    headers: {
      "content-type": "application/x-www-form-urlencoded",
      Accept: "*/*"
    }
  });
}
//新增消费机设备
export function insert(params) {
  return request.post("/eccard-iot/consumeDevice/insert", params);
}

//批量新增消费机设备
export function addBatch(params) {
  return request.put("/eccard-iot/consumeDevice/addBatch", params);
}
//查询消费机设备更换记录表
export function getRepalceRecord(params) {
  return request.get("/eccard-iot/replaces", { params });
}
//新增消费机设备更换记录表
export function insertRecords(params) {
  return request.post("/eccard-iot/replaces", params);
}
//修改消费机设备更换记录表
export function updateRecords(params) {
  return request.put("/eccard-iot/replaces", params);
}
// 删除消费机设备更换记录表
export function delRecords(params) {
  return request.delete("/eccard-iot/replaces/" + params);
}

//修改消费机设备信息表
export function update(params) {
  return request.put("/eccard-iot/consumeDevice/update", params);
}

//删除消费机设备信息表
export function delDavice(params) {
  return request.delete("/eccard-iot/consumeDevice/" + params);
}

/* 消费机 */
// 获取消费设备参数
export function getIotDeviceParamPage(params) {
  return request.get("/eccard-iot/consume/device/param/page", { params });
}
// 添加消费设备参数
export function saveIotDeviceParam(params) {
  return request.post("/eccard-iot/consume/device/param/save", params);
}
// 删除消费设备参数
export function deleteIotDeviceParam(params) {
  return request.delete("/eccard-iot/consume/device/param/delete/" + params);
}
// 编辑消费设备参数
export function updateIotDeviceParam(params) {
  return request.put("/eccard-iot/consume/device/param/update", params);
}

//消费机设备信息表分页列表
export function bindDeviceList(params) {
  return request.get("/eccard-iot/consume/device/param/devicePage", { params });
}

// 参数绑定设备
export function ParamBindDevice(params) {
  return request.post("/eccard-iot/consume/device/param/deviceBind", params);
}

//获取卡片列表select
export function getCardTypeList(params) {
  return request.get("/eccard-card/common/card/type/list", { params });
}
// 获取消费设备参数详情
export function getdeviceParamDetail(params) {
  return request.get(`/eccard-iot/deviceParam/${params}/detail`);
}
// 保存基本消费设备参数详情表
export function saveBase(params) {
  return request.post("/eccard-iot/deviceParam/saveBase", params);
}

// 保存转账参数详情表
export function saveTransfer(params) {
  return request.post("/eccard-iot/deviceParam/saveTransfer", params);
}

// 保存时段设备参数详情表
export function saveTime(params) {
  return request.post("/eccard-iot/deviceParam/saveTime", params);
}

// 保存卡权限设备参数详情表
export function saveCard(params) {
  return request.post("/eccard-iot/deviceParam/saveCard", params);
}
saveIotDeviceParam;

// 保存设备打印参数详情表
export function savePrint(params) {
  return request.post("/eccard-iot/deviceParam/savePrint", params);
}

// 保存样式消费设备参数详情表
export function saveStyle(params) {
  return request.post("/eccard-iot/deviceParam/saveStyle", params);
}

// 查询绑定设备列表
export function bindInfo(params) {
  return request.get("/eccard-iot/consume/device/param/detail/bindInfoPage", {
    params
  });
}
// 取消参数绑定设备
export function unbindDevice(params) {
  return request.put(
    `/eccard-iot/consume/device/param/detail/bindInfoPage/cancel/${params}`
  );
}

/* 设备监控 */

//获取设备类型（select）
export function getDeviceType(params) {
  return request.get("/eccard-iot/sysproduct/list", { params });
}

//获取设备状态
export function getDeviceStatus(params) {
  return request.post("/eccard-iot/deviceMonitor", params);
}

/* 工作站类型 */

//工作站类型管理分页列表
export function workStationTypeList(params) {
  return request.get("/eccard-iot/workStationType/list", { params });
}
//新增工作站类型管理
export function workStationTypeAdd(params) {
  return request.post("/eccard-iot/workStationType/insert", params);
}
//修改工作站类型管理
export function workStationTypeEdit(params) {
  return request.put("/eccard-iot/workStationType/update", params);
}
// 删除工作站类型管理
export function workStationTypeDel(params) {
  return request.delete("/eccard-iot/workStationType/" + params);
}

/* 工作站 */
//工作站分页列表
export function getWorkStationList(params) {
  return request.get("/eccard-iot/workStation/list", { params });
}
//新增工作站
export function workStationAdd(params) {
  return request.post("/eccard-iot/workStation/insert", params);
}
//修改工作站
export function workStationEdit(params) {
  return request.put("/eccard-iot/workStation/update", params);
}
// 删除工作站
export function workStationDel(params) {
  return request.delete("/eccard-iot/workStation/" + params);
}

/* 设备厂家 */
//设备厂商管理分页列表
export function deviceFactoryList(params) {
  return request.get("/eccard-iot/BaseDeviceFactory/list", { params });
}
//新增设备厂商管理
export function deviceFactoryAdd(params) {
  return request.post("/eccard-iot/BaseDeviceFactory/insert", params);
}
//修改设备厂商管理
export function deviceFactoryEdit(params) {
  return request.put("/eccard-iot/BaseDeviceFactory/update", params);
}
// 删除设备厂商管理
export function deviceFactoryDel(params) {
  return request.delete("/eccard-iot/BaseDeviceFactory/" + params);
}
//设备厂商-导出
export function deviceFactoryExport(params) {
  return request.post("/eccard-iot/BaseDeviceFactory/exportList", params, {
    headers: {
      "Content-Type": "application/octet-stream",
      Accept: "*/*"
    },
    responseType: "blob"
  });
}

/* 门禁设备 */
//门禁设备信息表分页列表
export function accessControlDeviceList(params) {
  return request.get("/eccard-iot/accessControlDevice/list", { params });
}
//新增门禁设备
export function accessControlDeviceAdd(params) {
  return request.post("/eccard-iot/accessControlDevice/insert", params);
}
//修改门禁设备
export function accessControlDeviceEdit(params) {
  return request.put("/eccard-iot/accessControlDevice/update", params);
}
// 删除门禁通道设备
export function accessControlDeviceDel(params) {
  return request.delete("/eccard-iot/accessControlDevice/" + params);
}
//门禁设备信息表信息
export function accessControlDeviceDetails(params) {
  return request.get(`/eccard-iot/accessControlDevice/${params}/detail`);
}
//门禁通道设备-门信息表分页列表
export function accessControlDoorList(params) {
  return request.get("/eccard-iot/accessControlDoor/list", { params });
}
//批量修改门禁通道设备-门信息表
export function accessControlDoorEdit(params) {
  return request.put("/eccard-iot/accessControlDoor/batchUpdate", params);
}

// 门禁通道设备-读头分页列表
export function accessControlReadRelationList(params) {
  return request.get("/eccard-iot/accessControlReadRelation/readDeviceList", {
    params
  });
}
//门禁通道控制器-绑定读头-绑定
export function accessControlDeviceBind(params) {
  return request.post("/eccard-iot/accessControlDevice/deviceBind", params);
}
//门禁通道控制器-读头-取消绑定
export function accessControlDeviceCanelBind(params) {
  return request.post("/eccard-iot/accessControlDevice/canelBind", params);
}

//导出门禁设备信息
export function exportDevice(params) {
  return request.post("/eccard-iot/accessControlDevice/exportList", params, {
    headers: {
      "Content-Type": "application/octet-stream",
      Accept: "*/*"
    },
    responseType: "blob"
  });
}

/* 通道设备 */
//通道设备信息表分页列表
export function getAccessControlDevice(params) {
  return request.get("/eccard-iot/accessControlDevice/listChannel", { params });
}
//新增通道设备
export function addAccessControlDevice(params) {
  return request.post("/eccard-iot/accessControlDevice/insertChannel", params);
}
//修改通道设备
export function editAccessControlDevice(params) {
  return request.put("/eccard-iot/accessControlDevice/updateChannel", params);
}

//删除通道设备
export function delAccessControl(params) {
  return request.delete("/eccard-iot/accessControlDevice/channel/" + params);
}

//导出通道控制器信息
export function exportaccessControl(params) {
  return request.post(
    "/eccard-iot/accessControlDevice/exportListChannel",
    params,
    {
      headers: {
        "Content-Type": "application/octet-stream",
        Accept: "*/*"
      },
      responseType: "blob"
    }
  );
}

/* 人脸识别智能设备 */
//人脸识别智能设备信息分页列表
export function faceDeviceList(params) {
  return request.get("/eccard-iot/face/device/list", { params });
}
//新增人脸识别智能设备
export function faceDeviceAdd(params) {
  return request.post("/eccard-iot/face/device/insert", params);
}
//修改人脸识别智能设备
export function faceDeviceEdit(params) {
  return request.put("/eccard-iot/face/device/update", params);
}
// 删除人脸识别智能设备
export function faceDeviceDel(params) {
  return request.delete("/eccard-iot/face/device/" + params);
}
//批量新增人脸识别智能设备
export function faceDeviceBatchAdd(params) {
  return request.put("/eccard-iot/face/device/batchInsert", params);
}
//人脸识别智能设备-导出
export function faceDeviceExport(params) {
  return request.post("/eccard-iot/face/device/exportList", params, {
    headers: {
      "Content-Type": "application/octet-stream",
      Accept: "*/*"
    },
    responseType: "blob"
  });
}

/* 指纹门禁一体机 */
//指纹门禁一体机信息分页列表
export function fingerprintList(params) {
  return request.get("/eccard-iot/fingerprintControlDevice/list", { params });
}

//新增指纹门禁一体机信息
export function fingerprintAdd(params) {
  return request.post("/eccard-iot/fingerprintControlDevice/insert", params);
}

//修改指纹门禁一体机信息
export function fingerprintEdit(params) {
  return request.put("/eccard-iot/fingerprintControlDevice/update", params);
}

//删除指纹门禁一体机信息
export function fingerprintDel(params) {
  return request.delete("/eccard-iot/fingerprintControlDevice/" + params);
}

//导出指纹门禁一体机信息表
export function fingerprintExport(params) {
  return request.post(
    "/eccard-iot/fingerprintControlDevice/exportList",
    params,
    {
      headers: {
        "Content-Type": "application/octet-stream",
        Accept: "*/*"
      },
      responseType: "blob"
    }
  );
}

//批量新增指纹门禁一体机信息
export function fingerprintBatchAdd(params) {
  return request.put(
    "/eccard-iot/fingerprintControlDevice/batchInsert",
    params
  );
}

//指纹门禁一体机表信息
export function fingerprintDetails(params) {
  return request.get(
    `/eccard-iot/fingerprintControlDevice/detail${params}/detail`
  );
}

/* 智能访客 */
//智能访客设备信息分页列表
export function visitorList(params) {
  return request.get("/eccard-iot/visitorDevice/list", { params });
}

//新增智能访客设备信息
export function visitorDeviceAdd(params) {
  return request.post("/eccard-iot/visitorDevice/insert", params);
}

//修改智能访客设备信息
export function visitorDeviceEdit(params) {
  return request.put("/eccard-iot/visitorDevice/update", params);
}

//删除智能访客设备信息
export function visitorDeviceDel(params) {
  return request.delete("/eccard-iot/visitorDevice/" + params);
}

//导出智能访客设备信息表
export function visitorExport(params) {
  return request.post("/eccard-iot/visitorDevice/exportList", params, {
    headers: {
      "Content-Type": "application/octet-stream",
      Accept: "*/*"
    },
    responseType: "blob"
  });
}

//批量新增智能访客设备信息
export function visitorBatchAdd(params) {
  return request.put("/eccard-iot/visitorDevice/batchInsert", params);
}

//智能访客设备信息表信息
export function visitorDetails(params) {
  return request.get(`/eccard-iot/visitorDevice/detail${params}/detail`);
}

/* 摄像头设备 */
//摄像头设备信息表分页列表
export function cameraDeviceList(params) {
  return request.get("/eccard-iot/cameraDevice/list", { params });
}
//新增摄像头设备
export function cameraDeviceAdd(params) {
  return request.post("/eccard-iot/cameraDevice/insert", params);
}
//修改摄像头设备
export function cameraDeviceEdit(params) {
  return request.put("/eccard-iot/cameraDevice/update", params);
}
// 删除摄像头设备
export function cameraDeviceDel(params) {
  return request.delete("/eccard-iot/cameraDevice/" + params);
}
//摄像头设备-导出
export function cameraDeviceExport(params) {
  return request.post("/eccard-iot/cameraDevice/exportList", params, {
    headers: {
      "Content-Type": "application/octet-stream",
      Accept: "*/*"
    },
    responseType: "blob"
  });
}

/* 智能门锁 */
//智能门锁设备信息表分页列表
export function doorLockDeviceList(params) {
  return request.get("/eccard-iot/door/list", { params });
}
//新增智能门锁设备
export function doorLockDeviceAdd(params) {
  return request.post("/eccard-iot/door/insert", params);
}

//批量新增智能门锁设备
export function doorLockDeviceBatchAdd(params) {
  return request.post("/eccard-iot/door/insertBatch", params);
}

//修改智能门锁设备
export function doorLockDeviceEdit(params) {
  return request.put("/eccard-iot/door/update", params);
}
// 删除智能门锁设备
export function doorLockDeviceDel(params) {
  return request.delete("/eccard-iot/door/" + params);
}

/* 人脸识别考勤终端 */
//人脸识别考勤终端设备信息表分页列表
export function attendanceDeviceList(params) {
  return request.get("/eccard-iot/attendanceDevice/list", { params });
}
//新增人脸识别考勤终端设备
export function attendanceDeviceAdd(params) {
  return request.post("/eccard-iot/attendanceDevice/insert", params);
}

//批量新增人脸识别考勤终端设备
export function attendanceDeviceBatchAdd(params) {
  return request.post("/eccard-iot/attendanceDevice/batchInsert", params);
}

//修改人脸识别考勤终端设备
export function attendanceDeviceEdit(params) {
  return request.put("/eccard-iot/attendanceDevice/update", params);
}
// 删除人脸识别考勤终端设备
export function attendanceDeviceDel(params) {
  return request.delete(`/eccard-iot/attendanceDevice/${params}`);
}
//人脸识别考勤终端设备-导出
export function attendanceDeviceExport(params) {
  return request.post("/eccard-iot/attendanceDevice/exportList", params, {
    headers: {
      "Content-Type": "application/octet-stream",
      Accept: "*/*"
    },
    responseType: "blob"
  });
}

/* 人脸智能分析盒 */
//人脸智能分析盒设备信息表分页列表
export function analyseDeviceList(params) {
  return request.get("/eccard-iot/face/analyseDevice/list", { params });
}
//新增人脸智能分析盒
export function analyseDeviceAdd(params) {
  return request.post("/eccard-iot/face/analyseDevice/insert", params);
}

//修改人脸智能分析盒
export function analyseDeviceEdit(params) {
  return request.put("/eccard-iot/face/analyseDevice/update", params);
}
// 删除人脸智能分析盒
export function analyseDeviceDel(params) {
  return request.delete(`/eccard-iot/face/analyseDevice/${params}`);
}
//人脸智能分析盒-导出
export function analyseDeviceExport(params) {
  return request.post("/eccard-iot/face/analyseDevice/exportList", params, {
    headers: {
      "Content-Type": "application/octet-stream",
      Accept: "*/*"
    },
    responseType: "blob"
  });
}

/* 节水控制器 */
//节水控制器设备信息表分页列表
export function waterControllerDeviceList(params) {
  return request.get("/eccard-iot/watercontrollerdevice/list", { params });
}
//新增节水控制器设备
export function waterControllerDeviceAdd(params) {
  return request.post("/eccard-iot/watercontrollerdevice/insert", params);
}

//批量新增节水控制器设备
export function waterControllerDeviceBatchAdd(params) {
  return request.put("/eccard-iot/watercontrollerdevice/batchInsert", params);
}

//修改节水控制器设备
export function waterControllerDeviceEdit(params) {
  return request.put("/eccard-iot/watercontrollerdevice/update", params);
}
// 删除节水控制器设备
export function waterControllerDeviceDel(params) {
  return request.delete(`/eccard-iot/watercontrollerdevice/${params}`);
}
//节水控制器设备未绑定参数分页列表
export function watercontrollerdeviceNoBind(params) {
  return request.get(
    "/eccard-iot/watercontrollerdeviceparams/noBindDevicePage",
    { params }
  );
}

//节水控制器设备-导出
export function waterControllerDeviceExport(params) {
  return request.post("/eccard-iot/watercontrollerdevice/exportList", params, {
    headers: {
      "Content-Type": "application/octet-stream",
      Accept: "*/*"
    },
    responseType: "blob"
  });
}

/* 远传水表 */
//远传水表设备信息表分页列表
export function remoteWaterDeviceList(params) {
  return request.get("/eccard-iot/watermeterDevice/list", { params });
}
//新增远传水表设备
export function remoteWaterDeviceAdd(params) {
  return request.post("/eccard-iot/watermeterDevice/insert", params);
}

//批量新增远传水表设备
export function remoteWaterDeviceBatchAdd(params) {
  return request.put("/eccard-iot/watermeterDevice/batchInsert", params);
}

//修改远传水表设备
export function remoteWaterDeviceEdit(params) {
  return request.put("/eccard-iot/watermeterDevice/update", params);
}
// 删除远传水表设备
export function remoteWaterDeviceDel(params) {
  return request.delete(`/eccard-iot/watermeterDevice/${params}`);
}
//远传水表设备未绑定参数分页列表
export function remoteWaterDeviceNoBind(params) {
  return request.get("/eccard-iot/watermeterDeviceParams/noBindDevicePage", {
    params
  });
}

//远传水表设备-导出
export function remoteWaterDeviceExport(params) {
  return request.post("/eccard-iot/watermeterDevice/exportList", params, {
    headers: {
      "Content-Type": "application/octet-stream",
      Accept: "*/*"
    },
    responseType: "blob"
  });
}

//远传水表设备类型管理分页列表
export function waterTypeList(params) {
  return request.get("/eccard-iot/watermeterDeviceTypes/page", { params });
}
//远传水表设备类型管理不分页列表
export function waterTypeListNoPage(params) {
  return request.get("/eccard-iot/watermeterDeviceTypes/list", { params });
}
//新增远传水表设备类型
export function waterTypeAdd(params) {
  return request.post("/eccard-iot/watermeterDeviceTypes/insert", params);
}
//修改远传水表设备类型
export function waterTypeEdit(params) {
  return request.put("/eccard-iot/watermeterDeviceTypes/update", params);
}
// 删除远传水表设备类型
export function waterTypeDel(params) {
  return request.delete(`/eccard-iot/watermeterDeviceTypes/${params}`);
}
// 批量删除远传水表设备类型
export function waterTypeBatchDel(params) {
  return request.post(`/eccard-iot/watermeterDeviceTypes/deleteBatch`, params);
}

/* 电控终端 */
//电控终端设备信息表分页列表
export function electricControlDeviceList(params) {
  return request.get("/eccard-iot/electricControlTerminal/list", { params });
}
//新增电控终端设备
export function electricControlDeviceAdd(params) {
  return request.post("/eccard-iot/electricControlTerminal/insert", params);
}

//批量新增电控终端设备
export function electricControlDeviceBatchAdd(params) {
  return request.post(
    "/eccard-iot/electricControlTerminal/insertBatch",
    params
  );
}

//修改电控终端设备
export function electricControlDeviceEdit(params) {
  return request.put("/eccard-iot/electricControlTerminal/update", params);
}
// 删除电控终端设备
export function electricControlDeviceDel(params) {
  return request.delete("/eccard-iot/electricControlTerminal/" + params);
}
//电控终端设备-导出
export function electricControlDeviceExport(params) {
  return request.post(
    "/eccard-iot/electricControlTerminal/exportList",
    params,
    {
      headers: {
        "Content-Type": "application/octet-stream",
        Accept: "*/*"
      },
      responseType: "blob"
    }
  );
}

/* 设备-更换记录 */
//更换记录分页列表
export function deviceReplaceList(params) {
  return request.get("/eccard-iot/face/device/replace/list", { params });
}
//新增更换记录
export function deviceReplaceAdd(params) {
  return request.post("/eccard-iot/face/device/replace/insert", params);
}
//修改更换记录
export function deviceReplaceEdit(params) {
  return request.put("/eccard-iot/face/device/replace/update", params);
}
// 删除更换记录
export function deviceReplaceDel(params) {
  return request.delete("/eccard-iot/face/device/replace/" + params);
}

/* 人脸识别参数 */
//人脸识别参数分页列表
export function faceParamList(params) {
  return request.get("/eccard-iot/face/device/param/list", { params });
}
//新增人脸识别参数
export function faceParamAdd(params) {
  return request.post("/eccard-iot/face/device/param/insert", params);
}
//修改人脸识别参数
export function faceParamEdit(params) {
  return request.put("/eccard-iot/face/device/param/update", params);
}
// 删除人脸识别参数
export function faceParamDel(params) {
  return request.delete("/eccard-iot/face/device/param/" + params);
}
//人脸识别设备参数-绑定设备
export function bindFaceDevice(params) {
  return request.post(`/eccard-iot/face/device/param/deviceBind`, params);
}

//人脸识别设备参数-详情-绑定设备列表
export function bindFaceDeviceList(params) {
  return request.get(`/eccard-iot/face/device/param/devicePage`, { params });
}
//人脸识别设备参数-详情-绑定设备列表-取消绑定
export function faceBindCancel(params) {
  return request.put(
    `/eccard-iot/face/device/param/detail/bindInfoPage/cancel/${params}`
  );
}

//人脸识别设备参数-参数信息表信息
export function faceParamDetails(params) {
  return request.get(`/eccard-iot/face/device/param/detail/${params}/detail`);
}

//人脸识别设备-参数详情表-获取参数字典值方法
export function deviceGetDict(params) {
  return request.get(`/eccard-iot/face/device/param/detail/getParam`, {
    params
  });
}

//新增人脸识别设备-参数详情
export function faceParamDetailAdd(params) {
  return request.post("/eccard-iot/face/device/param/detail/insert", params);
}
//修改人脸识别设备-参数详情
export function faceParamDetailEdit(params) {
  return request.put("/eccard-iot/face/device/param/detail/udpate", params);
}

/* 节水控制器参数 */
//节水控制器参数分页列表
export function saveWaterParamList(params) {
  return request.get("/eccard-iot/watercontrollerdeviceparams/page", {
    params
  });
}
//节水控制器参数不分页列表
export function saveWaterParamListNoPage(params) {
  return request.get("/eccard-iot/watercontrollerdeviceparams/lsit", {
    params
  });
}
//新增节水控制器参数
export function saveWaterParamAdd(params) {
  return request.post("/eccard-iot/watercontrollerdeviceparams/insert", params);
}
//修改节水控制器参数
export function saveWaterParamEdit(params) {
  return request.put("/eccard-iot/watercontrollerdeviceparams/update", params);
}
// 删除节水控制器参数
export function saveWaterParamDel(params) {
  return request.delete(`/eccard-iot/watercontrollerdeviceparams/${params}`);
}
//节水控制器设备参数-绑定设备
export function bindSaveWaterDevice(params) {
  return request.post(
    `/eccard-iot/watercontrollerdeviceparams/bindDevice`,
    params
  );
}

//节水控制器设备参数-详情-绑定设备列表
export function bindSaveWaterDeviceList(params) {
  return request.get(`/eccard-iot/watercontrollerdeviceparams/bindDevicePage`, {
    params
  });
}
//节水控制器设备参数-详情-绑定设备列表-取消绑定
export function saveWaterBindCancel(params) {
  return request.put(
    `/eccard-iot/watercontrollerdeviceparams/canelBindDevice/${params}`
  );
}

//新增节水控制器设备-参数详情
export function saveWaterParamDetailAdd(params) {
  return request.post(
    "/eccard-iot/watercontrollerdeviceparamsdetails/insert",
    params
  );
}
//修改节水控制器设备-参数详情
/* export function waterParamDetailEdit(params) {
  return request.put('/eccard-iot/watercontrollerdeviceparamsdetails/udpate', params);
} */
//节水控制器设备参数-参数信息表信息
export function saveWaterParamDetails(params) {
  return request.get(
    `/eccard-iot/watercontrollerdeviceparamsdetails/${params}/detail`
  );
}
//水控参数-获取参数字典值方法
export function saveWateretDict(params) {
  return request.get(
    `/eccard-iot/watercontrollerdeviceparamsdetails/getParam`,
    { params }
  );
}

/* 远传水表参数 */
//远传水表参数分页列表
export function watermeterParamList(params) {
  return request.get("/eccard-iot/watermeterDeviceParams/page", { params });
}
//远传水表参数不分页列表
export function watermeterParamListNoPage(params) {
  return request.get("/eccard-iot/watermeterDeviceParams/list", { params });
}
//新增远传水表参数
export function watermeterParamAdd(params) {
  return request.post("/eccard-iot/watermeterDeviceParams/insert", params);
}
//修改远传水表参数
export function watermeterParamEdit(params) {
  return request.put("/eccard-iot/watermeterDeviceParams/update", params);
}
// 删除远传水表参数
export function watermeterParamDel(params) {
  return request.delete(`/eccard-iot/watermeterDeviceParams/${params}`);
}
//远传水表设备参数-绑定设备
export function bindWatermeterDevice(params) {
  return request.post(`/eccard-iot/watermeterDeviceParams/bindDevice`, params);
}

//远传水表设备参数-详情-绑定设备列表
export function bindWatermeterDeviceList(params) {
  return request.get(`/eccard-iot/watermeterDeviceParams/bindDevicePage`, {
    params
  });
}
//远传水表设备参数-详情-绑定设备列表-取消绑定
export function watermeterBindCancel(params) {
  return request.put(
    `/eccard-iot/watermeterDeviceParams/canelBindDevice/${params}`
  );
}
//新增远传水表设备-参数详情
export function watermeterParamDetailAdd(params) {
  return request.post(
    "/eccard-iot/watermeterDeviceParamsDetails/insert",
    params
  );
}
//远传水表设备参数-参数信息表信息
export function watermeterParamDetails(params) {
  return request.get(
    `/eccard-iot/watermeterDeviceParamsDetails/${params}/detail`
  );
}
//远传水表参数-获取参数字典值方法
export function watermeterDict(params) {
  return request.get(`/eccard-iot/watermeterDeviceParamsDetails/getParam`, {
    params
  });
}

/* 网关管理 */
//金融支付设备网关——分页列表
export function financeGatewayList(params) {
  return request.get("/eccard-iot/device/gateway/listFinancial", { params });
}
//金融支付设备网关——不分页列表
export function financeGatewayListNoPage(params) {
  return request.get("/eccard-iot/device/gateway/listFinancialNoPage", {
    params
  });
}
//新增金融支付网关
export function financeGatewayAdd(params) {
  return request.post("/eccard-iot/device/gateway/insertFinancial", params);
}
//金融支付网关-导出
export function financeGatewayExport(params) {
  return request.post(
    "/eccard-iot/device/gateway/exportListFinancial",
    params,
    {
      headers: {
        "Content-Type": "application/octet-stream",
        Accept: "*/*"
      },
      responseType: "blob"
    }
  );
}

//身份识别设备网关——分页列表
export function identityGatewayList(params) {
  return request.get("/eccard-iot/device/gateway/listIdentification", {
    params
  });
}
//身份识别设备网关——不分页列表
export function identityGatewayListNoPage(params) {
  return request.get("/eccard-iot/device/gateway/listIdentificationNoPage", {
    params
  });
}
//新增身份识别设备网关
export function identityGatewayAdd(params) {
  return request.post(
    "/eccard-iot/device/gateway/insertIdentification",
    params
  );
}
//身份识别设备网关-导出
export function identityGatewayExport(params) {
  return request.post(
    "/eccard-iot/device/gateway/exportListIdentification",
    params,
    {
      headers: {
        "Content-Type": "application/octet-stream",
        Accept: "*/*"
      },
      responseType: "blob"
    }
  );
}

//节能水电设备网关——分页列表
export function hydropowerGatewayList(params) {
  return request.get("/eccard-iot/device/gateway/listWaterAndelectricity", {
    params
  });
}
//节能水电设备网关——不分页列表
export function hydropowerGatewayListNoPage(params) {
  return request.get(
    "/eccard-iot/device/gateway/listWaterAndelectricityNoPage",
    { params }
  );
}
//新增节能水电网关
export function hydropowerGatewayAdd(params) {
  return request.post(
    "/eccard-iot/device/gateway/insertWaterAndelectricity",
    params
  );
}
//节能水电设备-导出
export function hydropowerGatewayExport(params) {
  return request.post(
    "/eccard-iot/device/gateway/exportListWaterAndelectricity",
    params,
    {
      headers: {
        "Content-Type": "application/octet-stream",
        Accept: "*/*"
      },
      responseType: "blob"
    }
  );
}

//修改网关
export function gatewayEdit(params) {
  return request.put("/eccard-iot/device/gateway/update", params);
}
// 删除网关
export function gatewayDel(params) {
  return request.delete(`/eccard-iot/device/gateway/${params}`);
}

//设备未绑定网关列表-分页
export function noBindGatewayDeviceList(params) {
  return request.post("/eccard-iot/gateway/device/relation/noBindPage", params);
}
//设备绑定网关列表-分页
export function bindGatewayDeviceList(params) {
  return request.post("/eccard-iot/gateway/device/relation/bindPage", params);
}
//设备绑定网关
export function bindGateway(params) {
  return request.post(
    "/eccard-iot/gateway/device/relation/bindRelation",
    params
  );
}
//设备取消绑定网关
export function noBindGateway(params) {
  return request.post(
    "/eccard-iot/gateway/device/relation/canelBindRelation",
    params
  );
}

/* 数据分发 */
//设备数据分发分页列表
export function dataDistributeList(params) {
  return request.get("/eccard-iot/device/dataDistribute/list", { params });
}
//新增设备数据分发
export function dataDistributeAdd(params) {
  return request.post("/eccard-iot/device/dataDistribute/insert", params);
}
//修改设备数据分发
export function dataDistributeEdit(params) {
  return request.put("/eccard-iot/device/dataDistribute/udpate", params);
}
// 删除设备数据分发
export function dataDistributeDel(params) {
  return request.delete("/eccard-iot/device/dataDistribute/" + params);
}

//系统应用的列表-不分页
export function appList(params) {
  return request.get("/eccard-iot/distributesystemapp/listNoPage", { params });
}
//设备数据分发——已授权应用分页列表
export function appAuthList(params) {
  return request.get("/eccard-iot/dataDistribute/system/relation/list", {
    params
  });
}
//新增设备数据分发系统应用
export function dataDistributeAppAdd(params) {
  return request.post(
    "/eccard-iot/dataDistribute/system/relation/insertBatch",
    params
  );
}

//设备数据分发——未绑定设备分页列表
export function dataDistributeNoDeviceList(params) {
  return request.post(
    "/eccard-iot/dataDistribute/device/relation/noBingPage",
    params
  );
}
//设备数据分发——绑定设备分页列表
export function dataDistributeDeviceList(params) {
  return request.get("/eccard-iot/dataDistribute/device/relation/list", {
    params
  });
}

//设备数据分发——绑定设备
export function dataDistributeBindDevice(params) {
  return request.post(
    "eccard-iot/dataDistribute/device/relation/insertRelation",
    params
  );
}
// 设备数据分发——取消绑定设备
export function dataDistributeBindCancel(params) {
  return request.delete("/eccard-iot/dataDistribute/device/relation/" + params);
}

/* 无线联网水控 */
//无线联网水控设备信息表分页列表
export function infiniteWaterDeviceList(params) {
  return request.post("/eccard-iot/wirelessWaterController/page", params);
}
//新增无线联网水控设备
export function infiniteWaterDeviceAdd(params) {
  return request.post("/eccard-iot/wirelessWaterController/insert", params);
}

//批量新增无线联网水控设备
export function infiniteWaterDeviceBatchAdd(params) {
  return request.put("/eccard-iot/wirelessWaterController/batchInsert", params);
}

//修改无线联网水控设备
export function infiniteWaterDeviceEdit(params) {
  return request.put("/eccard-iot/wirelessWaterController/update", params);
}
// 删除无线联网水控设备
export function infiniteWaterDeviceDel(params) {
  return request.delete(`/eccard-iot/wirelessWaterController/${params}`);
}
//无线联网水控设备未绑定参数分页列表
export function infiniteWaterDeviceNoBind(params) {
  return request.get(
    "/eccard-iot/WirelessWaterControllerDeviceParams/noBindDevicePage",
    { params }
  );
}
//无线联网水控导入设备模板
export function downIotWirelessWaterControllerDeviceTemplate(params) {
  return request.get(
    "/eccard-iot/wirelessWaterController/downIotWirelessWaterControllerDeviceTemplate",
    {
      params,
      headers: {
        "Content-Type": "application/octet-stream",
        Accept: "*/*"
      },
      responseType: "blob"
    }
  );
}
//无线联网水控设备-导出
export function infiniteWaterDeviceExport(params) {
  return request.post("/eccard-iot/wirelessWaterController/export", params, {
    headers: {
      "Content-Type": "application/octet-stream",
      Accept: "*/*"
    },
    responseType: "blob"
  });
}

/* 无线联网水控参数 */
//无线联网水控参数分页列表
export function infiniteWaterParamList(params) {
  return request.get("/eccard-iot/WirelessWaterControllerDeviceParams/page", {
    params
  });
}
//无线联网水控参数不分页列表
export function infiniteWaterParamListNoPage(params) {
  return request.get("/eccard-iot/WirelessWaterControllerDeviceParams/list", {
    params
  });
}
//新增无线联网水控参数
export function infiniteWaterParamAdd(params) {
  return request.post(
    "/eccard-iot/WirelessWaterControllerDeviceParams/insert",
    params
  );
}
//修改无线联网水控参数
export function infiniteWaterParamEdit(params) {
  return request.put(
    "/eccard-iot/WirelessWaterControllerDeviceParams/update",
    params
  );
}
// 删除无线联网水控参数
export function infiniteWaterParamDel(params) {
  return request.delete(
    `/eccard-iot/WirelessWaterControllerDeviceParams/${params}`
  );
}
//无线联网水控设备参数-绑定设备
export function bindInfiniteWaterDevice(params) {
  return request.post(
    `/eccard-iot/WirelessWaterControllerDeviceParams/bindDevice`,
    params
  );
}

//无线联网水控设备参数-详情-绑定设备列表
export function bindInfiniteWaterDeviceList(params) {
  return request.get(
    `/eccard-iot/WirelessWaterControllerDeviceParams/bindDevicePage`,
    { params }
  );
}
//无线联网水控设备参数-详情-绑定设备列表-取消绑定
export function infiniteWaterBindCancel(params) {
  return request.put(
    `/eccard-iot/WirelessWaterControllerDeviceParams/canelBindDevice/${params}`
  );
}

//新增无线联网水控设备-参数详情
export function infiniteWaterParamDetailAdd(params) {
  return request.post(
    "/eccard-iot/wirelessWaterControllerDeviceParamsDetails/insert",
    params
  );
}
//无线联网水控设备参数-参数信息表信息
export function infiniteWaterParamDetails(params) {
  return request.get(
    `/eccard-iot/wirelessWaterControllerDeviceParamsDetails/${params}/detail`
  );
}
//无线联网水控参数-获取参数字典值方法
export function infiniteWaterDict(params) {
  return request.get(
    `/eccard-iot/wirelessWaterControllerDeviceParamsDetails/getParam`,
    { params }
  );
}

//设备管理-设备报修管理-分页
export function getDeviceRepairManageList(params) {
  return request.post("/eccard-iot/deviceRepairManagement/page", params);
}
//设备管理-设备报修管理-编辑
export function DeviceRepairManageEdit(params) {
  return request.put("/eccard-iot/deviceRepairManagement", params);
}
//设备管理-设备报修管理-导出
export function deviceRepairExport(params) {
  return request.post("/eccard-iot/deviceRepairManagement/export", params);
}
//基础信息管理-设备故障管理-分页
export function getdeviceFaultManageList(params) {
  return request.post("/eccard-iot/equipmentFailureType/page", params);
}
//基础信息管理-设备故障管理-新增
export function deviceFaultAdd(params) {
  return request.post("/eccard-iot/equipmentFailureType/add", params);
}
//基础信息管理-设备故障管理-编辑
export function deviceFaultUpdate(params) {
  return request.put("/eccard-iot/equipmentFailureType", params);
}

//水控设备监控列表
export function wirelessWaterControllerMonitor(params) {
  return request.post("/eccard-iot/wirelessWaterController/monitor", params);
}
//水控设备监控汇总
export function wirelessWaterControllerMonitorCount(params) {
  return request.post(
    "/eccard-iot/wirelessWaterController/monitorCount",
    params
  );
}
