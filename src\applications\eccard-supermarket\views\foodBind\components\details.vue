<template>
  <el-dialog :model-value="modelValue" title="设备详情" width="970px" :before-close="handleClose">
    <el-card>
      <template #header>设备信息</template>
      <el-form size="mini" label-width="90px" inline>
        <el-form-item label="设备名称：">
          <el-input :model-value="rowData.deviceName" readonly></el-input>
        </el-form-item>
        <el-form-item label="设备机号：">
          <el-input :model-value="rowData.deviceNo" readonly></el-input>
        </el-form-item>
        <el-form-item label="设备型号：">
          <el-input :model-value="rowData.deviceModel" readonly></el-input>
        </el-form-item>
        <el-form-item label="设备类型：">
          <el-input :model-value="rowData.deviceType" readonly></el-input>
        </el-form-item>
        <el-form-item label="所属区域：">
          <el-input :model-value="rowData.areaName" readonly></el-input>
        </el-form-item>
        <el-form-item label="设备ip：">
          <el-input :model-value="rowData.deviceIp" readonly></el-input>
        </el-form-item>
        <el-form-item label="设备状态：">
          <el-input :model-value="dictionaryFilter(rowData.deviceStatus)" readonly></el-input>
        </el-form-item>
      </el-form>
    </el-card>
    <el-card>
      <template #header>绑定菜品列表</template>
      <el-table :data="state.dataList" height="30vh" v-loading="false" border stripe>
        <el-table-column label="菜品编号" prop="dishCode" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column label="菜品名称" prop="dishName" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column label="单位" prop="dishUnit" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column label="价格" prop="dishPrice" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column label="菜品类型" width="100" prop="dishPrice" align="center" show-overflow-tooltip>
          <template #default="scope">
            {{dictionaryFilter(scope.row.dishType)}}
          </template>
        </el-table-column>
        <!--         <el-table-column label="标签" prop="labelEntityList" align="center" show-overflow-tooltip>
          <template #default="scope">
            {{scope.row.labelEntityList.map(item=>item.labelName).join("、")}}
          </template>
        </el-table-column> -->
        <el-table-column label="菜品图片" prop="dishImageUrl" align="center" show-overflow-tooltip>
          <template #default="scope">
            <el-image style="width: 50px; height: 50px" :src="scope.row.dishImageUrl"
              :preview-src-list="[scope.row.dishImageUrl]" fit="cover" />
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <div style="height:1px"></div>
  </el-dialog>
</template>

<script>
import { ElDialog, ElCard, ElForm, ElFormItem, ElInput, ElTable, ElTableColumn, ElImage } from "element-plus"
import {  reactive, ref, watch } from 'vue'
import { bindDishList } from "@/applications/eccard-supermarket/api.js";

export default {
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    rowData: {
      type: Object,
      default: null
    },
  },
  components: {
    ElDialog, ElCard, ElForm, ElFormItem, ElInput, ElTable, ElTableColumn, ElImage
  },
  setup(props, context) {
    const formRef = ref(null)
    const state = reactive({
      dataList: [],
    });
    watch(() => props.modelValue, async val => {
      if (val) {
        let { data } = await bindDishList({ deviceId: props.rowData.id })
        state.dataList = data.sort(function (x, y) {
          return x.dishCode > y.dishCode ? 1 : -1;
        })
      }
    })
    
    const handleClose = () => {
      context.emit("update:modelValue", false)
    };
    return {
      formRef,
      state,
      handleClose,
    }
  }
}
</script>

<style lang="scss" scoped>
.el-card {
  margin-bottom: 20px;
}
</style>