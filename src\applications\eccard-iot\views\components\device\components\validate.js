export const rules = {
  deviceFactory: [
    {
      required: true,
      message: "请选择设备厂家",
      trigger: "change",
    },
  ],
  deviceModel: [
    {
      required: true,
      message: "请选择终端型号",
      trigger: "change",
    },
  ],
  areaId: [
    {
      required: true,
      message: "请选择所属区域",
      trigger: "change",
    },
  ],
  deviceNo: [
    {
      required: true,
      message: "请输入设备机号",
    },
    {
      pattern: /^[0-9]*$/,
      message: "请输入数字",
    },
  ],
  deviceName: [
    {
      required: true,
      message: "请输入设备名称",
    },
    {
      max: 20,
      message: "设备名称长度不能超过20字符",
    },
  ],
  deviceSn: [
    {
      required: true,
      message: "请输入设备SN号",
    },
    {
      pattern: /^[0-9]*$/,
      message: "请输入数字",
    }
  ],
  deviceStatus: [
    {
      required: true,
      message: "请选择设备使用状态",
      trigger: "change",
    },
  ],
  electricType: [
    {
      required: true,
      message: "请选择电表类型",
      trigger: "change",
    },
  ],
  electricNum: [
    {
      required: true,
      message: "请输入电表集中器号",
    },
    {
      pattern: /^[0-9]*$/,
      message: "请输入数字",
    }
  ],
  channelNum: [
    {
      required: true,
      message: "请输入通道号",
    },
    {
      pattern: /^[0-9]*$/,
      message: "请输入数字",
    }
  ],
  deviceCardCount: [
    {
      required: true,
      message: "请输入临时卡数量",
    },
    {
      pattern: /^[0-9]*$/,
      message: "请输入数字",
    },
    {
      max: 20,
      message: "临时卡数量长度不能超过20字符",
    },
  ],
  verifyCode: [
    {
      required: true,
      message: "请输入设备验证码",
    },
    {
      pattern: /^[0-9]*$/,
      message: "请输入数字",
    },
    {
      max: 10,
      message: "设备验证码长度不能超过10字符",
    },
  ],
  devicePassword: [
    {
      required: true,
      message: "请输入通讯密码",
    },
    {
      pattern: /^[0-9]*$/,
      message: "请输入数字",
    },
    {
      max: 20,
      message: "通讯密码长度不能超过20字符",
    },
  ],
  devicePort: [
    {
      required: true,
      message: "请输入端口号",
    },
    {
      pattern: /^[0-9]*$/,
      message: "请输入数字",
    },
/*     {
      max: 10,
      message: "端口号长度不能超过10字符",
    }, */
  ],
  deviceAccount: [
    {
      required: true,
      message: "请输入通讯账号",
    },
    {
      pattern: /^[0-9]*$/,
      message: "请输入数字",
    },
    {
      max: 20,
      message: "通讯账号长度不能超过20字符",
    },
  ],
  deviceDirection: [
    {
      required: true,
      message: "请选择方向",
      trigger: "change",
    },
  ],
}