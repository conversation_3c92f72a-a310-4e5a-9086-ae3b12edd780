<template>
  <div class="padding-form-box setBasicParam" v-loading="state.loading">
    <el-form size="small" label-width="200px">
      <el-form-item label="识别回调Url:">
        <el-input v-model="state.form.paramContent.identifyCallbackUrl" placeholder="请输入">
          <template #prepend>Http://</template>
        </el-input>
      </el-form-item>
      <el-form-item label="设备心跳Url:">
        <el-input v-model="state.form.paramContent.deviceHeartbeatUrl" placeholder="请输入">
          <template #prepend>Http://</template>
        </el-input>
      </el-form-item>
      <el-form-item label="拍照注册Url :">
        <el-input v-model="state.form.paramContent.photoRegisteredUrl" placeholder="请输入">
          <template #prepend>Http://</template>
        </el-input>
      </el-form-item>
      <el-form-item label="测温版识别回调Url:">
        <el-input v-model="state.form.paramContent.temperatureIdentifyCallbackUrl" placeholder="请输入">
          <template #prepend>Http://</template>
        </el-input>
      </el-form-item>
    </el-form>
  </div>
  <div style="text-align: center">
    <el-button size="mini" @click="beforeClose()">取消</el-button>
    <el-button size="mini" type="primary" @click="saveClick()">保存</el-button>
  </div>
</template>

<script>
import { ElButton, ElForm, ElFormItem, ElInput, ElMessage } from "element-plus";
import { onMounted, reactive } from "vue";
import { useStore } from "vuex";

const defaultParamsFnc = () => {
  return {
    deviceHeartbeatUrl: "",	//设备心跳url	string	
    identifyCallbackUrl: "",	//识别回调Url	string	
    photoRegisteredUrl: "",	//拍照注册url	string	
    temperatureIdentifyCallbackUrl: "",	//测温版识别回调url	string
  };
};

export default {
  components: {
    ElForm,
    ElFormItem,
    ElInput,
    ElButton,
  },
  setup() {
    const store = useStore();
    const state = reactive({
      loading: false,
      form: {
        paramContent: defaultParamsFnc(),
        id: "",
        paramId: "",
        paramType: "callback",
      },
    });

    const saveClick = async () => {
      state.loading = true;
      let storeData = store.state.deviceParameters[store.state.app.activeTab]
      state.form.paramId = storeData.selectRow.id;
      let fn = state.form.id ? storeData.TabModule.detailsEditFnc : storeData.TabModule.detailsAddFnc
      let params = { ...state.form }
      params.paramContent = JSON.stringify(params.paramContent)
      let { message, code } = await fn(params);
      if (code === 0) {
        setTimeout(() => {
          ElMessage.success(message);
          store.dispatch("deviceParameters/getParams", store.state.deviceParameters[store.state.app.activeTab].selectRow.id);
          state.loading = false;
        }, 1500);
      }
    };

    const beforeClose = () => {
      store.commit("deviceParameters/updateState", {
        key: store.state.app.activeTab,
        childKey: "isSetParams",
        payload: false,
      });
    };
    onMounted(() => {
      let val = store.state.deviceParameters[store.state.app.activeTab].detailsParams
      if (val) {
        if (val.callback && val.callback.id) {
          state.form = { ...val.callback }
          state.form.paramContent = JSON.parse(state.form.paramContent)
        } else {
          state.form.id = "";
          state.form.paramContent = defaultParamsFnc();
        }
      } else {
        //设置默认选项
        state.form.id = "";
        state.form.paramContent = defaultParamsFnc();
      }
    });
    return {
      state,
      saveClick,
      beforeClose,
    };
  },
  name: "basicParam",
};
</script>

<style lang="scss" scoped>
.setBasicParam {
  border: 1px solid #dcdfe6;
  border-radius: 5px;
  margin-bottom: 10px;
  .params {
    margin-bottom: 10px;

    .title {
      margin-left: 100px;
      margin-bottom: 10px;
    }
  }
}
/* :deep(.el-form-item__content) {
  width: 215px;
  .el-select {
    width: 100% !important;
  }
} */
</style>
