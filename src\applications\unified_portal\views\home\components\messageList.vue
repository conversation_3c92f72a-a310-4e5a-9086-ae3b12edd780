<template>
  <div class="kade-message-list" v-loading="state.loading">
    <template v-if="state.dataList.length">
      <div :class="['kade-message-item', { unread: item.status !== 'TS_DONE' }]" v-for="item in state.dataList" :key="item.id">
        <div class="kmi-left">
          <div class="title">{{ item.taskContent }}</div>
          <div class="desc">{{ item.status }}}</div>
        </div>
        <div class="kmi-right">
          {{ item.taskEndDay }}
        </div>
      </div>
    </template>
    <el-empty description="暂时没有收到消息" v-else />
    <message-info title="消息详情" v-model="showInfoModal" :id="id" />
  </div>
</template>
<script>
import { ElEmpty } from 'element-plus';
import { ref } from 'vue';
import { getMessageList } from '@/applications/unified_portal/api';
import { usePagination } from '@/hooks/usePagination';
import MessageInfo from './messageInfo';
export default {
  components: {
    'el-empty': ElEmpty,
    'message-info': MessageInfo,
  },
  setup() {
    const { options } = usePagination(getMessageList, {}, { pageSize: 6 });
    const id = ref(null);
    const showInfoModal = ref(false);
    const handleInfo = (msgId) => {
      id.value = msgId;
      showInfoModal.value = true;
    }        
    return {
      state: options,
      handleInfo,
      id,
      showInfoModal,
    }
  }
}
</script>
<style lang="scss" scoped>
.kade-message-list{
  .kade-message-item{
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 20px 10px 35px;
    box-sizing: border-box;
    position: relative;
    cursor: pointer;
    &::before{
      content: '';
      display: block;
      width: 5px;
      height: 5px;
      border-radius: 50%;
      background-color: #777;
      position: absolute;
      left: 20px;
      top: 18px;
    }
    &.unread{
      &::before{
        background-color: $danger-color;
      }
    }
    .kmi-left{
      .title{
        color: $font-color;
      }
      .desc{
        color: $font-sub-color;
        font-size: $font1;
      }
      flex: 1;
    }
    .kmi-right{
      flex-basis: 100px;
      text-align: right;
      color: $font-sub-color;
    }
  }
  .kade-message-item + .kade-message-item{
    border-top: 1px solid $border-color;
  }
}
</style>