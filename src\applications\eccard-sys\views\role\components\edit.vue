<template>
  <kade-modal
    v-bind="attrs"
    :modelValue="modelValue"
    @update:modelValue="update"
  >
    <el-form
      ref="formRef"
      :label-width="labelWidth"
      :rules="rules"
      :model="state.model"
      size="small"
    >
      <el-form-item label="角色名称" prop="roleName">
        <el-input placeholder="请输入" v-model="state.model.roleName" />
      </el-form-item>
      <el-form-item label="角色编码" prop="roleCode">
        <el-input placeholder="请输入" v-model="state.model.roleCode" />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="state.model.status">
          <el-radio
            v-for="item in statusList"
            :key="item.value"
            :label="item.value"
            >{{ item.label }}</el-radio
          >
        </el-radio-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button icon="el-icon-circle-close" size="small" @click="cancel"
        >取消</el-button
      >
      <el-button
        icon="el-icon-circle-check"
        size="small"
        :loading="loading"
        @click="submit"
        type="primary"
        >保存</el-button
      >
    </template>
  </kade-modal>
</template>
<script>
import Modal from "@/components/modal";
import { computed, reactive, ref, watch } from "vue";
import {
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElMessage,
  ElRadio,
  ElRadioGroup,
} from "element-plus";
import { addRole, editRole } from "@/applications/eccard-sys/api";
import { useDict } from "@/hooks/useDict";
const getDefaultModel = () => ({
  roleName: "",
  canEdit: "TRUE",
  status: "ENABLE_TRUE",
});
export default {
  emits: ["update:modelValue", "change"],
  props: {
    title: {
      type: String,
      default: "",
    },
    modelValue: {
      type: Boolean,
      default: false,
    },
    role: {
      type: Object,
      default: null,
    },
  },
  components: {
    "kade-modal": Modal,
    "el-button": ElButton,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    "el-input": ElInput,
    "el-radio": ElRadio,
    "el-radio-group": ElRadioGroup,
  },
  setup(props, context) {
    const formRef = ref(null);
    const loading = ref(false);
    const state = reactive({
      model: getDefaultModel(),
    });
    const statusList = useDict("SYS_ENABLE");
    const states = useDict("SYS_BOOL_STRING");
    const rules = {
      roleName: [
        { required: true, message: "请输入角色名称" },
        { max: 20, message: "角色名字不能超过20个字符" },
      ],
      roleCode:[
        { required: true, message: "请输入角色编码" },
        { max: 20, message: "角色编码不能超过20个字符" },
      ]
    };
    const cancel = () => {
      formRef.value?.resetFields();
      context.emit("update:modelValue", false);
    };
    const submit = () => {
      console.log(state.model);
      formRef.value?.validate(async (valid) => {
        if (valid) {
          try {
            let fn = state.model.id ? editRole : addRole;
            loading.value = true;
            let { id, roleCode, roleName, status } = { ...state.model };
            let params = id
              ? {
                  id,
                  roleCode,
                  roleName,
                  status,
                }
              : {
                  roleCode,
                  roleName,
                  status,
                };
            const { message } = await fn(params);
            ElMessage.success(message);
            context.emit("change");
            context.emit("update:modelValue", false);
          } catch (e) {
            throw new Error(e.message);
          } finally {
            loading.value = false;
          }
        }
      });
    };
    const attrs = computed(() => {
      // eslint-disable-next-line no-unused-vars
      const { modelValue, role, ...attrs } = props;
      return attrs;
    });
    const update = (v) => {
      context.emit("update:modelValue", v);
    };
    watch(
      () => props.modelValue,
      (v) => {
        if (v) {
          state.model = Object.assign(getDefaultModel(), props.role);
        }
      }
    );
    return {
      attrs,
      update,
      formRef,
      cancel,
      submit,
      rules,
      labelWidth: THEMEVARS.formLabelWidth,
      state,
      loading,
      statusList,
      states,
    };
  },
};
</script>