<template>
    <div id="echartDiv" class="chartDiv"></div>
    <div class="person-box">
        <div class="yoy-box">
            <div class="title">周同比</div>
            <div class="icon"></div>
            <div>12%</div>
        </div>
        <div class="day-box">
            <div class="title">日环比</div>
            <div class="icon"></div>
            <div>11%</div>
        </div>
    </div>
</template>

<script>
import * as echarts from "echarts";
import { onMounted } from "vue";

export default {
    setup() {
        const echartInit = () => {
            var chartDom = document.getElementById("echartDiv");
            var myChart = echarts.init(chartDom);
            var option = {
                legend: {
                    show: false,
                },
                xAxis: {
                    show: false,
                    type: "category",
                    boundaryGap: false,
                },
                yAxis: {
                    type: "value",
                    show: false,
                },
                grid: {
                    top: 1,
                    right: 1,
                    bottom: 1,
                    left: 1,
                },
                series: [
                    {
                        data: [1, 7, 2, 4, 2, 1, 3, 1, 2, 5, 4, 3, 2,3,4,1,2,5],
                        type: "line",
                        smooth: true,
                        lineStyle: {
                            width: 0
                        },
                        areaStyle: {
                            color: "#8122c8",
                        },
                        label:{
                            show:false
                        },
                        symbol:"none",
                    },
                ],
            };
            // 使用刚指定的配置项和数据显示图表。
            myChart.setOption(option);
        };
        //挂载
        onMounted(() => {
            echartInit();
        });

        return {
            echartInit,
        };
    },
};
</script>


<style lang="scss" scoped>
.person-box {
    display: flex;
    color: #0000006d;
    align-items: center;
    border-top: 1px solid #eeeeee;
    padding: 5px 0 0 5px;
    min-width: 328px;
    .yoy-box {
        display: flex;
        font-size: 10px;
        align-items: center;
        margin-right: 80px;
        .icon {
            margin: 5px;
            width: 0;
            height: 0;
            border-right: 7px solid #01a855;
            border-bottom: 7px solid #fff;
            transform: rotate(-45deg);
        }
    }
    .day-box {
        display: flex;
        align-items: center;
        font-size: 10px;
        .icon {
            margin: 5px;
            width: 0;
            height: 0;
            border-left: 7px solid #f00;
            border-top: 7px solid #fff;
            transform: rotate(-45deg);
        }
    }
}
.chartDiv {
    width: 100%;
    height: 25px;
    padding-bottom: 15px;
}
</style>