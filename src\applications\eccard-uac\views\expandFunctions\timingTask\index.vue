<template>
  <div class="padding-box">
    <kade-table-filter @search="handleSearch" @reset="handleReset">
      <el-form inline label-width="100px" size="mini">
        <el-form-item label="任务编号">
          <el-input clearable v-model="state.form.taskNo" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="门号">
          <el-input clearable v-model="state.form.doorNo" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="时间段">
          <el-date-picker v-model="state.requestDate" type="daterange" range-separator="~" start-placeholder="请选择"
            end-placeholder="请选择" :size="size" />
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="定时任务列表">
      <template #extra>
        <el-button type="success" size="mini" icon="el-icon-plus" @click="handleEdit({}, 'add')">新增</el-button>
      </template>
      <el-table border height="55vh" v-loading="state.loading" :data="state.dataList">
        <el-table-column show-overflow-tooltip prop="taskNo" label="任务编号" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip label="星期一" align="center">
          <template #default="scope">
            <el-switch disabled :model-value="scope.row.monday" active-color="#13ce66" inactive-color="#ff4949" :active-value="1" :inactive-value="0" />
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip label="星期二" align="center">
          <template #default="scope">
            <el-switch disabled :model-value="scope.row.tuesday" active-color="#13ce66" inactive-color="#ff4949" :active-value="1" :inactive-value="0" />
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip label="星期三" align="center">
          <template #default="scope">
            <el-switch disabled :model-value="scope.row.wednesday" active-color="#13ce66" inactive-color="#ff4949" :active-value="1" :inactive-value="0" />
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip label="星期四" align="center">
          <template #default="scope">
            <el-switch disabled :model-value="scope.row.thursday" active-color="#13ce66" inactive-color="#ff4949" :active-value="1" :inactive-value="0" />
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip label="星期五" align="center">
          <template #default="scope">
            <el-switch disabled :model-value="scope.row.friday" active-color="#13ce66" inactive-color="#ff4949" :active-value="1" :inactive-value="0" />
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip label="星期六" align="center">
          <template #default="scope">
            <el-switch disabled :model-value="scope.row.saturday" active-color="#13ce66" inactive-color="#ff4949" :active-value="1" :inactive-value="0" />
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip label="星期日" align="center">
          <template #default="scope">
            <el-switch disabled :model-value="scope.row.sunday" active-color="#13ce66" inactive-color="#ff4949" :active-value="1" :inactive-value="0" />
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip prop="beginDate" label="开始日期" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="endDate" label="截止日期" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="actionTime" label="动作时间" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="doorName" label="适用于门" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="doorControlMode" label="控制方式" align="center">       
          <template #default="scope">
            {{ controlModeList.find(item=>item.value==scope.row.doorControlMode)?.label||scope.row.doorControlMode }}
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip label="操作" align="center" width="200px">
          <template #default="scope">
            <el-button type="text" size="mini" @click="handleEdit(scope.row, 'details')">详情</el-button>
            <el-button type="text" size="mini" @click="handleEdit(scope.row, 'edit')">编辑</el-button>
            <el-button type="text" size="mini" @click="handleDel(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination v-model:currentPage="state.form.currentPage" v-model:page-size="state.form.pageSize"
          :page-sizes="[10, 20, 30, 40]" background layout="total, sizes, prev, pager, next, jumper"
          :total="state.total" @size-change="handleSizeChange" @current-change="handleCurrentChange">
        </el-pagination>
      </div>
    </kade-table-wrap>
    <kade-timing-task-edit v-model:modelValue="state.isShow" :rowData="state.rowData" :dialogType="state.dialogType"
      @update:modelValue="close" />
  </div>
</template>
<script>
import { onMounted, reactive } from "vue"
import { ElForm, ElFormItem, ElInput, ElTable, ElTableColumn, ElPagination, ElButton, ElDatePicker, ElSwitch, ElMessageBox, ElMessage } from "element-plus"
import { dateStr } from "@/utils/date.js"
import { getTaskList, deleteTask } from '@/applications/eccard-uac/api.js'
import edit from "./components/edit.vue"

const controlModeList=[
  {label:'常开',value:0},
  {label:'常闭',value:1}
]
export default {
  components: {
    ElForm, ElFormItem, ElInput, ElTable, ElTableColumn, ElPagination, ElButton, ElDatePicker, ElSwitch,
    "kade-timing-task-edit": edit
  },
  setup() {
    const state = reactive({
      loading: false,
      isShow: false,
      form: {
        currentPage: 1,
        pageSize: 10
      },
      dataList: [],
      total: 0,
      requestDate: [],
      rowData: {},
      dialogType: ''
    })
    const handleEdit = (row, type) => {

      state.rowData = row
      state.dialogType = type
      state.isShow = true
    }
    const handleCurrentChange = val => {
      state.form.currentPage = val
      getList()
    }
    const handleSizeChange = val => {
      state.form.currentPage = 1
      state.form.pageSize = val
      getList()
    }
    const handleSearch = () => {
      getList()
    }
    const handleReset = () => {
      state.form = {
        currentPage: 1,
        pageSize: 10
      }
      state.requestDate = []
      getList()
    }
    const handleDel = (row) => {
      ElMessageBox.confirm("确认删除?", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        let { code, message } = await deleteTask(row.id)
        if (code === 0) {
          ElMessage.success(message)
          getList()
        }
      })
    }
    const getList = async () => {
      if (state.requestDate && state.requestDate.length) {
        state.form.beginDate = dateStr(state.requestDate[0])
        state.form.endDate = dateStr(state.requestDate[1])
      } else {
        delete state.form.beginDate
        delete state.form.endDate
      }
      state.loading = true
      try {
        let { data: { list, total } } = await getTaskList(state.form)
        state.dataList = list
        state.total = total
        state.loading = false
      }
      catch {
        state.loading = false
      }
    }
    const close = (val) => {
      if (val) {
        getList()
      }
      state.isShow = false
    }
    onMounted(() => {
      getList()
    })
    return {
      state,
      close,
      handleEdit,
      handleDel,
      handleSearch,
      handleReset,
      controlModeList,
      handleCurrentChange,
      handleSizeChange
    }
  }
}
</script>
<style lang="scss" scoped>
:deep(.el-dialog__footer) {
  text-align: center;
  border: none;
  margin-top: 20px;
}

:deep(.el-dialog) {
  border-radius: 8px;
  padding-bottom: 25px;
}
</style>