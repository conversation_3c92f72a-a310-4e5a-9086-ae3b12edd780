<template>
  <div class="padding-box">
    <kade-table-filter @search="handleSearch" @reset="handleReset">
      <el-form inline label-width="100px" size="mini">
        <el-form-item label="区域">
          <el-input clearable v-model="state.form.name" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="控制器编号">
          <el-input clearable v-model="state.form.ip" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="控制器SN号">
          <el-input clearable v-model="state.form.ip" placeholder="请输入"></el-input>
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="多门互锁列表">
      <el-table border height="55vh" v-loading="state.loading" :data="state.dataList">
        <el-table-column show-overflow-tooltip prop="name" label="所属区域" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="ip" label="控制器编号" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="ip" label="控制器SN号" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="ip" label="1号-2号门互锁" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="ip" label="3号-4号门互锁" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="ip" label="1号-2号-3号门互锁" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="ip" label="1号-2号-3号-4号门互锁" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="ip" label="所控制的门" align="center"></el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination v-model:currentPage="state.form.pageNum" v-model:page-size="state.form.pageSize"
          :page-sizes="[10, 20, 30, 40]" background layout="total, sizes, prev, pager, next, jumper"
          :total="state.total" @size-change="handleSizeChange" @current-change="handleCurrentChange">
        </el-pagination>
      </div>
    </kade-table-wrap>
  </div>
</template>
<script>
import { reactive } from "vue"
import { ElForm, ElFormItem, ElInput, ElTable, ElTableColumn, ElPagination } from "element-plus"
export default {
  components: {
    ElForm, ElFormItem, ElInput, ElTable, ElTableColumn, ElPagination,
  },
  setup() {
    const state = reactive({
      loading: false,
      isSet: false,
      isDetails:false,
      form: {
        pageNum: 1,
        pageSize: 10
      },
      dataList: [{id:1}],
      total: 0,

    })
    const handleCurrentChange = val => {
      state.form.pageNum = val
    }
    const handleSizeChange = val => {
      state.form.pageNum = 1
      state.form.pageSize = val
    }
    return {
      state,
      handleCurrentChange,
      handleSizeChange
    }
  }
}
</script>
<style lang="scss" scoped>
:deep(.el-dialog__footer) {
  text-align: center;
}
</style>