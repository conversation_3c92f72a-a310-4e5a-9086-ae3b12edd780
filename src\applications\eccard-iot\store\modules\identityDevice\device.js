const state = {
  isRecord: { //设备更换记录模态框显示隐藏数据
    accessAIO: { rowData: {}, isShow: false },
    visitorDevice: { rowData: {}, isShow: false },
    camera: { rowData: {}, isShow: false },
    doorLock: { rowData: {}, isShow: false },
    multimediaAttendanceDevice: { rowData: {}, isShow: false },
    faceAnalysisDevice: { rowData: {}, isShow: false },
    leaveDevice: { rowData: {}, isShow: false },
    bigScreenDevice: { rowData: {}, isShow: false },
    meetingSignInDevice: { rowData: {}, isShow: false },
    electronicClassDevice: { rowData: {}, isShow: false },
  },
};
const mutations = {
  updateState(state, { key, payload }) {
    state.isRecord[key] = payload;
  },
};
const actions = {

};
export default {
  namespaced: true,
  state,
  mutations,
  actions
}