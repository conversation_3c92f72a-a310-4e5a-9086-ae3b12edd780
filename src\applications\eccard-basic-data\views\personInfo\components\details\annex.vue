<template>
  <div style="text-align:right;margin-bottom:10px">
    <el-button @click="state.isAdd=true" type="primary" icon="el-icon-plus" size="mini">添加附件</el-button>
  </div>
  <el-table :data="state.dataList" border>
    <el-table-column align="center" label="附件名称" prop="annexName"></el-table-column>
    <el-table-column align="center" label="上传人员" prop="createUserName"></el-table-column>
    <el-table-column align="center" label="上传时间" prop="createTime"></el-table-column>
    <el-table-column align="center" label="附件" prop="annexUrl">
      <template #default="scope">
        <el-button @click="handleSee(scope.row)" class="green" type="text">查看附件</el-button>
      </template>
    </el-table-column>
    <el-table-column align="center" label="操作">
      <template #default="scope">
        <el-button @click="handleDel(scope.row)" class="green" type="text">删除</el-button>
      </template>
    </el-table-column>
  </el-table>
  <div class="pagination">
    <el-pagination background :current-page="state.form.pageNum" layout="total, sizes, prev, pager, next, jumper" :page-sizes="[10, 20, 50, 100, 200]" :total="state.total" :page-size="state.form.pageSize" @current-change="currentChange" @size-change="sizeChange">
    </el-pagination>
  </div>
  <kade-annex-dialog :isShow="state.isAdd" @close="close" />
</template>
<script>
import {
  ElButton,
  ElTable,
  ElTableColumn,
  ElMessageBox,
  ElMessage,
} from "element-plus";
import { reactive } from "@vue/reactivity";
import { useStore } from "vuex";
import { annex, delAnnex } from "@/applications/eccard-basic-data/api";
import annexDialog from "./components/annexDialog.vue";
import { onMounted } from "@vue/runtime-core";
export default {
  components: {
    ElButton,
    ElTable,
    ElTableColumn,
    "kade-annex-dialog": annexDialog,
  },
  setup() {
    const store = useStore();
    const state = reactive({
      isAdd: false,
      form: {
        pageNum: 1,
        pageSize: 10,
      },
      dataList: [],
      total: 0,
    });
    const getList = async () => {
      state.form.userId = store.state.userInfo.rowData.id;
      let {
        data: { list, total },
      } = await annex(state.form);
      state.dataList = list;
      state.total = total;
    };
    const handleSee = (row) => {
      window.open(row.annexUrl);
    };
    const handleDel = (row) => {
      ElMessageBox.confirm(`确认删除?`, {
        type: "warning",
        closeOnPressEscape: false,
        closeOnClickModal: false,
      }).then(async () => {
        try {
          const { message } = await delAnnex(row.id);
          ElMessage.success(message);
          getList();
        } catch (e) {
          throw new Error(e.message);
        }
      });
    };

    const currentChange = (val) => {
      state.form.pageNum = val;
      getList();
    };
    const sizeChange = (val) => {
      state.form.pageNum = 1;
      state.form.pageSize = val;
      getList();
    };
    const close = (val) => {
      if (val) {
        getList();
      }
      state.isAdd = false;
    };
    onMounted(() => {
      getList();
    });
    return {
      state,
      handleSee,
      handleDel,
      currentChange,
      sizeChange,
      close,
    };
  },
};
</script>
<style lang="scss" scoped>
.el-table--border {
  border-right: 1px solid #ebeef5;
  border-bottom: 1px solid #ebeef5;
}
</style>