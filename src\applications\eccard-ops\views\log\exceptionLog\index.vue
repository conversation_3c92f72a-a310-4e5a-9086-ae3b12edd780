<template>
<kade-route-card>
  <kade-table-filter @search="search" @reset="reset">
        <el-form inline label-width="100px" size="mini">
          <el-form-item label="所属应用">
            <el-select v-model="state.form.app" placeholder="全部" clearable>
                <el-option :label="item.applyName" :value="item.applyAppid" v-for="(item,index) in state.appList" :key="index" ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="功能模块">
            <el-input  v-model="state.form.bizModule" placeholder="请输入页面名称"></el-input>
          </el-form-item>
          <el-form-item label="操作类型">
            <el-select v-model="state.form.operateType" placeholder="全部" clearable>
              <el-option :label="item.label" :value="item.value" v-for="(item,index) in operateTypeList" :key="index"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="请求地址">
            <el-input  v-model="state.form.uri" placeholder="输入关键字搜索">
            </el-input>
          </el-form-item>
          <el-form-item label="请求参数">
            <el-input  v-model="state.form.request" placeholder="输入关键字搜索"></el-input>
          </el-form-item>
          <el-form-item label="返回参数">
            <el-input  v-model="state.form.result" placeholder="输入关键字搜索"></el-input>
          </el-form-item>
          <el-form-item label="操作员账号">
            <el-input  v-model="state.form.operateAccount" placeholder="输入关键字搜索"></el-input>
          </el-form-item>
          <el-form-item label="操作时间">
            <el-date-picker
              style="width: 200px"
              v-model="state.defaultTime"
              type="daterange"
              range-separator="~"
              @change="timeChange"
              unlink-panels
            >
            </el-date-picker>
          </el-form-item>
        </el-form>
    </kade-table-filter>
    <kade-table-wrap title="异常日志列表">
      <template #extra>
          <el-button class="btn-blue" icon="el-icon-daoru" size="mini">导出</el-button>
      </template>
      <el-table border :data="state.dataList" style="width: 100%">
        <el-table-column prop="id" label="日志ID" align="center"></el-table-column>
        <el-table-column prop="app" label="所属应用" align="center"></el-table-column>
        <el-table-column prop="bizModule" label="功能模块" align="center"></el-table-column>
        <el-table-column prop="operateType" label="操作类型" align="center"></el-table-column>
        <el-table-column prop="uri" label="请求地址" align="center">
          <template #default="scope">
            <div style="color:#3399FF;text-decoration: underline;" @click="jump(scope.row.uri)">{{ scope.row.uri }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="operateAccount" label="操作员账号" align="center"></el-table-column>
        <el-table-column prop="operateName" label="操作员姓名" align="center"></el-table-column>
        <el-table-column prop="operateTime" label="操作时间" align="center"></el-table-column>
        <el-table-column prop="spendTime" label="响应时间（毫秒）" align="center"></el-table-column>
        <el-table-column label="操作" align="center">
          <template #default="scope">
            <el-button type="text" @click="edtails(scope.row)">查看详情</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination
          background
          v-model:currentPage="state.form.currentPage"
          v-model:page-size="state.form.pageSize"
          :page-sizes="[5, 10, 20, 30]"
          layout="total,sizes, prev, pager, next, jumper"
          :total="state.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </div>
    </kade-table-wrap>
    <kade-exception-edtails :dataRow="state.dataRow" :dialogVisible="state.dialogVisible" @close="close"></kade-exception-edtails>
  </kade-route-card>
</template>

<script>
import {
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElDatePicker,
  ElTable,
  ElTableColumn,
  ElPagination,
  ElSelect,
  ElOption,
} from "element-plus";

const operateTypeList = [
  {label:"其它",value:0},
  {label:"新增",value:1},
  {label:"修改",value:2},
  {label:"删除",value:3},
  {label:"查询",value:4},
  {label:"授权",value:5},
  {label:"导出",value:6},
  {label:"导入",value:7},
  {label:"强退",value:8},
  {label:"生成代码",value:9},
  {label:"清空数据",value:10},
]

import { getexceptionLogList } from "@/applications/eccard-basic-data/api.js";
import { dateStr } from "@/utils/date.js"
import { getUserApplyList } from "@/applications/unified_portal/api";
import { reactive,onMounted } from "vue";
import edtails from "./components/edtails.vue"
export default {
  components: {
    ElButton,
    ElForm,
    ElFormItem,
    ElInput,
    ElDatePicker,
    ElTable,
    ElTableColumn,
    ElPagination,
    ElSelect,
    ElOption,
    "kade-exception-edtails":edtails,
  },
  setup() {
    const state = reactive({
      dialogVisible:false,
      defaultTime:[],
      dataList:[],
      appList:[],
      dataRow:"",
      form:{
        currentPage:1,
        pageSize:10,
      }, 
      total:0,
    });
    const edtails = (row)=>{
        state.dataRow=row
        state.dialogVisible=true
    };
    
    const getAppList=()=>{
      getUserApplyList().then((res)=>{
        console.log(res)
        state.appList = res.data
      })
    }
    
    const getList=()=>{
      getexceptionLogList(state.form).then((res)=>{
        console.log(res)
            state.dataList=res.data.list
            state.total=res.data.total
        })
    }
    const search=()=>{
        console.log(state.form);
        getList()

    }
    const jump = val =>{
      console.log(val)
      window.open(val)
    };
    const reset=()=>{
        state.form={
            currentPage:1,
            pageSize:10,
        }
        state.defaultTime=[]
    }

    const timeChange=(val)=>{
      if(val){
      state.form.operateStartTime=dateStr(val[0])
      state.form.operateEndtTime=dateStr(val[1])
      }else{
        delete state.form.operateStartTime
        delete state.form.operateEndtTime
      }
    }
    const close = ()=>{
        state.dialogVisible=false
    };
    const handleSizeChange = (val) => {
      state.form.pageSize=val
      getList()
    };
    const handleCurrentChange = (val) => {
      state.form.currentPage=val
      getList()
    };
    onMounted(()=>{
      getList()
      getAppList()
    })
    return {
      state,
      edtails,
      search,
      jump,
      reset,
      getList,
      getAppList,
      timeChange,
      close,
      operateTypeList,
      handleSizeChange,
      handleCurrentChange,
    };
  },
};
</script>

<style lang="scss" scoped>
:deep(.el-input--mini .el-input__inner){
  width: 180px;
}
:deep(.el-range-editor--mini.el-input__inner){
  width: 180px;
}
:deep(.el-pagination__editor.el-input .el-input__inner){
  width: 46px;
}
:deep(.el-pagination__sizes .el-input .el-input__inner){
  width: 100px;
}
:deep(.el-range-editor.el-input__inner){
  width: 180px;
}
:deep(.el-dialog__footer){
    border: 0;
}
:deep(.el-dialog__header){
    border-bottom: 1px solid #eeeeee;
}
:deep(.el-date-editor .el-range__icon) {
    display: none;
}
</style>