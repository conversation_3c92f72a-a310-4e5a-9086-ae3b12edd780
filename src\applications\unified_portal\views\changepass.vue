<template>
  <kade-modal v-bind="attrs" :modelValue="modelValue" @update:modelValue="update">
    <el-form
      :model="model" 
      :rules="rules" 
      ref="formRef" 
      :label-width="labelWidth"
      size="small"
      @keyup.enter="submit"
    >
      <el-form-item label="原密码" prop="oldPwd">
        <el-input type="password" placeholder="请输入" v-model="model.oldPwd" />
      </el-form-item>
      <el-form-item label="新密码" prop="newPwd">
        <el-input type="password" placeholder="请输入" v-model="model.newPwd" />
      </el-form-item>
      <el-form-item label="确认密码" prop="confirmPwd">
        <el-input type="password" placeholder="请输入" v-model="model.confirmPwd" />
      </el-form-item>        
    </el-form>
    <template #footer>
      <el-button size="small" icon="el-icon-circle-close" @click="cancel">取消</el-button>
      <el-button size="small" icon="el-icon-circle-check" :loading="loading" type="primary" @click="submit">保存</el-button>
    </template>
  </kade-modal>
</template>
<script>
import { 
  ElForm,
  ElFormItem,
  ElInput,
  ElButton,
  ElMessage,
} from 'element-plus';
import { reactive, ref, computed } from 'vue';
import { useStore } from 'vuex';
import { updatePwd } from '@/applications/eccard-sys/api';
import { FormLayout } from '@/service/dictionary';
import Modal from '@/components/modal';
export default {
  emits: ['update:modelValue', 'change'],
  components: {
    'el-form': ElForm,
    'el-form-item': ElFormItem,
    'el-input': ElInput,
    'el-button': ElButton,
    'kade-modal': Modal,
  },
  props: {
    title: {
      type: String,
      default: '',
    },
    modelValue: {
      type: Boolean,
      default: false,
    },    
  },
  setup(props, context) {
    const model = reactive({
      oldPwd: '',
      newPwd: '',
      confirmPwd: ''
    });
    const loading = ref(false);
    const formRef = ref(null);
    const store = useStore();
    const validatePass = (rule, value, callback) => {
      if (value !== model.newPwd) {
        callback(new Error('两次输入密码不一致!'));
      } else {
        callback()
      }
    };
    const submit = () => {
      formRef.value.validate(async (valid) => {
        if(valid) {
          try {
            loading.value = true;
            const { oldPwd, newPwd } = model;
            const { message } = await updatePwd({ oldPwd, newPwd, id: store.state.user.userInfo.id});
            formRef.value.resetFields();
            ElMessage.success(message);
          } catch(e) {
            throw new Error(e.message);
          } finally {
            loading.value = false;
          }
        }
      });
    };
    const attrs = computed(() => {
      // eslint-disable-next-line no-unused-vars
      const { modelValue, ...attrs } = props;
      return attrs;
    });
    const update = (v) => {
      formRef.value.resetFields();
      context.emit('update:modelValue', v);
    };   
    const cancel = () => {
      formRef.value.resetFields();
      context.emit('update:modelValue', false);
    }     
    return {
      loading,
      model,
      formRef,
      attrs,
      update,
      cancel,
      layout: FormLayout,
      labelWidth: THEMEVARS.formLabelWidth,
      submit,
      rules: {
        oldPwd: [{ required: true, message: '请输入密码' }],
        newPwd: [
          { required: true, message: '请输入新密码' },
          { min: 6, max: 16, message: '长度在6到16个字符' },
        ],
        confirmPwd: [
          { required: true, message: '请再次输入密码' },
          { validator: validatePass }
        ]
      }
    }    
  }
}
</script>