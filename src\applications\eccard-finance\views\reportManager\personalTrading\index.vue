<template>
  <!-- 个人账户交易明细 -->
  <div class="cashRecharge" v-loading="state.loading">
    <kade-route-card style="height: auto">
      <kade-table-filter @search="getList()" @reset="reset()">
        <el-form inline size="mini" label-width="100px">
          <el-form-item label="用户编号:">
            <el-input placeholder="编号关键字搜索" v-model="state.form.userCode"></el-input>
          </el-form-item>
          <el-form-item label="用户姓名:">
            <el-input placeholder="姓名关键字搜索" v-model="state.form.userName"></el-input>
          </el-form-item>
<!--           <el-form-item label="组织机构:">
            <kade-dept-select-tree style="width: 100%" :value="state.form.deptPath" valueKey="deptPath"
              :multiple="false" @valueChange="(val) => (state.form.deptPath = val.deptPath)" />
          </el-form-item>
          <el-form-item label="交易钱包:">
            <el-select clearable v-model="state.form.walletCode" placeholder="请选择" multiple collapse-tags>
              <el-option v-for="(item, index) in state.allWalletList" :key="index" :label="item.walletName"
                :value="item.walletCode">
              </el-option>
            </el-select>
          </el-form-item> -->
          <el-form-item label="交易类型:">
            <el-select clearable v-model="state.form.costType" placeholder="请选择" multiple collapse-tags
              @change="costTypeChange">
              <el-option v-for="(item, index) in state.costTypeList" :key="index" :label="item.costName"
                :value="item.costCode">
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="交易方式:">
            <el-select clearable v-model="state.form.tradeMode" placeholder="请选择" multiple collapse-tags>
              <el-option v-for="(item, index) in state.tradeModeList" :key="index" :label="item.name"
                :value="item.code">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="操作员:">
            <el-select clearable filterable multiple collapse-tags v-model="state.form.operatorId" placeholder="请选择">
              <el-option v-for="(item, index) in state.systemUserList" :key="index" :label="item.userName"
                :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="交易来源:">
            <el-select clearable multiple collapse-tags v-model="state.form.tradeSource" placeholder="请选择"
              @change="tradeSourceChange">
              <el-option v-for="(item, index) in state.tradeSourceList" :key="index" :label="item.tradeName"
                :value="item.tradeCode">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="交易区域:">
            <kade-area-select-tree style="width: 100%" :value="state.form.areaPath" valueKey="areaPath" :multiple="true"
              @valueChange="areaChange" />
          </el-form-item>
          <el-form-item label="交易终端:">
            <el-select clearable filterable v-model="state.form.deviceId" placeholder="请选择">
              <el-option v-for="(item, index) in state.deviceByTerminalTypeList" :key="index" :label="item.label"
                :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="统计时间:">
            <el-date-picker v-model="state.requestDate" type="datetimerange" unlink-panels
              :default-time="state.defaultTime" range-separator="~" start-placeholder="请选择日期" end-placeholder="请选择日期"
              @change="changeDate">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="按入账时间:">
            <el-checkbox v-model="state.timeType"></el-checkbox>
          </el-form-item>
        </el-form>
      </kade-table-filter>
      <kade-table-wrap title="个人账户交易明细表">
        <template #extra>
          <el-button @click="exportClick()" icon="el-icon-daoru" size="mini" class="btn-blue">导出查询明细</el-button>
        </template>
        <el-table style="width: 100%" :data="state.detailList" ref="multipleTable" height="55vh" highlight-current-row
          border stripe>
          <el-table-column v-for="(item,index) in column" :key="index" :width="item.width" :label="item.label"
            :prop="item.prop" align="center" show-overflow-tooltip>
            <template v-if="item.render" #default="scope">
              {{item.render(scope.row[item.prop])}}
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center">
            <template #default="scope">
              <el-button @click="handleRefund(scope.row)"
                v-if="scope.row.userCode!=='合计'&&(scope.row.costType==101||scope.row.costType==201)" type="text"
                size="mini">退款
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination">
          <el-pagination background :current-page="state.form.currentPage" :page-size="state.form.pageSize"
            layout="total, sizes, prev, pager, next, jumper" :page-sizes="[6, 10, 20, 50, 100]" :total="state.total"
            @current-change="handlePageChange" @size-change="handleSizeChange">
          </el-pagination>
        </div>
      </kade-table-wrap>
    </kade-route-card>
    <kade-refund v-model="state.isRefund" :rowData="state.rowData" @update:modelValue="close" />
  </div>
</template>
<script>
import {
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElTable,
  ElTableColumn,
  ElPagination,
  ElDatePicker,
  ElCheckbox,
} from "element-plus";
import { timeStr } from "@/utils/date.js";
import { reactive } from "@vue/reactivity";
import {
  getPersonalAccountTradeList,
  costType,
  tradeSource,
  tradeMode,
  getSystemUser,
  personalAccountTradeExport,
  getWalletActiveList,
  getTerminalTypeList,
} from "@/applications/eccard-finance/api";

import { getDeviceStatus } from "@/applications/eccard-iot/api";
import { onMounted } from "@vue/runtime-core";
/* import deptSelectTree from "@/components/tree/deptSelectTree.vue"; */
import areaSelectTree from "@/components/tree/areaSelectTree.vue";
import refund from "./components/refund.vue"
const column = [
  { label: "用户编号", prop: "userCode", width: "" },
  { label: "用户姓名", prop: "userName", width: "" },
  { label: "组织机构", prop: "deptName", width: "" },
  { label: "交易单号", prop: "tradeNo", width: "280" },
  { label: "交易钱包", prop: "walletName", width: "" },
  // { label: "交易前余额", prop: "tradeBeforeBalance", width: "" },
  { label: "交易金额", prop: "tradeAmount", width: "" },
  // { label: "交易后余额", prop: "tradeAfterBalance", width: "" },
  { label: "交易时间", prop: "tradeDate", width: "170", render: (val) => val && timeStr(val) },
  { label: "入账时间", prop: "createDate", width: "170", render: (val) => val && timeStr(val) },
  { label: "交易类型", prop: "costTypeName", width: "" },
  { label: "交易来源", prop: "tradeSourceName", width: "" },
  { label: "交易方式", prop: "tradeModeName", width: "" },
  { label: "操作员", prop: "operatorName", width: "" },
  { label: "交易区域", prop: "areaName", width: "" },
  { label: "交易终端", prop: "deviceName", width: "" },
]
export default {
  components: {
    "el-button": ElButton,
    "el-input": ElInput,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    "el-select": ElSelect,
    "el-option": ElOption,
    "el-table": ElTable,
    "el-table-column": ElTableColumn,
    "el-pagination": ElPagination,
    ElDatePicker,
    ElCheckbox,
/*     "kade-dept-select-tree": deptSelectTree, */
    "kade-area-select-tree": areaSelectTree,
    "kade-refund": refund
  },
  setup() {
    const state = reactive({
      loading: false,
      timeType: true,
      isRefund: false,
      form: {
        currentPage: 1,
        pageSize: 10,
      },
      detailList: [],
      total: 0,
      requestDate: "",
      costTypeList: [],
      allWalletList: [],
      departCheckList: [],
      tradeSourceList: [],
      tradeModeList: [],
      systemUserList: [],
      defaultTime: [
        new Date(new Date(new Date().toLocaleDateString()).getTime()),
        new Date(
          new Date(new Date().toLocaleDateString()).getTime() +
          24 * 60 * 60 * 1000 -
          1
        ),
      ],
    });


    //获取交易类型
    const getCostTypeList = () => {
      costType().then((res) => {
        state.costTypeList = res.data;
      });
    };

    //获取钱包列表
    const queryWalletActiveList = () => {
      getWalletActiveList().then((res) => {
        state.allWalletList = res.data;
      });
    };
    //获取交易来源
    const getTradeSource = () => {
      tradeSource().then((res) => {
        state.tradeSourceList = res.data;
      });
    };
    //获取交易方式
    const getTradeModeList = (val) => {
      tradeMode({ costType: val }).then((res) => {
        state.tradeModeList = res.data;
      });
    };
    //获取操作员
    const querySystemUser = () => {
      getSystemUser().then((res) => {
        state.systemUserList = res.data;
      });
    };
    //获取终端类型
    const queryTerminalTypeList = () => {
      getTerminalTypeList().then((res) => {
        state.terminalTypeList = res.data;
      });
    };
    //获取终端设备
    const queryDeviceByTerminalTypeList = () => {
      let params = {
        areaPath: state.form.areaPath,
      };
      getDeviceStatus(params).then((res) => {
        state.deviceByTerminalTypeList = res.data.map((item) => {
          return {
            ...item,
            label: item.deviceNo + "-" + item.deviceName,
          };
        });
      });
    };
    const getList = async () => {
      state.loading = true;
      state.form.timeType = state.timeType ? "BY_CREATE_DATE" : "BY_TRADE_DATE";
      for (let item in state.form) {
        if (!state.form[item]) {
          delete state.form[item];
        }
      }
      try {
        let { code, data } = await getPersonalAccountTradeList(
          state.form
        );
        if (code === 0) {
          let {
            page: { total, list },
            // tradeAmountCount,
          } = data;
          state.detailList = list;

/*           if (state.detailList.length) {
            state.detailList.push({
              tradeAmount: tradeAmountCount,
              userCode: "合计",
            });
          } */
          state.total = total;
        }
        state.loading = false;
      }
      catch {
        state.loading = false;
      }
    };
    const changeDate = (val) => {
      if (val && val.length) {
        state.form.startTime = timeStr(val[0]);
        state.form.endTime = timeStr(val[1]);
      } else {
        delete state.form.startTime;
        delete state.form.endTime;
      }
    };
    const costTypeChange = (val) => {
      if (val && val.length) {
        getTradeModeList(val)
      } else {
        state.tradeModeList = []
      }
    }
    const tradeSourceChange = (val) => {
      console.log(val);
      state.form.terminalDeviceId = "";
      if (val.includes(3) || val.includes(4)) {
        queryDeviceByTerminalTypeList();
      }
    };

    const areaChange = (val) => {
      state.form.areaPath = val;
      state.form.terminalDeviceId = "";
      if (val && val.length) {
        queryDeviceByTerminalTypeList();
      }
    };
    const handleRefund = row => {
      state.rowData = row
      state.isRefund = true
    }
    const exportClick = async () => {
      state.form.timeType = state.timeType ? "BY_CREATE_DATE" : "BY_TRADE_DATE";
      state.loading = true
      try {
        let res = await personalAccountTradeExport(state.form);
        let url = window.URL.createObjectURL(
          new Blob([res], { type: "application/vnd.ms-excel" })
        );
        let link = document.createElement("a");
        link.style.display = "none";
        link.href = url;
        link.setAttribute("download", "个人账户交易明细报表.xlsx");
        document.body.appendChild(link);
        link.click();
        state.loading = false
      }
      catch {
        state.loading = false
      }

    };

    const handlePageChange = (val) => {
      state.form.currentPage = val;
      getList();
    };
    const handleSizeChange = (val) => {
      state.form.currentPage = 1;
      state.form.pageSize = val;
      getList();
    };

    const reset = () => {
      state.form = {
        currentPage: 1,
        pageSize: 10,
      };
      state.requestDate = "";
    };
    const close = (val) => {
      if (val) {
        getList();

      }
      state.isRefund = false
    }
    onMounted(() => {
      getList();
      getCostTypeList();
      getTradeSource();
      // getTradeModeList();
      querySystemUser();
      queryWalletActiveList();
      queryTerminalTypeList();
    });
    return {
      column,
      state,
      timeStr,
      exportClick,
      getList,
      changeDate,
      areaChange,
      costTypeChange,
      tradeSourceChange,
      handlePageChange,
      handleSizeChange,
      reset,
      handleRefund,
      close
    };
  },
};
</script>
<style lang="scss" scoped>
:deep(.el-divider--horizontal) {
  margin: 0 0 10px 0;
}

.cashRecharge {
  :deep(.search-box) {
    .el-input__inner {
      width: 193px;
    }

    .el-date-editor {
      width: 400px;
    }
  }
}
</style>
