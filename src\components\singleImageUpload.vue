<template>
  <el-upload
    class="single-image-uploader"
    :show-file-list="false"
    :before-upload="beforeUpload"
    action=""
  >
    <el-image v-if="modelValue" :src="modelValue" class="images" >
      <template #error>
        <div class="image-slot">
          <i class="el-icon-picture-outline"></i>
        </div>
      </template>
    </el-image>
    <kade-icon v-else size="40px" color="#999" :name="icon" />    
  </el-upload>
</template>
<script>
// 单图片上传组件
import { ElUpload, ElImage } from 'element-plus';
import Icon from './icon';
export default {
  components: {
    'kade-icon': Icon,
    'el-image': ElImage,
    'el-upload': ElUpload,
  },
  emits: ['update:modelValue'],
  props: {
    modelValue: {
      type: String,
      default: ''
    },
    icon: {
      type: String,
      default: 'iconai-up-img'
    },
    action: {
      type: Function,
      default: () => {}
    }
  },
  setup(props, context) {
    const beforeUpload = async (f) => {
      console.log(f);
      const formData = new FormData();
      formData.append('file', f);
      const { data } = await props.action(formData);
      context.emit('update:modelValue', data);
      return false;
    }
    return {
      beforeUpload,
    }
  }
}
</script>
<style lang="scss">
.single-image-uploader{
  width: 100px;
  height: 100px;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  overflow: hidden;
  .el-upload{
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100px;
    height: 100px;
  }
  .images{
    width: 100px;
    height: 100px;    
  }
  .image-slot{
    width: 100px;
    height: 100px;
    display: flex;
    justify-content: center;
    align-items: center;
    i{
      font-size: 50px;
      color: #999;
    }
  }
}
</style>