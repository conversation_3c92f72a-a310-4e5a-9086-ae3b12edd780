<template>
  <el-dialog v-loading="state.loading" :model-value="modelValue" :title="editType === 'edit' ? '编辑成本' : '成本录入'" width="600px" :before-close="handleClose">
    <el-form label-width="100px" size="mini" ref="formRef" :model="state.form" :rules="rules">
      <el-row>
        <el-col :span="12">
          <el-form-item label="月份：" prop="month">
            <el-date-picker
              v-model="state.form.month"
              type="month"
              placeholder="请选择年月"
              format="YYYY-MM"
              value-format="YYYY-MM"
              style="width: 100%;" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>

        <el-col :span="12">
          <el-form-item label="用水量：" prop="waterNum">
            <el-input-number v-model="state.form.waterNum" :precision="2" :min="0" :max="100000" style="width: 100%;" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="水价：" prop="waterPrice">
            <el-input-number v-model="state.form.waterPrice" :precision="2" :min="0" :max="100000"
              style="width: 100%;" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="用电量：" prop="elecNum">
            <el-input-number v-model="state.form.elecNum" :precision="2" :min="0" :max="100000" style="width: 100%;" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="电价：" prop="elecPrice">
            <el-input-number v-model="state.form.elecPrice" :precision="2" :min="0" :max="100000"
              style="width: 100%;" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="维修成本：" prop="repairCost">
            <el-input-number v-model="state.form.repairCost" :precision="2" :min="0" :max="100000"
              style="width: 100%;" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="其他成本：" prop="otherCost">
            <el-input-number v-model="state.form.otherCost" :precision="2" :min="0" :max="100000"
              style="width: 100%;" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="维修说明：" prop="repairRemark">
        <el-input type="textarea" v-model="state.form.repairRemark" placeholder="请输入维修说明"></el-input>
      </el-form-item>
      <el-form-item label="其他说明：" prop="otherRemark">
        <el-input type="textarea" v-model="state.form.otherRemark" placeholder="请输入其他说明"></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button type="primary" @click="handleSave" size="mini">保存</el-button>
        <el-button @click="handleClose" size="mini">返回</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { ElDialog, ElButton, ElRow, ElCol, ElForm, ElFormItem, ElInput, ElInputNumber, ElDatePicker, ElMessage } from "element-plus"
import { reactive, ref, watch, nextTick } from 'vue'
import { addCost, updateCost } from "../../../api/cost.js"
export default {
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    selectRow: {
      types: Object,
      default: null
    },
    editType: {
      types: String,
      default: ''
    }
  },
  components: {
    ElDialog,
    ElButton,
    ElRow,
    ElCol,
    ElForm,
    ElFormItem,
    ElInput,
    ElInputNumber,
    ElDatePicker,
  },
  setup(props, context) {
    const formRef = ref(null)
    const state = reactive({
      loading: false,
      form: {}
    });
    watch(() => props.modelValue, val => {
      if (val) {
        if (props.editType == 'add') {
          state.form = {
            month: "",
            waterNum: 0,
            waterPrice: 0,
            elecNum: 0,
            elecPrice: 0,
            repairCost: 0,
            otherCost: 0,
            repairRemark: "",
            otherRemark: ""
          }
        } else {
          state.form = { ...props.selectRow }
        }
        nextTick(() => {
          formRef.value.clearValidate()
        })
      }
    })

    const rules = {
      month: [
        {
          required: true,
          message: "请输入月份",
        }
      ],
      waterNum: [
        {
          required: true,
          message: "请输入用水量",
        }
      ],
      waterPrice: [
        {
          required: true,
          message: "请输入水价",
        }
      ],
      elecNum: [
        {
          required: true,
          message: "请输入用电量",
        }
      ],
      elecPrice: [
        {
          required: true,
          message: "请输入电价",
        }
      ]
    }
    const handleSave = async () => {
      try {
        await formRef.value.validate()
        state.loading = true

        // 构建请求参数，根据接口文档格式
        // 确保月份格式为 YYYY-MM
        let monthValue = state.form.month
        console.log('原始月份数据:', monthValue, '类型:', typeof monthValue)

        if (monthValue) {
          if (typeof monthValue === 'object' && monthValue instanceof Date) {
            // 如果是Date对象，格式化为YYYY-MM
            const year = monthValue.getFullYear()
            const month = String(monthValue.getMonth() + 1).padStart(2, '0')
            monthValue = `${year}-${month}`
          } else if (typeof monthValue === 'string') {
            if (monthValue.includes('T')) {
              // 如果包含时间部分，只取日期部分的年月
              monthValue = monthValue.substring(0, 7)
            } else if (monthValue.length > 7) {
              // 如果是完整日期格式，只取年月部分
              monthValue = monthValue.substring(0, 7)
            }
          }
        }

        console.log('处理后月份数据:', monthValue)

        const params = {
          month: monthValue,
            waterNum: state.form.waterNum,
            waterPrice: state.form.waterPrice,
            elecNum: state.form.elecNum,
            elecPrice: state.form.elecPrice,
            repairCost: state.form.repairCost || 0,
            otherCost: state.form.otherCost || 0,
            repairRemark: state.form.repairRemark || "",
            otherRemark: state.form.otherRemark || "",
            tenantId: null
        }

        // 如果是编辑模式，需要包含id
        if (props.editType === 'edit' && state.form.id) {
          params.id = state.form.id
          params.createTime = state.form.createTime
          params.createUser = state.form.createUser
          params.lastModifyTime = state.form.lastModifyTime
          params.lastModifyUser = state.form.lastModifyUser
          params.tenantId = state.form.tenantId
        }

        console.log('请求参数:', params);

        let result
        if (props.editType === 'edit') {
          result = await updateCost(params)
        } else {
          result = await addCost(params)
        }

        const { code, message } = result
        if (code === 0) {
          ElMessage.success(message || (props.editType === 'edit' ? '更新成功' : '保存成功'))
          context.emit("update:modelValue", true) // 通知父组件刷新列表
        } else {
          ElMessage.error(message || (props.editType === 'edit' ? '更新失败' : '保存失败'))
        }
        state.loading = false
      } catch (error) {
        console.error('保存失败:', error)
        state.loading = false
        if (error !== false) { // 不是表单验证失败
          ElMessage.error('保存失败，请重试')
        }
      }
    }
    const handleClose = () => {
      context.emit("update:modelValue", false)
    };
    return {
      formRef,
      state,
      rules,
      handleSave,
      handleClose,
    }
  }
}
</script>

<style lang="scss" scoped>
.el-form {
  margin-top: 20px;
}
</style>