<template>
  <el-dropdown trigger="click" ref="dropdown" placement="bottom-end" :max-height="300">
    <el-input 
      ref="inputRef"
      :model-value="text" 
      readonly
      type="text"
      placeholder="请选择" 
      style="cursor: pointer"
      suffix-icon="el-icon-arrow-down"
      class="dropdown-select-tree-input"
    >
      <template #suffix v-if="text">
        <i class="el-icon-circle-close" @click.stop="handleClear"></i>
      </template>
    </el-input>
    <template #dropdown>
      <div class="dropdown-select-tree">
        <el-tree ref="treeRef" v-bind="attrs" :data="dataList" @node-click="handleNodeClick" @check-change="handleCheckChange"/>
      </div>
    </template>
  </el-dropdown>
</template>
<script>
// 下拉树组件
import { ElTree, ElInput, ElDropdown } from 'element-plus';
import { ref, onMounted, onUnmounted } from 'vue';
export default {
  components: {
    'el-tree': ElTree,
    'el-input': ElInput,
    'el-dropdown': ElDropdown,
  },
  emits: ['update:modelValue', 'change'],
  props: {
    modelValue: {
      type: [String, Number, Array],
      default: null,
    },
    text: {
      type: String,
      default: ''
    },
    multipe: {
      type: Boolean,
      default: false,
    },
    action: {
      type: Function,
      default: () =>  {}
    },
    idKey: {
      type: String,
      default: 'id'
    },
    queryKey: {
      type: String,
      default: 'id'
    },
    opts: {
      type: Object,
      default: () => ({})
    },
    query: {
      type: Object,
      default: () => ({})
    },
    lazy: {
      type: Boolean,
      default: true,
    },
    dataList: {
      type: Array,
    }
  },
  setup(props, context) {
    const treeRef = ref(null);
    const inputRef = ref(null);
    const dropdown = ref(null);
    const attrs = {
      lazy: props.lazy,
      showCheckbox: props.multipe,
      defaultCheckedKeys: props.modelValue,
      nodeKey: props.idKey,
      props: {
        children: 'children',
        label: 'label',
        isLeaf: 'isLeaf'
      },
      ...props.opts,
    };
    if(props.lazy) {
      attrs.load = async (node, resolve) => {
        const data = await props.action({ [props['queryKey']]: node.data?.[props['idKey']] ?? 0, ...props.query });
        if(!Array.isArray(data)) {
          resolve([]);
          return;
        }
        resolve(data);
      };
    }
    const handleCheckChange = () => {
      const data = treeRef.value.getCheckedNodes();
      context.emit('update:modelValue', data.map(it => it[props.idKey]));
      context.emit('change', data);
      dropdown.value.visible = false;
    }
    const handleNodeClick = (data) => {
      if(!props.multipe) {
        context.emit('update:modelValue', data[props.idKey]);
        context.emit('change', data);
        dropdown.value.visible = false;
      }
    }
    const handleHideClick = (e) => {
      if(!treeRef.value.$el.contains(e.target) && !inputRef.value.$el.contains(e.target)) {
        dropdown.value.visible = false;
      }
    }
    const handleClear = () => {
      context.emit('update:modelValue', null);
      context.emit('change', null);
    }
    onMounted(() => {
      document.body.addEventListener('click', handleHideClick);
    });
    onUnmounted(() => {
      document.body.removeEventListener('click', handleHideClick);
    });
    return {
      attrs,
      handleCheckChange,
      handleNodeClick,
      handleClear,
      treeRef,
      inputRef,
      dropdown,
    }
  }
}
</script>
<style lang="scss" scoped>
.dropdown-select-tree{
  padding: 20px;
  box-sizing: border-box;
  width: 300px;
  overflow-x: auto;
  .el-tree{
    width: auto;
    display: inline-block;
    min-width: 100%;    
  }
}
.dropdown-select-tree-input{
  .el-icon-circle-close{
    display: none;
  }
  &:hover{
    .el-icon-circle-close{
      display: inline-block;
    }
  }
}
</style>