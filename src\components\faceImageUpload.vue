<template>
  <el-upload class="single-image-uploader" :show-file-list="false" :before-upload="beforeUpload" action="">
    <el-image v-if="modelValue" :src="modelValue" class="images">
      <template #error>
        <div class="image-slot">
          <i class="el-icon-picture-outline"></i>
        </div>
      </template>
    </el-image>
    <kade-icon v-else size="40px" color="#999" :name="icon" />
  </el-upload>
</template>
<script>
// 单图片上传组件
import { ElUpload, ElImage } from 'element-plus';
import Icon from './icon';
export default {
  components: {
    'kade-icon': Icon,
    'el-image': ElImage,
    'el-upload': ElUpload,
  },
  emits: ['update:modelValue'],
  props: {
    modelValue: {
      type: String,
      default: ''
    },
    icon: {
      type: String,
      default: 'iconai-up-img'
    },
    action: {
      type: Function,
      default: () => { }
    }
  },
  setup(props, context) {
    const beforeUpload = async (f) => {
      console.log(f);
      let file = await compressImage(f)
      console.log(1);
      const formData = new FormData();
      formData.append('file', file);
      const { data } = await props.action(formData);
      context.emit('update:modelValue', data);
      return false;
    }

    function compressImage(file) {
      let files;
      let fileSize = parseFloat(parseInt(file['size']) / 1024 / 1024).toFixed(2)
      console.log("---------图片------大小--   ", fileSize);
      const read = new FileReader();
      read.readAsDataURL(file);
      const fileName = file.name;
      return new Promise(function (resolve) {
        read.onload = function (e) {
          let img = new Image();
          img.src = e.target.result;
          img.onload = function () {
            // 默认按比例压缩
            let w = img.width;
            let h = img.height;
            // 生成canvas
            let canvas = document.createElement("canvas");
            let ctx = canvas.getContext("2d");
            // 创建属性节点
            canvas.setAttribute("width", w);
            canvas.setAttribute("height", h);
            ctx.drawImage(this, 0, 0, w, h);
            var sizeLimit = 1060; //单位px，逻辑像素，欲压缩到的最大边的px数
            if (Math.max(w, h) > sizeLimit) {
              var percent = sizeLimit / Math.max(w, h);
              w = Math.trunc(w * percent)
              h = Math.trunc(h * percent)
            }
            console.log(w, h);
            // const dpr = wx.getSystemInfoSync().pixelRatio; //获取设备像素比，物理像素和逻辑像素的比值
            //重点：DrawImage使用的物理像素，canvas的width、height一定要设置，否则，图像会变形。因为系统给的初始值和你图片的比例不一致
            canvas.width = w;
            canvas.height = h;
            ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
            //使用jpeg格式，比png要小一个数量级
            let dataURL = canvas.toDataURL("image/jpeg", 0.7);
            // 回调函数返回file的值（将base64编码转成file）
            files = dataURLtoFile(dataURL, fileName); // 如果后台接收类型为base64的话这一步可以省略
            console.log(files);
            // 回调函数返回file的值（将base64转为二进制）
            //let fileBinary = dataURLtoBlob(base64);
            resolve(files);
          };
        };
      });
    }

    /*   function dataURLtoBlob(dataurl) {
        let arr = dataurl.split(",");
        let mime = arr[0].match(/:(.*?);/)[1];
        let bstr = atob(arr[1]);
        let n = bstr.length;
        let u8arr = new Uint8Array(n);
        while (n--) {
          u8arr[n] = bstr.charCodeAt(n);
        }
        return new Blob([u8arr], { type: mime });
      } */

    // base64转码（将base64编码转回file文件）  此方法我没用到
    // eslint-disable-next-line no-unused-vars
    function dataURLtoFile(dataurl, fileName) {
      let arr = dataurl.split(",");
      let mime = arr[0].match(/:(.*?);/)[1];
      let bstr = atob(arr[1]);
      let n = bstr.length;
      let u8arr = new Uint8Array(n);
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
      }
      if (fileName) {
        return new File([u8arr], fileName);
      } else {
        return new File([u8arr], { type: mime });
      }
    }


    return {
      beforeUpload,
    }
  }
}
</script>
<style lang="scss">
.single-image-uploader {
  width: 100px;
  height: 100px;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  overflow: hidden;

  .el-upload {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100px;
    height: 100px;
  }

  .images {
    width: 100px;
    height: 100px;
  }

  .image-slot {
    width: 100px;
    height: 100px;
    display: flex;
    justify-content: center;
    align-items: center;

    i {
      font-size: 50px;
      color: #999;
    }
  }
}
</style>