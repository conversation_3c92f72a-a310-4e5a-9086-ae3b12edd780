<template>
  <kade-tab-wrap :tabs="tabs" v-model="state.tab">
    <template #jccs>
      <kade-basic-params />
    </template>
    <template #gjcs>
      <kade-senior-params />
    </template>
    <template #klcs>
      <kade-card-params />
    </template>
  </kade-tab-wrap>
</template>
<script>
import { reactive, onMounted } from 'vue'
import { useStore } from "vuex";
import basicParams from "./basicParams";
import seniorParams from "./seniorParams"
import cardParams from "./cardParams"
const tabs = [
  { name: "jccs", label: "基础参数" },
  { name: "klcs", label: "卡类参数" },
  { name: "gjcs", label: "高级参数" },
];
export default {
  components: {
    "kade-basic-params": basicParams,
    "kade-senior-params": seniorParams,
    "kade-card-params": cardParams,
  },
  props: {
    data: {
      type: String,
      default: ""
    }
  },
  setup(props) {
    const store = useStore();
    const state = reactive({
      tab: "",
    });
    onMounted(async () => {
      await store.dispatch("hydropowerDevice/infiniteWater/getParams", props.data.paramId);
      state.tab = "jccs"
    });
    return {
      tabs,
      state,
    };
  }
}
</script>