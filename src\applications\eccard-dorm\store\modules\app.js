import { markRaw, defineAsyncComponent } from 'vue';
import { getDictionary } from '@/applications/unified_portal/api';
import {
  actions as mixinActions,
  state as mixinState,
  getters as mixinGettgers,
} from '@/utils/tab.mixin';

const dictionary = localStorage.getItem(`kade_cache_dictionary`);
const state = {
  ...mixinState,
  dictionary: dictionary ? JSON.parse(dictionary) : [],
  applyTypes: [],
  componentMap: {
    building: markRaw(defineAsyncComponent(() =>
      import( /* webpackChunkName: "baseDataManager" */ '../../views/baseDataManager/Building'))),
    roomType: markRaw(defineAsyncComponent(() =>
      import( /* webpackChunkName: "baseDataManager" */ '../../views/baseDataManager/roomType'))),
    room: markRaw(defineAsyncComponent(() =>
      import( /* webpackChunkName: "baseDataManager" */ '../../views/baseDataManager/room'))),
    dormManager: markRaw(defineAsyncComponent(() =>
      import( /* webpackChunkName: "baseDataManager" */ '../../views/baseDataManager/dormManager'))),
    /* 宿舍考勤 */
    attendanceAnalysis: markRaw(defineAsyncComponent(() =>
      import( /* webpackChunkName: "checkWork" */ '../../views/checkWork/attendanceAnalysis'))),
    attendanceRecord: markRaw(defineAsyncComponent(() =>
      import( /* webpackChunkName: "checkWork" */ '../../views/checkWork/attendanceRecord'))),
    attendanceResult: markRaw(defineAsyncComponent(() =>
      import( /* webpackChunkName: "checkWork" */ '../../views/checkWork/attendanceResult'))),
    attendanceTime: markRaw(defineAsyncComponent(() =>
      import( /* webpackChunkName: "checkWork" */ '../../views/checkWork/attendanceTime'))),
    buildingAttendanceSummary: markRaw(defineAsyncComponent(() =>
      import( /* webpackChunkName: "checkWork" */ '../../views/checkWork/buildingAttendanceSummary'))),
    classAttendanceSummary: markRaw(defineAsyncComponent(() =>
      import( /* webpackChunkName: "checkWork" */ '../../views/checkWork/classAttendanceSummary'))),
    manualCheck: markRaw(defineAsyncComponent(() =>
      import( /* webpackChunkName: "checkWork" */ '../../views/checkWork/manualCheck'))),
    roomAttendanceSummary: markRaw(defineAsyncComponent(() =>
      import( /* webpackChunkName: "checkWork" */ '../../views/checkWork/roomAttendanceSummary'))),
    attendanceGroup: markRaw(defineAsyncComponent(() =>
      import( /* webpackChunkName: "checkWork" */ '../../views/checkWork/attendanceGroup'))),
    leave: markRaw(defineAsyncComponent(() =>
      import( /* webpackChunkName: "checkWork" */ '../../views/checkWork/leave'))),

    /* 参数管理 */
    messagePushSet: markRaw(defineAsyncComponent(() => import( /* webpackChunkName: "parameterManager" */ '../../views/parameterManager/messagePushSet'))),
    bigScreenParameter: markRaw(defineAsyncComponent(() => import( /* webpackChunkName: "parameterManager" */ '../../views/parameterManager/bigScreenParameter'))),

    /* 宿舍水电 */
    hydropowerBalance: markRaw(defineAsyncComponent(() =>
      import( /* webpackChunkName: "checkWork" */ '../../views/hydropower/hydropowerBalance'))),
    hydropowerPay: markRaw(defineAsyncComponent(() =>
      import( /* webpackChunkName: "checkWork" */ '../../views/hydropower/hydropowerPay'))),
    hydropowerUse: markRaw(defineAsyncComponent(() =>
      import( /* webpackChunkName: "checkWork" */ '../../views/hydropower/hydropowerUse'))),

      /* 宿舍设备管理 */
      roomDevice: markRaw(defineAsyncComponent(() => import( /* webpackChunkName: "room" */ '../../views/dormDevice/roomDevice'))),
      unitDevice: markRaw(defineAsyncComponent(() => import( /* webpackChunkName: "room" */ '../../views/dormDevice/unitDevice'))),

    /* 住宿管理 */
    roomAssign: markRaw(defineAsyncComponent(() => import( /* webpackChunkName: "room" */ '../../views/room/roomAssign'))),
    bedAssign: markRaw(defineAsyncComponent(() => import( /* webpackChunkName: "room" */ '../../views/room/bedAssign'))),
    refundRoom: markRaw(defineAsyncComponent(() => import( /* webpackChunkName: "room" */ '../../views/room/refundRoom'))),
    selfChoice: markRaw(defineAsyncComponent(() => import( /* webpackChunkName: "room" */ '../../views/room/selfChoice'))),
    roomMoveIn: markRaw(defineAsyncComponent(() => import( /* webpackChunkName: "room" */ '../../views/room/roomMoveIn'))),
    classMoveIn: markRaw(defineAsyncComponent(() => import( /* webpackChunkName: "room" */ '../../views/room/classMoveIn'))),
    studentMoveIn:markRaw(defineAsyncComponent(() => import( /* webpackChunkName: "room" */ '../../views/room/studentMoveIn'))),

    /*日常管理*/
    hygieneCheck: markRaw(defineAsyncComponent(() => import( /* webpackChunkName: "dormDaily" */ '../../views/dormDaily/hygieneCheck'))),
    breachPrinciple: markRaw(defineAsyncComponent(() => import( /* webpackChunkName: "dormDaily" */ '../../views/dormDaily/breachPrinciple'))),
    notify: markRaw(defineAsyncComponent(() => import( /* webpackChunkName: "dormDaily" */ '../../views/dormDaily/notify'))),
    assets: markRaw(defineAsyncComponent(() => import( /* webpackChunkName: "dormDaily" */ '../../views/dormDaily/assets'))),
    visitor: markRaw(defineAsyncComponent(() => import( /* webpackChunkName: "dormDaily" */ '../../views/dormDaily/visitor'))),
    goodsInout: markRaw(defineAsyncComponent(() => import( /* webpackChunkName: "dormDaily" */ '../../views/dormDaily/goodsInout'))),
    repairInfo: markRaw(defineAsyncComponent(() => import( /* webpackChunkName: "dormDaily" */ '../../views/dormDaily/repairInfo'))),
    
    /*宿舍出入 */
    roomLockAuthority: markRaw(defineAsyncComponent(() => import( /* webpackChunkName: "dormDaily" */ '../../views/dormInOut/roomLockAuthority'))),
    passRecord: markRaw(defineAsyncComponent(() => import( /* webpackChunkName: "dormDaily" */ '../../views/dormInOut/passRecord'))),

    /* 总览 */
    dormHome: markRaw(defineAsyncComponent(() => import( /* webpackChunkName: "dormDaily" */ '../../views/home')))


  },
  customMenus: [],
}
  ;
const mutations = {
  updateState(state, { key, payload }) {
    state[key] = payload;
  },
};
const actions = {
  ...mixinActions,
  async loadDictionary({ commit }) {
    const { data } = await getDictionary();
    localStorage.setItem(`kade_cache_dictionary`, JSON.stringify(data));
    commit('updateState', { key: 'dictionary', payload: data });
  },
};

const getters = {
  ...mixinGettgers,
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters,
}
