<template>
  <el-dialog :model-value="modelValue" title="设置门禁权限" width="1700px" :before-close="handleClose"
    :close-on-click-modal="false">
    <div style="padding:20px 0 0" v-loading="state.loading">
      <kade-table-wrap title="门禁信息" icon="none">
        <el-divider></el-divider>
        <div class="padding-box">
          <el-table :data="rowList" border>
            <el-table-column label="门禁分组" prop="groupName" align="center"></el-table-column>
            <el-table-column label="门禁区域" prop="areaName" align="center"></el-table-column>
            <el-table-column label="设备类型" prop="deviceTypeName" align="center"></el-table-column>
            <el-table-column label="设备名称" prop="deviceName" align="center"></el-table-column>
          </el-table>
        </div>
      </kade-table-wrap>
      <kade-table-wrap title="授权人员" icon="none">
        <el-divider style="margin-bottom:20px"></el-divider>
        <kade-select-table :isShow="modelValue" :value='[]' :reqFnc="authManageUserList"
          :selectCondition="selectCondition" :column="column" :params="state.params" @change="personChange" />
      </kade-table-wrap>
      <kade-table-wrap title="其它选项" icon="none">
        <el-divider></el-divider>
        <el-form style="margin-top:20px" label-width="120px" size="mini" ref="formRef" :rules="rules"
          :model="state.form" inline>
          <el-form-item label="时段：" prop="periodNo">
            <el-select v-model="state.form.periodNo">
              <el-option v-for="(item, index) in state.periodNoList" :key="index" :label="item.periodNo"
                :value="item.periodNo"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="一次刷卡限制：" prop="oneCard">
            <el-radio-group v-model="state.form.oneCard">
              <el-radio :label="item.value" v-for="(item, index) in boolList" :key="index">{{ item.label }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="授权时间：" prop="beginTime">
            <el-date-picker v-model="state.form.beginTime" type="datetime" placeholder="请选择时间" />
          </el-form-item>
          <el-form-item label="过期时间：" prop="endTime">
            <el-date-picker v-model="state.form.endTime" type="datetime" placeholder="请选择时间" />
          </el-form-item>
        </el-form>
        <div class="red" style="margin-left:20px">说明：时段和一次刷卡限制仅支持门禁控制器和通道控制器设备</div>
      </kade-table-wrap>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose" size="mini">取消</el-button>
        <el-button type="primary" @click="submit" size="mini">保存</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script>
import { ElDialog, ElButton, ElDivider, ElTable, ElTableColumn, ElForm, ElFormItem, ElSelect, ElOption, ElRadioGroup, ElRadio, ElDatePicker, ElMessage } from "element-plus"
import { reactive, ref } from '@vue/reactivity'
import { useDict } from "@/hooks/useDict.js";
import { timeStr } from "@/utils/date.js"
import {
  getRolelist,
} from "@/applications/eccard-basic-data/api";
import { getCardTypeList } from "@/applications/eccard-iot/api";
import {
  getDevicePeriod, authManageAdd, authManageUserList
} from "@/applications/eccard-uac/api";
import selectTable from "@/components/table/selectTable.vue";
import { nextTick, onMounted, watch } from '@vue/runtime-core';
const column = [
  { label: "人员编号", prop: "userCode", isDict: false, width: "" },
  { label: "人员姓名", prop: "userName", isDict: false, width: "" },
  { label: "组织机构", prop: "deptName", isDict: false, width: "" },
  { label: "性别", prop: "userSex", isDict: true, width: "" },
  { label: "家属姓名", prop: "familyName", isDict: false, width: "" },

  { label: "身份类别", prop: "roleName", isDict: false, width: "", },
  { label: "卡类", prop: "cardTypeName", isDict: false, width: "" },
  { label: "卡片类别", prop: "cardCategoryName", isDict: true, width: "" },
];
const rules = {
  periodNo: [
    {
      required: true,
      message: "请选择时段",
      trigger: "change",
    },
  ],
  oneCard: [
    {
      required: true,
      message: "请选择一次刷卡限制",
      trigger: "change",
    },
  ],
  beginTime: [
    {
      required: true,
      message: "请选择授权时间",
      trigger: "change",
    },
  ],
  endTime: [
    {
      required: true,
      message: "请选择过期时间",
      trigger: "change",
    },
  ],
}
export default {
  components: {
    ElDialog, ElButton, ElDivider, ElTable, ElTableColumn, ElForm, ElFormItem, ElSelect, ElOption, ElRadioGroup, ElRadio, ElDatePicker,
    "kade-select-table": selectTable,
  },
  emits: ["update:modelValue"],
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    rowList: {
      type: Array,
      default: null
    }
  },
  setup(props, context) {
    const boolList = useDict("SYS_BOOL_INT")
    const formRef = ref(null)
    const state = reactive({
      loading: false,
      periodNoList: [],
      form: {},
      params: {
        currentPageKey: "currentPage",
        pageSizeKey: "pageSize",
        resListKey: "list",
        resTotalKey: "total",
        value: {
          cardCategory: "MASTER_CARD"
        },
        tagNameKey: "userName",
        valueKey: "id",
      },
      selectData: {},
    })
    const selectCondition = [
      { label: "组织机构", valueKey: "deptPath", dataKey: "deptPath", placeholder: "请选择", isSelect: false, isTree: "dept" },
      {
        label: "身份类别", valueKey: "userRole", placeholder: "请选择", isSelect: true,
        select: {
          list: [],
          option: {
            label: "roleName",
            value: "id",
          },
        },
      },
      {
        label: "性别", valueKey: "userSex", placeholder: "请输入", isSelect: true,
        select: {
          list: useDict("SYS_SEX"),
          option: {
            label: "label",
            value: "value",
          },
        },
      },
      {
        label: "卡类", valueKey: "cardType", placeholder: "请输入", isSelect: true,
        select: {
          list: [],
          option: {
            label: "ctName",
            value: "ctCode",
          },
        },
      },
      {
        label: "卡片类别", valueKey: "cardCategory", placeholder: "请输入", isSelect: true,
        select: {
          list: useDict("PERSON_CARD_CATEGORY"),
          option: {
            label: "label",
            value: "value",
          },
        },
      },
      { label: "编号/姓名", valueKey: "keyWord", placeholder: "请输入", isSelect: false, },
    ]
    watch(() => props.modelValue, val => {
      if (val) {
        state.form = {}
        nextTick(() => {
          formRef.value.clearValidate()
        })
      }
    })
    //获取卡片类型
    const queryCardTypeList = () => {
      getCardTypeList().then((res) => {
        selectCondition[3].select.list = res.data;
      });
    };
    const queryRolelist = async () => {
      let { data } = await getRolelist();
      selectCondition[1].select.list = data;
    };
    const getPeriodNoList = () => {
      getDevicePeriod().then((res) => {
        console.log(res)
        state.periodNoList = res.data.map(item => {
          return {
            periodNo: item.periodNo
          }
        })
        console.log(state.periodNoList)
      })
    }
    const personChange = (val) => {
      state.selectData = { ...val }
    }
    const submit = () => {
      if (!state.selectData.isSelectAll && !state.selectData.list) {
        return ElMessage.error("请选择人员！")
      }
      formRef.value.validate(async valid => {
        if (valid) {
          state.loading = true
          try {
            let params = {
              userInfoList: state.selectData.list.map(item => {
                return {
                  cardCategory: item.cardCategoryName,
                  familyId: item.familyUserId,
                  userId: item.id
                }
              }),
              deviceInfoList: props.rowList,
              ...state.form,
              beginTime: timeStr({ ...state.form }.beginTime),
              endTime: timeStr({ ...state.form }.endTime),
            }
            console.log(params);
            let { message, code } = await authManageAdd(params)
            if (code === 0) {
              ElMessage.success(message)
              context.emit("update:modelValue", true)
            }
            state.loading = false
          }
          catch {
            state.loading = false
          }
        }
      })
    }
    const handleClose = () => {
      context.emit("update:modelValue", false)
    }
    onMounted(() => {
      queryCardTypeList()
      queryRolelist()
      getPeriodNoList()
    })
    return {
      authManageUserList,
      column,
      rules,
      boolList,
      formRef,
      state,
      selectCondition,
      personChange,
      submit,
      handleClose
    }
  }
}
</script>
<style lang="scss" scoped>
:deep(.kade-table-wrap) {
  margin-bottom: 20px;
}

.padding-box {
  padding: 10px 10px 0
}

.el-divider--horizontal {
  margin: 0
}

:deep(.box) {
  padding: 10px 10px 0;

  .el-form {
    .el-select {
      width: 150px;
    }

    .el-input {
      width: 150px
    }
  }
}

.el-table {
  border-left: 1px solid #EBEEF5;
  border-right: 1px solid #EBEEF5;
}
</style>