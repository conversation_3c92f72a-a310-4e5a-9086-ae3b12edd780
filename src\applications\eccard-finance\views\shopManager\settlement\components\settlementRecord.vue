<template>
  <div class="settlement-record">
    <div class="padding-form-box">
      <el-form inline size="small" label-width="100px">
        <el-form-item label="结算类型:">
          <el-select v-model="querys.settlementType">
            <el-option v-for="(item,index) in merchantTypeList" :key="index" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="结算方式:">
          <el-select v-model="querys.settlementMode">
            <el-option v-for="(item,index) in merchantModeList" :key="index" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="选择周期:">
          <el-date-picker v-model="state.daterange" type="daterange" placeholder="请选择" :clearable="false" />
        </el-form-item>
        <el-form-item>
          <el-button :disabled="!id" @click="handleSearch" icon="el-icon-search" type="primary">查询</el-button>
          <el-button @click="handleReset" icon="el-icon-refresh-right">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <kade-table-wrap title="记录列表">
      <template #extra>
        <el-button :disabled="!id || options.dataList.length === 0" @click="handleDownloadDetail" type="primary" icon="el-icon-daochu" size="small">下载查询明细</el-button>
        <el-button :disabled="!id" icon="el-icon-printer" @click="handlePrint" size="small">打印</el-button>
      </template>
      <el-table style="width: 100%" :data="options.dataList" v-loading="options.loading" border stripe>
        <el-table-column label="结算类型" prop="settlementType" align="center" show-overflow-tooltip>
          <template #default="scope">
            {{dictionaryFilter(scope.row.settlementType)}}
          </template>
        </el-table-column>
        <el-table-column label="结算周期" align="center" prop="tenantMobile" show-overflow-tooltip>
          <template #default="scope">
            {{scope.row.settlementBeginDate+`~`+scope.row.settlementEndDate}}
          </template>
        </el-table-column>
        <el-table-column label="结算时间" align="center" show-overflow-tooltip prop="settlementDate"></el-table-column>
        <el-table-column label="结算方式" align="center" show-overflow-tooltip prop="tenantEmail">
          <template #default="scope">
            {{dictionaryFilter(scope.row.settlementMode)}}
          </template>
        </el-table-column>
        <el-table-column label="前期结余金额(元)" align="center" show-overflow-tooltip prop="previousBalanceAmount"></el-table-column>
        <el-table-column label="本期结算金额(元)" align="center" show-overflow-tooltip prop="currentSettlementAmount"></el-table-column>
        <el-table-column label="本期结余金额(元)" align="center" show-overflow-tooltip prop="currentSettlementAmount">
          <template #default="scope">
            {{scope.row.previousBalanceAmount-scope.row.currentSettlementAmount}}
          </template>
        </el-table-column>
        <el-table-column label="结算人" align="center" show-overflow-tooltip prop="settlementUser"></el-table-column>
        <!--         <el-table-column label="操作" align="center" width="120" fixed="right">
          <template>
            <el-button type="text" size="mini">查看详情</el-button>
          </template>
        </el-table-column> -->
      </el-table>
      <div class="pagination">
        <el-pagination background v-model:current-page="options.currentPage" layout="total, sizes, prev, pager, next, jumper" :page-sizes="[10, 20, 50, 100, 200]" :total="options.total" v-model:page-size="options.pageSize" @current-change="(val) => pageChange(val)" @size-change="(val) => sizeChange(val)">
        </el-pagination>
      </div>
    </kade-table-wrap>
  </div>
</template>
<script>
import {
  ElButton,
  ElDatePicker,
  ElForm,
  ElFormItem,
  ElSelect,
  ElOption,
  ElPagination,
  ElTable,
  ElTableColumn,
} from "element-plus";
import { reactive, onMounted, watch } from "vue";
import moment from "moment";
import { downloadXlsx,print } from "@/utils";
import { useDict } from "@/hooks/useDict.js";
import { usePagination } from "@/hooks/usePagination";
import {
  getMerchantSettlementListByPage,
  downMerchantSettlementList,
  getMerchantSettlementListByPrint,
} from "@/applications/eccard-finance/api";
export default {
  components: {
    "el-date-picker": ElDatePicker,
    "el-button": ElButton,
    "el-table": ElTable,
    "el-table-column": ElTableColumn,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    "el-pagination": ElPagination,
    ElSelect,
    ElOption,
  },
  props: {
    id: {
      type: [String, Number, undefined, null],
    },
  },
  setup(props) {
    const merchantTypeList = useDict("MERCHANT_SETTLEMENT_TYPE");
    const merchantModeList = useDict("MERCHANT_SETTLEMENT_MODE");
    const endDate = new Date();
    const beginDate = new Date();
    beginDate.setDate(beginDate.getDate() - 7);
    const { options, search, querys, loadData, pageChange, sizeChange } =
      usePagination(
        getMerchantSettlementListByPage,
        {
          beginDate: moment(beginDate).format("YYYY-MM-DD"),
          endDate: moment(endDate).format("YYYY-MM-DD"),
        },
        {},
        { currentPage: "currentPage", pageSize: "pageSize" },
        true
      );
    const state = reactive({
      daterange: [beginDate, endDate],
    });
    const handleSearch = () => {
      querys.beginDate = moment(state.daterange[0]).format("YYYY-MM-DD");
      querys.endDate = moment(state.daterange[1]).format("YYYY-MM-DD");
      search();
    };
    const handleReset = () => {
      state.daterange = [beginDate, endDate];
    };
    const handleDownloadDetail = async () => {
      const res = await downMerchantSettlementList(querys);
      downloadXlsx(res, "结算明细.xlsx");
      console.log(res);
    };
    const handlePrint =async () => {
      let { code, data } =await getMerchantSettlementListByPrint(querys);
      if (code === 0) {
        print(data);
      }
    };
    watch(
      () => props.id,
      (n, o) => {
        if (n !== o) {
          querys.merchantId = props.id;
          loadData();
        }
      }
    );
    onMounted(() => {
      if (props.id) {
        querys.merchantId = props.id;
        loadData();
      }
    });
    return {
      merchantTypeList,
      merchantModeList,
      options,
      querys,
      loadData,
      pageChange,
      sizeChange,
      state,
      handleSearch,
      handleReset,
      handleDownloadDetail,
      handlePrint,
    };
  },
};
</script>