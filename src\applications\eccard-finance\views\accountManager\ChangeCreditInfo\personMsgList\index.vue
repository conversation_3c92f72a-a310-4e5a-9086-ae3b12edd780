<template>
  <div class="person-msg-list">
    <kade-table-filter @search="queryPersonList()" @reset="reset()">
      <el-form inline size="small" label-width="100px">
        <el-form-item label="关键字:">
          <el-input placeholder="姓名或编号关键字搜索" v-model="state.form.keyWord"></el-input>
        </el-form-item>
        <el-form-item label="身份类别:">
          <el-select clearable v-model="state.form.userRoleId" placeholder="请选择">
            <el-option v-for="(item, index) in state.roleList" :key="index" :label="item.roleName" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <!--           <el-form-item label="账户状态:">
            <el-select clearable v-model="state.form.acctStatus" placeholder="请选择">
              <el-option v-for="(item, index) in state.accountStatusList" :key="index" :label="item.dictValue" :value="item.dictCode">
              </el-option>
            </el-select>
          </el-form-item> -->
      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="人员信息列表">
      <template #extra>
        <el-button @click="dialogBtn()" icon="el-icon-plus" size="small" class="btn-purple">冲正</el-button>
      </template>
      <el-table style="width: 100%" :data="state.personList" @row-click="selectCurrentRow" ref="table"
        @setCurrentRow="setCurrentRow(state.personList[0])" v-loading="state.getPersonLoading" highlight-current-row
        border stripe>
        <el-table-column show-overflow-tooltip width="150" label="用户编号" prop="userCode" align="center">
        </el-table-column>
        <el-table-column show-overflow-tooltip width="153" prop="userName" label="姓名" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip label="组织机构" prop="deptName" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip label="身份类别" prop="userRoleName" align="center"></el-table-column>
        <el-table-column label="账户状态" align="center">
          <template #default="scope">
            <div v-if="
              filterDictionary(
                scope.row.acctStatus,
                state.accountStatusList
              ) == '正常'
            ">
              <el-switch inline-prompt :model-value="true" border-color="#ffffff">
              </el-switch>
            </div>
            <div v-else-if="
              filterDictionary(
                scope.row.acctStatus,
                state.accountStatusList
              ) == '冻结'
            ">
              <el-switch inline-prompt :model-value="false" inactive-color="#ff4949">
              </el-switch>
            </div>
            <div v-else>
              {{
                  filterDictionary(
                    scope.row.acctStatus,
                    state.accountStatusList
                  )
              }}
            </div>
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip label="卡片类别" prop="ctName" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip label="卡片状态" align="center"><template #default="scope">
            {{ filterDictionary(scope.row.cardStatus, state.cardStatusList) }}
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip label="联系方式" prop="userTel" align="center" width="120"></el-table-column>
        <el-table-column label="操作" align="center">
          <template #default="scope">
            <el-button type="text" @click="listDetails(scope.row, 'see')" size="mini">
              查看详情</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination background :current-page="state.form.currentPage" :page-size="state.form.pageSize"
          layout="total, sizes, prev, pager, next, jumper" :page-sizes="[6, 10, 20, 50, 100]" :total="state.total"
          @current-change="handlePageChange" @size-change="handleSizeChange">
        </el-pagination>
      </div>
    </kade-table-wrap>
    <el-dialog :model-value="state.isRighting" title="冲正" width="90%" :before-close="offRighting"
      :close-on-click-modal="false">
      <kade-righting-person :personDetail="state.personDetail" @off="state.isRighting = false"
        @success="state.isRighting = false" />
    </el-dialog>
    <el-dialog :model-value="state.isPersonDetail" title="人员信息详情" width="60%" :before-close="offPersonDetail"
      :close-on-click-modal="false">
      <kade-person-details :personDetail="state.personDetail" @off="state.isPersonDetail = false" />
    </el-dialog>
  </div>
</template>

<script>
import {
  ElDialog,
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElTable,
  ElTableColumn,
  ElSwitch,
  ElPagination,
  ElMessage,
} from "element-plus";
import rightingPerson from "./components/rightingPerson.vue";
import personDetails from "../../components/personDetails.vue";
import { requestDate } from "@/utils/reqDefaultDate.js";
import { reactive } from "@vue/reactivity";
import {
  getPersonList,
  getPersonAccountInfo,
  getRolelist,
  getWalletActiveList,
} from "@/applications/eccard-finance/api";
import { onMounted } from "@vue/runtime-core";
import { useStore } from "vuex";
export default {
  components: {
    "el-button": ElButton,
    "el-input": ElInput,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    "el-select": ElSelect,
    "el-option": ElOption,
    "el-table": ElTable,
    "el-table-column": ElTableColumn,
    "el-pagination": ElPagination,
    "el-switch": ElSwitch,
    ElDialog,
    "kade-righting-person": rightingPerson,
    "kade-person-details": personDetails,
  },

  setup() {
    const store = useStore();
    const state = reactive({
      isPersonDetail: false,
      total: 0,
      form: {
        currentPage: 1,
        pageSize: 6,
        acctStatus: "NORMAL_ACCOUNT",
        deptPath: null,
        keyWord: null,
        userRoleId: null,
      },

      personList: [],
      getPersonLoading: false,
      departCheckList: [], //组织机构列表
      roleList: [], //身份类别列表
      accountStrategyList: [], //开户策略列表
      cardTypeList: [], //卡片类型列表
      selectPerson: "",
      personDetail: {},
      walletList: [], //个人钱包列表
      accountStatusList: JSON.parse(
        localStorage.getItem("kade_cache_dictionary")
      ).filter((item) => item.dictType == "PERSON_ACCOUNT_STATUS"), //账户状态
      cardStatusList: JSON.parse(
        localStorage.getItem("kade_cache_dictionary")
      ).filter((item) => item.dictType == "PERSON_CARD_STATUS"), //卡片状态
    });
    const filterDictionary = (val, dictionaryList) => {
      for (let i of dictionaryList) {
        if (val == i.dictCode) {
          return i.dictValue;
        }
      }
    };
    //获取人员信息列表
    const queryPersonList = () => {
      state.getPersonLoading = true;
      getPersonList(state.form)
        .then((res) => {
          state.personList = res.data.list;
          state.total = res.data.total;
          state.getPersonLoading = false;
        })
        .catch(() => {
          state.getPersonLoading = false;
        });
    };
    //获取身份类别
    const queryRolelist = () => {
      getRolelist().then((res) => {
        state.roleList = res.data;
      });
    };

    const cascaderChange = (val) => {
      if (val) {
        state.form.deptPath = val.length && val[val.length - 1];
      } else {
        state.form.deptPath = "";
      }
    };

    const selectCurrentRow = (val) => {
      store.commit("rightingData/updateState", {
        key: "selectPerson",
        payload: val,
      });
      let form = {
        beginDate: requestDate()[0],
        endDate: requestDate()[1],
        currentPage: 1,
        pageSize: 6,
        userId: store.state.rightingData.selectPerson.userId,
        maxAmount: null,
        minAmount: null,
        sysUserId: null,
      };
      store.dispatch("rightingData/queryRightingList", form);
    };
    const dialogBtn = () => {
      if (store.state.rightingData.selectPerson) {
        getPersonAccountInfo({ userId: store.state.rightingData.selectPerson.userId }).then(res => {
          state.personDetail = res.data;
          state.isRighting = true;
          store.commit("rightingData/updateState", {
            key: "isOff",
            payload: true,
          });
        })

      } else {
        ElMessage.error("请选择人员！");
      }
    };

    const listDetails = (val, type) => {
      if (type == "see") {
        getPersonAccountInfo({ userId: val.userId }).then((res) => {
          state.personDetail = res.data;
          getWalletActiveList().then((res) => {
            let data = res.data.map((item) => {
              return { ...item, walletStatus: "WALLET_NOT_ACTIVE" };
            });
            for (let i = 0; i < data.length; i++) {
              for (let j = 0; j < state.personDetail.acctWallets.length; j++) {
                if (
                  data[i].walletCode ==
                  state.personDetail.acctWallets[j].walletCode
                ) {
                  data[i] = state.personDetail.acctWallets[j];
                }
              }
            }
            console.log(data);
            state.personDetail.acctWallets = data;
            state.isPersonDetail = true;
          });
        });
      }
    };
    const offRighting = () => {
      state.isRighting = false;
    };
    const offPersonDetail = () => {
      state.isPersonDetail = false;
    };
    const handlePageChange = (val) => {
      state.form.currentPage = val;
      queryPersonList();
    };
    const handleSizeChange = (val) => {
      state.form.currentPage = 1;
      state.form.pageSize = val;
      queryPersonList();
    };
    //重置
    const reset = () => {
      state.form = {
        currentPage: 1,
        pageSize: 6,
        acctStatus: "NORMAL_ACCOUNT",
        deptPath: "",
        keyWord: "",
        userRoleId: "",
      };
    };
    //关闭模态框
    const offOpenAccount = () => {
      state.isOpenAccount = false;
    };

    onMounted(() => {
      queryPersonList();
      queryRolelist();
    });
    return {
      state,
      cascaderChange,
      queryPersonList,
      handlePageChange,
      handleSizeChange,
      selectCurrentRow,
      offRighting,
      offPersonDetail,
      reset,
      offOpenAccount,
      dialogBtn,
      listDetails,
      filterDictionary,
    };
  },
};
</script>
<style lang="scss" scoped>
.person-msg-list {
  .el-divider--horizontal {
    margin: 0 0 10px;
  }

  .el-form-item--small.el-form-item {
    margin-bottom: 10px;
  }

  .search-box {
    padding: 0 20px;
  }

  .el-dialog__header {
    border-bottom: 1px solid #efefef;
  }

  .el-dialog__headerbtn {
    font-size: 20px;
    top: 10px;
    right: 10px;
  }

  .el-dialog__header {
    padding: 10px 20px;
  }

  .el-overlay {
    position: absolute;
    background-color: rgba(0, 0, 0, 0.2);
  }

  .el-dialog__footer {
    text-align: center;
  }

  .el-dialog__title {
    font-size: 14px;
  }

  .el-table {
    border-left: none;

    tr>th:last-child,
    tr>td:last-child {
      border-right: none !important;
    }

    .el-table--border,
    .el-table--group {
      border: none;
    }

    &::after {
      background-color: transparent;
    }

    .el-table__row {
      height: 48px;
    }
  }
}
</style>
