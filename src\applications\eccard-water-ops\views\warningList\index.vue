<template>
  <div class="padding-box">
    <kade-table-filter @search="handleSearch" @reset="handleReset">
      <el-form inline size="small" label-width="80px">
        <el-form-item label="预警类型">
          <el-select v-model="state.form.warningType" placeholder="请选择预警类型">
            <el-option v-for="(item,index) in warningTypeList" :key="index" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="所属区域">
          <kade-area-select-tree style="width: 100%" :value="state.form.areaPath" valueKey="areaPath" :multiple="false" @valueChange="(val) => (state.form.areaPath = val.areaPath)" />
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <div class="table-box" v-loading="state.loading">
      <kade-table-wrap title="预警列表">
        <el-table border :data="state.dataList" @selection-change="handleSelectChange">
          <el-table-column prop="warningType" label="预警类型" align="center">
            <template #default="scope">
              {{ filterDictionary(scope.row.warningType,warningTypeList ) }}
            </template>
          </el-table-column>
          <el-table-column prop="areaName" label="区域" align="center"></el-table-column>
          <el-table-column prop="deviceName" label="预警设备" align="center"></el-table-column>
          <el-table-column prop="warningContent" label="预警内容" align="center"></el-table-column>
          <el-table-column prop="warningTime" label="预警时间" align="center">
            <template #default="scope">
              {{ timeStr(scope.row.warningTime) }}
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination">
          <el-pagination :currentPage="state.form.currentPage" :pageSize="state.form.pageSize" :page-sizes="[5, 10, 20, 30, 40]" background layout="total, sizes, prev, pager, next, jumper" :total="state.total" @size-change="handleSizeChange" @current-change="handleCurrentChange">
          </el-pagination>
        </div>
      </kade-table-wrap>
    </div>
    <kade-build-edit :dialogVisible="state.dialogVisible" :type="state.type" :rowData="state.rowData" @close="close"></kade-build-edit>
    <kade-build-import :dialogTable="state.dialogTable" @close="close"></kade-build-import>
  </div>
</template>

<script>
import {
  ElForm,
  ElFormItem,
  ElSelect,
  ElOption,
  ElTable,
  ElTableColumn,
  ElPagination,
} from "element-plus";
import { onMounted, reactive } from "vue";
import { filterDictionary } from "@/utils";
import {timeStr} from "@/utils/date"
import { warningTypeList } from "@/applications/eccard-water-ops/dict"
import { opsWaterWarnRecordPage } from "@/applications/eccard-water-ops/api/warningList";
import areaSelectTree from "@/components/tree/areaSelectTree.vue";
export default {
  components: {
    ElForm,
    ElFormItem,
    ElSelect,
    ElOption,
    ElTable,
    ElTableColumn,
    ElPagination,
    "kade-area-select-tree": areaSelectTree,
  },
  setup() {
    const state = reactive({
      loading: false,
      dialogVisible: false,
      dialogTable: false,
      type: "",
      rowData: {},
      form: {
        currentPage: 1,
        pageSize: 10
      },
      dataList: [],
      total: 0,
    });

    const getList = async () => {
      state.loading = true
      let { data: { list, total }, code } = await opsWaterWarnRecordPage(state.form)
      state.loading = false
      if (code === 0) {
        state.dataList = list
        state.total = total
      }
    }
    const handleSearch = () => {
      getList()
    }
    const handleReset = () => {
      state.form = {
        currentPage: 1,
        pageSize: 10
      }
    }
    const handleSizeChange = (val) => {
      state.form.currentPage = 1
      state.form.pageSize = val
      getList()
    };
    const handleCurrentChange = (val) => {
      state.form.currentPage = val
      getList()
    };
    onMounted(() => {
      getList()
    })
    return {
      timeStr,  
      filterDictionary,
      warningTypeList,
      state,
      handleSearch,
      handleReset,
      close,
      handleSizeChange,
      handleCurrentChange,
    };
  },
};
</script>

<style lang="scss" scoped>
</style>