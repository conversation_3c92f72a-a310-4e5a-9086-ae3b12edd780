<template>
  <el-form inline label-width="120px" size="small">
    <el-form-item label="所属区域：">
      <el-input :model-value="rowData.areaName" readonly></el-input>
    </el-form-item>
    <el-form-item label="所属设备类型：">
      <el-input :model-value="rowData.deviceTypeName" readonly></el-input>
    </el-form-item>
    <el-form-item label="网关机号：">
      <el-input :model-value="rowData.gatewayNo" readonly></el-input>
    </el-form-item>
    <el-form-item label="网关名称：">
      <el-input :model-value="rowData.gatewayName" readonly></el-input>
    </el-form-item>
    <el-form-item label="网关IP：">
      <el-input :model-value="rowData.gatewayIp" readonly></el-input>
    </el-form-item>
    <el-form-item  label="网关端口：">
      <el-input :model-value="rowData.gatewayPort" readonly></el-input>
    </el-form-item>
    <el-row>
      <el-col :span="24">
        <el-form-item label="备注：">
          <el-input :model-value="rowData.remarks" style="width:500px" type="textarea" readonly></el-input>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>
<script>
import { ElCol, ElRow, ElForm, ElFormItem, ElInput } from "element-plus";
export default {
  components: {
    ElCol,
    ElRow,
    ElForm,
    ElFormItem,
    ElInput,
  },
  props: {
    rowData: {
      type: Object,
      defalut: null,
    },
  },
};
</script>