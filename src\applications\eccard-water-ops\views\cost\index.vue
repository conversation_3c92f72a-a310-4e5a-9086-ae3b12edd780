  <template>
  <div class="padding-box">
    <kade-table-wrap title="成本录入列表" v-loading="state.loading">
      <template #extra>
        <el-button @click="handleEdit({},'add')" icon="el-icon-plus" size="small" class="btn-green">新建录入</el-button>
      </template>
      <el-table style="width: 100%" :data="state.dataList" @row-click="selectCurrentRow" ref="multipleTable" border stripe>
        <el-table-column width="80px" label="月份" prop="month" align="center"></el-table-column>
        <el-table-column width="100px" label="用水量" prop="waterNum" align="center"></el-table-column>
        <el-table-column width="100px" label="水价" prop="waterPrice" align="center"></el-table-column>
        <el-table-column width="100px" label="用电量" prop="elecNum" align="center"></el-table-column>
        <el-table-column width="100px" label="电价" prop="elecPrice" align="center"></el-table-column>
        <el-table-column width="100px" label="维修成本" prop="repairCost" align="center"></el-table-column>
        <el-table-column width="100px" label="其他成本" prop="otherCost" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip label="维修说明" prop="repairRemark" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip label="其他说明" prop="otherRemark" align="center"></el-table-column>
        <el-table-column width="200px" label="操作" align="center">
          <template #default="scope">
            <el-button size="mini" type="text" @click="handleEdit(scope.row,'edit')">编辑</el-button>
            <el-button size="mini" type="text" @click="handleDel(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <template #pagination>
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="state.form.currentPage"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="state.form.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="state.total">
        </el-pagination>
      </template>
    </kade-table-wrap>
    <kade-room-type-edit v-model:modelValue="state.isEdit" :editType="state.editType" :selectRow="state.selectRow" @update:modelValue="close" />
  </div>
</template> 
  <script>
import { ElButton, ElTable, ElTableColumn, ElPagination, ElMessageBox, ElMessage } from "element-plus"
import { reactive } from '@vue/reactivity'
import { onMounted } from '@vue/runtime-core';


import edit from "./components/edit"
import { getCostList, deleteCost } from "../../api/cost.js";
export default {
  components: {
    ElButton, ElTable, ElTableColumn, ElPagination,
    'kade-room-type-edit': edit,

  },
  setup() {
    const state = reactive({
      loading: false,
      isEdit: false,
      isRoomDeploy: false,
      form: {
        currentPage: 1,
        pageSize: 10
      },
      dataList: [{}],
      total: 0
    })
    const getList = async () => {
      state.loading = true
      try {
        // 根据分页接口文档构建请求参数
        const requestParams = {
           currentPage: state.form.currentPage,
            endTime: "",
            pageSize: state.form.pageSize,
            startTime: ""
        }

        let { data } = await getCostList(requestParams)
        // 根据实际返回的数据结构调整
        if (data) {
          state.dataList = data.list || []
          state.total = data.total || 0
        } else {
          state.dataList = []
          state.total = 0
        }
        state.loading = false
      }
      catch (error) {
        console.error('获取成本列表失败:', error)
        state.loading = false
      }
    }
    const handleEdit = (row, type) => {
      state.editType = type
      state.selectRow = row
      state.isEdit = true
    }
    const handleDeploy = (row) => {
      state.selectRow = row
      state.isRoomDeploy = true
    }
    const handleDel = (row) => {
      ElMessageBox.confirm("确认删除这条成本记录？", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        try {
          let { code, message } = await deleteCost(row.id);
          if (code === 0) {
            ElMessage.success(message || '删除成功');
            getList();
          } else {
            ElMessage.error(message || '删除失败');
          }
        } catch (error) {
          console.error('删除失败:', error);
          ElMessage.error('删除失败，请重试');
        }
      }).catch(() => {
        // 用户取消删除
      });
    };
    const handleSizeChange = (val) => {
      state.form.currentPage = 1
      state.form.pageSize = val
      getList()
    };
    const handleCurrentChange = (val) => {
      state.form.currentPage = val
      getList()

    };
    const close = (val) => {
      if (val) {
        getList()
      }
      state.isEdit = false
      state.isRoomDeploy = false
    }
    onMounted(() => {
      getList()
    })
    return {
      state,
      handleEdit,
      handleDeploy,
      handleDel,
      handleSizeChange,
      handleCurrentChange,
      close
    }
  }
}
  </script>