<template>
  <div class="box-dialog">
    <kade-table-wrap title="纠错人员信息">
      <el-table style="width: 100%" :data="[selectPerson]" border stripe>
        <el-table-column show-overflow-tooltip width="150" label="用户编号" prop="userCode" align="center">
        </el-table-column>
        <el-table-column show-overflow-tooltip prop="userName" label="姓名" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip label="组织机构" prop="deptName" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip label="身份类别" prop="userRoleName" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip label="账户状态" prop="acctStatus" align="center">
          <template #default="scope">
            {{ filterDictionary(scope.row.acctStatus, state.accountStatusList) }}
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip label="卡片类别" prop="ctName" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip label="卡片状态" align="center"><template #default="scope">
            {{ filterDictionary(scope.row.cardStatus, state.cardStatusList) }}
          </template></el-table-column>
        <el-table-column show-overflow-tooltip width="153" label="联系方式" prop="userTel" align="center"></el-table-column>
      </el-table>
    </kade-table-wrap>
    <kade-table-wrap title="选择纠错交易单" style="margin-top: 10px" v-loading="state.loading">
      <kade-tab-wrap :tabs="tabs" v-model="state.tab">
        <template #jelx>
          <kade-amount-type-list @formData="formData" />
        </template>
        <template #cslx>
          <kade-frequency-type-list @formData="formData" />
        </template>
      </kade-tab-wrap>
    </kade-table-wrap>
    <div class="submit-btn">
      <el-button @click="off()" size="mini">取&nbsp;&nbsp;消</el-button>
      <el-button @click="submitForm()" size="mini" type="primary">确&nbsp;&nbsp;认</el-button>
    </div>
  </div>
</template>
<script>
import { ElTable, ElTableColumn, ElButton, ElMessage } from "element-plus";
import { reactive } from "@vue/reactivity";
import { dateStr } from "@/utils/date.js";
import { personAccountCorrection } from "@/applications/eccard-finance/api";
import { computed } from "@vue/runtime-core";
import { useStore } from "vuex";
import amountTypeList from "./amountTypeList.vue";
import frequencyTypeList from "./frequencyTypeList.vue";

const tabs = [
  {
    name: "jelx",
    label: "金额类型",
  },
  {
    name: "cslx",
    label: "次数类型",
  },
];
export default {
  components: {
    ElTable,
    ElTableColumn,
    // ElForm,
    // ElFormItem,
    ElButton,
    /*     ElSelect,
    ElOption,
    ElInput, */
    "kade-frequency-type-list": frequencyTypeList,
    "kade-amount-type-list": amountTypeList,
  },
  props: {
    accountStrategyList: {
      types: Array,
      default: [],
    },
    cardTypeList: {
      types: Array,
      default: [],
    },
    walletList: {
      types: Array,
      default: [],
    },
  },
  setup(props, context) {
    const store = useStore();
    const state = reactive({
      tab: "jelx",
      formData: "",
      loading: false,
      accountStatusList: JSON.parse(
        localStorage.getItem("kade_cache_dictionary")
      ).filter((item) => item.dictType == "PERSON_ACCOUNT_STATUS"), //账户状态
      walletTypeList: JSON.parse(
        localStorage.getItem("kade_cache_dictionary")
      ).filter((item) => item.dictType == "PERSON_WALLET_TYPE"), //钱包类型
      walletStatusList: JSON.parse(
        localStorage.getItem("kade_cache_dictionary")
      ).filter((item) => item.dictType == "PERSON_WALLET_STATUS"), //钱包状态
      cardStatusList: JSON.parse(
        localStorage.getItem("kade_cache_dictionary")
      ).filter((item) => item.dictType == "PERSON_CARD_STATUS"), //卡片状态
    });
    const timeStrDate = computed(() => {
      return dateStr;
    });

    const selectPerson = computed(() => {
      return store.state.correctData.selectPerson;
    });

    const filterDictionary = (val, dictionaryList) => {
      for (let i of dictionaryList) {
        if (val == i.dictCode) {
          return i.dictValue;
        }
      }
    };

    const formData = (val) => {
      console.log(val);
      state.formData = val;
    };

    const off = () => {
      context.emit("off", false);
      store.commit("correctData/updateState", {
        key: "isOff",
        payload: false,
      });
    };
    const submitForm = () => {
      console.log(state.formData);
      if (
        !state.formData ||
        !state.formData.relationTradeId ||
        !state.formData.relationTradeNo
      ) {
        return ElMessage.error("请选择纠错交易单!");
      }
      if (!state.formData.actualTradeAmount) {
        return ElMessage.error("请输入纠错额!");
      }
      state.formData.userId = store.state.correctData.selectPerson.userId;

      state.formData.actualTradeAmount = Number(
        state.formData.actualTradeAmount
      );
      state.loading = true
      personAccountCorrection(state.formData)
        .then(({ code, message }) => {
          if (code === 0) {
            ElMessage.success(message);
            context.emit("success", true);
            store.commit("correctData/updateState", {
              key: "isOff",
              payload: false,
            });
          } else {
            ElMessage.error(message);
          }
          state.loading = false
        })
        .catch(() => {
          state.loading = false
          // context.emit("success", false);
          // state.form = {
          //   acctType: null,
          //   userId: null,
          //   walletList: null,
          // };
        });
    };
    return {
      tabs,
      state,
      timeStrDate,
      selectPerson,
      formData,
      filterDictionary,
      off,
      submitForm,
    };
  },
};
</script>
<style lang="scss" scoped>
.box-dialog {
  padding: 10px 0;

  .el-table__row {
    height: 30px !important;
  }

  .account-tips {
    text-align: center;
    margin-bottom: 10px;
    color: #f00;
  }
}

.table-box {
  padding: 10px;
}

.submit-btn {
  text-align: center;
  margin: 10px auto;
}
</style>