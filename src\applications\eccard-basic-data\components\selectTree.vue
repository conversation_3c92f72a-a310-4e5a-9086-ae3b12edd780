<template>
  <el-dropdown trigger="click" ref="dropdown" placement="bottom-start">
    <el-input ref="inputRef" :model-value="label" readonly type="text" placeholder="请选择" suffix-icon="el-icon-arrow-down" />
    <template #dropdown>
      <el-cascader-panel ref="cascaderRef" :props="options" @change="handleChange" />
    </template>
  </el-dropdown>
</template>
<script>
import { ElCascaderPanel, ElInput, ElDropdown } from 'element-plus';
import { computed, ref, onMounted, onUnmounted } from 'vue';
export default {
  components: {
    'el-cascader-panel': ElCascaderPanel,
    'el-input': ElInput,
    'el-dropdown': ElDropdown,
  },
  emits: ['update:modelValue', 'change'],
  props: {
    modelValue: {
      type: Object,
      default: () => ({
        label: '',
        value: ''
      }),
    },
    action: {
      type: Function,
      default: () =>  {}
    },
    idKey: {
      type: String,
      default: 'id'
    },
    labelKey: {
      type: String,
      default: 'label'
    },
    valueKey: {
      type: String,
      default: 'value'
    },
    leaf: {
      type: Function,
      default: (row) => {
        if(row['isLeaf'] === undefined) {
          return true;
        }
        return !!row.isLeaf;
      }
    },
    opts: {
      type: Object,
      default: () => ({})
    }
  },
  setup(props, context) {
    const label = computed(() => props.modelValue.label);
    const cascaderRef = ref(null);
    const inputRef = ref(null);
    const dropdown = ref(null);
    const options = {
      ...props.opts,
      lazy: true,
      lazyLoad: async (node, resolve) => {
        const data = await props.action({ [props['idKey']]: node.value || 0 });
        if(!Array.isArray(data)) {
          resolve([]);
          return;
        }
        resolve(
          data.map(it => ({
            label: it[props.labelKey],
            value: it[props.valueKey],
            leaf: props.leaf(it),
          }))
        );
      }
    };
    const handleChange = () => {
      const [{ label, value }] = cascaderRef.value.getCheckedNodes();
      dropdown.value.visible = false;
      context.emit('change', { label, value });
    }
    const handleHideClick = (e) => {
      if(!cascaderRef.value.$el.contains(e.target) && !inputRef.value.$el.contains(e.target)) {
        dropdown.value.visible = false;
      }
    }
    onMounted(() => {
      document.body.addEventListener('click', handleHideClick);
    });
    onUnmounted(() => {
      document.body.removeEventListener('click', handleHideClick);
    });
    return {
      options,
      label,
      handleChange,
      cascaderRef,
      inputRef,
      dropdown,
    }
  }
}
</script>