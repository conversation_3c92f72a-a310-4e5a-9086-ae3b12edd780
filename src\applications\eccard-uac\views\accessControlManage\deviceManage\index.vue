<template>
  <kade-route-card style="height: 100%">
    <div class="deviceRealTimeMonitor" v-loading="state.loading">
      <kade-table-wrap title="门禁区域" icon="el-icon-map-location"
        style="width: 300px; height: 100%; margin-right: 20px;min-width:300px">
        <el-divider></el-divider>
        <div class="area">
          <el-tree class="area-tree" ref="treeRef" :data="state.areaCheckTreeList" :props="defaultProps" node-key="id"
            @node-click="handleNodeClick">
            <template #default="{ node, data }">
              <div class="custom-tree-node hasbtn">
                <span class="left">
                  <i v-if="data.type == 'ROOM'" :style="{ color: data.bindDevice == 'TRUE' ? '#E6A23C' : '#909399' }"
                    class="el-icon-s-home"></i>
                  <i v-else class="el-icon-map-location"></i>
                  <span style="margin-left: 5px; color: #333">{{
                      node.label
                  }}</span>
                </span>
              </div>
            </template>
          </el-tree>
        </div>
      </kade-table-wrap>
      <kade-table-wrap title="已绑定设备" style="height: 100%;flex: 1;">
        <el-divider></el-divider>
        <div class="census-box" v-if="state.deviceStatic.length">
          <div class="census-item" v-for="(item, index) in state.deviceStatic" :key="index">
            <div class="item-title">{{ item.deviceTypeName }}</div>
            <div class="item-num">{{ item.deviceCount }}<span style="margin-left:5px;font-size:16px">台</span></div>
          </div>
        </div>
        <el-empty v-else description="暂无数据!" :image-size="40"></el-empty>
        <div class="title-box">
          <div class="title">
            <i class="el-icon-map-location" style="color:#409EFF;margin-right: 5px;"></i>{{ state.areaNameTitle }}
          </div>
          <el-button type="success" icon="el-icon-plus" size="mini" @click="handleBind">新增绑定</el-button>
        </div>
        <div class="table-box">
          <el-table :data="state.dataList" @selection-change="handleSelectChange" border height="50vh">
            <el-table-column show-overflow-tooltip prop="userCode" width="50px" label="序号" align="center">
              <template #default="scope">
                {{ scope.$index + 1 }}
              </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip prop="deviceTypeName" label="设备类型" align="center"></el-table-column>
            <el-table-column show-overflow-tooltip prop="deviceNo" label="设备机号" align="center"></el-table-column>
            <el-table-column show-overflow-tooltip prop="deviceName" label="设备名称" align="center"></el-table-column>
            <el-table-column show-overflow-tooltip prop="doorName" label="控制门号" align="center"></el-table-column>
            <el-table-column show-overflow-tooltip prop="createTime" label="绑定时间" align="center"></el-table-column>
            <el-table-column label="操作" align="center">
              <template #default="scope">
                <el-button class="green" type="text" @click="handelDel(scope.row)" size="mini">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </kade-table-wrap>
    </div>
    <kade-bind-device v-model="state.isBind" :data="state.form" @modelValue:update="close" />
  </kade-route-card>
</template>
<script>
import { onMounted, reactive, ref } from "vue";
import { makeTree } from "@/utils/index.js";
import { queryBuildingList, acsDeviceList, deleteRoomDevice, acsDeviceInfoStatic, baseRoomInfoList } from "@/applications/eccard-uac/api";
import { getUserAreaPurview } from "@/applications/eccard-basic-data/api";
import bindDevice from "./components/bind.vue";
import {
  ElDivider,
  ElTree,
  ElEmpty,
  ElButton,
  ElTable,
  ElTableColumn,
  ElMessageBox,
  ElMessage
} from "element-plus";
const defaultProps = {
  children: "children",
  label: "areaName",
};
export default {
  components: {
    ElDivider,
    ElTree,
    ElEmpty,
    ElButton,
    ElTable,
    ElTableColumn,
    "kade-bind-device": bindDevice
  },
  setup() {
    const treeRef = ref(null)
    const state = reactive({
      loading: false,
      isBind: false,
      areaNameTitle: "区域",
      form: {},
      areaCheckTreeList: [],
      dataList: [],
      deviceStatic: []
    });
    const queryAreaCheckList = () => {
      getUserAreaPurview().then((res) => {
        let arr = res.data.map(item => {
          return {
            ...item,
            type: 'AREA',
            areaId: item.id
          }
        })
        let arrTree = makeTree(arr, "id", "areaParentId", "children");
        state.areaCheckTreeList = [...arrTree];
      });
    };
    const getList = async () => {
      state.loading = true
      try {
        let params = {
          areaPath: state.form.areaPath,
          buildId: state.form.buildId,
          floorNum: state.form.floorNum,
          roomId: state.form.roomId,
          unitNum: state.form.unitNum,
        }
        let { data } = await acsDeviceList(params)
        state.dataList = data
        state.loading = false
      }
      catch {
        state.loading = false
      }
    }
    const deviceStaticInfo = async () => {
      let params = {
        areaPath: state.form.areaPath,
        buildId: state.form.buildId,
        floorNum: state.form.floorNum,
        roomId: state.form.roomId,
        unitNum: state.form.unitNum,
      }
      let { data } = await acsDeviceInfoStatic(params)
      state.deviceStatic = data
    }


    const handleNodeClick = async (val, TreeNode) => {
      let nameArr = []
      const getParent = (obj) => {
        if (obj.parent) {
          nameArr.push(obj.data.areaName)
          getParent(obj.parent)
        } else {
          return false
        }
      }
      getParent(TreeNode)
      nameArr.reverse()
      state.areaNameTitle = nameArr.join(">")


      state.form = val
      console.log(val);
      getList()
      deviceStaticInfo()
      // state.loading = true
      if (val.type == 'AREA') {
        queryBuildingList({ areaId: val.id }).then((res) => {
          //区域拼接楼栋单元楼层
          let arr = res.data.map((item, index) => {
            let childArr1 = []
            for (let i = 0; i < item.unitCount; i++) {
              let childArr2 = []
              for (let j = 0; j < item.floorCount; j++) {
                childArr2.push({
                  ...item,
                  areaId: state.form.areaId,
                  areaParentId: Math.ceil((i + 1) * Math.random().toFixed(8) * 100000000),
                  id: Math.ceil((j + 1) * Math.random().toFixed(10) * 10000000000),
                  type: 'FLOOR',
                  floorNum: j + 1,
                  unitNum: i + 1,
                  areaName: j + 1 + '楼',
                })
              }
              childArr1.push({
                ...item,
                type: 'UNIT',
                areaId: state.form.areaId,
                id: Math.ceil((i + 1) * Math.random().toFixed(8) * 100000000),
                areaParentId: Math.ceil((index + 1) * Math.random().toFixed(6) * 1000000),
                areaName: i + 1 + '单元',
                unitNum: i + 1,
                children: childArr2,
              })
            }
            let obj = {
              ...item,
              isArea: false,
              areaId: state.form.areaId,
              areaName: item.buildName,
              id: Math.ceil((index + 1) * Math.random().toFixed(6) * 1000000),
              areaParentId: val.id,
              isBuilding: true,
              type: 'BUILD',

            }
            if (childArr1.length > 0) {
              obj.children = childArr1
            }
            return obj

          })
          console.log(arr);
          if (val.children) {
            arr.push(...val.children.filter(item=>item.type=='AREA'))
          }
          treeRef.value.updateKeyChildren(val.id, arr);
        });

      } else if (val.type == 'FLOOR') {
        let params = {
          buildId: val.buildId,
          unitNum: val.unitNum,
          floorNum: val.floorNum,
        }
        baseRoomInfoList(params).then(({ data }) => {
          let chlidrenList = data.map((item, index) => {
            return {
              ...item,
              areaParentId: val.id,
              id: Math.ceil(val.id * (index + 10)),
              type: 'ROOM',
              roomId: item.id,
              areaName: item.roomName,
            }
          })
          treeRef.value.updateKeyChildren(val.id, chlidrenList);
        })
      }
      else {
        //请求房间列表
        state.form.buildId = val.buildId
        if (val.unitNum) {
          state.form.unitNum = val.unitNum
        } else {
          delete state.form.unitNum
        }
        if (val.floorNum) {
          state.form.floorNum = val.floorNum
        } else {
          delete state.form.floorNum
        }
      }
    }
    const handleBind = () => {
      if (!state.form.id) {
        return ElMessage.error("请选择门禁区域！")
      }
      state.isBind = true
    }
    const handelDel = (row) => {
      ElMessageBox.confirm("确认删除?", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        let { code, message } = await deleteRoomDevice(row.id);
        if (code === 0) {
          ElMessage.success(message);
          getList();
        }
      });
    }
    const close = (val) => {
      if (val) {
        getList()
      }
      state.isBind = false
    }
    onMounted(() => {
      queryAreaCheckList()
      deviceStaticInfo()
      getList()
    })
    return {
      defaultProps,
      treeRef,
      state,
      handleNodeClick,
      handleBind,
      handelDel,
      close
    };
  },
};
</script>
<style lang="scss" scoped>
.deviceRealTimeMonitor {
  height: 100%;
  display: flex;
  justify-content: space-between;

  .area {
    height: calc(100% - 40px);
    padding: 10px;
    box-sizing: border-box;
    overflow-y: auto;
  }

  .census-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 10px;

    .census-item {
      box-sizing: border-box;
      // width: 19%;
      flex: 1;
      margin-right: 10px;
      text-align: center;
      padding: 10px;
      background: #f2f2f2;
      border-radius: 5px;
      box-shadow: 3px 3px 5px #ccc;

      &:last-child {
        margin-right: 0
      }

      .item-title {
        text-align: left;
        margin-bottom: 10px;
      }

      .item-num {
        font: 500 36px/50px arial;
        color: #666
      }
    }
  }

  .title-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px;
    border-top: 1px solid #DCDFE6;
    border-bottom: 1px solid #DCDFE6;
  }

  .table-box {
    padding: 20px 10px 0;
  }
}

:deep(.el-divider--horizontal) {
  margin: 0;
}

:deep(.el-table) {
  border-left: 1px solid #EBEEF5 !important;
  border-right: 1px solid #EBEEF5 !important;
  // border-bottom: 1px solid #EBEEF5 !important;
}

:deep(.el-dialog__footer) {
  text-align: center;
}

:deep(.el-empty) {
  height: 146px;

  .el-empty__image {
    display: none;

  }
}
</style>