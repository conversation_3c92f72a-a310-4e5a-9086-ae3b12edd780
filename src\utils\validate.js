// 邮箱验证
export function validateEmail(rule, value, callback) {
  const reg = /^[A-Za-z0-9\u4e00-\u9fa5]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/;
  if (value) {
    if(!reg.test(value)) {
      callback(new Error('邮箱格式错误'));
      return;
    }
  }
  callback();
}

// 手机
const checkMobile = (tel) => {
  const reg = /^1(3[0-9]|4[01456879]|5[0-35-9]|6[2567]|7[0-8]|8[0-9]|9[0-35-9])\d{8}$/;
  return reg.test(tel);
}

// 座机
const checkFixTel = (tel) => {
  const pattern1 = /^(0\d{2}-\d{8}?)$/;
  const pattern2 = /^(4\d{2}-\d{8}?)$/;
  const pattern3 = /^(8\d{2}-\d{8}?)$/;
  const pattern4 = /^(0\d{3}-\d{7}?)$/;
  const pattern5 = /^(4\d{3}-\d{7}?)$/;
  const pattern6 = /^(8\d{3}-\d{7}?)$/;
  const pattern7 = /^(0\d{3}-\d{6}?)$/;
  const pattern8 = /^(4\d{3}-\d{6}?)$/;
  const pattern9 = /^(8\d{3}-\d{6}?)$/;
  const pattern10 = /^(4\d{2}-\d{4}-\d{4}?)|(4\d{3}-\d{3}-\d{3,4}?)|(8\d{2}-\d{4}-\d{4}?)|(8\d{3}-\d{3}-\d{3,4}?)|(0\d{2}-\d{4}-\d{4}?)|(0\d{3}-\d{3}-\d{3,4}?)$/;
  if (
    !pattern1.test(tel) &&
    !pattern2.test(tel) &&
    !pattern3.test(tel) &&
    !pattern4.test(tel) &&
    !pattern5.test(tel) &&
    !pattern6.test(tel) &&
    !pattern7.test(tel) &&
    !pattern8.test(tel) &&
    !pattern9.test(tel) &&
    !pattern10.test(tel)
  ) {
      return false;
  } else {
      return true;
  } 
}

// 手机号验证
export function validateMobile(rule, value, callback) {
  if (value) {
    if(!checkMobile(value)) {
      callback(new Error('手机号码格式错误'));
      return;
    }
  }
  callback();  
}

// 手机号或座机验证
export function validateMobileAndFixTel(rule, value, callback) {
  if (value) {
    if (!checkMobile(value) && !checkFixTel(value)) {
      callback(new Error('手机号码或座机号码格式错误'));
      return;
    }
  }
  callback();  
}

// 身份证
export function checkIdCard(value) {
  const reg = /(^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$)|(^[1-9]\d{5}\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{2}[0-9Xx]$)/;
  if (value == "111111111111111" || !reg.test(value)) {
    return false;
  } else {
    return true;
  }
}

// 护照
export function checkPassport(code){
  if(code){
    return /^(SE|DE|PE)\d{7}$/gim.test(code);
  }
  return true;
}