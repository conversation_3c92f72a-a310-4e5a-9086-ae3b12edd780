<template>
  <div class="padding-box">
    <el-table :data="details" border>
      <el-table-column v-for="(item,index) in column" :key="index" :label="item.label" :prop="item.filed" align="center"></el-table-column>
    </el-table>
  </div>
</template>
<script>
import { computed } from "@vue/runtime-core";
import { useStore } from "vuex";
import { ElTable, ElTableColumn } from "element-plus"

const column = [
  { label: "阶梯", filed: "label" },
  { label: "温度(°C)", filed: "temperature" },
  { label: "温控费率（元/计费单位）", filed: "temperatureControlRate" },
]
export default {
  components: {
    ElTable,
    ElTableColumn
  },
  setup() {
    const store = useStore();
    const details = computed(() => {
      let data = "";
      for (let item of store.state.deviceParameters[store.state.app.activeTab].detailsParams) {
        if (item.paramType == "TEMPERETURE") {
          data = JSON.parse(item.paramContent);
          break;
        }
      }
      if (!data) {
        data = []
      }
      return data;
    });
    return {
      column,
      details,
    };
  },
}
</script>
<style lang="scss" scoped>
</style>