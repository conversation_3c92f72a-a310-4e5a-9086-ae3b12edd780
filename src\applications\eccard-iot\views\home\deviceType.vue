<template>
  <kade-route-card>
    <template #header>
      <span class="header-title">设备分类统计</span>
    </template>
    <el-table :data="[1, 2, 3, 4, 5, 6, 7, 8]" height="260px" style="width: 100%">
      <el-table-column align="center" prop="date" label="排行">
        <template #default="scope">
          <div class="index"
            :style="{ color: scope.$index < 3 ? '#fff' : '', backgroundColor: colorFnc(scope.$index) }">{{
                scope.$index + 1
            }}</div>
        </template>
      </el-table-column>
      <el-table-column show-overflow-tooltip prop="date" label="设备类型">
        <template #default>
          <div class="device">消费机</div>
        </template>
      </el-table-column>
      <el-table-column show-overflow-tooltip prop="date" label="设备数量">
        <template #default>
          <div class="num">654</div>
        </template>
      </el-table-column>
      <el-table-column show-overflow-tooltip align="center" prop="date" label="占比">
        <template #default>
          <div class="num">20%</div>
        </template>
      </el-table-column>
    </el-table>
  </kade-route-card>
</template>
<script>
import { ElTable, ElTableColumn } from "element-plus"
const colorFnc = (val) => {
  if (val === 0) {
    return "#ff6633"
  } else if (val === 1) {
    return "#fc3"

  } else if (val === 2) {
    return "#0c3"
  } else {
    return false
  }
}
export default {
  components: {
    ElTable, ElTableColumn
  },
  setup() {
    return {
      colorFnc
    }
  }
}
</script>
<style scoped lang="scss">
:deep(.el-table) {

  th,
  td {
    padding: 5px 0;
    font-size: 13px;
    color: #999999;
    border: none;
    font-weight: 400;
  }
}

.index {
  margin: 0 auto;
  text-align: center;
  width: 20px;
  line-height: 20px;
  border-radius: 50%;
  background: #f0f2f5;
  color: #000000a5;
}

.device {
  font-style: normal;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.647058823529412);
}

.num {
  font-weight: 700;
  font-size: 18px;
  color: #000000;
  line-height: 22px;
}
</style>