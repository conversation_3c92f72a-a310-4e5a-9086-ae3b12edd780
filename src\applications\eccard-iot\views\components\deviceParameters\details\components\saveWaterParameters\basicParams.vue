<template>
  <div class="padding-box">
    <el-form v-if="details" label-width="150px" size="mini" inline>
      <el-form-item label="预扣金额（元）：">
        <el-input :model-value="details.Withholdingmoney" readonly></el-input>
      </el-form-item>
    </el-form>
    <el-empty v-else description="当前设备参数未设定"></el-empty>
  </div>
</template>
<script>
import { computed } from "@vue/runtime-core";
import { useStore } from "vuex";
import { ElForm, ElFormItem, ElInput, ElEmpty } from "element-plus"
export default {
  components: {
    ElForm,
    ElFormItem,
    ElInput,
    ElEmpty
  },
  setup() {
    const store = useStore();
    const details = computed(() => {
      let data = "";
      for (let item of store.state.deviceParameters[store.state.app.activeTab].detailsParams) {
        if (item.paramType == "BASE") {
          data = JSON.parse(item.paramContent);
          break;
        }
      }
      if (!data) {
        data = ""
      }
      return data;
    });
    return {
      details,
    };
  },
};
</script>
<style lang="scss" scoped>
</style>