<template>
  <el-dialog :model-value="modelValue" title="参数详情" width="1000px" :before-close="handleClose"
    :close-on-click-modal="false">
    <div style="padding-bottom:20px">
      <el-button @click="handleEdit({}, 'add')" style="margin:20px 0 10px" type="success" size="mini">新增参数类</el-button>
      <el-table border height="55vh" :data="state.dataList" v-loading="state.loading" default-expand-all row-key="id">
        <el-table-column show-overflow-tooltip prop="paramsName" label="参数名称" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="paramsCode" label="参数编码" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="paramsType" label="参数值类型" align="center">
          <template #default="scope">
            <span>{{ scope.row.paramsType == 'INPUT' ? '输入框' : '选择框' }}</span>
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip prop="paramsFormat" label="参数值格式" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="paramsValue" label="可选值" align="center"></el-table-column>
        <el-table-column label="操作" align="center">
          <template #default="scope">
            <el-button type="text" @click="handleEdit(scope.row, 'children')" v-if="scope.row.parentId === 0"
              size="mini">
              新增子类
            </el-button>
            <el-button type="text" @click="handleEdit(scope.row, 'edit')" size="mini">编辑</el-button>
            <el-button type="text" @click="handleDel(scope.row, scope.$index)" size="mini">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </el-dialog>
  <kade-paramTemplate-edit v-model="state.isEdit" :data="state.rowData" :type="state.type" @update:modelValue="close" />
</template>

<script>
import {
  ElDialog,
  ElButton,
  ElTable,
  ElTableColumn,
  ElMessageBox
} from "element-plus";
import { reactive, watch, ref } from "vue";
import { makeTree } from "@/utils"
import edit from "./edit.vue";

export default {
  props: {
    modelValue: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Object,
      default: null
    },
    deviceTypelist: {
      type: Array,
      default: () => []
    },
  },
  components: {
    ElDialog,
    ElButton,
    ElTable,
    ElTableColumn,
    'kade-paramTemplate-edit': edit,
  },
  setup(props, context) {
    const formRef = ref(null)
    const state = reactive({
      isEdit: false,
      dataList: [],
      paramsList: [],
    });
    watch(() => props.modelValue, val => {
      if (val) {
        state.paramsList = JSON.parse(JSON.stringify(props.data)).paramsList
        getList(JSON.parse(JSON.stringify(props.data)).paramsList)
      }
    })
    const getList = (list) => {
      state.dataList = makeTree(list, "id", "parentId", "children");
    }
    const handleEdit = (row, type) => {
      state.type = type
      state.rowData = row
      state.isEdit = true;
    };

    const handleDel = (row) => {
      ElMessageBox.confirm(`确认删除?`, {
        type: "warning",
        closeOnPressEscape: false,
        closeOnClickModal: false,
      }).then(async () => {
        state.paramsList.splice(state.paramsList.findIndex(item => item.id == row.id), 1)
        getList(state.paramsList)
      });

    }


    const handleClose = () => {
      let list = JSON.parse(sessionStorage.getItem("templateList"))
      let index = list.findIndex(item => item.id == props.data.id)
      state.paramsList = state.paramsList.map(item => {
        delete item.children
        return item
      })
      list[index].paramsList = state.paramsList
      sessionStorage.setItem("templateList", JSON.stringify(list))
      context.emit("update:modelValue", false);
    };
    const close = (val) => {
      state.paramsList = state.paramsList.map(item => {
        delete item.children
        return item
      })
      if (val) {
        if (val.type == 'add') {
          let data = { ...val.data, id: state.paramsList.length ? state.paramsList[state.paramsList.length - 1].id + 1 : 1 }
          data.parentId = 0
          state.paramsList.push(data)
        } else if (val.type == 'edit') {
          state.paramsList.splice(state.paramsList.findIndex(item => item.id == val.data.id), 1, val.data)
        } else if (val.type == 'children') {
          let data = { ...val.data, id: state.paramsList.length ? state.paramsList[state.paramsList.length - 1].id + 1 : 1 }
          data.parentId = state.rowData.id
          state.paramsList.push(data)
        }
        state.dataList = []
        getList(state.paramsList)
        state.isEdit = false;
      }
    }
    return {
      formRef,
      state,
      handleEdit,
      handleDel,
      handleClose,
      close
    };
  },
};
</script>

<style lang="scss" scoped>
.el-form {
  margin-top: 10px;
}

.table-box {
  border: 1px solid #eeeeee;
  border-radius: 0 0 8px 8px;

  .pagination {
    margin: 10px;
  }
}

.footer-box {
  margin-top: 10px;
  border: 1px solid #eeeeee;
  border-radius: 4px;

  .text-box {
    margin: 15px;
  }

  .el-form {
    margin-top: 20px;
  }
}

:deep(.el-select) {
  width: 100%;
}
</style>