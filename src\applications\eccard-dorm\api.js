import request from "@/service";

/* 楼栋管理 */
//楼栋列表分页查询
export function getBuildingList(params) {
  return request.get('/eccard-dorm/Building/page', { params });
}
//添加楼栋信息
export function buildingAdd(params) {
  return request.post('/eccard-dorm/Building/addBuilding', params);
}
//修改楼栋信息
export function buildingEdit(params) {
  return request.put('/eccard-dorm/Building/updateBuilding', params);
}
//删除楼栋信息
export function buildingDelete(params) {
  return request.delete(`/eccard-dorm/Building/deleteBuildingById/${params}`);
}
//批量删除楼栋信息
export function buildingBatchDelete(params) {
  return request.delete(`/eccard-dorm/Building/batchDeleteBuilding/${params}`);
}

//楼栋列表导出
export function exportBuilding(params) {
  return request.get('/eccard-dorm/Building/exportBuilding', {
    params,
    headers: {
      'Content-Type': 'application/octet-stream',
      Accept: "*/*"
    },
    responseType: 'blob',
  });
}

//楼栋列表下载导入模板
export function downImportTemplate(params) {
  return request.get('/eccard-dorm/Building/downImportTemplate', {
    params,
    headers: {
      'Content-Type': 'application/octet-stream',
      Accept: "*/*"
    },
    responseType: 'blob',
  });
}

//获取导入楼栋信息列表数据-分页
export function getImportBuildingList(params) {
  return request.get('/eccard-dorm/Building/getImportBuildingListByPage', { params });
}
//删除待保存楼栋信息
export function deleteWaitBuilding(params) {
  return request.delete(`/eccard-dorm/Building/deleteWaitBuildingById/${params.projectNo}/${params.id}`);
}
//保存导入楼栋数据
export function saveImportBuilding(params) {
  return request.post('/eccard-dorm/Building/saveImportBuilding', params);
}


/* 房间类型 */

//房间类型选择列表查询
export function getRoomTypeSelectList(params) {
  return request.get('/eccard-dorm/RoomType/list', { params });
}

//房间类型分页查询
export function getRoomTypeList(params) {
  return request.get('/eccard-dorm/RoomType/page', { params });
}
//添加房间类型
export function roomTypeAdd(params) {
  return request.post('/eccard-dorm/RoomType/addRoomType', params);
}
//修改房间类型
export function roomTypeEdit(params) {
  return request.put('/eccard-dorm/RoomType/updateRoomType', params);
}
//删除房间类型
export function roomTypeDelete(params) {
  return request.delete(`/eccard-dorm/RoomType/deleteRoomType/${params}`);
}

//获取房间物品配置
export function getRoomGoods(params) {
  return request.get(`/eccard-dorm/RoomGoods/list`, { params });
}
//添加房间物品设置
export function addRoomGoodsAdd(params) {
  return request.post('/eccard-dorm/RoomGoods/addRoomGoods', params);
}
//修改房间物品设置
export function addRoomGoodsEdit(params) {
  return request.post('/eccard-dorm/RoomGoods/editRoomGoods', params);
}
//删除房间物品设置
export function roomGoodsDelete(params) {
  return request.delete(`/eccard-dorm/RoomGoods/deleteRoomGoods/${params}`);
}
//批量删除房间物品设置
export function roomGoodsBatchDelete(params) {
  return request.delete(`/eccard-dorm/RoomGoods/deleteRoomGoodsBatch/${params}`);
}
//导出房间物品配置
export function exportRoomGoods(params) {
  return request.get('/eccard-dorm/RoomGoods/exportRoomGoods', {
    params,
    headers: {
      'Content-Type': 'application/octet-stream',
      Accept: "*/*"
    },
    responseType: 'blob',
  });
}


/* 房间管理 */
//房间分页查询
export function getRoomList(params) {
  return request.get('/eccard-dorm/RoomInfo/page', { params });
}
//添加房间
export function roomAdd(params) {
  return request.post('/eccard-dorm/RoomInfo/addRoomInfo', params);
}
//修改房间
export function roomEdit(params) {
  return request.put('/eccard-dorm/RoomInfo/updateRoomInfo', params);
}
//删除房间
export function roomDelete(params) {
  return request.delete(`/eccard-dorm/RoomInfo/deleteRoomInfo/${params}`);
}

//批量添加房间
export function roomBatchAdd(params) {
  return request.post('/eccard-dorm/RoomInfo/addRoomInfoBatch', params);
}
//批量删除房间
export function roomBatchDelete(params) {
  return request.delete(`/eccard-dorm/RoomInfo/batchDeleteBuilding/${params}`);
}

//房间列表下载导入模板
export function roomDownImportTemplate(params) {
  return request.get('/eccard-dorm/RoomInfo/downImportTemplate', {
    params,
    headers: {
      'Content-Type': 'application/octet-stream',
      Accept: "*/*"
    },
    responseType: 'blob',
  });
}

//获取导入房间信息列表数据-分页
export function getImportRoomList(params) {
  return request.get('/eccard-dorm/RoomInfo/getImportRoomInfoListByPage', { params });
}
/* //删除待保存房间信息
export function deleteWaitBuilding(params) {
  return request.delete(`/eccard-dorm/Building/deleteWaitBuildingById/${params.projectNo}/${params.id}`);
} */
//保存导入房间数据
export function saveImportRoom(params) {
  return request.post('/eccard-dorm/RoomInfo/saveImport', params);
}

//导出房间列表
export function exportRoom(params) {
  return request.get('/eccard-dorm/RoomInfo/exportBuilding', {
    params,
    headers: {
      'Content-Type': 'application/octet-stream',
      Accept: "*/*"
    },
    responseType: 'blob',
  });
}


/* 宿舍管理员 */
//宿舍管理员分页查询
export function getDormManagerList(params) {
  return request.get('/eccard-dorm/Manager/page', { params });
}
//添加宿舍管理员
export function dormManagerAdd(params) {
  return request.post('/eccard-dorm/Manager/addManager', params);
}
//修改宿舍管理员
export function dormManagerEdit(params) {
  return request.post('/eccard-dorm/Manager/editManager', params);
}
//删除宿舍管理员
export function dormManagerDelete(params) {
  return request.delete(`/eccard-dorm/Manager/deleteManagerById/${params}`);
}
//批量删除管理员信息
export function dormManagerBatchDelete(params) {
  return request.delete(`/eccard-dorm/Manager/batchDelete/${params}`);
}


//楼栋选择列表查询
export function queryBuildingList(params) {
  return request.get(`/eccard-dorm/Building/list`, { params });
}
//获取楼栋单元选择列表
export function queryBuildingUnitList(params) {
  return request.get(`/eccard-dorm/Building/getBuildingUnitList/${params}`);
}
//获取楼栋楼层选择列表
export function queryBuildingFloorList(params) {
  return request.get(`/eccard-dorm/Building/getBuildingFloorList/${params.buildId}/${params.unitId}`);
}
//获取房间选择列表
export function queryRoomList(params) {
  return request.get(`/eccard-dorm/RoomInfo/list`, { params });
}



/* 房间设备管理 */
//房间设备分页查询
export function roomDeviceList(params) {
  return request.get('/eccard-dorm/RoomDevice/page', { params });
}
//添加房间设备
export function roomDeviceAdd(params) {
  return request.post('/eccard-dorm/RoomDevice/addRoomDevice', params);
}
//删除房间设备
export function roomDeviceDelete(params) {
  return request.delete(`/eccard-dorm/RoomDevice/deleteRoomDevice/${params}`);
}

//房间设备查询
export function roomDeviceListNoPage(params) {
  return request.get('/eccard-dorm/RoomDevice/list', { params });
}
//分页获取待绑定宿管设备列表
export function unBindDormDeviceList(params) {
  return request.post('/eccard-dorm/RoomDevice/getUnBindDormDeviceList', params);
}

//房间设备下载导入模板
export function roomDeviceImportTemplate(params) {
  return request.get('/eccard-dorm/RoomDevice/downImportTemplate', {
    params,
    headers: {
      'Content-Type': 'application/octet-stream',
      Accept: "*/*"
    },
    responseType: 'blob',
  });
}
//获取导入房间设备列表数据-分页
export function importRoomDeviceList(params) {
  return request.get('/eccard-dorm/RoomDevice/getImportRoomDeviceListByPage', { params });
}
//保存导入房间设备数据
export function saveImportRoomDevice(params) {
  return request.post('/eccard-dorm/RoomDevice/saveImport', params);
}

/* 单元设备管理 */
//房间设备分页查询
export function unitDeviceList(params) {
  return request.get('/eccard-dorm/UnitDevice/page', { params });
}
//添加单元设备
export function unitDeviceAdd(params) {
  return request.post('/eccard-dorm/UnitDevice/addUnitDevice', params);
}
//删除单元设备
export function unitDeviceDelete(params) {
  return request.delete(`/eccard-dorm/UnitDevice/deleteUnitDevice/${params}`);
}

//单元设备查询
export function unitDeviceListNoPage(params) {
  return request.get('/eccard-dorm/UnitDevice/list', { params });
}
//分页获取待绑定宿管设备列表
export function unBindUnitDeviceList(params) {
  return request.post('/eccard-dorm/UnitDevice/getUnBindDormUnitDeviceList', params);
}

//单元设备下载导入模板
export function unitDeviceImportTemplate(params) {
  return request.get('/eccard-dorm/UnitDevice/downImportTemplate', {
    params,
    headers: {
      'Content-Type': 'application/octet-stream',
      Accept: "*/*"
    },
    responseType: 'blob',
  });
}
//获取导入单元设备列表数据-分页
export function importUnitDeviceList(params) {
  return request.get('/eccard-dorm/UnitDevice/getImportUnitDeviceListByPage', { params });
}
//保存导入单元设备数据
export function saveImportUnitDevice(params) {
  return request.post('/eccard-dorm/UnitDevice/saveImport', params);
}





//获取卫生检查列表
export function getHygieneCheckList(params) {
  return request.post('/eccard-dorm/HygieneCheck/page', params);
}

//添加卫生检查数据
export function addHygieneCheck(params) {
  return request.post('/eccard-dorm/HygieneCheck', params);
}

//删除卫生检查数据
export function deleteHygieneCheckInfo(params) {
  return request.delete('/eccard-dorm/HygieneCheck/' + params);
}

//修改卫生检查数据
export function editHygieneCheck(params) {
  return request.put(`/eccard-dorm/HygieneCheck`, params);
}

//批量删除卫生检查数据
export function deleteHygieneCheckInfos(params) {
  return request.delete(`/eccard-dorm/HygieneCheck/deleteBatch/${params}`);
}

//批量新增卫生检查数据
export function batchAddHygieneCheck(params) {
  return request.post('/eccard-dorm/HygieneCheck/insertBatch', params);
}

//批量新增卫生检查记录未选择房间房间信息查询接口
export function batchSelectInfo(params) {
  return request.post('/eccard-dorm/HygieneCheck/batchSelectInfo', params);
}
//批量新增卫生检查记录已选择房间房间信息查询接口
export function batchSelectedInfo(params) {
  return request.post('/eccard-dorm/HygieneCheck/batchSelectedInfo', params);
}
//批量新增卫生检查记录操作(选择或者移除操作)接口
export function batchSelectedOp(params) {
  return request.post('/eccard-dorm/HygieneCheck/batchSelectedOp', params);
}

//卫生检查列表下载导入模板
export function HygieneCheckImportTemplate(params) {
  return request.get('/eccard-dorm/HygieneCheck/downImportTemplate', {
    params,
    headers: {
      'Content-Type': 'application/octet-stream',
      Accept: "*/*"
    },
    responseType: 'blob',
  });
}

//获取导入卫生检查得分信息列表数据-分页
export function getImportHygieneCheckScore(params) {
  return request.post(`/eccard-dorm/HygieneCheck/getImportHygieneCheckScoreByPage`, params);
}

//保存导入卫生检查得分数据
export function saveImportHygieneCheckScore(params) {
  return request.post(`/eccard-dorm/HygieneCheck/saveHygieneCheckScore/${params}`);
}


//获取违规违纪列表
export function getBreachPrincipleList(params) {
  return request.post('/eccard-dorm/breachPrinciple/page', params);
}


//删除违规违纪数据
export function deleteBreachPrincipleInfo(params) {
  return request.delete('/eccard-dorm/breachPrinciple/' + params);
}

//添加违规违纪数据
export function addBreachPrinciple(params) {
  return request.post('/eccard-dorm/breachPrinciple', params);
}

//修改违规违纪数据
export function editBreachPrinciple(params) {
  return request.put(`/eccard-dorm/breachPrinciple`, params);
}

//批量删除违规违纪数据
export function batchBreachPrinciple(params) {
  return request.delete(`/eccard-dorm/breachPrinciple/deleteBatch/${params}`);
}

//获取公共资产列表
export function getAssetsInfo(params) {
  return request.post('/eccard-dorm/assetsInfo/page', params);
}

//添加公共资产数据
export function addAssetsInfo(params) {
  return request.post('/eccard-dorm/assetsInfo', params);
}

//修改公共资产数据
export function editAssetsInfo(params) {
  return request.put(`/eccard-dorm/assetsInfo`, params);
}

//删除公共资产数据
export function delAssetsInfo(params) {
  return request.delete('/eccard-dorm/assetsInfo/' + params);
}

//批量删除公共资产数据
export function batchAssetsInfo(params) {
  return request.delete(`/eccard-dorm/assetsInfo/deleteBatch/${params}`);
}

//导出公共资产数据
export function exportAssetsInfo(params) {
  return request.post('/eccard-dorm/assetsInfo/export', params,
    {
      headers: {
        'Content-Type': 'application/octet-stream',
        Accept: "*/*"
      },
      responseType: 'blob'
    });
}


//获取报修单管理列表
export function getRepairList(params) {
  return request.post('/eccard-dorm/repair/page', params);
}


//删除报修单管理数据
export function delRepair(params) {
  return request.delete('/eccard-dorm/repair/' + params);
}


//批量删除报修单管理数据
export function batchRepair(params) {
  return request.delete(`/eccard-dorm/repair/deleteBatch/${params}`);
}


//新增报修单管理数据
export function addRepair(params) {
  return request.post('/eccard-dorm/repair', params);
}

//编辑报修单管理数据
export function editRepair(params) {
  return request.put(`/eccard-dorm/repair`, params);
}

//获取报修项目列表
export function getRepairItemList(params) {
  return request.post('/eccard-dorm/repair/repairItemList', params);
}

//处理报修信息
export function handleRepair(params) {
  return request.put(`/eccard-dorm/repair/${params}`);
}

//获取贵重物品出入登记信息列表
export function getGoodsInOut(params) {
  return request.post('/eccard-dorm/goodsInOut/page', params);
}


//新增贵重物品出入登记信息
export function addGoodsInOut(params) {
  return request.post('/eccard-dorm/goodsInOut', params);
}


//编辑贵重物品出入登记信息
export function editGoodsInOut(params) {
  return request.put(`/eccard-dorm/goodsInOut`, params);
}

//删除贵重物品出入登记信息
export function delGoodsInOut(params) {
  return request.delete(`/eccard-dorm/goodsInOut/${params}`);
}


//批量删除贵重物品出入登记信息
export function batchGoodsInOut(params) {
  return request.delete(`/eccard-dorm/goodsInOut/deleteBatch/${params}`);
}


//查询贵重物品出入登记信息
export function queryGoodsInOut(params) {
  return request.get(`/eccard-dorm/goodsInOut/${params.id}`);
}

//导出贵重物品出入登记数据
export function exportGoodsInout(params) {
  return request.post('/eccard-dorm/goodsInOut/export', params,
    {
      headers: {
        'Content-Type': 'application/octet-stream',
        Accept: "*/*"
      },
      responseType: 'blob'
    });
}


//获取访客登记信息表
export function getVisitor(params) {
  return request.post('/eccard-dorm/dormVisitor/page', params);
}

//添加访客登记信息
export function addVisitor(params) {
  return request.post('/eccard-dorm/dormVisitor', params);
}

//修改访客登记信息
export function editVisitor(params) {
  return request.put(`/eccard-dorm/dormVisitor`, params);
}

//删除访客登记信息
export function delVisitor(params) {
  return request.delete(`/eccard-dorm/dormVisitor/${params}`);
}

//批量删除访客登记信息
export function batchVisitor(params) {
  return request.delete(`/eccard-dorm/dormVisitor/deleteBatch/${params}`);
}

//获取通知公告列表
export function getMsgList(params) {
  return request.post('/eccard-dorm/notifyInfo/page', params);
}

//添加通知公告
export function addMsg(params) {
  return request.post('/eccard-dorm/notifyInfo', params);
}

//修改通知公告
export function editMsg(params) {
  return request.put(`/eccard-dorm/notifyInfo`, params);
}

//删除通知公告
export function delMsg(params) {
  return request.delete(`/eccard-dorm/notifyInfo/${params}`);
}

//批量删除公告
export function delMsgs(params) {
  return request.delete(`/eccard-dorm/notifyInfo/deleteBatch/${params}`);
}

/* 宿舍水电 */

//获取水电费缴存记录列表
export function getHydropowerPay(params) {
  return request.post('/eccard-dorm/hydropowerPay/page', params);

}

//获取水电费余额汇总
export function getPayTotal(params) {
  return request.post('/eccard-dorm/hydropowerPay/balanceTotal', params);

}

//获取水电费缴余额查询列表
export function getHydropowerBalance(params) {
  return request.post('/eccard-dorm/hydropowerBalance/page', params);

}

//获取水电费缴余额查询汇总
export function getBalanceTotal(params) {
  return request.post('/eccard-dorm/hydropowerBalance/balanceTotal', params);

}

//获取水电用量查询列表
export function getHydropowerBae(params) {
  return request.post('/eccard-dorm/hydropowerBalance/page', params);

}


/* 宿舍出入*/

//获取权限管理数据
export function getRoomLockAuthority(params) {
  return request.post('/eccard-dorm/deviceAuthority/page', params);

}

//新增权限管理数据
export function addDeviceAuthority(params) {
  return request.post('/eccard-dorm/deviceAuthority', params);
}

//删除权限管理数据
export function delRoomLockAuthority(params) {
  return request.delete(`/eccard-dorm/deviceAuthority/${params}`);
}


//批量删除权限管理数据
export function batchRoomLockAuthority(params) {
  return request.delete(`/eccard-dorm/deviceAuthority/deleteBatch/${params}`);
}


//区域房间信息选择
export function getAuthorityAreaInfo(params) {
  return request.post('/eccard-dorm/deviceAuthority/authorityAreaInfo', params);
}


//获取设备通行记录数据
export function getAccessPassRecord(params) {
  return request.post('/eccard-dorm/accessPassRecord/page', params);

}

//设备通行记录数据导出
export function exportAccessPassRecord(params) {
  return request.post('/eccard-dorm/accessPassRecord/export', params,
    {
      headers: {
        'Content-Type': 'application/octet-stream',
        Accept: "*/*"
      },
      responseType: 'blob'
    });
}


/* 宿舍考勤 */
//获取考勤时段列表
export function attendancePeriodList(params) {
  return request.get('/eccard-dorm/attendancePeriod/list', { params });
}

//修改考勤时段信息
export function attendancePeriodEdit(params) {
  return request.put('/eccard-dorm/attendancePeriod', params);
}


//获取请假记录列表
export function getLeaveInfo(params) {
  return request.post('/eccard-dorm/leaveRecord/page', params);
}

//导出请假记录表
export function exportLeaveInfo(params){
  return request.post('/eccard-dorm/leaveRecord/export',params,
  {
    headers: {
      'Content-Type': 'application/octet-stream',
      Accept: "*/*"
    },
    responseType: 'blob'
  });
}

//获取人工复核列表
export function manualCheckList(params) {
  return request.post('/eccard-dorm/manualCheck/page', params);
}

//添加人工复核信息
export function manualCheckAdd(params) {
  return request.post('/eccard-dorm/manualCheck', params);
}

//修改人工复核信息
export function manualCheckEdit(params) {
  return request.put(`/eccard-dorm/manualCheck`, params);
}

//获取考勤结果列表
export function attendanceResultList(params) {
  return request.post('/eccard-dorm/personAttendanceResult/page', params);
}


//导出考勤结果列表
export function exportAttendanceResult(params) {
  return request.post('/eccard-dorm/personAttendanceResult/export', params,
    {
      headers: {
        'Content-Type': 'application/octet-stream',
        Accept: "*/*"
      },
      responseType: 'blob'
    });
}


//获取楼栋考勤汇总列表
export function attendanceBuildingSummaryList(params) {
  return request.post('/eccard-dorm/attendanceBuildingSummary/page', params);
}


//导出楼栋考勤汇总列表
export function exportAttendanceBuildingSummary(params) {
  return request.post('/eccard-dorm/attendanceBuildingSummary/export', params,
    {
      headers: {
        'Content-Type': 'application/octet-stream',
        Accept: "*/*"
      },
      responseType: 'blob'
    });
}


//获取宿舍考勤汇总列表
export function attendanceRoomSummaryList(params) {
  return request.post('/eccard-dorm/attendanceRoomSummary/page', params);
}


//导出宿舍考勤汇总列表
export function exportAttendanceRoomSummary(params) {
  return request.post('/eccard-dorm/attendanceRoomSummary/export', params,
    {
      headers: {
        'Content-Type': 'application/octet-stream',
        Accept: "*/*"
      },
      responseType: 'blob'
    });
}


//获取宿舍考勤未归、晚归列表
export function getNotReturnOrLate(params) {
  return request.post(`/eccard-dorm/attendanceRoomSummary/notReturnOrLate`,params);
}


//获取班级考勤汇总列表
export function attendanceClassSummaryList(params) {
  return request.post('/eccard-dorm/attendanceClassSummary/page', params);
}


//导出班级考勤汇总列表
export function exportAttendanceClassSummary(params) {
  return request.post('/eccard-dorm/attendanceClassSummary/export', params,
    {
      headers: {
        'Content-Type': 'application/octet-stream',
        Accept: "*/*"
      },
      responseType: 'blob'
    });
}


//获取班级考勤未归、晚归列表
export function notReturnOrLateList(params) {
  return request.post('/eccard-dorm/attendanceClassSummary/notReturnOrLate', params);
}


//获取考勤分析列表
export function attendanceAnalysisList(params){
  return request.post('/eccard-dorm/attendanceAnalysis/page',params)
}

//重新分析考勤
export function queryAttendanceAnalysis(params){
  return request.post('/eccard-dorm/attendanceAnalysis/reAnalysis',params)
}


//获取考勤原始记录列表
export function attendanceRecordList(params){
  return request.post('/eccard-dorm/attendanceRecord/page',params)
}

//导出考勤原始记录表
export function exportAttendanceRecord(params){
  return request.post('/eccard-dorm/attendanceRecord/export',params,
    {
      headers:{
        'Content-Type': 'application/octet-stream',
        Accept: "*/*"
      },
      responseType: 'blob'
    }
  )
}

//获取考勤分组列表
export function getAttendanceGroup(params){
  return  request.post('/eccard-dorm/attendanceGroup/page',params)
}

//新增考勤分组信息
export function addAttendanceGroup(params){
  return request.post('/eccard-dorm/attendanceGroup',params)
}

//修改考勤分组列表
export function editAttendanceGroup(params){
  return request.put('/eccard-dorm/attendanceGroup',params)
}

//删除考勤分组信息
export function delAttendanceGroup(params){
  return request.delete(`/eccard-dorm/attendanceGroup/${params}`);
}

/* 房间分配 */
//获取楼栋信息列表
export function roomAllocationBuildList(params) {
  return request.get('/eccard-dorm/RoomAllocation/getBuildingByAreaId', { params });
}
//获取楼栋统计信息
export function roomAllocationBuildingStatistics(params) {
  return request.get('/eccard-dorm/RoomAllocation/getBuildingStatisticsByAreaId', { params });
}
//获取分配房间信息列表
export function roomAllocationRoomInfoList(params) {
  return request.get('/eccard-dorm/RoomAllocation/getAllocRoomInfo', { params });
}
//获取房间统计信息
export function roomAllocationRoomStatistics(params) {
  return request.get('/eccard-dorm/RoomAllocation/getAllocRoomStatistics', { params });
}

//获取部门房间分配信息
export function roomtDeptAllocationRoomInfo(params) {
  return request.get('/eccard-dorm/RoomAllocation/getDeptAllocatedRoomInfo', { params });
}

//获取楼栋未分配的房间信息
export function getNotAllocatedRoom(params) {
  return request.get('/eccard-dorm/RoomAllocation/getNotAllocatedRoom', { params });
}

//分配组织机构宿舍房间
export function allocDeptRoom(params) {
  return request.post('/eccard-dorm/RoomAllocation/allocDeptRoom', params);
}
//撤销已分配的组织机构宿舍房间
export function revokeDeptAllocatedRoom(params) {
  return request.post('/eccard-dorm/RoomAllocation/revokeDeptAllocatedRoom', params);
}

//房间分配列表下载导入模板
export function roomAllocationImportTemplate(params) {
  return request.get('/eccard-dorm/RoomAllocation/downImportTemplate', {
    params,
    headers: {
      'Content-Type': 'application/octet-stream',
      Accept: "*/*"
    },
    responseType: 'blob',
  });
}

//获取导入房间分配信息列表数据-分页
export function getImportRoomAllocationList(params) {
  return request.get('/eccard-dorm/RoomAllocation/getImportRoomAllocateByPage', { params });
}
//保存导入房间分配数据
export function saveImportRoomAllocation(params) {
  return request.post('/eccard-dorm/RoomAllocation/saveImport', params);
}

//根据房间id获取房间人员
export function getRoomStayInfo(params) {
  return request.get('/eccard-dorm/RoomAllocation/getRoomStayInfo', { params });
}

/* 床位分配 */
//获取楼栋房间信息
export function getBuildRoomInfo(params) {
  return request.post('/eccard-dorm/bedAlloc/getBuildRoomInfo', params);
}

//指定床位号给用户分配床位
export function userBedAllocateToBedNum(params) {
  return request.post('/eccard-dorm/bedAlloc/userBedAllocateToBedNum', params);
}

//添加房间床位信息(数量),在原有的房间床位数量加1
export function addRoomBed(params) {
  return request.post(`/eccard-dorm/bedAlloc/addRoomBed/${params}`);
}

//根据房间ID分配指定用户的床位信息
export function userBedAllocate(params) {
  return request.post(`/eccard-dorm/bedAlloc/userBedAllocate`, params);
}

//根据房间id获取房间床位人员
export function getRoomBedAssignData(params) {
  return request.get('/eccard-dorm/RoomAllocation/getRoomBedAssignData', { params });
}

//自动分配用户床位
export function autoAllocateUserBed(params) {
  return request.post(`/eccard-dorm/bedAlloc/autoAllocateUserBed`, params);
}

//床位分配列表下载导入模板
export function bedAssignImportTemplate(params) {
  return request.get('/eccard-dorm/bedAlloc/downImportTemplate', {
    params,
    headers: {
      'Content-Type': 'application/octet-stream',
      Accept: "*/*"
    },
    responseType: 'blob',
  });
}

//获取导入床位分配信息列表数据-分页
export function getImportBedAssignList(params) {
  return request.post('/eccard-dorm/bedAlloc/getImportRoomBedAllocateByPage', params);
}
//保存导入床位分配数据
export function saveImportBedAssign(params) {
  return request.post(`/eccard-dorm/bedAlloc/saveImportRoomBedAllocate/${params}`);
}

/* 退宿管理 */
//可退宿列表分页查询
export function getCheckOutList(params) {
  return request.post('/eccard-dorm/checkOut/page', params);
}
//可退宿列表导出
export function checkOutExport(params) {
  return request.post('/eccard-dorm/checkOut/export', params,
    {
      headers: {
        'Content-Type': 'application/octet-stream',
        Accept: "*/*"
      },
      responseType: 'blob'
    });
}
//批量用户退宿
export function batchCheckOut(params) {
  return request.post('/eccard-dorm/checkOut/batchCheckOut', params);
}
//单个人员退宿
export function checkOut(params) {
  return request.post('/eccard-dorm/checkOut', params);
}

/* 自助选宿 */
//可退宿列表分页查询
export function selfChoiceList(params) {
  return request.post('/eccard-dorm/selfChoice/page', params);
}
export function addSelfChoice(params) {
  return request.post('/eccard-dorm/selfChoice', params);
}
export function editSelfChoice(params) {
  return request.put('/eccard-dorm/selfChoice', params);
}
export function delSelfChoice(params) {
  return request.delete(`/eccard-dorm/selfChoice/${params}`);
}

//获取人员入住列表
export function personStayInfoList(params) {
  return request.post('/eccard-dorm/personStayInfo/page', params);
}

//导出人员入住列表
export function exportPersonStayInfo(params) {
  return request.post('/eccard-dorm/personStayInfo/export', params,
    {
      headers: {
        'Content-Type': 'application/octet-stream',
        Accept: "*/*"
      },
      responseType: 'blob'
    });
  }

//获取班级入住列表
export function deptStayInfoList(params) {
  return request.post('/eccard-dorm/deptStayInfo/page', params);
}

//导出班级入住列表
export function exportDeptStayInfo(params) {
  return request.post('/eccard-dorm/deptStayInfo/export', params,
    {
      headers: {
        'Content-Type': 'application/octet-stream',
        Accept: "*/*"
      },
      responseType: 'blob'
    });
  }

//获取宿舍入住列表
export function dormStayInfoList(params) {
  return request.post('/eccard-dorm/dormStayInfo/page', params);
}

//导出宿舍入住列表
export function exportDormStayInfo(params) {
  return request.post('/eccard-dorm/dormStayInfo/export', params,
    {
      headers: {
        'Content-Type': 'application/octet-stream',
        Accept: "*/*"
      },
      responseType: 'blob'
    });
  }



//大屏监控参数管理列表
export function largeScreenParamList(params) {
  return request.post('/eccard-dorm/largeScreenParam/list', params);
}

//修改大屏监控参数管理信息
export function editLargeScreenParam(params) {
  return request.put(`/eccard-dorm/largeScreenParam`, params);
}

//消息推送参数管理列表
export function messagePushParamList(params) {
  return request.post('/eccard-dorm/messagePushParam/list', params);
}

//修改消息推送参数管理信息
export function editMessagePushParam(params) {
  return request.put(`/eccard-dorm/messagePushParam`, params);
}