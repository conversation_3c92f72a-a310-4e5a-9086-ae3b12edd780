<template>
  <kade-route-card style="height: 100%">
    <div class="deviceRealTimeMonitor">
      <kade-table-wrap title="组列表" style="width: 300px; height: 100%; margin-right: 20px;min-width:300px"
        v-loading="state.loading">
        <el-divider></el-divider>
        <div class="search-box">
          <el-input v-model="state.form.groupName" placeholder="请输入关键字搜索" size="small" clearable>
            <template #append>
              <el-button icon="el-icon-sousuo" @click="search()">查询</el-button>
            </template>
          </el-input>
          <div class="list-box" v-infinite-scroll="load" :infinite-scroll-immediate="false">
            <div class="list-item" :class="index == state.dataIndex && 'item-active'"
              @click="dataNameClick(item, index)" v-for="(item, index) in state.dataList" :key="index">
              <i class="el-icon-liebiaolist46"></i>
              <div class="item-name">{{ item.groupName }}</div>
            </div>
          </div>
          <el-button @click="handleAdd()" size="mini" icon="el-icon-plus" type="success">添加</el-button>
          <el-button @click="handleEdit()" size="mini" icon="el-icon-edit" type="primary">编辑</el-button>
          <el-button @click="handleDel()" size="mini" icon="el-icon-close" type="danger">删除</el-button>
        </div>
      </kade-table-wrap>
      <kade-bind-device-list :rowData="state.rowData" /> 
    </div>
    <kade-acGroup-edit v-model="state.isEdit" :rowData="state.rowData" :editType="state.editType"
      @update:modelValue="close" />

  </kade-route-card>
</template>
<script> 
import { onMounted, reactive } from "vue";
import {
  ElDivider,
  ElInput,
  ElButton, 
  ElMessage,
  ElMessageBox
} from "element-plus";
import { acsGroupingInfoList, acsGroupingInfoDel } from "@/applications/eccard-uac/api";
import edit from "./components/edit.vue"
import bindDeviceList from "./components/bindDeviceList.vue"
export default {
  components: {
    ElDivider,
    ElInput,
    ElButton,
    "kade-acGroup-edit": edit,
    "kade-bind-device-list": bindDeviceList,
  },
  setup() {
    const state = reactive({
      tab: "yysq",
      areaKeyword: "",
      loading: false,
      isEdit: false,
      dataIndex: 0,
      editType: "",
      form: {
        groupType:"deviceGroup"
      },
      dataList: [],
      rowData: {},
      appList: []
    });
    const getList = async () => {

      for (let key in state.form) {
        if (!state.form[key]) {
          delete state.form[key]
        }
      }
      state.loading = true;
      try {
        let { data, code } = await acsGroupingInfoList(state.form);
        if (code === 0) {
          state.dataList = data
          if (state.dataList.length) {
            state.rowData = state.dataList[state.dataIndex] ? state.dataList[state.dataIndex] : state.dataList[0]
          } else {
            state.rowData = {}
          }
        }
        state.loading = false;
      }
      catch {
        state.loading = false;
      }
    };

    const load = () => {
      state.form.pageNum++
      getList()
    }
    const search = () => {
      state.form.pageNum = 1
      state.deviceList = []
      getList()
    }
    const handleAdd = () => {
      state.editType = 'add'
      state.isEdit = true
    }
    const handleEdit = () => {
      if (!state.rowData.id) {
        return ElMessage.error("请选择组！")
      }
      state.editType = 'edit'
      state.isEdit = true
    }
    const handleDel = () => {
      if (!state.rowData.id) {
        return ElMessage.error("请选择组！")
      }
      ElMessageBox.confirm("确认删除?", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        let { code, message } = await acsGroupingInfoDel(state.rowData.id);
        if (code === 0) {
          ElMessage.success(message);
          state.form.pageNum = 1
          state.deviceList = []
          getList();
        }
      });
    }
    const dataNameClick = (item, index) => {
      console.log(item, index);
      state.dataIndex = index
      state.rowData = item
    }
    const close = (val) => {
      if (val) {
        state.form.pageNum = 1
        state.deviceList = []
        getList()
      }
      state.isEdit = false
    }
    onMounted(() => {
      getList();
    });
    return {
      state,
      load,
      search,
      dataNameClick,
      handleAdd,
      handleEdit,
      handleDel,
      close
    };
  },
};
</script>
<style lang="scss" scoped>
.deviceRealTimeMonitor {
  height: 100%;
  display: flex;
  justify-content: space-between;

  .search-box {
    padding: 10px;
    height: 70vh;
  }

  .list-box {
    margin: 10px 0;
    height: 90%;
    overflow-y: scroll;

     .list-item {
      display: flex;
      align-items: center;
      padding: 10px;

      .item-name {
        margin-left: 10px;
      }
    }

    .item-active {
      background: #409eff;
      color: #fff;
    }
  }
}

:deep(.el-tabs__item) {
  height: 52px;
  line-height: 52px;
}

:deep(.el-divider--horizontal) {
  margin: 0;
}

:deep(.el-dialog__footer) {
  text-align: center;
}
</style>