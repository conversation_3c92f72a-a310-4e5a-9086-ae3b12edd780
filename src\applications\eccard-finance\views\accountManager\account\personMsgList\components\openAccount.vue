<template>
  <div class="box-dialog">
    <kade-table-wrap title="开户人员">
      <el-table style="width: 100%" :data="[selectPerson]" border stripe>
        <el-table-column width="150" label="用户编号" prop="userCode" align="center"></el-table-column>
        <el-table-column prop="userName" label="姓名" align="center">
        </el-table-column>
        <el-table-column label="组织机构" prop="deptName" align="center"></el-table-column>
        <el-table-column label="身份类别" prop="userRoleName" align="center"></el-table-column>
        <el-table-column label="账户状态" prop="acctStatus" align="center">
          <template #default="scope">
            {{
            filterDictionary(scope.row.acctStatus, state.accountStatusList)
            }}
          </template>
        </el-table-column>
        <el-table-column label="卡片类别" prop="ctName" align="center"></el-table-column>
        <el-table-column label="卡片状态" prop="cardStatus" align="center"><template #default="scope">
            {{ filterDictionary(scope.row.cardStatus, state.cardStatusList) }}
          </template></el-table-column>
        <el-table-column width="153" label="联系方式" prop="userTel" align="center"></el-table-column>
      </el-table>
    </kade-table-wrap>
    <kade-table-wrap title="开户信息设置" style="margin-top: 10px">
      <el-divider></el-divider>
      <div class="account-tips">
        选择开户策略后，卡片类型和钱包类型及有效期，设置为开户策略的设定，不选择开户类型则手动设置卡片类型和钱包类型及有效期
      </div>
      <el-form inline size="small" label-width="100px">
        <el-form-item label="开户策略:">
          <el-select clearable v-model="state.accountStrategy" placeholder="请选择" @change="handleChange" @clear="clear">
            <el-option v-for="(item, index) in accountStrategyList" :key="index" :label="item.strategyName"
              :value="item.strategyId">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="卡片类型:">
          <el-select :disabled="state.isDisabled" clearable v-model="state.form.acctType" placeholder="请选择">
            <el-option v-for="(item, index) in cardTypeList" :key="index" :label="item.ctName" :value="item.ctCode">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <el-table style="width: 100%" :data="state.form.walletList" v-loading="false" border stripe>
        <el-table-column label="钱包名称" prop="walletName" align="center"></el-table-column>
        <el-table-column label="钱包类型" prop="walletType" align="center">
          <template #default="scope">
            <div>
              {{ filterDictionary(scope.row.walletType, state.walletTypeList) }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="是否启用" align="center">
          <template #default="scope">
            <el-switch v-model="scope.row.walletStatus" inline-prompt :active-value="'WALLET_NORMAL'"
              :inactive-value="'WALLET_NOT_ACTIVE'" />
          </template>
        </el-table-column>
        <el-table-column label="有效期（年）" align="center"><template #default="scope">
            <div v-if="scope.row.walletStatus == 'WALLET_NOT_ACTIVE'" class="ash">
              未开启
            </div>
            <el-input v-else class="date-num-input" placeholder="请输入" type="number"
              v-model="scope.row.walletValidityDateNum" :disabled="state.isDisabled" :min="1" size="mini"></el-input>
          </template></el-table-column>
        <el-table-column label="有效期至" width="300" align="center">
          <template #default="scope">
            <div v-if="scope.row.walletStatus == 'WALLET_NOT_ACTIVE'" class="ash">
              未开启
            </div>

            <div v-else>
              {{
              scope.row.walletValidityDateNum
              ? timeStrDate(
              scope.row.walletValidityDateNum * 3600 * 1000 * 24 * 365 +
              new Date().getTime()
              )
              : ""
              }}
            </div>
          </template>
        </el-table-column>
      </el-table>
    </kade-table-wrap>
    <div class="submit-btn">
      <el-button @click="off()" size="mini">取&nbsp;&nbsp;消</el-button>
      <el-button @click="submitForm()" size="mini" type="primary" :loading="state.loading">确&nbsp;&nbsp;认</el-button>
    </div>
  </div>
</template>
<script>
import {
  ElDivider,
  ElTable,
  ElTableColumn,
  ElForm,
  ElFormItem,
  ElSelect,
  ElOption,
  ElButton,
  ElInput,
  ElMessage,
  ElSwitch,
} from "element-plus";
import { dateStr } from "@/utils/date.js";
import {
  getAccountStrategyInfo,
  openPersonAccount,
  getWalletActiveList,
} from "@/applications/eccard-finance/api";
import { reactive } from "@vue/reactivity";
import { computed, watch } from "@vue/runtime-core";
import { useStore } from "vuex";
export default {
  components: {
    ElDivider,
    ElTable,
    ElTableColumn,
    ElForm,
    ElFormItem,
    ElButton,
    ElSelect,
    ElOption,
    ElInput,
    ElSwitch,
  },
  props: {
    accountStrategyList: {
      types: Array,
      default: [],
    },
    cardTypeList: {
      types: Array,
      default: [],
    },
    allWalletList: {
      types: Array,
      default: [],
    },
    isOpenAccount: {
      types: Boolean,
      default: false,
    },
  },
  setup(props, context) {
    const store = useStore();
    console.log(props);
    const state = reactive({
      loading: false,
      isDisabled: false,
      form: {
        acctType: null,
        queryCondition: {
          deptPath: null,
          keyWord: null,
          userRoleId: null,
        },
        selectAll: false,
        userIds: null,
        walletList: [],
      },
      accountStrategy: "",
      accountStatusList: JSON.parse(
        localStorage.getItem("kade_cache_dictionary")
      ).filter((item) => item.dictType == "PERSON_ACCOUNT_STATUS"), //账户状态
      walletTypeList: JSON.parse(
        localStorage.getItem("kade_cache_dictionary")
      ).filter((item) => item.dictType == "PERSON_WALLET_TYPE"), //钱包类型
      walletStatusList: JSON.parse(
        localStorage.getItem("kade_cache_dictionary")
      ).filter((item) => item.dictType == "PERSON_WALLET_STATUS"), //钱包状态
      cardStatusList: JSON.parse(
        localStorage.getItem("kade_cache_dictionary")
      ).filter((item) => item.dictType == "PERSON_CARD_STATUS"), //卡片状态
    });
    const timeStrDate = computed(() => {
      return dateStr;
    });
    const selectPerson = computed(() => {
      return store.state.data.selectPerson;
    });

    const filterDictionary = (val, dictionaryList) => {
      for (let i of dictionaryList) {
        if (val == i.dictCode) {
          return i.dictValue;
        }
      }
    };

    watch(
      () => props.allWalletList,
      (val) => {
        state.form.walletList = val;
      }
    );
    watch(
      () => props.isOpenAccount,
      (val) => {
        if (!val) {
          off();
        }
      }
    );
    const handleChange = (val) => {
      if (!val) return
      getAccountStrategyInfo({ strategyId: val }).then((res) => {
        state.form.acctType = res.data.cardType;
        state.isDisabled = true;
        state.form.walletList = res.data.strategyWallets.map((item) => ({
          walletName: item.walletName,
          walletCode: item.walletCode,
          walletStatus: "WALLET_NOT_ACTIVE",
          walletType: item.walletType,
          walletValidityDate: "",
          walletValidityDateNum: item.termMonth / 12,
        }));
      });
    };

    //获取钱包列表
    const queryWalletActiveList = () => {
      getWalletActiveList().then((res) => {
        state.form.walletList = res.data.map((item) => {
          return {
            walletCode: item.walletCode,
            walletName: item.walletName,
            walletStatus: "WALLET_NOT_ACTIVE",
            walletType: item.walletType,
            walletValidityDate: "",
            walletValidityDateNum: 5,
          };
        });
      });
    };

    const clear = () => {
      state.accountStrategy = "";
      state.form.acctType = "";
      state.isDisabled = false;
      queryWalletActiveList();
    };

    const off = () => {
      context.emit("off", false);
      state.form = {
        acctType: null,
        queryCondition: {
          deptPath: null,
          keyWord: null,
          userRoleId: null,
        },
        selectAll: false,
        userIds: null,
        walletList: [],
      };
      state.accountStrategy = "";
      state.isDisabled = false;
    };
    const submitForm = () => {
      console.log(state.form);
      if (!state.form.walletList.length) {
        return ElMessage.error("请选择开户策略！");
      }
      if (!state.form.acctType) {
        return ElMessage.error("请选择卡片类型！");
      }

      for (let item of state.form.walletList) {
        if (!item.walletStatus) {
          ElMessage.error("请选择钱包状态！");
          return false;
        }
        if (!item.walletType) {
          ElMessage.error("请选择钱包类型！");
          return false;
        }
        if (!item.walletValidityDateNum) {
          ElMessage.error("请输入钱包有效期！");
          return false;
        }
        item.walletValidityDate = dateStr(
          item.walletValidityDateNum * 3600 * 1000 * 24 * 365 +
          new Date().getTime()
        );
      }
      state.form.userIds = [store.state.data.selectPerson.userId];
      console.log(state.form);
      state.loading = true
      openPersonAccount(state.form)
        .then(({ code, message }) => {
          if (code === 0) {
            ElMessage.success(message);
            context.emit("success", true);
            state.form = {
              acctType: null,
              queryCondition: {
                deptPath: null,
                keyWord: null,
                userRoleId: null,
              },
              selectAll: false,
              userIds: null,
              walletList: [],
            };
          } else {
            ElMessage.error(message);
          }
          state.loading = false
        })
        .catch(() => {
          state.loading = false
        });
    };
    return {
      state,
      timeStrDate,
      selectPerson,
      off,
      clear,
      queryWalletActiveList,
      handleChange,
      submitForm,
      filterDictionary,
    };
  },
};
</script>
<style lang="scss" scoped>
.box-dialog {
  padding: 10px 0;

  .el-table__row {
    height: 30px !important;
  }

  .account-tips {
    text-align: center;
    margin-bottom: 10px;
    color: #f00;
  }
}

.table-box {
  padding: 10px;
}

.submit-btn {
  text-align: center;
  margin: 10px auto;
}

.el-input__inner {
  border: 0;
}

.el-divider--horizontal {
  margin: 0 0 10px;
}
</style>