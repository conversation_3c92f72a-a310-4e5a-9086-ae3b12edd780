<template>
  <div class="panel" :style="styles">
    <kade-nav-menu ref="menu" :isWideDevice="isWideDevice" :toggle="toggle" />
    <div class="panel-right">
      <div class="panel-header">
        <kade-header>
          <template #title>
            <div class="panel-header-title">
              <kade-icon :reverse="toggle" size="22px" color="#666" @click.stop="toggleLeft" name="iconzhankai" />
            </div>
          </template>
        </kade-header>
      </div>
      <div class="panel-content">
        <div class="panel-wrap">
          <kade-menu-tab />
        </div>
       <!--  <div class="copyright">
          成都卡德智能科技有限公司 Copyright @ 2021-2022 All right Reserved
        </div> -->
      </div>
    </div>
  </div>
</template>
<script>
// 通用后台系统面板
import Header from '@/components/header';
import NavMenu from '@/components/navMenu';
import Icon from '@/components/icon';
import MenuTab from '@/components/menuTab';
export default {
  components: {
    'kade-header': Header,
    'kade-icon': Icon,
    'kade-nav-menu': NavMenu,
    'kade-menu-tab': MenuTab,
  },
  data() {
    return {
      toggle: false,
      isWideDevice: true,
    }
  },
  methods: {
    toggleLeft() {
      this.toggle = !this.toggle;
    },
    initDevice() {
      this.isWideDevice = document.body.clientWidth > 998 ? true : false;
      if (!this.isWideDevice) {
        this.toggle = false;
      }
    },
    handleWindowClick(e) {
      if (this.toggle && !this.isWideDevice) {
        if (!this.$refs.menu.$refs.box.contains(e.target)) {
          this.toggle = !this.toggle;
        }
      }
    }
  },
  computed: {
    styles() {
      return {
        paddingLeft: this.isWideDevice ? (this.toggle ? '65px' : '210px') : 0,
      }
    }
  },
  mounted() {
    this.initDevice();
    window.addEventListener('resize', this.initDevice);
    window.addEventListener('click', this.handleWindowClick);
  },
  unmounted() {
    window.removeEventListener('resize', this.initDevice);
    window.removeEventListener('click', this.handleWindowClick);
  },
}
</script>
<style lang="scss" scoped>
.panel {
  width: 100%;
  height: 100%;
  position: relative;
  transition: padding .2s linear;
  box-sizing: border-box;

  .panel-right {
    width: 100%;
    height: calc(100% - 50px);
  }

  .panel-header-title {
    display: flex;
    justify-content: flex-start;
    align-items: center;

    ::v-deep .el-breadcrumb {
      margin-left: 30px;

      .el-breadcrumb__inner {
        color: $panel-font-breamb-color;

        &.is-link {
          color: $primary-color;
        }
      }

      .el-breadcrumb__separator {
        color: $panel-font-breamb-seperate-color;
      }
    }
  }

  .panel-content {
    width: 100%;
    height: 100%;
    background-color: $panel-main-bg;
    padding: 20px;
    box-sizing: border-box;

    .copyright {
      text-align: center;
      color: #999;
      font-size: 12px;
      line-height: 20px;
    }
  }

  .panel-wrap {
    width: 100%;
    height: 100%;
    overflow: hidden;
  }
}
</style>