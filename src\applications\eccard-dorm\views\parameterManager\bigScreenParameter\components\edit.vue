<template>
    <el-dialog
    :model-value="dialogVisible"
    title="编辑参数值"
    width="350px"
    :before-close="handleClose">
    <el-form inline size="mini" label-width="90px" :model="state.form" :rules="rules" ref="formRef">
      <el-form-item label="参数名称：">
          <el-input v-model="state.form.paramName" disabled  placeholder="宿舍考勤通知"></el-input>
      </el-form-item>
      <el-form-item label="参数值：" prop="paramValue">
          <el-input v-model="state.form.paramValue" maxlength="20"></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button type="primary" @click="submit()" size="mini">保存</el-button>
        <el-button @click="handleClose" size="mini">返回</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { ElDialog,ElButton,ElForm,ElFormItem,ElInput, ElMessage } from "element-plus"
import { watch, nextTick } from '@vue/runtime-core';
import { reactive,ref } from '@vue/reactivity'
import { editLargeScreenParam } from '@/applications/eccard-dorm/api.js'
export default {
    props:{
        dialogVisible:{
            type:Boolean,
            default:false
        },
        rowData:{
            type:String,
            default:""
        }
    },
    components:{
        ElDialog,
        ElButton,
        ElForm,
        ElFormItem,
        ElInput
    },
    setup(props,context){
        const formRef=ref(null)
        const state=reactive({
            form:{},
        });
        const rules={
            paramValue:{ required: true, message: "请输入", trigger: "blur" }
        };
        const handleClose=()=>{
            context.emit('close',false)
        };
        watch(()=>props.dialogVisible,val=>{
            if(val){
            state.form={ ...props.rowData }
            console.log(state.form)
            nextTick(()=>{
                formRef.value.clearValidate()
            })
            }
        })
        const submit=()=>{
            formRef.value.validate(async(valid)=>{
                if(valid){
                    let param = {...state.form}
                    let { code,message } = await editLargeScreenParam(param)
                    if(code===0){
                        ElMessage.success(message)
                        context.emit('close',true)
                    }
                }
            })
        };
        return{
            state,
            submit,
            formRef,
            rules,
            handleClose,
        }
    }
}
</script>