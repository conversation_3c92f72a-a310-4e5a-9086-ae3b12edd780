<template>
  <el-dialog :model-value="modelValue" :title="title" width="700px" :before-close="handleClose">
    <el-form v-loading="state.loading" :model="state.form" ref="formRef" :rules="type!='details'&&rules" inline label-width="110px" size="mini">
      <el-form-item v-if="type=='details'" label="所属区域：">
        <el-input :model-value="state.form.areaName" readonly></el-input>
      </el-form-item>
      <el-form-item v-if="type=='details'" label="管理楼栋：">
        <el-input :model-value="state.form.buildName" readonly></el-input>
      </el-form-item>
      <el-form-item v-if="type=='details'" label="管理单元：">
        <el-input :model-value="state.form.unitName" readonly></el-input>
      </el-form-item>
      <kade-linkage-select v-if="type!=='details'" :isEdit="modelValue?true:false" :data="linkageData" :value="state.form" @change="linkageChange" />
      <el-form-item label="资产编号：" prop="code">
        <el-input v-if="type=='details'" :model-value="state.form.code" readonly></el-input>
        <el-input v-else v-model="state.form.code" placeholder="请输入"></el-input>
      </el-form-item>
      <el-form-item label="资产名称：" prop="name">
        <el-input v-if="type=='details'" :model-value="state.form.name" readonly></el-input>
        <el-input v-else v-model="state.form.name" placeholder="请输入"></el-input>
      </el-form-item>
      <el-form-item label="规格型号：">
        <el-input v-if="type=='details'" :model-value="state.form.specs" readonly></el-input>
        <el-input v-else v-model="state.form.specs" placeholder="请输入" :max="50"></el-input>
      </el-form-item>
      <el-form-item label="数量：" prop="num">
        <el-input v-if="type=='details'" :model-value="state.form.num" readonly></el-input>
        <el-input-number v-else v-model="state.form.num" :min="1" :max="10" @change="handleChange" />
      </el-form-item>
      <el-form-item label="资产位置：">
        <el-input v-if="type=='details'" :model-value="state.form.position" readonly class="position"></el-input>
        <el-input v-else v-model="state.form.position" placeholder="请输入" class="position"></el-input>
      </el-form-item>
      <el-form-item label="资产图片：">
        <div class="img-box">
          <el-image v-if="type=='details'" style="width: 100px; height: 100px; margin-right: 20px" :src="state.form.resourceUrl" :preview-src-list="[state.form.resourceUrl]" :initial-index="0" fit="cover"></el-image>
          <kade-single-image-upload v-else style="width:100px; height: 100px" v-model="state.form.resourceUrl" :action="uploadApplyLogo" icon="el-icon-plus" />
        </div>
      </el-form-item>
    </el-form>
    <template #footer v-if="type!=='details'">
      <span class="dialog-footer">
        <el-button @click="handleClose" size="mini">取消</el-button>
        <el-button type="primary" @click="submit" size="mini">确认</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { reactive, watch, computed, ref, nextTick } from 'vue'
import { ElDialog, ElButton, ElForm, ElFormItem, ElInput, ElInputNumber, ElImage, ElMessage } from "element-plus"
import SingleImageUpload from "@/components/singleImageUpload"
import { uploadApplyLogo } from "@/applications/unified_portal/api";
import linkageSelect from '@/applications/eccard-dorm/components/linkageSelect'
import { addAssetsInfo, editAssetsInfo } from "@/applications/eccard-dorm/api.js"
const linkageData = {
  area: { label: '所属区域：', valueKey: 'areaId', key: 'id' },
  building: { label: '管理楼栋：', valueKey: 'buildId' },
  unit: { label: '管理单元：', valueKey: 'unitNum' }
}
const rules = {
  areaId: [{ required: true, message: '请选择区域', trigger: 'blur' }],
  buildId: [{ required: true, message: '请选择楼栋', trigger: 'blur' }],
  unitNum: [{ required: true, message: '请选择单元', trigger: 'blur' }],
  code: [{ required: true, message: '请输入编号', trigger: 'blur' },{max:20,message:'资产编号不能超过20字符'}],
  name: [{ required: true, message: '请输入名称', trigger: 'blur' },{max:50,message:'资产名称不能超过50字符'}],
  num: [{ required: true, message: '请输入数量', trigger: 'blur' }]
}
export default {
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: ""
    },
    rowData: {
      type: Object,
      default: null
    }
  },
  components: {
    ElDialog,
    ElButton,
    ElForm,
    ElFormItem,
    ElInput,
    ElInputNumber,
    ElImage,
    "kade-single-image-upload": SingleImageUpload,
    "kade-linkage-select": linkageSelect
  },
  setup(props, context) {
    const formRef = ref(null)
    const state = reactive({
      title: '',
      form: {
        resourceUrl: '',
        remark: 0,
      },
      loading: false,
    })
    const linkageChange = (val) => {
      state.form = { ...state.form, ...val }
    }
    const title = computed(() => {
      if (props.type == 'add') {
        return '新增资产'
      } else if (props.type == 'edit') {
        return '编辑资产'
      } else {
        return '资产详情'
      }
    })
    watch(() => props.modelValue, (val) => {
      if (val) {
        if (props.type === 'add') {
          state.form = {
            resourceUrl: '',
            remark: 0,
          }
        } else {
          let { id,areaId, buildId, code, name, num, position, remark, resourceUrl, specs, unitNum,areaName,buildName,unitName } = { ...props.rowData }
          state.form = { id,areaId, buildId, code, name, num, position, remark, resourceUrl, specs, unitNum,areaName,buildName,unitName }

        }
        nextTick(() => {
          formRef.value.clearValidate()
        })
      }
    })
    const submit = () => {
      formRef.value.validate(async (valid) => {
        if (valid) {
          let param = { ...state.form }
            console.log(param)
          let fn = props.type == 'add' ? addAssetsInfo : editAssetsInfo
          state.loading = true
          try {
            let { code, message } = await fn(param)
            if (code === 0) {
              ElMessage.success(message)
              context.emit("update:modelValue", true)
            }
            state.loading = false
          }
          catch{
            state.loading = false
          }
        }
      })
    }
    const handleClose = () => {
      context.emit("update:modelValue", false)
      formRef.value.clearValidate()
    }
    return {
      state,
      title,
      rules,
      formRef,
      submit,
      handleClose,
      linkageData,
      linkageChange,
      uploadApplyLogo,
    }
  }
}
</script>

<style lang="scss" scoped>
:deep(.position) {
  width: 516px !important;
  .el-input__inner {
    width: 516px !important;
  }
}
.img-box{
  display: flex;
  align-items: center;
}
</style>