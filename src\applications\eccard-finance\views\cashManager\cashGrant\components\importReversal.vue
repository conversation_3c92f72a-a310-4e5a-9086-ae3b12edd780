<template>
  <div>
    <el-dialog
      :model-value="isImportReversal"
      title="导入冲正"
      width="90%"
      :before-close="beforeClose"
      :close-on-click-modal="false"
    >
      <div>
        <div class="padding-form-box">
          <el-form
            ref="form"
            :model="state.form"
            :rules="state.rules"
            inline
            size="small"
            label-width="100px"
          >
            <el-form-item label="项目名称:" prop="projectName">
              <el-input
                placeholder="请输入"
                v-model="state.form.projectName"
              ></el-input>
            </el-form-item>
            <el-form-item label="充值类型:" prop="walletCode">
              <el-select
                disabled
                v-model="state.form.walletCode"
                placeholder="请选择"
              >
                <el-option
                  v-for="(item, index) in [{ label: '现金钱包', value: 1 }]"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="充值方式:" prop="rechargeMode">
              <el-select
                clearable
                v-model="state.form.rechargeMode"
                placeholder="请选择"
              >
                <el-option
                  v-for="(item, index) in rechargeType"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-form>
          <el-form size="small" label-width="100px">
            <el-form-item label="发放说明:">
              <el-input
                placeholder="请输入"
                type="textarea"
                v-model="state.form.grantRemark"
              ></el-input>
            </el-form-item>
          </el-form>
          <el-form inline size="small" label-width="100px">
            <el-form-item label=" 上传文件:">
              <el-upload
                class="upload-demo"
                :action="state.requestUrl"
                :headers="{ Authorization: `bearer ${state.requestHeader}` }"
                :data="{ projectId: store.state.cashData.selectRow.projectId }"
                :on-success="handleSuccess"
                :on-remove="handleRemove"
                :on-exceed="handleExceed"
                :limit="1"
                :file-list="fileList"
                ref="uploadDom"
              >
                <el-button size="small" type="primary">上传充值清单</el-button>
              </el-upload>
            </el-form-item>
            <el-form-item>
              <div class="blue" @click="uploadSample()">下载样例</div>
            </el-form-item>
          </el-form>
          <div style="margin-bottom: 10px" v-if="state.listData.errorCount > 0">
            <span class="red"> • </span>异常数据
            <span class="red"> {{ state.listData.errorCount }}</span>
            条，请修改确认
          </div>
          <el-table
            style="width: 100%"
            :data="state.personList"
            v-loading="state.personLoading"
            border
            stripe
          >
            <el-table-column
              label="校检结果"
              prop="verifyResult"
              align="center"
            >
              <template #default="scope">
                <div :style="scope.row.error && 'color:#f00'">
                  {{ scope.row.verifyResult }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              label="用户编号"
              prop="userCode"
              align="center"
            ></el-table-column>
            <el-table-column
              label="姓名"
              prop="userName"
              align="center"
            ></el-table-column>
            <el-table-column
              label="组织机构"
              prop="deptName"
              align="center"
            ></el-table-column>
            <el-table-column
              label="身份类别"
              prop="userRole"
              align="center"
            ></el-table-column>
            <el-table-column
              label="充值金额（元）"
              prop="rechargeAmount"
              align="center"
            ></el-table-column>
            <el-table-column label="操作" prop="userName" align="center">
              <template #default="scope">
                <!-- <span @click="delClick(scope.row)" style="margin-right: 10px"
                  >编辑</span
                > -->
                <span @click="delClick(scope.row)" class="green">删除</span>
              </template>
            </el-table-column>
          </el-table>
          <div class="pagination-box" v-if="state.personList.length">
            <div>
              <span
                >合计：<span>{{ state.listData.totalCount }}</span
                >人，<span>{{ state.listData.totalAmount }}</span
                >元</span
              >
            </div>
            <div class="pagination">
              <el-pagination
                background
                :current-page="state.cashPersonForm.currentPage"
                :page-size="state.cashPersonForm.pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :page-sizes="[6, 10, 20, 50, 100]"
                :total="state.total"
                @current-change="handlePageChange"
                @size-change="handleSizeChange"
              >
              </el-pagination>
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="beforeClose()" size="mini"
            >关&nbsp;&nbsp;闭</el-button
          >
          <el-button @click="submitForm()" size="mini" type="primary"
            >确&nbsp;&nbsp;认</el-button
          >
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script>
import { computed, onMounted, reactive, ref } from "vue";
import { getToken, downloadXlsx } from "@/utils";

import { timeStr } from "@/utils/date.js";
import {
  exportRightingExample,
  getWaitRechargeRightingList,
  deleteWaitRighting
} from "@/applications/eccard-finance/api";
import { useDict } from "@/hooks/useDict";

import { useStore } from "vuex";

import {
  ElDialog,
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElTable,
  ElTableColumn,
  ElPagination,
  ElUpload,
  ElMessage,
} from "element-plus";
export default {
  components: {
    "el-dialog": ElDialog,
    "el-button": ElButton,
    "el-input": ElInput,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    "el-select": ElSelect,
    "el-option": ElOption,
    "el-table": ElTable,
    "el-table-column": ElTableColumn,
    "el-pagination": ElPagination,
    ElUpload,
  },
  setup() {
    const uploadDom = ref(null);
    const store = useStore();
    let ruleForm = ref(null);
    const rechargeType = useDict("SYS_RECHARGE_MODE");

    const state = reactive({
      loading: false,
      grantType: "IMPORT",
      personList: [],
      listData: {
        totalCount: 0,
        totalAmount: 0,
      },
      form: {
        walletCode: 1,
      },
      rules: {
        projectName: [
          {
            required: true,
            message: "请输入项目名称",
            trigger: "blur",
          },
        ],
        rechargeMode: [
          {
            required: true,
            message: "请选择充值方式",
            trigger: "change",
          },
        ],
      },
      cashPersonForm: {
        currentPage: 1,
        pageSize: 6,
      },
      total: 0,
      requestHeader: getToken(),
      requestUrl: `${CONFIG.BASE_API_PATH}eccard-finance/CashGrant/importRechargeRightingList`,
    });

    const isImportReversal = computed(() => {
      return store.state.cashData.isImportReversal;
    });

    const handleSuccess = (res) => {
      state.cashPersonForm.projectNo = res.data.projectNo;
      console.log(state.cashPersonForm);
      getpersonList();
    };

    const handleRemove = () => {
      state.listData = {
        totalCount: 0,
        totalAmount: 0,
      };
      state.total = 0;
      state.personList = [];
      state.cashPersonForm = {
        currentPage: 1,
        pageSize: 6,
      };
    };

    //上传文件个数超出限制提示
    const handleExceed = (files, fileList) => {
      if (fileList.length === 1) {
        ElMessage.error("最多上传一个文件！");
      }
    };

    const uploadSample = async () => {
      let res = await exportRightingExample();
      downloadXlsx(res, "现金发放冲正样例.xlsx");
    };

    const getpersonList = async () => {
      state.loading = true;
      let {
        data: {
          generateRechargeDto: { errorCount, totalAmount, totalCount },
          pageInfo,
        },
      } = await getWaitRechargeRightingList(state.cashPersonForm);
      state.personList = pageInfo.list;
      state.total = pageInfo.size;
      state.listData = { errorCount, totalAmount, totalCount };
      state.loading = false;
    };

    const delClick=async (row)=>{
      let {message,code}=await deleteWaitRighting({id:row.id,projectNo:state.cashPersonForm.projectNo})
      if(code===0){
        ElMessage.success(message)
        getpersonList()
      }
    }

    const beforeClose = () => {
      store.commit("cashData/updateState", {
        key: "isImportReversal",
        payload: false,
      });
    };

    const handlePageChange = (val) => {
      state.cashPersonForm.currentPage=val
        getpersonList()
    };
    const handleSizeChange = (val) => {
      state.cashPersonForm.pageSize=val
        getpersonList()
    };

    const submitForm = async () => {
      console.log(ruleForm);
      ruleForm.value.validate(async (valid) => {
        if (valid) {
          if (!state.form.projectNo) {
            return ElMessage.error("请先生成冲正清单！");
          }
          state.form.subsidyMonth = timeStr(state.form.subsidyMonth);
          state.form.grantMode = "IMPORT";
          let data = { ...state.form, ...state.generateSubsidyForm };
          data.relationProjectId = store.state.subsidyData.selectRow.id;
          /* let { code, message } = await saveImportSubsidyRightingList(data);
          if (code === 0) {
            ElMessage.success(message);
            context.emit("offImportReversal", true);
          } else {
            ElMessage.error(message);
          } */
        } else {
          return false;
        }
      });
    };

    onMounted(() => {});
    return {
      store,
      state,
      uploadDom,
      ruleForm,
      submitForm,
      rechargeType,
      isImportReversal,
      handleSuccess,
      handleRemove,
      handleExceed,
      uploadSample,
      getpersonList,
      delClick,
      beforeClose,
      handlePageChange,
      handleSizeChange,
    };
  },
};
</script>
<style lang="scss" scoped>
:deep(.el-dialog__header) {
  border-bottom: 1px solid #efefef;
}
:deep(.el-dialog__headerbtn) {
  font-size: 30px;
  line-height: 30px;
}
:deep(.el-overlay) {
  position: absolute;
  background-color: rgba(0, 0, 0, 0);
}
:deep(.el-dialog__footer) {
  text-align: center;
}
.select-input-lang {
  .el-select {
    width: 500px;
  }
}
.pagination-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>