<template>
  <el-dialog
    :model-value="dialogVisible"
    :title="dialogTitle"
    width="60%"
    :before-close="handleClose"
  >
    <el-form inline label-width="150px" size="mini">
      <el-form-item label="服务器IP：">
        <el-input :readonly="isReadOnly" v-model="state.form.ip"></el-input>
      </el-form-item>
      <el-form-item label="客户端系统：">
        <el-input :readonly="isReadOnly" v-model="state.form.system"></el-input>
      </el-form-item>
      <el-form-item label="CPU核心数：">
        <el-input :readonly="isReadOnly" v-model="state.form.CPU"></el-input>
      </el-form-item>
      <el-form-item label="CPU频率（GHz）：">
        <el-input :readonly="isReadOnly" v-model="state.form.CPUhz"></el-input>
      </el-form-item>
      <el-form-item label="内存大小（G）：">
        <el-input :readonly="isReadOnly" v-model="state.form.Memory"></el-input>
      </el-form-item>
      <el-form-item label="磁盘大小（GB）：">
        <el-input :readonly="isReadOnly" v-model="state.form.disk"></el-input>
      </el-form-item>
    </el-form>
    <el-form label-width="150px" size="mini">
      <el-form-item label="部署服务：">
        <div class="server-box">
          <template v-if="dialogTitle !=='服务器详情'">
            <div class="server-item" v-for="(item,index) in state.form.server" :key="index">
              <div>{{ item.value }}</div>
              <el-checkbox v-model="state.form.server.checked" size="large"></el-checkbox>
            </div>
          </template>
          <template v-else>
            <div class="server-item" v-for="(item,index) in state.form.server" :key="index">
              <div>{{ item.value }}</div>
            </div>
          </template>
        </div>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button
          type="primary"
          v-if="dialogTitle !== '服务器详情'"
          @click="handleClose"
          size="mini"
          >确定</el-button
        >
        <el-button @click="handleClose" size="mini">返回</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { ElDialog, ElButton, ElForm, ElFormItem, ElInput} from "element-plus";
import { reactive } from "@vue/reactivity";
import { computed, watch } from "@vue/runtime-core";
export default {
  components: {
    ElDialog,
    ElButton,
    ElForm,
    ElFormItem,
    ElInput,
  },
  props: {
    dialogVisible: {
      types: Boolean,
      default: false,
    },
    dialogTitle: {
      types: String,
      default: "服务器",
    },
    dialogData: {
      types: Object,
      default: {},
    },
  },
  setup(prop, context) {
    const state = reactive({
      form: {},
    });
    // const isReadOnly=computed(()=>{
    //     return prop.dialogTitle=="服务器详情"?true:false
    // });
    const isReadOnly = computed(() => {
      if (prop.dialogTitle == "服务器详情") {
        return true;
      } else {
        return false;
      }
    });

    watch(
      () => prop.dialogData,
      (val) => {
        state.form = { ...val };
        if(state.form.server){
          state.form.server=state.form.server.split(",")
          state.form.server=state.form.server.map((item)=>{
            return{
              value:item,
              checked:false
            }
          })
          }
      }
    );

    const handleClose = () => {
      context.emit("close", false);
      state.form = {};
    };
    return {
      state,
      handleClose,
      isReadOnly,
    };
  },
};
</script>

<style lang="scss" scoped>
.server-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  min-width: 120px;
  .server-item {
    width: 20%;
    min-width: 120px;
    margin-bottom: 15px;
    border: 1px solid #eeeeee;
    border-radius: 5px;
     display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 4px;
  }
}
:deep(.el-dialog__header){
  border-bottom: 1px solid  #eeeeee;
}

</style>
