<template>
  <div class="reversal-record">
    <div class="header-flexbox">
      <kade-tab-wrap :tabs="tabs" style="margin-top: 20px" v-model="state.tab">
        <template #czjl>
          <div>
            <div class="padding-form-box">
              <el-form inline size="small" label-width="100px">
                <el-form-item label="选择日期:">
                  <el-col :span="24">
                    <el-date-picker v-model="state.requestDate" type="datetimerange" :default-time="state.defaultTime"
                      range-separator="~" start-placeholder="请选择日期" end-placeholder="请选择日期" @change="changeDate">
                    </el-date-picker>
                  </el-col>
                  <el-col :span="3" class="date" v-for="(item, index) in state.defaultDateList" :key="index"
                    @click="changeDefaultDate(item.value)">
                    {{ item.label }}
                  </el-col>
                </el-form-item>
                <el-form-item label="&nbsp;">
                  <el-button @click="search()" size="small" type="primary" icon="el-icon-search">搜索
                  </el-button>
                  <el-button icon="el-icon-refresh-right" @click="reset()" size="small">重置
                  </el-button>
                </el-form-item>
                <el-form-item label="金额范围:" class="money-range">
                  <el-col :span="1"> ￥ </el-col>
                  <el-col :span="10">
                    <el-input placeholder="输入冲正金额" v-model="state.form.minAmount"></el-input>
                  </el-col>
                  <el-col :span="3">&nbsp;&nbsp;&nbsp;- &nbsp;&nbsp;&nbsp;
                  </el-col>
                  <el-col :span="10">
                    <el-input placeholder="输入冲正金额" v-model="state.form.maxAmount"></el-input>
                  </el-col>
                </el-form-item>
                <el-form-item label="纠错钱包:">
                  <el-select clearable v-model="state.form.walletCode" placeholder="请选择">
                    <el-option v-for="(item, index) in state.allWalletList" :key="index" :label="item.walletName"
                      :value="item.walletCode">
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="操作员:">
                  <el-select clearable v-model="state.form.sysUserId" placeholder="请选择">
                    <el-option v-for="(item, index) in state.systemUserList" :key="index" :label="item.userName"
                      :value="item.id">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-form>
            </div>
            <el-divider></el-divider>
            <el-table style="width: 100%" :data="RightingList" v-loading="false" border stripe>
              <el-table-column width="160" label="时间" prop="tradeDate" align="center">
                <template #default="scope">
                  <div>
                    {{ timeStrDate(scope.row.tradeDate) }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="冲正钱包" prop="walletName" align="center"></el-table-column>
              <el-table-column label="原交易单号" prop="relationTradeNo" align="center"></el-table-column>
              <el-table-column label="交易单号" prop="tradeNo" align="center"></el-table-column>
              <el-table-column label="冲正前余额" prop="tradeBeforeBalance" align="center"></el-table-column>
              <el-table-column label="冲正金额" prop="tradeAmount" align="center"></el-table-column>
              <el-table-column label="冲正后余额" prop="tradeAfterBalance" align="center"></el-table-column>
              <el-table-column label="操作员" prop="operatorName" align="center"></el-table-column>
            </el-table>
            <div class="pagination">
              <el-pagination background :current-page="state.form.currentPage"
                layout="total, sizes, prev, pager, next, jumper" :page-size="state.form.pageSize"
                :page-sizes="[6, 10, 20, 50, 100]" :total="RightingListTotal" @current-change="handlePageChange"
                @size-change="handleSizeChange">
              </el-pagination>
            </div>
          </div>
        </template>
      </kade-tab-wrap>
      <div class="header-btn">
        <el-button icon="el-icon-daoru" :disabled="isDisabled" type="primary" @click="upload()" size="mini"
          class="shop-upload">下载查询明细</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import {
  ElButton,
  ElCol,
  ElForm,
  ElFormItem,
  ElDatePicker,
  ElInput,
  // ElDivider,
  ElSelect,
  ElOption,
  ElTable,
  ElTableColumn,
  // ElSwitch,
  ElPagination,
  // ElMessage,
} from "element-plus";
import { timeStr } from "@/utils/date.js";
import { reactive } from "@vue/reactivity";
import {
  getSystemUser,
  getRightingListByExport,
  getWalletActiveList,
} from "@/applications/eccard-finance/api";
import { onMounted, watch, computed } from "@vue/runtime-core";
import { useStore } from "vuex";
import {
  requestDefaultTime,
  requestDate,
  defaultDateList,
} from "@/utils/reqDefaultDate.js";
const tabs = [
  {
    name: "czjl",
    label: "冲正记录",
  },
];
export default {
  components: {
    ElCol,
    // "el-divider": ElDivider,
    "el-button": ElButton,
    "el-input": ElInput,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    "el-select": ElSelect,
    "el-option": ElOption,
    ElDatePicker,
    "el-table": ElTable,
    "el-table-column": ElTableColumn,
    "el-pagination": ElPagination,
    // "el-switch": ElSwitch,
  },

  setup() {
    const store = useStore();
    const state = reactive({
      tab: "czjl",
      isRighting: false,
      total: 0,
      form: {
        beginDate: requestDate()[0],
        endDate: requestDate()[1],
        currentPage: 1,
        pageSize: 6,
        userId: null,
        maxAmount: null,
        minAmount: null,
        sysUserId: null,
      },
      systemUserList: [],
      allWalletList: [],
      requestDate: requestDate(),
      defaultTime: requestDefaultTime(),
      defaultDateList,
    });
    const timeStrDate = computed(() => {
      return timeStr;
    });
    const RightingList = computed(() => {
      return store.state.rightingData.RightingList;
    });
    const RightingListTotal = computed(() => {
      return store.state.rightingData.RightingListTotal;
    });

    const isDisabled = computed(() => {
      return store.state.rightingData.selectPerson ? false : true;
    });

    watch(
      () => store.state.rightingData.selectPerson,
      () => {
        reset();
      }
    );

    const filterDictionary = (val, dictionaryList) => {
      for (let i of dictionaryList) {
        if (val == i.dictCode) {
          return i.dictValue;
        }
      }
    };

    //获取操作员
    const querySystemUser = () => {
      getSystemUser().then((res) => {
        state.systemUserList = res.data;
      });
    };
    //获取钱包列表
    const queryWalletActiveList = () => {
      getWalletActiveList().then((res) => {
        state.allWalletList = res.data;
      });
    };

    const queryRightingList = () => {
      state.form.userId = store.state.rightingData.selectPerson.userId;
      store.dispatch("rightingData/queryRightingList", state.form);
    };

    const search = () => {
      queryRightingList();
    };

    const upload = () => {
      state.form.userId = store.state.rightingData.selectPerson.userId;
      getRightingListByExport(state.form).then((res) => {
        console.log(res);
        let url = window.URL.createObjectURL(new Blob([res]));
        let link = document.createElement("a");
        link.style.display = "none";
        link.href = url;
        link.setAttribute(
          "download",
          store.state.rightingData.selectPerson.userName + "冲正记录.xlsx"
        );
        document.body.appendChild(link);
        link.click();
      });
    };

    const changeDefaultDate = (val) => {
      state.requestDate = val();
      state.form.beginDate = state.requestDate[0];
      state.form.endDate = state.requestDate[1];
    };
    const changeDate = (val) => {
      state.form.beginDate = timeStr(val[0]);
      state.form.endDate = timeStr(val[1]);
    };

    const handlePageChange = (val) => {
      state.form.currentPage = val;
      queryRightingList();
    };
    const handleSizeChange = (val) => {
      state.form.currentPage = 1;
      state.form.pageSize = val;
      queryRightingList();
    };
    //重置
    const reset = () => {
      state.form = {
        beginDate: requestDate()[0],
        endDate: requestDate()[1],
        currentPage: 1,
        pageSize: 6,
        userId: null,
        maxAmount: null,
        minAmount: null,
        sysUserId: null,
      };
      state.requestDate = requestDate();
    };
    //关闭模态框
    const offOpenAccount = () => {
      state.isOpenAccount = false;
    };

    onMounted(() => {
      querySystemUser();
      queryWalletActiveList();
    });
    return {
      state,
      tabs,
      RightingList,
      RightingListTotal,
      isDisabled,
      timeStrDate,
      changeDefaultDate,
      changeDate,
      handlePageChange,
      handleSizeChange,
      search,
      upload,
      reset,
      offOpenAccount,
      filterDictionary,
    };
  },
};
</script>
<style lang="scss" scoped>
.date {
  margin: 0 10px;
  color: rgba(0, 0, 0, 0.***************);
}

.reversal-record {
  .el-divider--horizontal {
    margin: 0 0 10px;
  }

  .el-form-item--small.el-form-item {
    margin-bottom: 10px;
  }

  .search-box {
    padding: 0 20px;
  }

  .el-dialog__header {
    border-bottom: 1px solid #efefef;
  }

  .el-dialog__headerbtn {
    font-size: 20px;
    top: 10px;
    right: 10px;
  }

  .el-dialog__header {
    padding: 10px 20px;
  }

  .el-overlay {
    position: absolute;
    background-color: rgba(0, 0, 0, 0.2);
  }

  .el-dialog__footer {
    text-align: center;
  }

  .el-dialog__title {
    font-size: 14px;
  }

  .el-table {
    border-left: none;

    tr>th:last-child,
    tr>td:last-child {
      border-right: none !important;
    }

    .el-table--border,
    .el-table--group {
      border: none;
    }

    &::after {
      background-color: transparent;
    }

    .el-table__row {
      height: 48px;
    }
  }

  .kade-tab-wrap {
    margin-top: 0 !important;
  }

  .header-flexbox {
    position: relative;
  }

  .header-btn {
    position: absolute;
    top: 8px;
    right: 20px;
  }
}

.el-table {
  border-left: 1px solid #ebeef5 !important;
  border-right: 1px solid #ebeef5 !important;
}
</style>
