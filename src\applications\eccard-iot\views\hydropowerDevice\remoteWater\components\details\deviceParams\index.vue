<template>
  <kade-tab-wrap :tabs="tabs" v-model="state.tab">
    <template #jbcs>
      <kade-basic-params />
    </template>
    <template #sfcs>
      <kade-water-charge-params />
    </template>
    <template #fjfcs>
      <kade-attach-params />
    </template>
  </kade-tab-wrap>
</template>
<script>
import { reactive, onMounted } from 'vue'
import { useStore } from "vuex";
import basicParams from "./basicParams";
import attachParams from "./attachParams";
import waterChargeParams from "./waterChargeParams";
const tabs = [
  { name: "jbcs", label: "基本参数" },
  { name: "sfcs", label: "水费参数" },
  { name: "fjfcs", label: "附加费参数" },
];
export default {
  components: {
    "kade-basic-params": basicParams,
    "kade-water-charge-params": waterChargeParams,
    "kade-attach-params": attachParams,
  },
  props: {
    data: {
      type: String,
      default: ""
    }
  },
  setup(props) {
    const store = useStore();
    const state = reactive({
      tab: "",
    });
    onMounted(async () => {
      await store.dispatch("hydropowerDevice/remoteWater/getParams", props.data.paramId);
      state.tab = "jbcs"
    });
    return {
      tabs,
      state,
    };
  }
}
</script>