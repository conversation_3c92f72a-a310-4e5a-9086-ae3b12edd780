<template>
  <el-row :gutter="30">
    <el-col :lg="{ span: state.show ? 12 : 24 }" :md="{ span: 24 }" :style="{
        marginBottom: '20px',
        borderRight: state.show ? '1px solid #efefef' : 'none',
      }">
      <div class="table-toolbox">
        <el-button icon="el-icon-plus" @click="handleAdd" type="primary" size="small">添加成员</el-button>
      </div>
      <el-table border :data="state.dataList" :loading="state.tableLoading" @row-click="handleRowClick">
        <el-table-column label="称呼" prop="fuserRelationship" align="center"></el-table-column>
        <el-table-column label="姓名" prop="fname" align="center"></el-table-column>
        <el-table-column label="联系电话" prop="ftel" align="center"></el-table-column>
      </el-table>
    </el-col>
    <el-col :lg="{ span: 12 }" :md="{ span: 24 }" v-show="state.show">
      <el-alert :closable="false" title="详情" show-icon type="info" />
      <el-form :model="state.model" :rules="rules" ref="formRef" :label-width="labelWidth" size="small" style="margin-top: 20px; max-width: 500px" @keyup.enter="handleSubmit" v-loading="state.loadLoading">
        <el-form-item label="称呼" prop="fuserRelationship">
          <el-select clearable placeholder="请选择" style="width: 100%" v-model="state.model.fuserRelationship">
            <el-option value="父亲">父亲</el-option>
            <el-option value="母亲">母亲</el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="姓名" prop="fname">
          <el-input placeholder="请输入" v-model="state.model.fname"></el-input>
        </el-form-item>
        <el-form-item label="联系电话" prop="ftel">
          <el-input placeholder="请输入" v-model="state.model.ftel"></el-input>
        </el-form-item>
        <!-- <el-form-item label="性别" prop="fsex">
          <el-select
            clearable
            placeholder="请选择"
            style="width: 100%"
            v-model="state.model.fsex"
          >
            <el-option
              v-for="item in sexs"
              :key="item.value"
              :value="item.value"
              :label="item.label"
            ></el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item label="来源" prop="fsource">
          <el-select clearable placeholder="请选择" style="width: 100%" v-model="state.model.fsource">
            <el-option v-for="item in sources" :key="item.value" :value="item.value" :label="item.label"></el-option>
          </el-select>
        </el-form-item>
        <template v-if="state.model.id">
          <!-- <el-form-item label="指纹采集">已采集</el-form-item>
          <el-form-item label="卡类型">学生卡</el-form-item>
          <el-form-item label="卡状态">未发卡/正常/已挂失/已退卡/冻结</el-form-item>
          <el-form-item label="物理卡号">已采集</el-form-item>
          <el-form-item label="逻辑卡号">已采集</el-form-item>
          <el-form-item label="2.4G卡号">已采集</el-form-item>
          <el-form-item label="有效期">已采集</el-form-item> -->
        </template>
        <el-form-item label="家庭编码" prop="fcode">
          <el-input placeholder="请输入" v-model="state.model.fcode"></el-input>
        </el-form-item>
        <!--         <el-form-item label="启用账户">
          <el-switch
            v-model="state.model.fstate"
            :active-color="themes.primaryColor"
            :inactive-color="themes.dangerColor"
            active-value="ENABLE_TRUE"
            inactive-value="ENABLE_FALSE"
          >
          </el-switch>
        </el-form-item> -->

        <el-form-item>
          <el-button icon="el-icon-circle-check" :loading="state.saveLoading" type="primary" @click="handleSubmit">保存</el-button>
          <el-button icon="el-icon-circle-close" @click="handleBack">关闭</el-button>
          <el-button icon="el-icon-remove-outline" v-if="state.model.id" @click="handleDel" type="danger" plain>删除</el-button>
        </el-form-item>
      </el-form>
    </el-col>
  </el-row>
</template>
<script>
import {
  ElRow,
  ElCol,
  ElForm,
  ElFormItem,
  ElInput,
  ElAlert,
  ElButton,
  ElTable,
  ElSelect,
  ElOption,
  ElTableColumn,
  ElMessage,
  ElMessageBox,
} from "element-plus";
import { ref, reactive, onMounted, nextTick } from "vue";
import { useStore } from "vuex";
import { useDict } from "@/hooks/useDict";
import { fillData } from "@/utils";
import { validateMobileAndFixTel } from "@/utils/validate";
import {
  addFamilyInfo,
  delFamilyInfo,
  updateFamilyInfo,
  getFamilyDetailsById,
  getFamilyList,
} from "@/applications/eccard-basic-data/api";
const getDefaultModel = () => ({
  fname: "",
  fcode: "",
  // fsex: "",
  fsource: "",
  ftel: "",
  fuserId: "",
  fuserRelationship: "",
  fstate: "ENABLE_TRUE",
});
export default {
  components: {
    "el-row": ElRow,
    "el-col": ElCol,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    "el-input": ElInput,
    "el-alert": ElAlert,
    "el-button": ElButton,
    "el-table": ElTable,
    "el-table-column": ElTableColumn,
    "el-select": ElSelect,
    "el-option": ElOption,
  },
  setup() {
    const formRef = ref(null);
    const state = reactive({
      loadLoading: false,
      saveLoading: false,
      tableLoading: false,
      dataList: [],
      model: getDefaultModel(),
      show: false,
    });

    const store = useStore();
    const sexs = useDict("SYS_SEX");
    const sources = useDict("SYS_DATA_SOURCE");

    const rules = {
      fsex: [{ required: true, message: "请选择性别", trigger: "change" }],
      fsource: [{ required: true, message: "请选择来源", trigger: "change" }],
      fname: [
        { required: true, message: "请输入姓名" },
        { max: 20, message: "姓名长度不得超过20字符" },
      ],
      ftel: [
        { required: true, message: "请输入电话" },
        { validator: validateMobileAndFixTel },
      ],
      fstate: [{ required: true, message: "请选择卡状态", trigger: "change" }],
      fuserRelationship: [
        { required: true, message: "请选择称呼", trigger: "change" },
      ],
      fcode: [{ required: true, message: "请输入家庭编码", trigger: "blur" }],
    };
    const loadData = async () => {
      try {
        state.tableLoading = true;
        const { data } = await getFamilyList({
          userId: store.getters["app/query"]?.id,
        });
        state.dataList = data;
      } catch (e) {
        throw new Error(e.message);
      } finally {
        state.tableLoading = false;
      }
    };
    const loadInfo = async () => {
      try {
        state.loadLoading = true;
        const { data } = await getFamilyDetailsById({ id: state.model.id });
        const target = getDefaultModel();
        fillData(target, data, "id");
        state.model = target;
      } catch (e) {
        throw new Error(e.message);
      } finally {
        state.loadLoading = false;
      }
    };
    const handleSubmit = () => {
      formRef.value.validate(async (valid) => {
        if (valid) {
          try {
            state.saveLoading = true;
            const fn = state.model.id ? updateFamilyInfo : addFamilyInfo;
            const { message } = await fn({
              ...state.model,
              fuserId: store.getters["app/query"]?.id,
            });
            ElMessage.success(message);
            loadData();
          } catch (e) {
            throw new Error(e.message);
          } finally {
            state.saveLoading = false;
          }
        }
      });
    };
    const handleDel = () => {
      ElMessageBox.confirm(`确认删除?`, {
        type: "warning",
        closeOnPressEscape: false,
        closeOnClickModal: false,
      }).then(async () => {
        try {
          const { message } = await delFamilyInfo({ id: state.model.id });
          ElMessage.success(message);
          state.model = getDefaultModel();
          nextTick(() => {
            formRef.value?.clearValidate?.();
          });
          loadData();
        } catch (e) {
          throw new Error(e.message);
        }
      });
    };
    const handleBack = () => {
      state.show = false;
    };
    const handleAdd = () => {
      state.model = getDefaultModel();
      state.show = true;
      nextTick(() => {
        formRef.value?.clearValidate?.();
      });
    };
    const handleRowClick = (row) => {
      state.model = Object.assign(getDefaultModel(), row);
      state.show = true;
      loadInfo();
    };
    onMounted(() => {
      loadData();
    });
    return {
      labelWidth: THEMEVARS.formLabelWidth,
      themes: THEMEVARS,
      handleSubmit,
      handleDel,
      handleBack,
      state,
      sexs,
      sources,
      rules,
      formRef,
      handleAdd,
      handleRowClick,
    };
  },
};
</script>
