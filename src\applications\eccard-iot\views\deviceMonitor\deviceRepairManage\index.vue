<template>
  <kade-route-card style="height: auto" v-loading="state.loading">
    <kade-table-filter @search="getList()" @reset="reset">
      <el-form inline size="mini" label-width="100px">
        <el-form-item label="设备类型">
          <el-select v-model="state.form.deviceType" clearable>
            <el-option v-for="(item, index) in state.deviceTypeList" :key="index" :label="item.cfgValue"
              :value="item.cfgKey"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="处理进度">
          <el-select clearable placeholder="请选择处理进度" v-model="state.form.status">
            <el-option v-for="item in statusList" :label="item.label" :value="item.value"
              :key="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="用户编号">
          <el-input placeholder="请输入用户编号" v-model="state.form.userCode" maxlength="30" clearable></el-input>
        </el-form-item>
        <el-form-item label="设备机号">
          <el-input placeholder="请输入设备机号" v-model="state.form.deviceNo" maxlength="30" clearable></el-input>
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="设备列表">
      <template #extra>
        <el-button @click="exportPerson" class="btn-blue" icon="el-icon-daochu" size="mini">导出</el-button>
      </template>
      <el-table style="width: 100%" :data="state.dataList" ref="multipleTable" v-loading="state.loading" border stripe>
        <el-table-column show-overflow-tooltip label="设备类型" prop="deviceType" align="center">
          <template #default="scope">
            {{ deviceType(scope.row) }}
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip label="所属商户" prop="merchantName" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip label="所属区域" prop="areaName" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip label="设备机号" prop="deviceNo" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip label="设备名称" prop="deviceName" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip label="用户编号" prop="userCode" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip label="手机号" prop="userTel" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip label="故障类型" prop="failureType" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip label="故障内容" prop="failureDetail" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip label="报修时间" prop="lastModifyTime" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip label="处理进度" prop="status" align="center">
          <template #default="scope">
            <div :style="{ color: scope.row.status == 'NOT_REPAIRED' && '#f00' }">{{ dictionaryFilter(scope.row.status) }}</div>
          </template>
        </el-table-column>
        <el-table-column label="操作" prop="endtermBalance" align="center" width="100">
          <template #default="scope">
            <el-button v-if="scope.row.status == 'NOT_REPAIRED'" type="text" @click="handleEdit(scope.row)">处理</el-button>
            <!-- <el-button type="text" @click="handleDel(scope.row)">删除</el-button> -->
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination background v-model:currentPage="state.form.currentPage" v-model:page-size="state.form.pageSize"
          :page-sizes="[5, 10, 20, 30]" layout="total,sizes, prev, pager, next, jumper" :total="state.total"
          @size-change="handleSizeChange" @current-change="handlePageChange">
        </el-pagination>
      </div>
    </kade-table-wrap>
    <kd-deviceRepairManage-edit :isShow="state.isEdit" :rowData="state.rowData" @close="closeEdit" />
  </kade-route-card>
</template>
<script>
import {
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElTable,
  ElTableColumn,
  ElPagination,
  // ElMessage,
  // ElMessageBox,
  ElSelect,
  ElOption,
} from "element-plus";
import { reactive } from "@vue/reactivity";
import { onMounted } from "@vue/runtime-core";
import { downloadXlsx } from "@/utils";
import {
  getDeviceRepairManageList,
  iotCfg,
  deviceRepairExport
} from "@/applications/eccard-iot/api";
import { useDict } from '@/hooks/useDict'
import edit from "./components/edit.vue";
export default {
  components: {
    "el-button": ElButton,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    "el-table": ElTable,
    "el-table-column": ElTableColumn,
    "el-pagination": ElPagination,
    ElInput,
    ElSelect,
    ElOption,
    "kd-deviceRepairManage-edit": edit,
    // ElMessage,
    // ElMessageBox,
  },
  setup() {
    const statusList = useDict('DORM_REPAIR_STATUS')
    const state = reactive({
      loading: false,
      isEdit: false,
      //搜索条件
      form: {
        currentPage: 1,
        pageSize: 10,
      },
      //表格数据
      dataList: [],
      deviceTypeList: [],
      total: 0,
      rowData: {},
    });
    //获取表格数据
    const getList = async function () {
      state.loading = true;
      try {
        let {
          data: { total, list }
        } = await getDeviceRepairManageList(state.form);
        console.log(list);
        state.dataList = list;
        state.total = total;
        state.loading = false;
      } catch {
        state.loading = false;
      }
    };
    //获取设备类型
    const queryDeviceType = async () => {
      let { data } = await iotCfg();
      state.deviceTypeList = data;
    };
    //导出excel
    const exportPerson = async () => {
      let res = await deviceRepairExport(state.form)
      downloadXlsx(res, "人员信息表.xlsx");
    };
    //编辑
    const handleEdit = (row) => {
      state.rowData = row;
      state.isEdit = true;
    };
    //删除
    const handleDel = () => {
      // ElMessageBox.confirm("确认删除?", "提示", {
      //   confirmButtonText: "确认",
      //   cancelButtonText: "取消",
      //   type: "warning",
      // }).then(async () => {
      //   let { code, message } = await delUserInfo(row.id);
      //   if (code === 0) {
      //     ElMessage.success(message);
      //     getList();
      //   }
      // });
    };
    //分页 下一页
    const handlePageChange = (val) => {
      state.form.currentPage = val;
      getList();
    };
    //分页 当前多少页
    const handleSizeChange = (val) => {
      state.form.currentPage = 1;
      state.form.pageSize = val;
      getList();
    };
    //重置
    const reset = () => {
      state.form = {
        currentPage: 1,
        pageSize: 10,
      };
      getList();
    };
    //关闭编辑弹窗
    const closeEdit = (val) => {
      if (val) {
        getList();
      }
      state.isEdit = false;
    };
    const deviceType = (row) => {
      // cfgValue
      return state.deviceTypeList.filter(d => d.cfgKey == row.deviceType)[0]?.cfgValue
    }

    onMounted(() => {
      getList();
      queryDeviceType()
    });



    return {
      statusList,
      state,
      getList,
      handleEdit,
      handleDel,
      handlePageChange,
      handleSizeChange,
      reset,
      closeEdit,
      exportPerson,
      queryDeviceType,
      deviceType
    };
  },
};
</script>
<style lang="scss" scoped></style>
