<template>
  <el-dialog :model-value="modelValue" title="批量添加房间" width="800px" :before-close="handleClose">
    <el-form inline label-width="150px" size="mini" ref="formRef" :model="state.form" :rules="rules">
      <kade-linkage-select v-if="editType !== 'details'" :isEdit="modelValue ? true : false" :value="state.form"
        :data="linkageData" @change="linkageChange" />
      <el-form-item label="每层房间数：" prop="roomCount">
        <el-input v-model="state.form.roomCount" placeholder="请输入每层房间数" maxlength="20" clearable></el-input>
      </el-form-item>
      <el-form-item label="房间类型：" prop="roomTypeId">
        <el-select v-model="state.form.roomTypeId" clearable placeholder="全部" @change="roomTypeChange">
          <el-option v-for="(item, index) in roomTypeList" :key="index" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="容纳人数：" prop="capacityCount">
        <el-input-number v-model="state.form.capacityCount" placeholder="请输入容纳人数" :min="0" :max="10000">
        </el-input-number>
      </el-form-item>
    </el-form>
    <div style="text-align:center;color:#f00">
      备注：批量添加房间按选择的楼栋、单元和楼层范围生成房间数据，房间号以楼层号开头并按设定的房间数从1依次递增，房间名称为房间号+“房间”
    </div>
    <template #footer v-if="editType !== 'details'">
      <span class="dialog-footer">
        <el-button type="primary" @click="submit" size="mini">保存</el-button>
        <el-button @click="handleClose" size="mini">返回</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { ElDialog, ElButton, ElForm, ElFormItem, ElInputNumber, ElInput, ElSelect, ElOption, ElMessage } from "element-plus"
import { reactive, ref, watch, nextTick } from 'vue'
import { roomBatchAdd } from "@/applications/eccard-uac/api";
import linkageSelect from "@/applications/eccard-uac/views/components/linkageSelect.vue"
const linkageData = {
  area: { label: "所属区域：", valueKey: "areaId", key: "id" },
  building: { label: "所属楼栋：", valueKey: "buildId" },
  unit: { label: "所属单元：", valueKey: "unitNum" },
  floor: { label: "开始楼层：", valueKey: "startFloorNum" },
  copyFloor: { label: "结束楼层：", valueKey: "endFloorNum" },
}
export default {
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    roomTypeList: {
      type: Array,
      default: null
    }
  },
  components: {
    ElDialog,
    ElButton,
    ElForm,
    ElFormItem,
    ElInputNumber,
    ElInput,
    ElSelect,
    ElOption,
    "kade-linkage-select": linkageSelect,
  },

  setup(props, context) {
    const formRef = ref(null)
    const state = reactive({
      form: {}
    });
    const rules = {
      areaId: [
        {
          required: true,
          message: "请选择所属区域",
          trigger: "change",
        },
      ],
      buildId: [
        {
          required: true,
          message: "请选择所属楼栋",
          trigger: "change",
        },
      ],
      unitNum: [
        {
          required: true,
          message: "请选择所属单元",
          trigger: "change",
        },
      ],
      startFloorNum: [
        {
          required: true,
          message: "请选择开始楼层",
          trigger: "change",
        },
      ],
      endFloorNum: [
        {
          required: true,
          message: "请选择结束楼层",
          trigger: "change",
        },
      ],
      roomTypeId: [
        {
          required: true,
          message: "请选择房间类型",
          trigger: "change",
        },
      ],
      roomCount: [
        {
          required: true,
          message: "请输入每层房间数",
          trigger: "blur",
        },
        {
          pattern: /^[0-9]*[1-9][0-9]*$/,
          message: "请输入数字整数",
        },
      ],
      capacityCount: [
        {
          required: true,
          message: "请输入容纳人数",
          trigger: "blur",
        },
        {
          pattern: /^[0-9]*[1-9][0-9]*$/,
          message: "请输入数字整数",
        },
      ],
    }
    watch(() => props.modelValue, val => {
      if (val) {
        state.form = {}
      }
      nextTick(() => {
        formRef.value.clearValidate()
      })
    })
    const linkageChange = (val) => {
      state.form = { ...state.form, ...val }
    }
    const roomTypeChange = (val) => {
      let typeObj = props.roomTypeList.find(item => item.id == val)
      if (typeObj) {
        state.form.capacityCount = typeObj.capacityCount
      }
    }
    const submit = () => {
      formRef.value.validate(async (valid) => {
        if (valid) {
          let params = state.form
          state.loading = true
          try {
            let { code, message } = await roomBatchAdd(params)
            if (code === 0) {
              ElMessage.success(message)
              context.emit("update:modelValue", true);
            }
            state.loading = false
          }
          catch {
            state.loading = false
          }
        } else {
          return false
        }
      })
    }

    const handleClose = () => {
      context.emit("update:modelValue", false)
    };
    return {
      linkageData,
      formRef,
      state,
      rules,
      linkageChange,
      roomTypeChange,
      submit,
      handleClose,
    }
  }
}
</script>

<style lang="scss" scoped>
.el-form {
  margin-top: 20px;
}

:deep(.el-input-number) {
  width: 192px;
}

:deep(.el-input) {
  width: 192px;
}

:deep(.single-image-uploader) {
  width: 100px;
  height: 100px;

  .el-upload {
    width: 100px;
    height: 100px;
  }

  .element-icons {
    font-size: 40px !important;
  }
}
</style>