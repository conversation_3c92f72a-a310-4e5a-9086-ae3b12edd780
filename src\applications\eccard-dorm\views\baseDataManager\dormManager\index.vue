<template>
  <kade-route-card>
    <kade-table-filter @search="handleSearch" @reset="handleReset">
      <el-form inline label-width="90px" size="mini">
        <kade-linkage-select :data="linkageData" :value="state.form" @change="linkageChange" />
        <el-form-item label="管理员">
          <el-input placeholder="管理员编号或管理员姓名关键字搜索" v-model="state.form.keyWord"></el-input>
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="管理员列表" v-loading="state.loading">
      <template #extra>
        <el-button @click="handleEdit({},'add')" class="btn-green" icon="el-icon-plus" size="mini">添加管理员</el-button>
        <el-button @click="batchDel" type="danger" icon="el-icon-close" size="mini">批量删除</el-button>
      </template>
      <el-table :data="state.dataList" @selection-change="handleSelectChange" border height="55vh">
        <el-table-column prop="" show-overflow-tooltip type="selection" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="userCode" label="管理员编号" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="userName" label="管理员姓名" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="userTel" label="联系电话" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="buildName" label="管理楼栋" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="unitNumName" label="管理单元" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="buildTypeName" label="楼栋类型" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="areaName" label="所属区域" align="center"></el-table-column>
        <el-table-column label="操作" align="center" width="200px">
          <template #default="scope">
            <el-button class="green" type="text" @click="handleEdit(scope.row,'edit')" size="mini">编辑</el-button>
            <el-button class="green" type="text" @click="handelDel(scope.row)" size="mini">删除</el-button>
            <el-button class="green" type="text" @click="handleEdit(scope.row,'details')" size="mini">查看详情</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination v-model:currentPage="state.form.currentPage" v-model:page-size="state.form.pageSize" :page-sizes="[10, 20, 30, 40]" :small="small" :disabled="disabled" background layout="total, sizes, prev, pager, next, jumper" :total="state.total" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      </div>
    </kade-table-wrap>
    <kade-dorm-manager-edit v-model:modelValue="state.isEdit" :editType="state.editType" :rowData="state.rowData" @update:modelValue="close" />
  </kade-route-card>
</template>

<script>
import { reactive } from '@vue/reactivity';
import { ElForm, ElFormItem, ElButton, ElTable, ElTableColumn, ElInput, ElPagination, ElMessageBox, ElMessage } from "element-plus"
import { onMounted } from '@vue/runtime-core';
import { getDormManagerList, dormManagerDelete, dormManagerBatchDelete } from "@/applications/eccard-dorm/api";
import linkageSelect from "@/applications/eccard-dorm/components/linkageSelect.vue"
import edit from './components/edit'
const linkageData = {
  area: { label: "所属区域", valueKey: "areaId", key: "id" },
  buildingType: { label: "楼栋类型", valueKey: "buildType" },
  building: { label: "管理楼栋", valueKey: "buildId" },
}
export default {
  components: {
    ElForm,
    ElFormItem,
    ElButton,
    ElInput,
    ElTable,
    ElTableColumn,
    ElPagination,
    // "kade-area-select-tree": areaSelectTree,
    "kade-linkage-select": linkageSelect,
    "kade-dorm-manager-edit": edit,
  },
  setup() {
    const state = reactive({
      loading: false,
      form: {
        pageSize: 10,
        currentPage: 1,
      },
      dataList: [],
      isEdit: false,
      isBatchAdd: false,
      rowData: {},
      editType: "",
      total: 0,
      selectRowList:[]
    });
    const getList = async () => {
      for(let key in state.form){
        if(!state.form[key]){
          delete state.form[key]
        }
      }
      state.loading = true
      try {
        let { data: { list, total } } = await getDormManagerList(state.form)
        state.dataList = list
        state.total = total
        state.loading = false
      }
      catch {
        state.loading = false
      }
    };
    const closePersonSelect = (val) => {
      if (val) {
        state.form.userName = val.userName
      }
      state.isPersoner = false;
    };
    const linkageChange = (val) => {
      state.form = { ...state.form, ...val }
    }
    const handleEdit = (row, type) => {
      state.rowData = row
      state.editType = type
      state.isEdit = true
    }
    const handelDel = (row) => {
      ElMessageBox.confirm("确认删除?", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        let { code, message } = await dormManagerDelete(row.id);
        if (code === 0) {
          ElMessage.success(message);
          getList();
        }
      });
    }
    const handleSelectChange = (val) => {
      state.selectRowList = val
    }
    const batchDel = () => {
      if (!state.selectRowList.length) {
        return ElMessage.error("请先选择需要删除的管理员！")
      }
      ElMessageBox.confirm("确认删除已选择管理员?", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        let param = state.selectRowList.map(item => item.id).join(",")
        let { code, message } = await dormManagerBatchDelete(param);
        if (code === 0) {
          ElMessage.success(message);
          getList();
        }
      });
    }
    const handleSearch = () => {
      getList()
    };
    const handleReset = () => {
      state.form = {
        pageSize: 10,
        currentPage: 1
      }
    };
    const handleSizeChange = (val) => {
      state.form.currentPage = 1
      state.form.pageSize = val
      getList()
    };
    const handleCurrentChange = (val) => {
      state.form.currentPage = val
      getList()
    }
    const close = (val) => {
      if (val) {
        getList()
      }
      state.isEdit = false
    }
    onMounted(() => {
      getList();
    });
    return {
      linkageData,
      state,
      getList,
      closePersonSelect,
      linkageChange,
      handleEdit,
      handelDel,
      handleSelectChange,
      batchDel,
      handleSearch,
      handleReset,
      handleSizeChange,
      handleCurrentChange,
      close
    }
  }
}
</script>

<style lang="scss" scoped>
.green :hover {
  text-decoration: underline;
}
</style>