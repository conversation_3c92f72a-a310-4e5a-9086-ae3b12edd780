<template>
  <el-dialog :model-value="modelValue" title="绑定设备" width="1560px" :before-close="beforeClose">
    <el-row class="table" v-loading="state.loading">
      <el-col :span="9" class="table-left" style="height:55vh">
        <span class="title">已绑定设备</span>
        <el-divider></el-divider>
        <div style="padding: 10px 20px">
          <el-form size="mini" inline label-width="80px">
            <kade-linkage-select :value="state.form" :data="linkageData" @change="linkageChange" />
            <el-button type="primary" size="mini" @click="search">查询</el-button>
          </el-form>
          <el-table :data="state.dataList" border size="small" height="300px">
            <el-table-column prop="deviceTypeName" label="设备类型" align="center"></el-table-column>
            <el-table-column prop="deviceNo" label="设备机号" align="center"></el-table-column>
            <el-table-column prop="deviceName" label="设备名称" align="center"></el-table-column>
            <el-table-column label="操作" align="center">
              <template #default="scope">
                <el-button class="green" type="text" size="mini" @click="del(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-col>
      <el-col :span="14" class="table-right" style="height:55vh">
        <span class="title">待绑定设备</span>
        <el-divider></el-divider>
        <div style="padding:10px 20px">
          <kade-select-table :isShow="modelValue" :value='[]' :column="column" :selectCondition="selectCondition"
            :params="params" :reqFnc="unBindDormDeviceList" :isMultiple="true" :isCurrentSelect="true"
            @change="handleChange" />
        </div>
      </el-col>
    </el-row>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="submit()" type="primary" size="mini">保&nbsp;&nbsp;存</el-button>
        <el-button @click="beforeClose()" size="mini">返&nbsp;&nbsp;回</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import {
  ElDialog,
  ElRow,
  ElCol,
  ElForm,
  ElDivider,
  ElButton,
  ElTable,
  ElTableColumn,
  ElMessage,
  ElMessageBox
} from "element-plus";
import { reactive } from "@vue/reactivity";
import { useDict } from "@/hooks/useDict.js";
import { roomDeviceListNoPage, roomDeviceDelete, unBindDormDeviceList, roomDeviceAdd } from "@/applications/eccard-dorm/api";
import { getModel } from "@/applications/eccard-iot/api";
import linkageSelect from "@/applications/eccard-dorm/components/linkageSelect.vue"
import selectTable from "@/components/table/selectTable";
import { onMounted } from '@vue/runtime-core';
const linkageData = {
  area: { label: '区域', valueKey: "areaId", key: "id" },
  building: { label: '楼栋', valueKey: "buildId" },
  unit: { label: '单元', valueKey: "unitNum" },
  floor: { label: '楼层', valueKey: "floorNum" },
  room: { label: '房间', valueKey: "roomId" },
}
const column = [
  { label: "终端型号", prop: "deviceModel" },
  { label: "所属商户", prop: "merchantName" },
  { label: "所属区域", prop: "areaName" },
  { label: "设备机号", prop: "deviceNo" },
  { label: "设备名称", prop: "deviceName" },
  { label: "设备IP", prop: "deviceIP" },
  { label: "设备类型", prop: "deviceTypeName" },
];
export default {
  props: {
    modelValue: {
      type: Boolean,
      default: false,
    },
  },
  components: {
    ElDialog,
    ElRow,
    ElCol,
    ElForm,
    ElDivider,
    ElButton,
    ElTable,
    ElTableColumn,
    "kade-linkage-select": linkageSelect,
    "kade-select-table": selectTable,
  },
  setup(props, context) {
    const state = reactive({
      loading: false,
      form: {
        deviceList: []
      },
      total: 0,
      dataList: [],
    });
    const selectCondition = [
      {
        label: "型号",
        valueKey: "deviceModel",
        placeholder: "请选择",
        isSelect: true,
        select: {
          list: [],
          option: { label: "productMode", value: "productMode" },
        },
      },
      {
        label: "区域",
        valueKey: "areaPath",
        dataKey: "areaPath",
        placeholder: "全部",
        isTree: "area"
      },
      {
        label: "机号",
        valueKey: "deviceNo",
        placeholder: "请输入",
        isSelect: false,
      },
      {
        label: "IP地址",
        valueKey: "deviceIp",
        placeholder: "请输入",
        isSelect: false,
      },
      {
        label: "设备类型",
        valueKey: "deviceType",
        placeholder: "请选择",
        isSelect: true,
        select: {
          list: useDict("DORM_ROOM_DEVICE_TYPE"),
          option: { label: "label", value: "value" },
        },
      },
    ];
    const params = {
      currentPageKey: "currentPage",
      pageSizeKey: "pageSize",
      resListKey: "list",
      resTotalKey: "total",
      value: {
      },
      tagNameKey: "deviceName",
      valueKey: "id",
    };
    //获取终端型号
    const queryModel = async () => {
      let { data } = await getModel();
      selectCondition[0].select.list = data;
    };
    const linkageChange = (val) => {
      state.form = { ...state.form, ...val }
    }
    const beforeClose = () => {
      context.emit("update:modelValue", false);
    };
    const search = async () => {
      if (!state.form.roomId) {
        return ElMessage.error("请先选择房间！")
      }
      state.loading = true
      try {
        let { data } = await roomDeviceListNoPage({ roomId: state.form.roomId })
        state.dataList = data
        state.loading = false
      }
      catch {
        state.loading = false
      }
    };
    const handleChange = (val) => {
      state.form.deviceList = val.list
    }
    const submit = async () => {
      if (!state.form.roomId) {
        return ElMessage.error("请先选择房间！")
      }
      if (!state.form.deviceList.length) {
        return ElMessage.error("请选择设备！")
      }
      state.loading = true
      try {
        let { code, message } = await roomDeviceAdd(state.form)
        if (code === 0) {
          ElMessage.success(message)
          context.emit("update:modelValue", true);
        }
        state.loading = false
      }
      catch {
        state.loading = false
      }
    };
    const handleSizeChange = (val) => {
      state.form.currentPage = val;
    };
    const handleCurrentChange = (val) => {
      state.form.pageSize = val;
    };
    const del = (row) => {
      ElMessageBox.confirm("确认删除?", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        state.loading = true
        try {
          let { code, message } = await roomDeviceDelete(row.id);
          if (code === 0) {
            ElMessage.success(message);
            search();
          }
          state.loading = false
        }
        catch {
          state.loading = false
        }
      });
    };
    onMounted(() => {
      queryModel()
    })
    return {
      linkageData,
      unBindDormDeviceList,
      state,
      column,
      selectCondition,
      linkageChange,
      search,
      del,
      params,
      handleChange,
      submit,
      beforeClose,
      handleSizeChange,
      handleCurrentChange,
    };
  },
};
</script>

<style lang="scss" scoped>
.table {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 30px 0;

  .title {
    margin: 10px;
  }

  .table-left {
    border: 1px solid #eeeeee;
    margin-right: 30px;
    padding: 10px 0 70px 0;

    .el-form {
      .el-button {
        margin-left: 80px;
      }
    }
  }

  .table-right {
    border: 1px solid #eeeeee;
    padding: 10px 0;

    .button {
      margin-left: 70px;
    }

    .el-table {
      width: 730px;
      margin: 0 10px 0 15px;
    }

    .pagination {
      width: 708px;
      padding: 10px;
      margin: 0 10px 30px 15px;
      border: 1px solid #eeeeee;
      border-top: 0;
      border-bottom-left-radius: 8px;
      border-bottom-right-radius: 8px;
    }
  }
}

:deep(.el-pagination .el-select .el-input .el-input__inner) {
  width: 100px !important;
}

:deep(.el-pagination__editor.el-input .el-input__inner) {
  width: 46px !important;
}

:deep(.el-select, .el-input__inner) {
  width: 170px !important;
}

:deep(.el-form) {
  .el-select {
    width: 170px;
  }

  .el-input {
    width: 170px;
  }

  .el-input__inner {
    width: 170px !important;
  }
}
</style>