<template>
  <kade-route-card>
    <kade-table-filter @search="handleSearch" @reset="handleReset">
      <el-form inline label-width="90px" size="mini">
        <kade-linkage-select :data="linkageData" :value="state.form" @change="linkageChange" />
        <el-form-item label="考勤日期">
          <el-date-picker v-model="state.requestDate" type="daterange" range-separator="~" start-placeholder="请选择"
            end-placeholder="请选择" :size="size" />
        </el-form-item>
        <el-form-item label="考勤时段">
          <el-select clearable v-model="state.form.attendancePeriodId" placeholder="全部">
            <el-option v-for="(item, index) in state.periodIdList" :key="index" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="楼栋考勤汇总列表">
      <template #extra>
        <el-button class="btn-purple" icon="el-icon-bottom" type="" size="small" @click="exportClick">导出</el-button>
      </template>
      <el-table border :data="state.data" v-loading="state.loading">
        <el-table-column prop="areaName" label="区域" align="center"></el-table-column>
        <el-table-column prop="buildName" label="楼栋" align="center"></el-table-column>
        <el-table-column prop="buildType" label="楼栋类型" align="center">
          <template #default="scope">
            {{ dictionaryFilter(scope.row.buildType) }}
          </template>
        </el-table-column>
        <el-table-column prop="attendanceDate" label="考勤日期" align="center"></el-table-column>
        <el-table-column prop="attendancePeriodName" label="考勤时段" align="center"></el-table-column>
        <el-table-column prop="dueNumber" label="入住人数" align="center"></el-table-column>
        <el-table-column prop="actuaNumber" label="实到人数" align="center">
          <template #default="scope">
            <div :style="{'backgroundColor':scope.row.actuaNumber==scope.row.dueNumber?'#03c316':''}">{{scope.row.actuaNumber}}</div>
          </template>
        </el-table-column>
        <el-table-column prop="leaveNumber" label="请假人数" align="center">
          <template #default="scope">
            <div :style="{'backgroundColor':scope.row.leaveNumber>0?'#aaaaaa':''}">{{scope.row.leaveNumber}}</div>
          </template>
        </el-table-column>
        <el-table-column prop="lateNumber" label="晚归人数" align="center">
          <template #default="scope">
            <div :style="{'backgroundColor':scope.row.lateNumber>0?'#f59a23':''}">{{scope.row.lateNumber}}</div>
          </template>
        </el-table-column>
        <el-table-column prop="notReturnNumber" label="未归人数" align="center">
          <template #default="scope">
            <div :style="{'backgroundColor':scope.row.notReturnNumber>0?'#d9001b':''}">{{scope.row.notReturnNumber}}</div>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination">
        <el-pagination :currentPage="state.form.currentPage" :page-size="state.form.pageSize"
          :page-sizes="[10, 20, 30, 40]" background layout="total, sizes, prev, pager, next, jumper"
          :total="state.total" @size-change="handleSizeChange" @current-change="handleCurrentChange">
        </el-pagination>
      </div>
    </kade-table-wrap>
  </kade-route-card>
</template>

<script>
import {
  ElButton,
  ElForm,
  ElFormItem,
  ElSelect,
  ElOption,
  ElTable,
  ElTableColumn,
  ElPagination,
  ElDatePicker,
} from "element-plus";
import linkageSelect from '@/applications/eccard-dorm/components/linkageSelect'
import { reactive } from "@vue/reactivity";
import { onMounted } from '@vue/runtime-core';
import { dateStr } from "@/utils/date.js"
import { attendanceBuildingSummaryList, exportAttendanceBuildingSummary, attendancePeriodList } from '@/applications/eccard-dorm/api.js'
const linkageData = {
  area: { label: "区域", valueKey: "areaPath", key: "areaPath" },
  buildingType: { label: '楼栋类型', valueKey: 'buildType' },
  building: { label: '楼栋', valueKey: 'buildId' }
}
export default {
  components: {
    ElButton,
    ElForm,
    ElFormItem,
    ElSelect,
    ElOption,
    ElTable,
    ElTableColumn,
    ElPagination,
    ElDatePicker,
    "kade-linkage-select": linkageSelect
  },
  setup() {
    const state = reactive({
      form: {
        pageSize: 10,
        currentPage: 1
      },
      data: [],
      requestDate: [],
      total: 0,
      loading: false,
    });
    const getPeriodIdList = async () => {
      let { data } = await attendancePeriodList()
      state.periodIdList = data
    }


    const getList = async () => {
      if (state.requestDate && state.requestDate.length) {
        state.form.beginDate = dateStr(state.requestDate[0])
        state.form.endDate = dateStr(state.requestDate[1])
      } else {
        delete state.form.beginDate
        delete state.form.endDate
      }
      state.loading = true
      try {
        let { data: { list, total } } = await attendanceBuildingSummaryList(state.form)
        state.data = list
        state.total = total
        state.loading = false
      }
      catch {
        state.loading = false
      }
    };
    const handleSizeChange = (val) => {
      state.currentPage = 1
      state.pageSize = val
      getList()
    };
    const handleCurrentChange = (val) => {
      state.currentPage = val
      getList()
    };
    const handleSearch = () => {
      getList()
    };
    const linkageChange = (val) => {
      state.form = { ...state.form, ...val }
    }
    const handleReset = () => {
      state.form = {
        pageSize: 10,
        currentPage: 1
      }
      state.requestDate = []
      getList()
    };
    const exportClick = async () => {
      let res = await exportAttendanceBuildingSummary(state.form)
      let url = window.URL.createObjectURL(
        new Blob([res], { type: 'application/vnd.ms-excel' })
      )
      let link = document.createElement('a')
      link.style.display = 'none'
      link.href = url
      link.setAttribute('download','楼栋考勤汇总表.xlsx')
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }
    onMounted(() => {
      getList()
      getPeriodIdList()
    })
    return {
      state,
      handleSizeChange,
      handleCurrentChange,
      handleSearch,
      handleReset,
      linkageData,
      exportClick,
      linkageChange,
    };
  },
};
</script>

<style lang="scss" scoped>
:deep(.el-icon-date:before) {
  display: none;
}
:deep(.el-select .el-input__inner) {
  width: 210px;
}
:deep(.el-pagination .el-select .el-input .el-input__inner) {
  width: 100px;
}
:deep(.el-input--prefix .el-input__inner) {
  width: 210px;
}
:deep(.el-date-editor.el-input, .el-date-editor.el-input__inner) {
  width: 210px;
}
:deep(.el-table td){
  margin: 0;
  padding: 0;
  height: 50px;
}
:deep(.el-table .cell){
  margin: 0 ;
  padding: 0;

}
:deep(.el-table td div) {
    box-sizing: border-box;
    height: 50px;
    line-height: 50px;
}
</style>