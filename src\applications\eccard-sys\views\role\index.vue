<template>
  <kade-route-card>
    <el-row :gutter="20">
      <el-col :span="10">
        <kade-table-wrap title="角色权限管理">
          <template #extra>
            <el-button icon="el-icon-plus" size="mini" type="primary" @click="addRole">添加角色</el-button>
          </template>
          <!-- <el-divider></el-divider> -->
          <el-table style="width: 100%" height="70vh" :data="state.dataList" @row-click="tableRowClick"
            highlight-current-row border stripe>
            <el-table-column label="角色名称" prop="roleName" align="center" show-overflow-tooltip></el-table-column>
            <el-table-column label="最后修改时间" prop="lastModifyTime" align="center" show-overflow-tooltip>
              <template #default="scope">
                {{ timeFilter(scope.row.lastModifyTime) }}
              </template>
            </el-table-column>
            <el-table-column label="状态" align="center">
              <template #default="scope">
                <el-switch :model-value="scope.row.status" active-value="ENABLE_TRUE" inactive-value="ENABLE_FALSE"
                  @click="statusChange(scope.row)" />
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="120">
              <template #default="scope">
                <el-button :disabled="scope.row.canEdit === 'FALSE'" type="text" @click="handleEditRole(scope.row)"
                  size="mini">编辑</el-button>
              </template>
            </el-table-column>
          </el-table>
          <div class="pagination">
            <el-pagination background :current-page="state.form.currentPage"
              layout="total, sizes, prev, pager, next, jumper" :page-sizes="[10, 20, 50, 100, 200]" :total="state.total"
              :page-size="state.form.pageSize" @current-change="currentChange" @size-change="sizeChange">
            </el-pagination>
          </div>
        </kade-table-wrap>
      </el-col>
      <el-col :span="8" style="min-width:400px">
        <kade-tab-wrap :tabs="tabs" v-model="state.tab" @active="active">
          <template #mkqx>
            <kade-module-auth class="scroll-box" />
          </template>
          <template #qyqx>
            <kade-area-auth class="scroll-box" />
          </template>
          <template #zzqx>
            <kade-dept-auth class="scroll-box" />
          </template>
          <template #extra>
            <el-button size="mini" :disabled="!selectRow" type="primary" @click="saveAuth">保存</el-button>
          </template>
        </kade-tab-wrap>

      </el-col>
      <el-col :span="6" style="min-width:400px" v-if="state.tab == 'mkqx'">
        <kade-table-wrap title="按钮权限">
          <template #extra>
            <el-button size="mini" type="primary" :disabled="!btnAuthList.length" @click="saveBtnAuth">保存</el-button>
          </template>
          <el-divider></el-divider>
          <div class="padding-box scroll-box" style="height:74.5vh">
            <el-checkbox-group v-model="state.btnAuthIdList" v-if="btnAuthList.length">
              <el-checkbox :label="item.id" v-for="(item, index) in btnAuthList" :key="index">{{ item.permName }}
              </el-checkbox>
            </el-checkbox-group>
            <el-empty v-else description="暂无数据"></el-empty>
          </div>
        </kade-table-wrap>
      </el-col>
    </el-row>
    <kade-role-edit @change="getList" :role="state.role" :title="state.role.id ? '角色编辑' : '新增角色'"
      v-model="showCreateModal" />
  </kade-route-card>
</template>
<script>
import { computed, onMounted, reactive, ref } from "vue";
import { useStore } from "vuex"
import { ElCol, ElRow, ElDivider, ElButton, ElSwitch, ElTable, ElTableColumn, ElPagination, ElMessage, ElCheckbox, ElCheckboxGroup, ElEmpty, ElMessageBox } from "element-plus";
import { getRoleForPage,editRole, saveRoleMenu, saveRoleArea, saveRoleDept, saveRolePermission } from '@/applications/eccard-sys/api';
import areaAuth from './components/areaAuth';
import deptAuth from './components/deptAuth';
import moduleAuth from './components/moduleAuth';
import EditModal from './components/edit';
const tabs = [
  { name: "mkqx", label: "模块权限" },
  { name: "qyqx", label: "区域权限" },
  { name: "zzqx", label: "组织权限" },
]

const treeProps = {
  children: 'children',
  label: 'menuName',
  isLeaf: 'leaf'
}
export default {
  components: {
    ElCol,
    ElRow,
    ElDivider,
    ElButton,
    ElSwitch,
    ElTable,
    ElTableColumn,
    ElPagination,
    ElCheckbox,
    ElCheckboxGroup,
    ElEmpty,
    'kade-role-edit': EditModal,
    'kade-area-auth': areaAuth,
    'kade-dept-auth': deptAuth,
    'kade-module-auth': moduleAuth,
  },
  setup() {
    const store = useStore()
    const showCreateModal = ref(false);
    const state = reactive({
      tab: "mkqx",
      role: {},
      roleId: "",
      form: {
        pageNum: 1,
        pageSize: 10
      },
      total: 0,
      btnAuthIdList: store.state["sys/role"].btnAuthList.filter(item => item.checked).map(item => item.id),
      dataList: [],
    });

    const selectRow = computed(() => {
      return store.state["sys/role"].rowRoleData
    })
    const btnAuthList = computed(() => {
      return store.state["sys/role"].btnAuthList
    })
    //分页获取角色列表
    const getList = async () => {
      store.commit("sys/role/updateState", {
        key: "rowRoleData",
        payload: '',
      });
      store.commit("sys/role/updateState", {
        key: "menuList",
        payload: [],
      });
      let { data: { list, total } } = await getRoleForPage(state.form)
      state.dataList = list
      state.total = total
    }

    const addRole = () => {
      state.role = {};
      showCreateModal.value = true;
    }
    const handleEditRole = (role) => {
      state.role = role;
      showCreateModal.value = true;
    }
    const statusChange = row => {
      console.log(row);
      ElMessageBox.confirm(`确认${row.status == 'ENABLE_TRUE' ? '停用' : '启用'}该角色?`, "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        console.log(1);
        let { code, message } = await editRole({ ...row, status: row.status == 'ENABLE_TRUE' ? 'ENABLE_FALSE' : 'ENABLE_TRUE'});
        if (code === 0) {
          ElMessage.success(message);
          getList()
        }
      });
    }
    const saveAuth = async () => {
      let fn
      let params
      if (state.tab == "mkqx") {
        fn = saveRoleMenu
        params = {
          roleId: store.state["sys/role"].rowRoleData.id,
          menuIds: store.state["sys/role"].moduleAuthIdList
        }
      } else if (state.tab == "qyqx") {
        fn = saveRoleArea
        params = {
          roleId: store.state["sys/role"].rowRoleData.id,
          areaIds: store.state["sys/role"].areaAuthIdList
        }
      } else if (state.tab == "zzqx") {
        fn = saveRoleDept
        params = {
          roleId: store.state["sys/role"].rowRoleData.id,
          deptIds: store.state["sys/role"].deptAuthIdList
        }
      }
      let { code, message } = await fn(params)
      if (code === 0) {
        ElMessage.success(message)
        if (state.tab == "mkqx") {
          store.dispatch("sys/role/getMenuList")
        } else if (state.tab == "qyqx") {
          store.dispatch("sys/role/getAreaList")
        } else if (state.tab == "zzqx") {
          store.dispatch("sys/role/getDeptList")
        }
      }
    }

    //点击角色列表行触发
    const tableRowClick = (row) => {
      store.commit("sys/role/updateState", {
        key: "rowRoleData",
        payload: row,
      });
      active()
    }

    const active = () => {
      if (store.state['sys/role'].rowRoleData) {
        if (state.tab == "mkqx") {
          store.dispatch("sys/role/getMenuList")
        } else if (state.tab == "qyqx") {
          store.dispatch("sys/role/getAreaList")
        } else if (state.tab == "zzqx") {
          store.dispatch("sys/role/getDeptList")
        }
      }
    }

    const saveBtnAuth = async () => {
      let params = {
        roleId: store.state["sys/role"].rowRoleData.id,
        menuId: store.state["sys/role"].rowModuleMenu.id,
        ids: state.btnAuthIdList
      }
      console.log(params);
      let { code, message } = await saveRolePermission(params)
      if (code === 0) {
        ElMessage.success(message)
        store.dispatch("sys/role/getAuthMenuList")
      }
    }


    //角色列表分页
    const currentChange = (val) => {
      state.form.pageNum = val
      getList()
    }
    const sizeChange = (val) => {
      state.form.pageSize = val
      getList()
    }
    onMounted(() => {
      getList()
    });
    return {
      tabs,
      treeProps,
      showCreateModal,
      state,
      statusChange,
      selectRow,
      btnAuthList,
      getList,
      addRole,
      handleEditRole,
      saveAuth,
      active,
      tableRowClick,
      saveBtnAuth,
      currentChange,
      sizeChange,
    };
  },
};
</script>
<style lang="scss" scoped>
.scroll-box {
  height: 73.5vh;
  overflow-y: scroll;
  padding-top: 20px;
}

.el-divider--horizontal {
  margin: 0;
}

:deep(.el-tabs__item) {
  height: 50px;
  line-height: 50px;
}

:deep(.el-tabs__header) {
  margin: 0;
}

:deep(.el-tabs__content) {
  padding: 0px 0px 0px 20px;
}

.el-row {
  flex-wrap: nowrap;
}
</style>