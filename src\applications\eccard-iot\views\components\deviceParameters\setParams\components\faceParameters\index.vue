<template>
  <kade-tab-wrap :tabs="tabs" style="margin-top: 20px" v-model="state.tab">
    <template #jbcs>
      <kade-basic-params />
    </template>
    <template #cwcs>
      <kade-temperature-params />
    </template>
    <template #kzcs>
      <kade-mask-params />
    </template>
    <template #wlcs>
      <kade-network-params />
    </template>
    <template #hdcs>
      <kade-callback-params />
    </template>
  </kade-tab-wrap>
</template>
<script>
import { reactive } from "@vue/reactivity";
import { useStore } from "vuex";
import { watch } from "@vue/runtime-core";
import basicParams from "./basicParams"
import temperatureParams from "./temperatureParams"
import maskParams from "./maskParams"
import networkParams from "./networkParams"
import callbackParams from "./callbackParams"
const tabs = [
  { name: "jbcs", label: "基本参数" },
  { name: "cwcs", label: "测温参数" },
  { name: "kzcs", label: "口罩参数" },
  { name: "wlcs", label: "网络参数" },
  { name: "hdcs", label: "回调参数" },

];
export default {
  components: {
    "kade-basic-params": basicParams,
    "kade-temperature-params": temperatureParams,
    "kade-mask-params": maskParams,
    "kade-network-params": networkParams,
    "kade-callback-params": callbackParams,
  },
  setup() {
    const store = useStore();
    const state = reactive({
      tab: "jbcs",
      form: {},
    });

    watch(() =>  store.state.deviceParameters[store.state.app.activeTab]&&store.state.deviceParameters[store.state.app.activeTab].isSetParams, val => {
      if (val) {
        state.tab = "jbcs"
      }else{
        state.tab=""
      }
    })

    return {
      tabs,
      state,
    };
  },
};
</script>
<style lang="scss" scoped>
</style>