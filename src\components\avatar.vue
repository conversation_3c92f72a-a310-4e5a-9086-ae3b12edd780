<template>
  <div :class="['kade-avatar', {'is-app-image': isAppImage, dark }]" :style="sizeStyle">
      <el-image v-if="!!src" :src="src" fit="fill" :style="sizeStyle">
        <template #placeholder>
          <kade-icon :size="fontSize" :name="icon" :color="color" />
        </template>
        <template #error>
          <kade-icon :size="fontSize"  :name="icon" :color="color" />
        </template>        
      </el-image>
      <kade-icon :size="fontSize" :name="icon" :color="color" v-else />   
  </div>
</template>
<script>
// 头像
import { ElImage } from 'element-plus';
import { isNumber } from '@/utils';
import Icon from './icon';

export default {
  components: {
    'el-image': ElImage,
    'kade-icon': Icon,
  },
  props: {
    src: String,
    icon: {
      type: String,
      default: 'el-icon-touxiang'
    },
    color: {
      type: String,
      default: '#fff'
    },
    size: {
      type: [String, Number],
      default: 30,
    },
    isAppImage: {
      type: Boolean,
      default: false,
    },
    dark: {
      type: Boolean,
      default: false,
    }
  },
  computed: {
    sizeStyle() {
      const size = isNumber(this.size) ? `${this.size}px` : this.size;
      return {
        width: size,
        height: size,
      }
    },
    fontSize() {
      return isNumber(this.size) ? `${this.size}px` : this.size;
    }
  },
}
</script>
<style lang="scss">
.kade-avatar{
  width: 30px;
  height: 30px;
  border-radius: 3px;
  border: 1px solid rgba(255,255,255,.3);
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 5px;
  overflow: hidden;
  &.dark{
    border: none;
  }
  &.is-app-image{
    background-color: $primary-color;
    border: none;
    i{
      font-size: $app-image-icon-size!important;
    }
  }
  .el-image{
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>