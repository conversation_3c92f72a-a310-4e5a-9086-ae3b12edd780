<template>
  <kade-route-card>
    <kade-table-filter @search="handleSearch" @reset="handleReset">
      <el-form inline label-width="100px" size="mini">
        <el-form-item label="物品信息">
          <el-input v-model="state.form.goodsName" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="物品所有人">
          <el-input v-model="state.form.goodsOwnerName" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="出入时间">
          <el-date-picker v-model="state.requestDate" type="daterange" range-separator="~" start-placeholder="开始日期" end-placeholder="结束日期" />
        </el-form-item>
        <kade-linkage-select :data="linkageData" :value="state.form" @change="linkageChange" />
      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="贵重物品出入登记列表">
      <template #extra>
        <el-button type="success" icon="el-icon-plus" size="mini" @click="edit('add')">新增</el-button>
        <el-button type="primary" icon="el-icon-delete-solid" size="mini" @click="batchDel()">批量删除</el-button>
        <el-button class="btn-purple" icon="el-icon-daochu" size="mini" @click="handleExport">导出</el-button>
      </template>
      <el-table border :data="state.data" v-loading="state.loading" @selection-change="selectChange">
        <el-table-column type="selection" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip v-for="(item,index) in column" :key="index" :label="item.label" :prop="item.prop" :width="item.width" align="center"></el-table-column>
        <el-table-column label="操作" align="center" width="200px">
          <template #default="scope">
            <el-button type="text" class="green" size="mini" @click="edit('details',scope.row)">详情</el-button>
            <el-button type="text" class="green" size="mini" @click="edit('edit',scope.row)">编辑</el-button>
            <el-button type="text" class="green" size="mini" @click="handleDel(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination v-model:currentPage="state.form.currentPage" v-model:page-size="state.form.pageSize" :page-sizes="[10, 20, 30, 40]" background layout="total, sizes, prev, pager, next, jumper" :total="state.total" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      </div>
    </kade-table-wrap>
    <kade-goods-inout-edit v-model:modelValue="state.isShow" :type="state.type" :rowData="state.rowData" @update:modelValue="close"/>
  </kade-route-card>
</template>

<script>
import { reactive, onMounted } from 'vue'
import { dateStr } from "@/utils/date.js"
import edit from "./components/edit.vue"
import linkageSelect from '@/applications/eccard-dorm/components/linkageSelect'
import { getGoodsInOut,delGoodsInOut,batchGoodsInOut,exportGoodsInout } from "@/applications/eccard-dorm/api.js"
import { ElForm, ElFormItem, ElInput,ElDatePicker,ElButton,ElTable,ElTableColumn,ElMessageBox,ElMessage,ElPagination } from "element-plus"
const linkageData={
  area:{label:'所属区域',valueKey:'areaPath',key:'areaPath'},
  building:{label:'楼栋',valueKey:'buildId'},
  unit:{label:'单元',valueKey:'unitNum'},
  floor:{label:'楼层',valueKey:'floorNum'},
  room:{label:'房间',valueKey:'roomId'},
}
const column = [
  { label: '房间', prop: 'roomString', width: '300px' },
  { label: '物品所有人', prop: 'goodsOwnerName' },
  { label: '出入时间', prop: 'inoutTime' },
  { label: '物品信息', prop: 'goodsInfo',width:'500px' }
]
export default {
  components: {
    ElForm,
    ElFormItem,
    ElInput,
    ElDatePicker,
    ElButton,
    ElTable,
    ElTableColumn,
    ElPagination,
    "kade-goods-inout-edit":edit,
    "kade-linkage-select":linkageSelect,
  },
  setup() {
    const state = reactive({
      form: {
        pageSize: 10,
        currentPage: 1
      },
      requestDate:[],
      loading:false,
      data:[],
      isShow:false,
      type:'',
      rowData:'',
      total:0,
      selectData:''
    })
    const handleReset=()=>{
      state.form={
        currentPage:1,
        pageSize:10
      }
      state.requestDate=[]
      getList()
    }
    const handleSearch=()=>{
      getList()
    }
    const linkageChange=(val)=>{
      state.form={...state.form,...val}
    }
    const handleDel = (row)=>{
      ElMessageBox.confirm(`确认删除已选择信息`,{
        type:'warning',
        confirmButtonText:'确定',
        cancelButtonText:'取消'
      }).then(async()=>{
        let {code,message} = await delGoodsInOut(row.id)
        if(code===0){
          ElMessage.success(message)
          getList()
        }
      })
    }
    const selectChange=(val)=>{
      state.selectData=val
    }
    const batchDel = ()=>{
      if(!state.selectData.length){
       return ElMessage.error('请先选择要删除的信息!')
      }
      ElMessageBox.confirm(`确定删除已选择信息？`,`提示`,{
        type:'warning',
        confirmButtonText:'确定',
        cancelButtonText:'取消'
      }).then(async()=>{
        let param = state.selectData.map((item)=>item.id).join(',')
        let {code,message} = await batchGoodsInOut(param)
        if(code===0){
          ElMessage.success(message)
          getList()
        }
      })
    }
    const handleCurrentChange=(val)=>{
      state.form.currentPage=val
      getList()
    }
    const handleSizeChange=(val)=>{
      state.form.currentPage=1
      state.form.pageSize=val
      getList()
    }
    const handleExport=async()=>{
      let res = await exportGoodsInout(state.form)
      let url = window.URL.createObjectURL(
        new Blob([res], { type: "application/vnd.ms-excel" })
      );
      let link = document.createElement("a");
      link.style.display = "none";
      link.href=url
      link.setAttribute("download",'贵重物品出入登记列表.xlsx')
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }
    const getList=async()=>{
      if(state.requestDate&&state.requestDate.length){
        state.form.inOutBeginDate=dateStr(state.requestDate[0])
        state.form.inOutEndDate=dateStr(state.requestDate[1])
      }else{
        delete state.form.inOutBeginDate
        delete state.form.inOutEndDate
      }
      state.loading=true
      try{
        let { data:{list,total}} = await getGoodsInOut(state.form)
        state.data=list
        state.total=total
        state.loading=false
      }catch{
        state.loading=false
      }
    }
    const edit = (type,row)=>{
      state.isShow=true
      state.type=type
      state.rowData=row
    }
    const close=(val)=>{
      if(val){
        getList()
      }
      state.isShow=false
    }
    onMounted(()=>{
      getList()
    })
    return {
      state,
      column,
      linkageData,
      handleReset,
      handleSearch,
      linkageChange,
      getList,
      batchDel,
      edit,
      close,
      handleExport,
      handleDel,
      selectChange,
      handleCurrentChange,
      handleSizeChange,
    }
  }
}
</script>

<style lang="scss" scoped>
.green :hover{
  text-decoration: underline;
}
:deep(.el-input--mini .el-input__inner){
  width: 198px;
}
:deep(.el-dialog){
  border-radius: 6px;
  padding-bottom: 40px;
}
:deep(.el-dialog__header){
  border-bottom: 1px solid #eeeeee;
  margin-bottom: 20px;
}
:deep(.el-dialog__footer){
  border: none;
  text-align: center;
  margin-top: 10px;
}
:deep(.el-textarea__inner){
  width: 525px;
}
:deep(.el-pagination .el-select .el-input .el-input__inner){
  width: 100px;
}
:deep(.el-pagination__editor.el-input .el-input__inner){
  width:46px;
}
</style>
