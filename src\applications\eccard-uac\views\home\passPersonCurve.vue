<template>
  <kade-route-card>
    <template #header>
      <div class="header">
        <span class="header-title">一天通行人次数曲线</span>
        <span class="header-right">单位：人次</span>
      </div>
    </template>
    <div id="passPersonCurveCharts" class="chartline"></div>
  </kade-route-card>
</template>
<script>
import * as echarts from "echarts";
import { onMounted } from "vue";
export default {
  setup() {
    const echartInit = () => {
      var chartDom = document.getElementById('passPersonCurveCharts');
      var myChart = echarts.init(chartDom);
      var option;
      const fn = (val) => {
        let arr = []
        for (let i = 0; i < val; i++) {
          arr.push((Math.random() * 1000).toFixed(0))
        }
        return arr
      }

      let arr = []
      for (let i = 0; i < 24; i++) {
        arr.push(i)
      }
      option = {
        xAxis: {
          type: 'category',
          data: arr
        },
        grid: {
          top: 20,
          bottom: 20,
          left: 40,
          right: 0
        },
        yAxis: [
          {
            show: true,
            type: "value",
            splitLine: {
              show: true,
              lineStyle: {
                type: "solid",
              },
            },
            axisLabel: {
              margin: 20,

            },
            minInterval: 1
          }
        ],
        series: [
          {
            data: fn(24),
            type: 'bar',
            barWidth: 27,
            label: {
              show: true,
              distance: 0,
              formatter: '{c}'
            },
          }
        ]
      };

      option && myChart.setOption(option);
    };
    onMounted(() => {
      echartInit();
    });
    return {
      echartInit,
    };
  }
}
</script>
<style scoped lang="scss">
.chartline {
  height: 260px;
  width: 100%;
}
</style>