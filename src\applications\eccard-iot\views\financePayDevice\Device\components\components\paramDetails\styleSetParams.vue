<template>
  <div class="padding-box">
    <el-form size="mini" inline label-width="120px">
      <el-form-item label="标题名称：">
        <el-input readonly :model-value="details.title"></el-input>
      </el-form-item>
      <el-form-item label="轮播间隔(秒)：">
        <el-input readonly :model-value="details.interval"></el-input>
      </el-form-item>
    </el-form>
    <el-table border :data="details.bannerList">
      <el-table-column label="排序" align="center">
        <template #default="scope">
          {{scope.$index+1}}
        </template>
      </el-table-column>
      <el-table-column label="图片" align="center">
        <template #default="scope">
          <img style="width:100px;height:100px" :src="scope.row" alt="">
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<script>
import { ElForm, ElFormItem,ElInput, ElTableColumn, ElTable } from "element-plus";
import { computed } from "vue";
import { useStore } from "vuex";
const defaultParamsFnc = () => {
  /* return {
    title: "智能消费终端轮播图",
    interval: 6,
    bannerList: [],
  }; */
  return {
    title: "",
    interval: "",
    bannerList: [],
  };
};
export default {
  components: {
    ElForm,
    ElFormItem,
    ElInput,
    ElTableColumn,
    ElTable,
  },
  setup() {
    const store = useStore();
    const details = computed(() => {
      let data = "";
      for (let item of store.state.deviceData.detailsParams) {
        if (item.paramType == "style") {
          data = JSON.parse(item.paramContent);
          break;
        }
      }
      if(!data){
        data=defaultParamsFnc()
      }
      console.log(data);
      return data;
    });

    return {

      details,
    };
  },
};
</script>
<style lang="scss" scoped>
</style>