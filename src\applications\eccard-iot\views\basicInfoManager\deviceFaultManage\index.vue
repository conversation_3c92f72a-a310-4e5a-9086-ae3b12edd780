<template>
  <kade-route-card style="height: auto" v-loading="state.loading">
    <kade-table-filter @search="getList()" @reset="reset">
      <el-form inline size="mini" label-width="100px">
        <el-form-item label="设备类型">
          <el-select v-model="state.form.deviceType" clearable>
            <el-option v-for="(item, index) in state.deviceTypeList" :key="index" :label="item.cfgValue"
              :value="item.cfgKey"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="故障列表">
      <template #extra>
        <el-button @click="handleEdit({})" class="btn-blue" icon="el-icon-plus" size="mini">新增</el-button>
      </template>
      <el-table style="width: 100%" :data="state.dataList" ref="multipleTable" v-loading="state.loading" border stripe>
        <el-table-column label="故障序号" align="center" prop="id" width="100" show-overflow-tooltip></el-table-column>
        <el-table-column show-overflow-tooltip label="设备类型" prop="deviceType" align="center">
          <template #default="scope">
            {{ deviceType(scope.row) }}
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip label="故障名称" prop="failureName" align="center"></el-table-column>
        <el-table-column label="操作" prop="endtermBalance" align="center">
          <template #default="scope">
            <el-button type="text" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button type="text" @click="handleDel(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination background v-model:currentPage="state.form.currentPage" v-model:page-size="state.form.pageSize"
          :page-sizes="[5, 10, 20, 30]" layout="total,sizes, prev, pager, next, jumper" :total="state.total"
          @size-change="handleSizeChange" @current-change="handlePageChange">
        </el-pagination>
      </div>
    </kade-table-wrap>
    <kd-deviceFaultManage-edit :isShow="state.isEdit" :rowData="state.rowData" @close="closeEdit" />
  </kade-route-card>
</template>
<script>
import {
  ElButton,
  ElForm,
  ElFormItem,
  ElTable,
  ElTableColumn,
  ElPagination,
  // ElMessage,
  // ElMessageBox,
  ElSelect,
  ElOption,
} from "element-plus";
import { reactive } from "@vue/reactivity";
import { onMounted } from "@vue/runtime-core";
import {
  getdeviceFaultManageList,
  iotCfg,
} from "@/applications/eccard-iot/api";

import edit from "./components/edit.vue";
export default {
  components: {
    "el-button": ElButton,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    "el-table": ElTable,
    "el-table-column": ElTableColumn,
    "el-pagination": ElPagination,
    ElSelect,
    ElOption,
    "kd-deviceFaultManage-edit": edit,
    // ElMessage,
    // ElMessageBox,
  },
  setup() {
    const state = reactive({
      loading: false,
      isEdit: false,
      //搜索条件
      form: {
        currentPage: 1,
        pageSize: 10,
      },
      //表格数据
      dataList: [],
      deviceTypeList: [],
      total: 0,
      rowData: {},
    });
    //获取表格数据
    const getList = async function () {
      state.loading = true;
      try {
        let {
          data: { total, list }
        } = await getdeviceFaultManageList(state.form);
        console.log(list);
        state.dataList = list;
        state.total = total;
        state.loading = false;
      } catch {
        state.loading = false;
      }
    };
    //获取设备类型
    const queryDeviceType = async () => {
      let { data } = await iotCfg();
      state.deviceTypeList = data;
    };
    //编辑/新增
    const handleEdit = (row) => {
      state.rowData = row;
      state.isEdit = true;
    };
    //删除
    const handleDel = () => {
      // ElMessageBox.confirm("确认删除?", "提示", {
      //   confirmButtonText: "确认",
      //   cancelButtonText: "取消",
      //   type: "warning",
      // }).then(async () => {
      //   let { code, message } = await delUserInfo(row.id);
      //   if (code === 0) {
      //     ElMessage.success(message);
      //     getList();
      //   }
      // });
    };
    //分页 下一页
    const handlePageChange = (val) => {
      state.form.currentPage = val;
      getList();
    };
    //分页 当前多少页
    const handleSizeChange = (val) => {
      state.form.currentPage = 1;
      state.form.pageSize = val;
      getList();
    };
    //重置
    const reset = () => {
      state.form = {
        currentPage: 1,
        pageSize: 10,
      };
      getList();
    };
    //关闭编辑弹窗
    const closeEdit = (val) => {
      if (val) {
        getList();
      }
      state.isEdit = false;
    };
    const deviceType = (row) => {
      // cfgValue
      return state.deviceTypeList.filter(d => d.cfgKey == row.deviceType)[0]?.cfgValue
    }
    onMounted(() => {
      getList();
      queryDeviceType()
    });
    return {
      state,
      getList,
      handleEdit,
      handleDel,
      handlePageChange,
      handleSizeChange,
      reset,
      closeEdit,
      queryDeviceType,
      deviceType
    };
  },
};
</script>
<style lang="scss" scoped></style>
