<template>
  <el-dialog :model-value="dialogViseble" title="编辑配置信息" width="700px" :before-close="handleClose">
    <el-form inline label-width="120px" size="mini" :model="state.form" ref="formRef" :rules="rules">
      <el-form-item label="消息名称：">
        <el-input v-model="state.form.msgName" placeholder="请输入" readonly></el-input>
      </el-form-item>
      <el-form-item label="启用状态：" prop="status">
        <el-select v-model="state.form.status" placeholder="请选择">
          <el-option v-for="(item, index) in statusList" :key="index" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="是否强制接收：" prop="forceReceive">
        <el-select v-model="state.form.forceReceive" placeholder="请选择">
          <el-option v-for="(item, index) in receiveList" :key="index" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="消息类型：" prop="msgType">
        <el-select v-model="state.form.msgType" placeholder="请选择">
          <el-option v-for="(item, index) in typeList" :key="index" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="推送周期：" v-if="state.form.msgType == 'DEFINITE_TIME'" prop="pushCycle">
        <el-select v-model="state.form.pushCycle" placeholder="请选择" @change="pushCycleChange">
          <el-option v-for="(item, index) in pushList" :key="index" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="推送时间：" prop="pushTime" v-if="state.form.msgType == 'DEFINITE_TIME'">
        <el-select v-if="state.form.pushCycle == 'BY_WEEK'" v-model="state.form.pushTime">
          <el-option v-for="(item, index) in weekList" :key="index" :label="item.label" :value="item.value"></el-option>
        </el-select>
        <el-select v-else-if="state.form.pushCycle == 'MONTHLY'" v-model="state.form.pushTime">
          <el-option v-for="(item, index) in 31" :key="index" :label="item" :value="String(item)"></el-option>
        </el-select>
        <el-time-picker v-else v-model="state.form.pushTime" placeholder="请选择" />
      </el-form-item>
      <el-form-item label="推送方式：" prop="pushMode">
        <el-checkbox-group v-model="state.form.pushMode">
          <el-checkbox v-for="(item, index) in modeList" :key="index" :label="item.value">{{ item.label }}</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="接收人：" prop="receiveRole">
        <el-checkbox-group v-model="state.form.receiveRole">
          <el-checkbox v-for="(item, index) in roleList" :key="index" :label="item.value">{{ item.label }}</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="消息模板：">
        <el-input v-model="state.form.msgTemplate" type="textarea" :rows="5" width="400px" show-word-limit
          maxlength="200"></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button type="primary" @click="submit" size="mini">保存</el-button>
        <el-button @click="handleClose" size="mini">取消</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import {
  ElDialog,
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElCheckbox,
  ElTimePicker,
  ElMessage,
  ElCheckboxGroup
} from "element-plus";
import { reactive, ref } from "@vue/reactivity";
import { watch, nextTick } from '@vue/runtime-core';
import { useDict } from "@/hooks/useDict.js"
import { hourStr,time_to_sec } from "@/utils/date.js"
import { editMessagePushParam } from '@/applications/eccard-dorm/api.js'
const rules = {
  status: { required: true, message: "请选择", trigger: "change" },
  forceReceive: { required: true, message: "请选择", trigger: "change" },
  msgType: { required: true, message: "请选择", trigger: "change" },
  pushCycle: { required: true, message: "请选择", trigger: "change" },
  pushTime: { required: true, message: "请选择", trigger: "change" },
  pushMode: { required: true, message: "请选择", trigger: 'change' },
  receiveRole: { required: true, message: "请选择", trigger: "change" }
}
const weekList = [
  { label: '星期一', value: '星期一' },
  { label: '星期二', value: '星期二' },
  { label: '星期三', value: '星期三' },
  { label: '星期四', value: '星期四' },
  { label: '星期五', value: '星期五' },
  { label: '星期六', value: '星期六' },
  { label: '星期日', value: '星期日' }
]
export default {
  props: {
    dialogViseble: {
      type: Boolean,
      default: false,
    },
    rowData: {
      type: String,
      default: ""
    }
  },
  components: {
    ElDialog,
    ElButton,
    ElForm,
    ElFormItem,
    ElInput,
    ElSelect,
    ElOption,
    ElCheckbox,
    ElTimePicker,
    ElCheckboxGroup
  },
  setup(props, context) {
    const formRef = ref(null)
    const statusList = useDict('SYS_ENABLE') //启用状态
    console.log(statusList)
    const receiveList = useDict('SYS_BOOL_STRING') //是否强制接收
    const typeList = useDict('SYS_MESSAGE_PUSH_TYPE') //消息推送类型
    console.log(typeList)
    const pushList = useDict('SYS_MESSAGE_PUSH_CYCLE_TIME') //推送周期
    const modeList = useDict('SYS_MESSAGE_PUSH_METHOD')
    const roleList = useDict('SYS_MESSAGE_PUSH_RECEIVE_ROLE')
    const state = reactive({
      form: {},
    });
    const pushCycleChange = () => {
      state.form.pushTime = ""
    }
    const handleClose = () => {
      context.emit("close", false);
    };
    const submit = () => {
      formRef.value.validate(async (valid) => {
        if (valid) {
          let param = { ...state.form }
          param.pushMode = param.pushMode.join(',')
          param.receiveRole = param.receiveRole.join(',')
          console.log(param)
          if (param.pushCycle == 'BY_DAY') {
            param.pushTime = hourStr(param.pushTime)
          }
          if(param.msgType == 'REAL_TIME'){
            param.pushCycle=''
            param.pushTime=''
          }
          let { code, message } = await editMessagePushParam(param)
          if (code === 0) {
            ElMessage.success(message)
            context.emit('close', true)
          }
        }

      })
    };
    watch(() => props.dialogViseble, val => {
      if (val) {
        state.form = { ...props.rowData }
        state.form.pushMode = state.form.pushMode.split(',')
        state.form.receiveRole = state.form.receiveRole.split(',')
        if (state.form.pushCycle == 'BY_DAY') {
          state.form.pushTime =new Date(time_to_sec(state.form.pushTime)+new Date(new Date().toLocaleDateString()).getTime())
          console.log(state.form.pushTime);
        }
        console.log(state.form)
        nextTick(() => {
          formRef.value.clearValidate()
        })
      }
    })
    return {
      state,
      submit,
      handleClose,
      formRef,
      rules,
      statusList,
      receiveList,
      pushCycleChange,
      typeList,
      pushList,
      weekList,
      modeList,
      roleList,
    };
  },
};
</script>

<style lang="scss" scoped>
:deep(.el-input--mini .el-input__inner) {
  width: 182px;
}
</style>

