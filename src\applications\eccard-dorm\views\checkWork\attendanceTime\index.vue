<template>
  <kade-route-card>
    <kade-table-wrap title="时段列表">
      <el-table border :data="state.data" v-loading="state.loading">
        <el-table-column prop="code" label="时段号" align="center"></el-table-column>
        <el-table-column prop="name" label="时段名称" align="center"></el-table-column>
        <el-table-column prop="beginTime" label="正常签到开始时间" align="center"></el-table-column>
        <el-table-column prop="endTime" label="正常签到结束时间" align="center"></el-table-column>
        <el-table-column prop="terminateTime" label="终止签到时间" align="center"></el-table-column>
        <el-table-column prop="status" label="启用状态" align="center">
          <template #default="scope">
          {{dictionaryFilter(scope.row.status)}}
          </template>
        </el-table-column>
        <el-table-column prop="remarks" label="备注" align="center" width="300px"></el-table-column>
        <el-table-column label="操作" align="center" width="80px">
          <template #default="scope">
            <el-button size="mini" class="green" type="text" @click="edit(scope.row)">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
    </kade-table-wrap>
    <kade-time-edit :dialogVisible="state.dialogVisible" :selectRow="state.selectRow" @close="close"></kade-time-edit>
    <div class="text">
      时段说明：<br />

      1、正常签到开始时间到正常签到结束时间之间的签到记录系统会视为正常；<br />

      2、正常签到结束时间到终止签到时间之间的签到记录系统会视为晚归；<br />

      3、其余签到记录和无签到记录系统会视为未归。
    </div>
  </kade-route-card>
</template>

<script>
import { ElTable, ElTableColumn, ElButton } from "element-plus";
import { reactive } from "@vue/reactivity";
import edit from "./components/edit.vue";
import { onMounted } from "@vue/runtime-core";
import { attendancePeriodList } from '@/applications/eccard-dorm/api.js'
export default {
  components: {
    ElTable,
    ElTableColumn,
    ElButton,
    "kade-time-edit": edit,
  },
  setup() {
    const state=reactive({
      data:[],
      loading:false,
    })
    const getList = async () => { 
      state.loading=true
      try{
        let { data } = await attendancePeriodList()
        state.data=data
        state.loading=false
      }
      catch{
        state.loading=false
      }
    };
    const edit = (row) => {
      state.selectRow = row;
      state.dialogVisible = true;
    };
    const close = (val) => {
      if(val){
        getList()
      }
      state.dialogVisible = false;
    };
    onMounted(() => {
      getList();
    });
    return {
      state,
      edit,
      close,

    };
  },
};
</script>

<style lang="scss" scoped>
.green :hover {
  text-decoration: underline;
}

.kade-table-wrap {
  padding: 0;
  border-bottom: none;
}

.text {
  color: rgb(199, 8, 8);
  margin-top: 15px;
  font-size: 12px;
}

:deep(.el-dialog__footer) {
  text-align: center;
  border: 0;
}

:deep(.el-dialog__header) {
  border-bottom: 1px solid #eeeeee;
}

:deep(.el-dialog) {
  border-radius: 8px;
}

:deep(.el-input--mini .el-input__inner) {
  width: 182px;
}

:deep(.el-date-editor.el-input) {
  width: 193px;
}

:deep(.el-input-number) {
  width: 193px;
}
:deep(.el-input__suffix){
  right:15px
}
</style>