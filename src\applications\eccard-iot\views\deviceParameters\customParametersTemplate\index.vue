<template>
  <div class="attendance-device padding-box">
    <kade-table-filter @search="handleSearch" @reset="handleReset">
      <el-form inline label-width="80px" size="mini">
        <el-form-item label="模板名称">
          <el-input v-model="state.form.templateName" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="设备类型">
          <el-select v-model="state.form.deviceType">
            <el-option v-for="(item, index) in state.deviceTypelist" :key="index" :label="item.label"
              :value="item.value"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="参数模板列表">
      <template #extra>
        <el-button icon="el-icon-plus" type="success" size="mini" @click="handleEdit({ status: true }, 'add')">新增
        </el-button>
      </template>
      <el-table border height="55vh" :data="state.dataList" v-loading="state.loading">
        <el-table-column show-overflow-tooltip prop="templateName" label="模板名称" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="deviceType" label="设备类型" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="status" label="启用状态" align="center">
          <template #default="scope">
            {{ scope.row.status ? '启用' : '禁用' }}
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip prop="dateTime" label="添加时间" align="center">
          <template #default="scope">
            {{ timeStr(scope.row.dateTime) }}
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip prop="remarks" label="备注" align="center"></el-table-column>
        <el-table-column label="操作" align="center" width="200px">
          <template #default="scope">
            <el-button type="text" @click="handleDetails(scope.row)" size="mini">参数详情</el-button>
            <el-button type="text" @click="handleEdit(scope.row, 'edit')" size="mini">编辑</el-button>
            <el-button type="text" @click="handleDel(scope.row, scope.$index)" size="mini">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination v-model:currentPage="state.form.pageNum" v-model:page-size="state.form.pageSize"
          :page-sizes="[10, 20, 30, 40]" background layout="total, sizes, prev, pager, next, jumper"
          :total="state.total" @size-change="handleSizeChange" @current-change="handleCurrentChange">
        </el-pagination>
      </div>
    </kade-table-wrap>
    <kade-template-edit :dialogVisible="state.dialogVisible" :data="state.rowData" :type="state.type"
      :deviceTypelist="state.deviceTypelist" @edit="state.type = 'edit'" @close="close"></kade-template-edit>
    <kade-param-template v-model="state.isParamTemplate" :data="state.rowData" @update:modelValue="paramsClose" />
  </div>
</template>

<script>
import {
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElTable,
  ElTableColumn,
  ElPagination,
  ElMessageBox,
} from "element-plus";
import { reactive, onMounted } from "vue";
// import { objToArray } from "@/utils"
import { timeStr } from "@/utils/date"
// import { iotCfg } from "@/applications/eccard-iot/api";
import edit from "./edit.vue";
import paramTemplate from "./components/paramTemplate.vue";
export default {
  components: {
    ElButton,
    ElForm,
    ElFormItem,
    ElInput,
    ElSelect,
    ElOption,
    ElTable,
    ElTableColumn,
    ElPagination,
    "kade-template-edit": edit,
    "kade-param-template": paramTemplate,
  },
  setup() {
    const state = reactive({
      loading: false,
      dialogVisible: false,
      isParamTemplate: false,
      deviceTypelist: [],
      form: {
        pageNum: 1,
        pageSize: 10
      },
      dataList: [],
      total: 0,
      rowData: {},
    });

    const list = [
      { id: 1, templateName: "智能卡消费终端自定义参数", deviceType: "智能卡消费终端", status: true, dateTime: new Date('2022/5/26 09:21:31'), remarks: "--", paramsList: [] },
    ]
    if (!sessionStorage.getItem("templateList")) {
      sessionStorage.setItem("templateList", JSON.stringify(list))
    }

    const getDeviceTypeList = async () => {
      // let { data } = await iotCfg();
      // state.deviceTypelist = objToArray(JSON.parse(data.dvalue));
      state.deviceTypelist = [
        { value: "自定义", label: "自定义" },
        { value: "智能卡消费终端", label: "智能卡消费终端" },
        { value: "智能消费终端", label: "智能消费终端" },
        { value: "自助式洗衣机", label: "自助式洗衣机" },
        { value: "自助式烘干机", label: "自助式烘干机" },
        { value: "自助式吹风机", label: "自助式吹风机" },
        { value: "门禁控制器", label: "门禁控制器" },
        { value: "通道控制器", label: "通道控制器" },
        { value: "人脸识别智能终端", label: "人脸识别智能终端" },
        { value: "指纹门禁一体机", label: "指纹门禁一体机" },
        { value: "访客一体终端", label: "访客一体终端" },
        { value: "智能摄像机", label: "智能摄像机" },
        { value: "无线联网智能门锁", label: "无线联网智能门锁" },
        { value: "人脸识别考勤终端", label: "人脸识别考勤终端" },
        { value: "智能人脸分析盒", label: "智能人脸分析盒" },
        { value: "智慧大屏终端", label: "智慧大屏终端" },
        { value: "手持会议签到终端", label: "手持会议签到终端" },
        { value: "电子班牌终端", label: "电子班牌终端" },
        { value: "智能卡节水控制器", label: "智能卡节水控制器" },
        { value: "智能远传水表", label: "智能远传水表" },
        { value: "智能电表", label: "智能电表" },
        { value: "IC卡读写器", label: "IC卡读写器" },
        { value: "前置服务网关", label: "前置服务网关" },
        { value: "密钥加密服务器", label: "密钥加密服务器" },
        { value: "金融加密机", label: "金融加密机" },
        { value: "自助服务终端", label: "自助服务终端" },
        { value: "自助请假终端", label: "自助请假终端" },
      ]
    }
    const getList = async () => {
      state.dataList = JSON.parse(sessionStorage.getItem("templateList"))
    }
    const handleEdit = (row, type) => {
      state.type = type
      state.rowData = row
      state.dialogVisible = true;
    };
    const handleDetails = (row) => {
      state.rowData = row
      state.isParamTemplate = true;
    }
    const handleDel = (row, index) => {
      ElMessageBox.confirm(`确认删除?`, {
        type: "warning",
        closeOnPressEscape: false,
        closeOnClickModal: false,
      }).then(async () => {
        let list = JSON.parse(sessionStorage.getItem("templateList"))
        list.splice(index, 1)
        sessionStorage.setItem("templateList", JSON.stringify(list))
        getList()
      });
    }
    const handleSearch = () => {
      getList()
    }
    const handleReset = () => {
      state.form = {
        pageNum: 1,
        pageSize: 10
      }
    }
    const handleSizeChange = (val) => {
      state.form.pageNum = 1
      state.form.pageSize = val
      getList()
    }
    const handleCurrentChange = (val) => {
      state.form.pageNum = val
      getList()
    }
    const close = (val) => {
      if (val) {
        getList()
      }
      state.dialogVisible = false;
    };
    const paramsClose = () => {
      getList()
      state.isParamTemplate = false
    }
    onMounted(() => {
      getDeviceTypeList()
      getList()
    })
    return {
      timeStr,
      state,
      handleEdit,
      handleDetails,
      handleDel,
      handleSearch,
      handleReset,
      handleSizeChange,
      handleCurrentChange,
      close,
      paramsClose
    };
  },
};
</script>

<style lang="scss" scoped>
:deep(.el-divider--horizontal) {
  margin: 0;
}

:deep(.el-dialog__footer) {
  text-align: center;
  border: 0;
}

:deep(.el-dialog__header) {
  border-bottom: 1px solid #eeeeee;
}

:deep(.el-dialog) {
  border-radius: 8px;
}
</style>