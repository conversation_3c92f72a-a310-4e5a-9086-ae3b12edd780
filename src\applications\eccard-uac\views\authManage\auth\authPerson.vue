<template>
  <kade-table-filter @search="handleSearch" @reset="handleReset">
    <el-form inline label-width="100px" size="mini">
      <el-form-item label="组织机构">
        <kade-dept-select-tree style="width: 100%" :value="state.form.deptPath" valueKey="deptPath" :multiple="false"
          @valueChange="(val) => (state.form.deptPath = val.deptPath)" />
      </el-form-item>
      <el-form-item label="身份类别">
        <el-select clearable v-model="state.form.userRole">
          <el-option v-for="(item, index) in state.roleList" :key="index" :label="item.roleName" :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="性别">
        <el-select clearable v-model="state.form.userSex">
          <el-option v-for="(item, index) in sexList" :key="index" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="卡类">
        <el-select clearable v-model="state.form.cardType">
          <el-option v-for="(item, index) in state.cardTypeList" :key="index" :label="item.ctName" :value="item.ctCode">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="卡片类别">
        <el-select clearable v-model="state.form.cardCategory">
          <el-option v-for="(item, index) in cardList" :key="index" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="是否授权">
        <el-select clearable v-model="state.form.workstationType">
          <el-option v-for="(item, index) in boolList" :key="index" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="关键字">
        <el-input clearable v-model="state.form.keyWord" placeholder="请输入"></el-input>
      </el-form-item>
    </el-form>
  </kade-table-filter>
  <kade-table-wrap title="用户权限列表">
    <el-table border height="46vh" v-loading="state.loading" :data="state.dataList">
      <el-table-column show-overflow-tooltip prop="userCode" label="用户编号" align="center"></el-table-column>
      <el-table-column show-overflow-tooltip prop="userName" label="用户名称" align="center"></el-table-column>
      <el-table-column show-overflow-tooltip prop="userSex" label="性别" align="center">
        <template #default="scope">
          {{ dictionaryFilter(scope.row.userSex) }}
        </template>
      </el-table-column>
      <el-table-column show-overflow-tooltip prop="deptName" label="组织机构" align="center"></el-table-column>
      <el-table-column show-overflow-tooltip prop="roleName" label="身份类别" align="center"></el-table-column>
      <el-table-column show-overflow-tooltip prop="cardTypeName" label="卡类" align="center"></el-table-column>
      <el-table-column show-overflow-tooltip prop="cardCategoryName" label="卡片类别" align="center">
        <template #default="scope">
          {{ dictionaryFilter(scope.row.cardCategoryName) }}
        </template>
      </el-table-column>
      <el-table-column show-overflow-tooltip prop="ip" label="是否授权" align="center"></el-table-column>
      <el-table-column label="操作" align="center">
        <template #default="scope">
          <el-button type="text" @click="handleAuth(scope.row)" size="mini">授权</el-button>
          <el-button type="text" @click="handleDetails(scope.row)" size="mini">详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="pagination">
      <el-pagination v-model:currentPage="state.form.pageNum" v-model:page-size="state.form.pageSize"
        :page-sizes="[10, 20, 30, 40]" background layout="total, sizes, prev, pager, next, jumper" :total="state.total"
        @size-change="handleSizeChange" @current-change="handleCurrentChange">
      </el-pagination>
    </div>
  </kade-table-wrap>
  <kade-auth-person-dialog v-model="state.isAuth" :rowData="state.rowData" @update:modelValue="state.isAuth = false" />
  <kade-details-person-dialog v-model="state.isDetails" :rowData="state.rowData"
    @update:modelValue="state.isDetails = false" />
</template>
<script>

import { reactive, onMounted } from "vue"
import { useDict } from "@/hooks/useDict.js";
import { ElForm, ElFormItem, ElSelect, ElOption, ElInput, ElTable, ElTableColumn, ElPagination, ElButton } from "element-plus"
import {
  getRolelist
} from "@/applications/eccard-basic-data/api";
import { getCardTypeList } from "@/applications/eccard-iot/api";
import { authManageUserList } from "@/applications/eccard-uac/api";
import authPersonDialog from "./components/authPersonDialog.vue"
import detailsPersonDialog from "./components/detailsPersonDialog.vue"
import deptSelectTree from "@/components/tree/deptSelectTree.vue";
export default {
  components: {
    ElForm, ElFormItem, ElSelect, ElOption, ElInput, ElTable, ElTableColumn, ElPagination, ElButton,
    "kade-auth-person-dialog": authPersonDialog,
    "kade-details-person-dialog": detailsPersonDialog,
    "kade-dept-select-tree": deptSelectTree,
  },
  setup() {
    const cardList = useDict("PERSON_CARD_CATEGORY")
    const sexList = useDict("SYS_SEX")
    const boolList = useDict("SYS_BOOL_STRING")
    const state = reactive({
      loading: false,
      isAuth: false,
      isDetails: false,
      cardTypeList: [],
      roleList: [],
      form: {
        currentPage: 1,
        pageSize: 10,
        cardCategory: "MASTER_CARD"
      },
      dataList: [],
      total: 0,
      rowData: {}
    })
    //获取卡片类型
    const queryCardTypeList = () => {
      getCardTypeList().then((res) => {
        state.cardTypeList = res.data;
      });
    };
    const queryRolelist = async () => {
      let { data } = await getRolelist();
      state.roleList = data;
    };
    const getList = async () => {
      state.loading = true
      try {
        let { data: { list, total } } = await authManageUserList(state.form)
        state.dataList = list
        state.total = total
        state.loading = false
      }
      catch {
        state.loading = false
      }
    }
    const handleAuth = (row) => {
      console.log(row);
      state.rowData = row
      state.isAuth = true
    }
    const handleDetails = (row) => {
      console.log(row);
      state.rowData = row
      state.isDetails = true
    }
    const handleSearch = () => {
      getList()
    }
    const handleReset = () => {
      state.form = {
        currentPage: 1,
        pageSize: 10,
        cardCategory: "MASTER_CARD"
      }
    }
    const handleCurrentChange = val => {
      state.form.currentPage = val
      getList()
    }
    const handleSizeChange = val => {
      state.form.currentPage = 1
      state.form.pageSize = val
      getList()
    }
    onMounted(() => {
      getList()
      queryCardTypeList()
      queryRolelist()
    })
    return {
      cardList,
      sexList,
      boolList,
      state,
      handleAuth,
      handleDetails,
      handleSearch,
      handleReset,
      handleCurrentChange,
      handleSizeChange
    }
  }
}
</script>