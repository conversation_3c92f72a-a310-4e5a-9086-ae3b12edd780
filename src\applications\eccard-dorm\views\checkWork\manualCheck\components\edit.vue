<template>
  <el-dialog :model-value="dialogVisible" title="编辑补查信息" width="630px" :before-close="handleClose">
    <el-form inline label-width="90px" size="mini" :model="state.form" ref="formRef" :rules="rules">
      <el-form-item label="人员编号">
        <el-input v-model="state.form.userCode" disabled />
      </el-form-item>
      <el-form-item label="人员姓名">
        <el-input v-model="state.form.userName" disabled />
      </el-form-item>
      <el-form-item label="复核日期">
        <el-input v-model="state.form.attendanceDate" disabled />
      </el-form-item>
      <el-form-item label="组织机构">
        <el-input v-model="state.form.deptName" disabled />
      </el-form-item>
      <el-form-item label="复核时段">
        <el-input v-model="state.form.checkPeriodName" disabled />
      </el-form-item>
      <el-form-item label="复核结果" prop="checkResult">
        <el-select v-model="state.form.checkResult" clearable>
          <el-option v-for="(item, index) in resultList" :key="index" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="复核人员" prop="checkPerson">
        <el-input v-model="state.form.checkPerson" clearable></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose" size="mini">取消</el-button>
        <el-button type="primary" @click="submit()" size="mini">确认</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { ElDialog, ElButton, ElForm, ElFormItem, ElInput, ElOption, ElSelect, ElMessage } from "element-plus"
import { reactive } from '@vue/reactivity'
import { nextTick, watch, ref } from 'vue';
import { useDict } from "@/hooks/useDict";
import { manualCheckEdit } from '@/applications/eccard-dorm/api.js'
const rules = {
  checkResult: [
    {
      required: true,
      message: "请选择",
      trigger: "change",
    },
  ],
  checkPerson: [
    {
      required: true,
      message: "请输入",
      trigger: "blur",
    },
  ],
}
export default {
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    selectRow: {
      type: String,
      default: ""
    }
  },
  components: {
    ElDialog,
    ElButton,
    ElForm,
    ElFormItem,
    ElInput,
    ElOption,
    ElSelect,
  },
  setup(props, context) {
    const formRef = ref(null)
    const resultList = useDict('DORM_ATTENDANCE_RESULT')
    const state = reactive({
      form: {},
    });
    watch(() => props.dialogVisible, val => {
      if (val) {
        state.form = { ...props.selectRow, }
        console.log(props.selectRow)
      }
      nextTick(() => {
        formRef.value.clearValidate();
      })
    })
    const submit = () => {
      formRef.value.validate(async (valid) => {
        if (valid) {
          let { checkPerson,checkResult,id } = { ...state.form }
          let param = { checkPerson,checkResult,id }
          let { code, message } = await manualCheckEdit(param)
          if (code === 0) {
            ElMessage.success(message)
            context.emit('close', true)
          } else {
            return false
          }
        }
      })
    }
    const handleClose = () => {
      state.form = {}
      context.emit("close", false)
    };
    return {
      state,
      rules,
      formRef,
      submit,
      resultList,
      handleClose,
    }
  }
}
</script>

<style lang="scss" scoped>
.el-form {
  margin-top: 20px;
}

:deep(.el-input--mini .el-input__inner) {
  width: 182px !important;
}

:deep(.el-date-editor.el-input, .el-date-editor.el-input__inner) {
  width: 182px !important;
}
</style>