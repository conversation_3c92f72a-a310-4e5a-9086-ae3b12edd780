<template>
  <el-dialog :model-value="isShow" :title="title" width="90%" :before-close="beforeClose" :close-on-click-modal="false">
    <div class="dialog-box" v-loading="state.loading">
      <div class="title">设备基本信息</div>
      <el-divider></el-divider>
      <div class="padding-form-box">
        <el-form label-width="120px" ref="formRef" inline size="mini" :model="state.form" :rules="rules">
          <el-form-item label="设备厂家：" prop="deviceFactory">
            <el-select v-model="state.form.deviceFactory">
              <el-option v-for="(item,index) in listData.deviceFactoryList" :key="index" :label="item.factoryName" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="终端型号：" prop="deviceModel">
            <el-select v-model="state.form.deviceModel">
              <el-option v-for="(item,index) in listData.modelList" :key="index" :label="item.productMode" :value="item.productMode"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="所属区域：" prop="areaId">
            <kade-area-select-tree style="width: 100%" :value="state.form.areaId" valueKey="id" :multiple="false" @valueChange="(val) => (state.form.areaId = val.id)" />
          </el-form-item>
          <el-form-item label="设备机号：" prop="deviceNo">
            <el-input v-model="state.form.deviceNo" placeholder="请输入" maxlength="9"></el-input>
          </el-form-item>
          <el-form-item label="设备名称：" prop="deviceName">
            <el-input v-model="state.form.deviceName" placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="设备使用状态：" prop="deviceStatus">
            <el-select v-model="state.form.deviceStatus">
              <el-option v-for="(item, index) in deviceStaticList" :key="index" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="所属网关：">
            <el-select v-model="state.form.gatewayNo">
              <el-option v-for="(item,index) in listData.gatewayList" :key="index" :label="item.gatewayName" :value="Number(item.id)"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="所属工作站：">
            <el-select v-model="state.form.workstationId">
              <el-option :label="item.name" :value="item.id" v-for="(item,index) in listData.workStationList" :key="index"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="连接类型：">
            <el-select v-model="state.form.deviceConnectType">
              <el-option :label="item.label" :value="item.value" v-for="(item,index) in deviceConnectTypeList" :key="index"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="设备IP：">
            <el-input v-model="state.form.deviceIp" placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="固件版本：">
            <el-select v-model="state.form.deviceVersion">
              <el-option v-for="(item, index) in state.deviceVersion" :key="index" :label="item.deviceVersionName" :value="item.deviceVersionCode"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="参数：">
            <el-select v-model="state.form.paramId">
              <el-option v-for="(item,index) in listData.paramsList" :key="index" :label="item.name" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="设备SN号：" prop="deviceSn">
            <el-input v-model="state.form.deviceSn" placeholder="请输入"></el-input>
          </el-form-item>
        </el-form>
        <el-form label-width="120px" size="mini">
          <el-form-item label="设备位置：">
            <el-input class="device-position-width" v-model="state.form.devicePosition" placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="备注：">
            <el-input v-model="state.form.deviceRemarks" type="textarea" placeholder="请输入备注"></el-input>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="dialog-box">
      <div class="title">设备扩展信息</div>
      <el-divider></el-divider>
      <div class="padding-form-box">
        <el-form label-width="120px" inline size="mini">
          <el-form-item label="所属商户：">
            <el-select v-model="state.form.merchantId">
              <el-option v-for="(item, index) in listData.merchantList" :key="index" :label="item.merchantName" :value="item.merchantId"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="submit()" type="primary" size="mini">保&nbsp;&nbsp;存</el-button>
        <el-button @click="beforeClose()" size="mini">返&nbsp;&nbsp;回</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script>
import { ElDialog, ElButton, ElDivider, ElForm, ElFormItem, ElSelect, ElOption, ElInput, ElMessage } from "element-plus"
import { reactive, ref } from '@vue/reactivity'
import { computed, watch, nextTick } from '@vue/runtime-core'
import { useDict } from "@/hooks/useDict.js";
import { waterControllerDeviceAdd, waterControllerDeviceEdit } from "@/applications/eccard-iot/api";

import areaSelectTree from "@/components/tree/areaSelectTree.vue";
export default {
  components: {
    ElDialog,
    ElButton,
    ElDivider,
    ElForm,
    ElFormItem,
    ElSelect,
    ElOption,
    ElInput,
    "kade-area-select-tree": areaSelectTree,

  },
  props: {
    isShow: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: "add"
    },
    data: {
      type: String,
      default: ""
    },
    listData: {
      type: Object,
      default: null
    }
  },
  setup(props, context) {
    const formRef = ref(null)
    const deviceStaticList = useDict("SYS_DEVICE_STATICE")
    const deviceConnectTypeList = useDict("SYS_DEVICE_CONNECT_TYPE")
    const state = reactive({
      deviceVersion: [],
      form: {}
    })
    const title = computed(() => {
      return props.type == "add" ? "新增设备" : "编辑设备"
    })
    const rules = {
      deviceFactory: [
        {
          required: true,
          message: "请选择设备厂家",
          trigger: "change",
        },
      ],
      deviceModel: [
        {
          required: true,
          message: "请选择终端型号",
          trigger: "change",
        },
      ],
      areaId: [
        {
          required: true,
          message: "请选择所属区域",
          trigger: "change",
        },
      ],
      deviceNo: [
        {
          required: true,
          message: "请输入设备机号",
        },
        {
          pattern: /^[0-9]*$/,
          message: "请输入数字",
        },
      ],
      deviceName: [
        {
          required: true,
          message: "请输入设备名称",
        },
        {
          max: 20,
          message: "设备名称长度不能超过20字符",
        },
      ],
      deviceSn: [
        {
          required: true,
          message: "请输入设备SN号",
        },
        {
          pattern: /^[0-9]*$/,
          message: "请输入数字",
        }
      ],
      deviceStatus: [
        {
          required: true,
          message: "请选择设备使用状态",
          trigger: "change",
        },
      ],
    }
    watch(() => props.isShow, val => {
      if (val) {
        state.form = { ...props.data }
        nextTick(() => {
          formRef.value.clearValidate()
        })
      }
    })
    const submit = () => {
      formRef.value.validate(async (valid) => {
        if (valid) {
          state.loading = true
          let fn = props.data.id ? waterControllerDeviceEdit : waterControllerDeviceAdd
          try {
            let { code, message } = await fn(state.form)
            if (code === 0) {
              ElMessage.success(message)
              context.emit("close", true);

            }
            state.loading = false
          }
          catch {
            state.loading = false

          }
        } else {
          return false
        }
      })
    }
    const beforeClose = () => {
      context.emit("close", false)
    }
    return {
      formRef,
      rules,
      deviceStaticList,
      deviceConnectTypeList,
      state,
      title,
      submit,
      beforeClose
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-box {
  border: 1px solid #dcdfe6;
  border-radius: 5px;
  margin: 10px;

  .title {
    padding: 10px;
  }
}
.el-divider--horizontal {
  margin: 0;
}
.device-position-width {
  :deep(.el-input__inner) {
    width: 100% !important;
  }
}
</style>