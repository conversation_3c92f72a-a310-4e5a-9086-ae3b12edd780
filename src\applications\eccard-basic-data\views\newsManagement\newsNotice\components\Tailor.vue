<template>
  <!--  图片剪裁 -->
  <el-dialog
    title="图片剪裁"
    :model-value="isShow"
    :before-close="beforeClose"
    append-to-body
  >
    <div class="cropper-content">
      <div class="cropper" style="text-align: center">
        <div id="Box">
          <div class="box">
            <div class="box_1">
              <!-- <img
                ref="image"
              /> -->
            </div>
          </div>
          <h3>预览</h3>
          <!--  预览的图片  -->
          <div class="look">
            <div class="before"></div>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="beforeClose" size="mini">取消</el-button>
        <el-button type="primary" @click="submit()" size="mini">提交</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script>
import { ElDialog, ElButton } from "element-plus";
import { nextTick, onMounted, reactive, watch } from "vue";

import Cropper from "cropperjs";
import "cropperjs/dist/cropper.css";
export default {
  components: {
    ElDialog,
    ElButton,
  },
  props: {
    isShow: {
      types: Boolean,
      default: false,
    },
    file: {
      types: String,
      default: "",
    },
  },
  setup(props, context) {
    const state = reactive({
      myCropper: null,
      submitImg: "",
    });

    watch(
      () => [props.isShow, props.file],
      () => {
        if (props.isShow && props.file) {
          nextTick(() => {
            let box = document.querySelector(".box_1");
            let img = document.createElement("img");
            img.style.width="100%"
            img.src = props.file;
            box.appendChild(img);
            if (!state.myCropper) {
              state.myCropper = new Cropper(img, {
                viewMode: 0,
                dragMode: "none",
                // 是否显示图片后面的网格背景,一般默认为true
                background: true,
                // 进行图片预览的效果
                preview: ".before",
                // 设置裁剪区域占图片的大小 值为 0-1 默认 0.8 表示 80%的区域
                autoCropArea: 0.8,
                // 设置图片是否可以进行收缩功能
                zoomOnWheel: false,
                // 是否显示 + 箭头
                center: true,
                aspectRatio: 3 / 2,
              });
            }
          });
        }
      }
    );
    const submit = () => {
      state.submitImg = state.myCropper
        .getCroppedCanvas({
          imageSmoothingQuality: "high",
        })
        .toDataURL("image/jpeg");
      context.emit("close", state.submitImg);
      state.submitImg = "";
      let box = document.querySelector(".box_1");
      while (box.hasChildNodes()) {
        box.removeChild(box.lastChild);
      }
      state.myCropper=null
    };

    const beforeClose = () => {
      let box = document.querySelector(".box_1");
      while (box.hasChildNodes()) {
        box.removeChild(box.lastChild);
      }
      state.myCropper=null

      context.emit("close", false);
      state.submitImg = "";
    };

    onMounted(() => {});
    return {
      state,
      submit,
      beforeClose,
    };
  },
};
</script>
<style lang="scss" scoped>
#Box {
  border: 1px silver solid;
  padding: 20px;
  margin-top: 20px;
  border-radius: 5px;
  text-align: center;

  // height: 800px;
}

.look {
  display: flex;
  justify-content: center;
}

.before {
  width: 150px;
  height: 150px;

  overflow: hidden;
}
.box {
  // display: flex;
  column-gap: 6rem;
  // align-items: center;
  // justify-content: center;
  margin-top: 20px;
  div {
    // flex: 1;
    // height: 500px;
    width: 100%;
    background: #ccc;
    img {
      width: 100%;
      display: block;
    }

  }
}
</style>