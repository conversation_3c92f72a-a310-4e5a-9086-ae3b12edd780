<template>
   <kade-tab-wrap :tabs="tabs" style="margin-top: 20px" v-model="state.tab">
      <template #jbcs>
        <kade-basic-params />
      </template>
      <template #sdcs>
        <kade-time-params />
      </template>
      <template #klqxcs>
        <kade-card-auth-params />
      </template>
      <template #pjdycs>
        <kade-bill-print-params />
      </template>
      <template #yssz>
        <kade-style-set-params />
      </template>
    </kade-tab-wrap>
</template>
<script>
import { reactive, ref } from "@vue/reactivity";
import { useStore } from "vuex";
import {watch } from "@vue/runtime-core";
import basicParams from "./basicParams"
import timeParams from "./timeParams"
import cardAuthParams from "./cardAuthParams"
import billPrintParams from "./billPrintParams"
import styleSetParams from "./styleSetParams"
const tabs = [
  { name: "jbcs", label: "基本参数" },
  { name: "sdcs", label: "时段参数" },
  { name: "klqxcs", label: "卡类权限参数" },
  { name: "pjdycs", label: "票据打印参数" },
  { name: "yssz", label: "样式设置" },
];
export default {
  components: {
    "kade-basic-params":basicParams,
    "kade-time-params":timeParams,
    "kade-card-auth-params":cardAuthParams,
    "kade-bill-print-params":billPrintParams,
    "kade-style-set-params":styleSetParams,
  },
  setup() {
    const store = useStore();
    const formDom = ref(null);
    const state = reactive({
      tab: "jbcs",
      form: {},
    });
    watch(()=>store.state.deviceParameters[store.state.app.activeTab].isSetParams,val=>{
      if(val){
        state.tab="jbcs"
      }else{
        state.tab=""
      }
    })
    return {
      tabs,
      formDom,
      state,
    };
  },
};
</script>
<style lang="scss" scoped>
</style>