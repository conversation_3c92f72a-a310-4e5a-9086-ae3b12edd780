<template>
  <div class="box ScaleBox" ref="ScaleBox">
    <kade-header-box title="男生宿舍一栋AI监控大屏" />
    <div class="main">
      <el-row :gutter="15">
        <el-col :span="18">
          <el-row :gutter="15">
            <el-col class="margin-bt" :span="6" v-for="(item, index) in 4" :key="index">
              <div class="person-box">
                <div class="box-title">入住人数</div>
                <div class="box-num">1295</div>
              </div>
            </el-col>
            <el-col class="margin-bt" :span="12">
              <img src="http://tva3.sinaimg.cn/large/bf976b12gy1g51fau0uvdg208c0784a1.gif" alt="">
            </el-col>
            <el-col class="margin-bt" :span="12">
              <img src="http://tva3.sinaimg.cn/large/bf976b12gy1g51fau0uvdg208c0784a1.gif" alt="">
            </el-col>
            <el-col :span="12">
              <img src="http://tva3.sinaimg.cn/large/bf976b12gy1g51fau0uvdg208c0784a1.gif" alt="">
            </el-col>
            <el-col :span="12">
              <img src="http://tva3.sinaimg.cn/large/bf976b12gy1g51fau0uvdg208c0784a1.gif" alt="">
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="6">
          <kade-border-box style="height:100%;padding: 10px 0;">
            <kade-contrast-list />
          </kade-border-box>
        </el-col>
      </el-row>
    </div>
  </div>
</template>
<script>
import { reactive } from "vue"
import { ElCol, ElRow, } from "element-plus"
import headerBox from "../components/headerBox.vue"
import borderBox from "../components/borderBox.vue"
import contrastList from "./components/contrastList.vue"

export default {
  components: {
    ElCol, ElRow,
    "kade-header-box": headerBox,
    "kade-border-box": borderBox,
    "kade-contrast-list": contrastList,

  },
  setup() {
    const state = reactive({

    })

    return {
      state
    }
  }
}
</script>
<style scoped lang="scss">
.box {
  background: #001034;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow-y: auto;

  .main {
    flex: 1;
    padding: 0 20px 20px;
  }

  .margin-bt {
    margin-bottom: 15px;
  }

  .person-box {
    color: #fff;
    padding: 10px;
    background-color: rgb(51, 153, 255);

    .box-title {
      font-weight: 400;
      font-size: 14px;
    }

    .box-num {
      margin: 20px 0;
      font-weight: 700;
      font-size: 72px;
      color: rgb(255, 255, 255);
      text-align: center;
    }


  }

  img {
    box-sizing: border-box;
    width: 100%;
    height: 369px;
    border: 1px solid #00bfbf;

  }

  .border-box {
    border: 1px solid #00bfbf;
    box-sizing: border-box;
    margin-bottom: 15px;
  }

}
</style>