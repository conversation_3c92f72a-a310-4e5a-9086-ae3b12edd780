<template>
  <kade-route-card>
    <template #header>
      <div class="header">
        <span class="header-title">智能门锁监控预警</span>
        <span class="header-right">单位：把</span>
      </div>
    </template>
    <div id="lockMonitorCharts" class="chartline"></div>
  </kade-route-card>
</template>
<script>
import * as echarts from "echarts";
import { onMounted } from "vue";
export default {
  setup() {
    const echartInit = () => {
      var chartDom = document.getElementById('lockMonitorCharts');
      var myChart = echarts.init(chartDom);
      var option;

      option = {
        xAxis: {
          type: 'category',
          data: ['门锁不在线', '门锁电量不足']
        },
        grid: {
          top: 20,
          bottom: 20,
          left: 40,
          right: 0
        },
        yAxis: [
          {
            show: true,
            type: "value",
            splitLine: {
              show: true,
              lineStyle: {
                type: "solid",
              },
            },
            axisLabel: {
              margin: 20,

            },
            minInterval :1
          }
        ],
        series: [
          {
            data: [120, 200, ],
            type: 'bar',
            barWidth: 50,
            label: {
              show: true,
              distance: 0,
              formatter: '{c}'
            },
          }
        ]
      };

      option && myChart.setOption(option);
    };
    onMounted(() => {
      echartInit();
    });
    return {
      echartInit,
    };
  }
}
</script>
<style scoped lang="scss">
.chartline {
  height: 260px;
  width: 100%;
}
</style>