<template>
  <div class="income-detail border-box" v-loading="listLoading">
    <div class="padding-form-box">
      <el-form inline size="small" label-width="100px">
        <el-form-item label="项目名称:">
          <el-input placeholder="关键字搜索" v-model="state.form.projectName" :maxlength="20"></el-input>
        </el-form-item>
        <el-form-item label="收费类型:">
          <el-select clearable v-model="state.form.unifiedChangeType" placeholder="请选择">
            <el-option v-for="(item, index) in ChargeTypeList" :key="index" :label="item.uctName" :value="item.uctId">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="选择日期:">
          <el-col :span="24">
            <el-date-picker v-model="state.requestDate" type="datetimerange" range-separator="~"
              start-placeholder="请选择日期" end-placeholder="请选择日期" unlink-panels @change="changeDate">
            </el-date-picker>
          </el-col>
        </el-form-item>
        <el-button @click="search()" size="small" type="primary" icon="el-icon-search">查询</el-button>
        <el-button icon="el-icon-refresh-right" @click="reset()" size="small">重置</el-button>
      </el-form>
    </div>
    <el-table style="width: 100%" :data="state.dataList" v-loading="state.loading" @row-click="rowClick" fit
      highlight-current-row border stripe>
      <el-table-column width="150" label="项目名称" prop="projectName" align="center"></el-table-column>
      <el-table-column width="150" label="类型名称" prop="unifiedChangeTypeName" align="center"></el-table-column>
      <el-table-column width="100" prop="auditStatus" label="审核状态" align="center">
        <template #default="scope">
          <div>
            {{ dictionaryFilter(scope.row.auditStatus) }}
          </div>
        </template>
      </el-table-column>
      <el-table-column width="150" label="收费总额（元）" prop="totalAmount" align="center"></el-table-column>
      <el-table-column width="150" label="收费总人数" prop="totalPerson" align="center"></el-table-column>
      <el-table-column label="人员清单" align="center">
        <template #default="scope">
          <el-button type="text" @click="handleBtnClick(scope.row)" size="mini">查看清单</el-button>
        </template>
      </el-table-column>
      <el-table-column label="导入人员" prop="createUserName" align="center"></el-table-column>
      <el-table-column label="审核人员" prop="auditPerson" align="center"></el-table-column>
      <el-table-column width="160" label="未通过原因" prop="auditRemark" align="center" show-overflow-tooltip>
      </el-table-column>
      <el-table-column width="160" label="创建时间" prop="createTime" align="center">
        <template #default="scope">
          {{ timeStr(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column width="160" label="审核时间" prop="auditTime" align="center">
        <template #default="scope">
          {{ timeStr(scope.row.auditTime) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template #default="scope">
          <el-button type="text" @click="listDetailsClick(scope.row)" size="mini">查看详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="pagination">
      <el-pagination background :current-page="state.form.currentPage" :page-size="state.form.pageSize"
        layout="total, sizes, prev, pager, next, jumper" :page-sizes="[6, 10, 20, 50, 100]" :total="state.total"
        @current-change="handlePageChange" @size-change="handleSizeChange">
      </el-pagination>
    </div>
  </div>
</template>
<script>
import { onMounted, reactive, computed, watch } from "vue";
import { useStore } from "vuex";
import {
  ElCol,
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElDatePicker,
  ElSelect,
  ElOption,
  ElTable,
  ElTableColumn,
  ElPagination,
} from "element-plus";

import { timeStr } from "@/utils/date.js";
import { getUnifiedChargeList } from "@/applications/eccard-finance/api";
export default {
  components: {
    "el-button": ElButton,
    "el-input": ElInput,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    "el-date-picker": ElDatePicker,
    "el-col": ElCol,
    "el-select": ElSelect,
    "el-option": ElOption,
    "el-table": ElTable,
    "el-table-column": ElTableColumn,
    "el-pagination": ElPagination,
  },
  setup() {
    const store = useStore();
    const state = reactive({
      loading: false,
      form: {
        pageNum: 1,
        pageSize: 10,
        auditStatus: "SYS_AUDIT_FAIL",
      },
      requestDate: [],
      total: 0
    });
    const ChargeTypeList = computed(() => {
      return store.state.chargeData.ChargeTypeList
    })

    watch(() => store.state.chargeData.isReq, (val) => {
      if (val) {
        getList()
        store.commit("chargeData/updateState", {
          key: "isReq",
          payload: false,
        });
      }
    })

    const getList = async () => {
      state.loading = true
      let { data: { list, total } } = await getUnifiedChargeList(state.form)
      state.dataList = list
      state.total = total
      state.loading = false
    }

    const changeDate = (val) => {
      if (val && val.length) {
        state.form.startDate = timeStr(val[0])
        state.form.endDate = timeStr(val[1])
      } else {
        delete state.form.startDate
        delete state.form.endDate
      }

    }

    const rowClick = (row) => {
      console.log(row, store);
    }

    const search = () => {
      getList()
    }
    const reset = () => {
      state.form = {
        pageNum: 1,
        pageSize: 10
      }
      state.requestDate = []
    }

    const handleBtnClick = (row) => {
      store.commit("chargeData/updateState", {
        key: "selectRow",
        payload: row,
      });
      store.commit("chargeData/updateState", {
        key: "isPerson",
        payload: true,
      });
    }

    const listDetailsClick = row => {
      store.commit("chargeData/updateState", {
        key: "selectRow",
        payload: row,
      });
      store.commit("chargeData/updateState", {
        key: "isDetails",
        payload: true,
      });
    }


    const handlePageChange = (val) => {
      state.form.pageNum = val;
      getList()
    };
    const handleSizeChange = (val) => {
      state.form.pageNum = 1;
      state.form.pageSize = val;
      getList()
    };

    onMounted(() => {
      getList()
    });
    return {
      state,
      timeStr,
      ChargeTypeList,
      changeDate,
      rowClick,
      search,
      reset,
      handleBtnClick,
      listDetailsClick,
      handlePageChange,
      handleSizeChange,
    };
  },
};
</script>
<style lang="scss" scoped>
.income-detail {
  min-height: 680px;
}
</style>
