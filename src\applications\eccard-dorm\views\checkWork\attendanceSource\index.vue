<template>
  <div class="attendance-source padding-box">
    <kade-table-wrap title="考勤数据源列表">
        <el-table border :data="state.form" width="100%">
          <el-table-column prop="id" label="数据源ID" align="center"></el-table-column>
          <el-table-column prop="name" label="数据源名称" align="center"></el-table-column>
          <el-table-column prop="status" label="启用状态" align="center">
            <template #default="scope">
              <el-switch v-model="scope.row.status" />
            </template>
          </el-table-column>
          <el-table-column prop="remarks" label="备注" align="center" width="600px"></el-table-column>
      </el-table>
    </kade-table-wrap>
    <div class="text">
      备注：系统会根据启用的数据源获取考勤签到数据，用于宿舍考勤分析。
    </div>
  </div>
</template>

<script>
import { ElTable,ElTableColumn,ElSwitch, } from "element-plus"
import { reactive } from "@vue/reactivity";
export default {
  components: {
      ElTable,
      ElTableColumn,
      ElSwitch,
  },
  setup() {
    const state = reactive({
      form:[{id:'1'}]
    });
    return {
      state,
    };
  },
};
</script>

<style lang="scss" scoped>
.kade-table-wrap{
  padding: 0;
}
.text{
   color: rgb(199, 8, 8);
    margin-top: 15px;
    font-size: 12px;
}
</style>