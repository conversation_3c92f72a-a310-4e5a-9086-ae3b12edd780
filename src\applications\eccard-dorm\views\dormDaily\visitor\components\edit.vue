<template>
  <el-dialog :model-value="modelValue" :title="title" width="700px" :before-close="handleClose">
    <el-form :model="state.form" inline label-width="110px" size="mini" ref="formRef"
      :rules="type !== 'details' && rules">
      <div class="visitor-box">
        <div class="info-title">访客信息</div>
        <el-divider></el-divider>
        <el-form-item label="访客类型：" prop="visitorType">
          <el-input v-if="type == 'details'" :model-value="dictionaryFilter(state.form.visitorType)" readonly>
          </el-input>
          <el-select v-else v-model="state.form.visitorType" placeholder="请选择">
            <el-option v-for="(item, index) in typeList" :key="index" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="访客姓名" prop="visitorName">
          <el-input v-if="type == 'details'" :model-value="state.form.visitorName" readonly></el-input>
          <el-input v-else v-model="state.form.visitorName" :readonly="state.form.visitorType == 'INSIDER'"
            @click="handleSelectPerson" :placeholder="
              state.form.visitorType == 'INSIDER'
                ? '请选择人员'
                : '请输入人员姓名'
            "></el-input>
          <select-person-dialog :isMultiple="false" :isShow="state.isName" @close="closePersonSelect" />
        </el-form-item>
        <el-form-item label="手机号码：" prop="visitorTel">
          <el-input v-if="type == 'details'" :model-value="state.form.visitorTel" readonly></el-input>
          <el-input v-else v-model="state.form.visitorTel" placeholder="请输入"></el-input>
        </el-form-item>

        <el-form-item label="身份证号：" prop="visitorIdcard">
          <el-input v-if="type == 'details'" :model-value="state.form.visitorIdcard" readonly></el-input>
          <el-input v-else v-model="state.form.visitorIdcard" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="访问事由：" prop="visitorReason">
          <el-input v-if="type == 'details'" type="textarea" :row="3" :model-value="state.form.visitorReason" readonly>
          </el-input>
          <el-input v-else v-model="state.form.visitorReason" placeholder="请输入" type="textarea" :rows="3"></el-input>
        </el-form-item>
      </div>
      <div class="interview-box">
        <div class="info-title">被访人员信息</div>
        <el-divider></el-divider>
        <el-form-item v-if="type == 'details'" label="所属区域：">
          <el-input :model-value="state.form.areaName" readonly></el-input>
        </el-form-item>
        <el-form-item v-if="type == 'details'" label="楼栋：">
          <el-input :model-value="state.form.buildName" readonly></el-input>
        </el-form-item>
        <el-form-item v-if="type == 'details'" label="单元：">
          <el-input :model-value="state.form.unitName" readonly></el-input>
        </el-form-item>
        <el-form-item v-if="type == 'details'" label="楼层：">
          <el-input :model-value="state.form.floorName" readonly></el-input>
        </el-form-item>
        <el-form-item v-if="type == 'details'" label="房间号：">
          <el-input :model-value="state.form.roomName" readonly></el-input>
        </el-form-item>
        <kade-linkage-select v-else :isEdit="modelValue ? true : false" :data="linkageData" :value="state.form"
          @change="linkageChange" />
        <el-form-item label="被访者：" prop="intervieweeId">
          <el-input v-if="type == 'details'" :model-value="state.form.intervieweeName" readonly></el-input>
          <el-select v-else v-model="state.form.intervieweeId" placeholder="请选择" clearable @change="intervieweeIdChange">
            <el-option v-for="(item, index) in state.interviewList" :key="index" :label="item.userName"
              :value="item.userId"></el-option>
          </el-select>
        </el-form-item>
      </div>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose" size="mini">取消</el-button>
        <el-button type="primary" @click="submit" size="mini">确认</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { reactive, computed, ref, watch, nextTick } from 'vue'
import { useDict } from '@/hooks/useDict'
import { editVisitor, addVisitor, getRoomStayInfo } from "@/applications/eccard-dorm/api.js"
import linkageSelect from '@/applications/eccard-dorm/components/linkageSelect'
import selectPersonDialog from "@/components/table/selectPersonDialog.vue";
import { ElDialog, ElButton, ElDivider, ElForm, ElFormItem, ElInput, ElSelect, ElOption, ElMessage, } from "element-plus"
const linkageData = {
  area: { label: '所属区域：', valueKey: 'areaId', key: 'id' },
  building: { label: '楼栋：', valueKey: 'buildId' },
  unit: { label: '单元：', valueKey: 'unitNum' },
  floor: { label: '楼层：', valueKey: 'floorNum' },
  room: { label: '房间号：', valueKey: 'roomId' }
}
const rules = {
  visitorType: [{ required: true, message: "请选择用户类型", trigger: "blur" }],
  visitorName: [{ required: true, message: "请输入或选择", trigger: "change" }, { max: 20, message: '姓名不能超过20字符' }],
  visitorTel: [{ required: true, message: "请输入手机号", trigger: "blur" }, { max: 20, message: '手机号不能超过20字符' }],
  visitorIdcard: [{ required: true, message: "请输入身份证号", trigger: "blur" }, { max: 20, message: '身份证号不能超过20字符' }],
  visitorReason: [{ required: true, message: "请输入访问事由", trigger: "blur" }, { max: 200, message: '访问是由不能超过200字符' }],
  intervieweeId: [{ required: true, message: "请输入或选择", trigger: "blur" }]
}
export default {
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: ''
    },
    rowData: {
      type: String,
      default: ""
    }
  },
  components: {
    ElDialog, ElButton, ElDivider, ElForm, ElFormItem, ElInput, ElSelect, ElOption,
    "kade-linkage-select": linkageSelect,
    "select-person-dialog": selectPersonDialog,
  },
  setup(props, context) {
    const formRef = ref(null)
    const typeList = useDict('DORM_VISTOR_TYPE') //访客类型
    const state = reactive({
      form: {},
      isName: false,
      isTel: false,
      interviewList: []
    })
    //获取房间人员
    const queryPerson = async (roomId) => {
      let { data } = await getRoomStayInfo({ roomId })
      state.interviewList = data
    }
    const closePersonSelect = (val) => {
      if (val) {
        console.log(val);
        state.form.visitorName = val.userName
        state.form.visitorTel = val.userTel
        state.form.visitorIdcard = val.userIdNo
      }
      state.isName = false
    }
    const handleClose = () => {
      context.emit('update:modelValue', false)
    }
    const linkageChange = (val) => {
      state.form = { ...state.form, ...val }
      state.form.intervieweeId=""
      state.interviewList=[]
      if (val.roomId) {
        queryPerson(val.roomId)
      }
    }
    const title = computed(() => {
      if (props.type == 'add') {
        return '新增访客登记'
      } else if (props.type == 'edit') {
        return '编辑访客登记'
      } else {
        return '访客登记详情'
      }
    })
    watch(() => props.modelValue, val => {
      if (val) {
        if (props.type == 'add') {
          state.form = {}
        } else {
          let { id, intervieweeId, visitorId, visitorType, visitorName, visitorTel, visitorIdcard, visitorReason, intervieweeName, areaId, areaName, buildId, buildName, unitNum, unitName, floorNum, floorName, roomId, roomName, } = { ...props.rowData }
          state.form = { id, intervieweeId, visitorId, visitorType, visitorName, visitorTel, visitorIdcard, visitorReason, intervieweeName, areaId, areaName, buildId, buildName, unitNum, unitName, floorNum, floorName, roomId, roomName, }
          queryPerson(roomId)
        }
        nextTick(() => {
          formRef.value.clearValidate()
        })
      }
    })
    const handleSelectPerson = () => {
      if (state.form.visitorType === 'INSIDER') {
        state.isName = true
      }
    }
    const submit = () => {
      formRef.value.validate(async (valid) => {
        if (valid) {
          let param = { ...state.form }
          let fn = props.type == 'add' ? addVisitor : editVisitor
          let { code, message } = await fn(param)
          if (code === 0) {
            ElMessage.success(message)
            context.emit("update:modelValue", true)
          }
        }
      })
    }
    return {
      state,
      title,
      rules,
      submit,
      typeList,
      formRef,
      queryPerson,
      handleSelectPerson,
      handleClose,
      linkageData,
      linkageChange,
      closePersonSelect
    }
  }
}
</script>

<style lang="scss" scoped>
.visitor-box {
  border: 1px solid #eeeeee;
  border-radius: 4px;
}

.interview-box {
  border: 1px solid #eeeeee;
  margin-top: 15px;
  border-radius: 4px;
}

.info-title {
  margin: 10px;
}
</style>