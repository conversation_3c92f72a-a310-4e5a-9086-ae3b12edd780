<template>
  <kade-modal v-bind="attrs" :modelValue="modelValue" @update:modelValue="update">
    <el-form v-loading="state.dataLoading" ref="formRef" label-width="170px" :rules="rules" :model="state.model" size="small">
      <el-form-item label="上级租户" prop="tenantParentId" v-if="!(state.model.tenantId === 1)">
        <el-select clearable v-model="state.model.tenantParentId" placeholder="请选择" popper-append-to-bod>
          <el-option v-for="item in topTenants" :key="item.tenantId" :label="item.tenantName" :value="item.tenantId"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="租户名称" prop="tenantName">
        <el-input placeholder="请输入" v-model="state.model.tenantName" />
      </el-form-item>
      <el-form-item label="手机号码" prop="tenantMobile">
        <div :class="['tel-wrap', { 'is-edit': !!id }]">
          <el-input placeholder="请输入" style="width: 150px" v-model="state.model.tenantMobile" />
          <template v-if="!!id">
            <el-button :disabled="state.sending" :loading="state.sendLoading" @click="handleReplaceTel" type="primary">{{ replaceText }}</el-button>
            <el-input v-if="state.isReplaceTel" v-model="state.mobileCode" placeholder="验证码"></el-input>
          </template>
        </div>
      </el-form-item>
      <el-form-item label="邮箱地址" prop="tenantEmail">
        <el-input placeholder="请输入" v-model="state.model.tenantEmail" />
      </el-form-item>
      <el-form-item label="租户类型" prop="tenantType">
        <el-select clearable v-model="state.model.tenantType" placeholder="请选择" popper-append-to-bod>
          <el-option v-for="item in tenantTypes" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="租户类别" prop="tenantCategory">
        <el-select clearable v-model="state.model.tenantCategory" placeholder="请选择" popper-append-to-bod>
          <el-option v-for="item in categorys" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="租户性质" prop="tenantNature">
        <el-select clearable v-model="state.model.tenantNature" placeholder="请选择" popper-append-to-bod>
          <el-option v-for="item in natures" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="租户授权URL" prop="tenantAuthUrl">
        <el-input placeholder="请输入" v-model="state.model.tenantAuthUrl" />
      </el-form-item>
      <el-form-item label="授权终端数" prop="tenantAuthDevice">
        <el-input-number v-model="state.model.tenantAuthDevice" :min="0" :step="1"></el-input-number>
      </el-form-item>
      <el-form-item label="授权用户数" prop="tenantAuthUser">
        <el-input-number v-model="state.model.tenantAuthUser" :min="0" :step="1"></el-input-number>
      </el-form-item>

      <el-form-item label="AppID(小程序ID)" prop="tenantEappid">
        <el-input placeholder="请输入" v-model="state.model.tenantEappid" />
      </el-form-item>
      <el-form-item label="AppSecret(小程序密钥)" prop="tenantEappsecret">
        <el-input placeholder="请输入" v-model="state.model.tenantEappsecret" />
      </el-form-item>

      <el-form-item label="机构有效期" prop="tenantExpirationTime">
        <el-date-picker
          v-model="state.actualTimes"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="handleDateChange"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="机构联系地址" prop="tenantAddress">
        <el-input show-word-limit maxlength="100" v-model="state.model.tenantAddress" placeholder="请输入" type="textarea" :rows="3"></el-input>
      </el-form-item>
      <el-form-item label="操作员姓名" prop="tenantOperName">
        <el-input placeholder="请输入" v-model="state.model.tenantOperName" />
      </el-form-item>
      <el-form-item label="操作员联系地址" prop="tenantOperAddress">
        <el-input show-word-limit maxlength="100" v-model="state.model.tenantOperAddress" placeholder="请输入" type="textarea" :rows="3"></el-input>
      </el-form-item>
      <el-form-item label="备注" prop="tenantRemark">
        <el-input show-word-limit maxlength="140" v-model="state.model.tenantRemark" placeholder="请输入" type="textarea" :rows="3"></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button size="small" @click="cancel">取消</el-button>
      <el-button size="small" :loading="state.loading" @click="submit" type="primary">提交</el-button>
    </template>
  </kade-modal>
</template>
<script>
import Modal from "@/components/modal";
import { computed, reactive, ref, watch } from "vue";
import { useStore } from "vuex";
import moment from "moment";
import {
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElMessage,
  ElOption,
  ElInputNumber,
  ElSelect,
  ElDatePicker,
} from "element-plus";
import {
  getTenantDetails,
  addTenant,
  editTenant,
  sendSms,
} from "@/applications/eccard-ops/api";
import { useDict } from "@/hooks/useDict";
import { validateMobile, validateEmail } from "@/utils/validate";
const getDefaultModel = () => ({
  tenantActivationTime: "",
  tenantAddress: "",
  tenantAuthDevice: 0,
  tenantAuthUser: 0,
  tenantCategory: "",
  tenantEmail: "",
  tenantExpirationTime: "",
  tenantMobile: "",
  tenantName: "",
  tenantNature: "",
  tenantOperAddress: "",
  tenantOperName: "",
  tenantParentId: "",
  tenantRemark: "",
  tenantType: "",
});
export default {
  emits: ["update:modelValue", "change"],
  props: {
    title: {
      type: String,
      default: "",
    },
    modelValue: {
      type: Boolean,
      default: false,
    },
    id: {
      type: [String, Number],
      default: null,
    },
  },
  components: {
    "kade-modal": Modal,
    "el-button": ElButton,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    "el-input": ElInput,
    "el-select": ElSelect,
    "el-option": ElOption,
    "el-input-number": ElInputNumber,
    "el-date-picker": ElDatePicker,
  },
  setup(props, context) {
    const formRef = ref(null);
    const timer = ref(null);
    const store = useStore();
    const tenantTypes = useDict("TENANT_TYPE");
    const categorys = useDict("TENANT_CATEGORY");
    const natures = useDict("TENANT_NATURE");
    const state = reactive({
      model: getDefaultModel(),
      actualTimes: "",
      dataLoading: false,
      loading: false,
      mobileCode: "",
      sending: false,
      times: 60,
      sendLoading: false,
      isReplaceTel: false,
    });

    watch(
      () => props.modelValue,
      (val) => {
        if (val) {
          state.sending = false;
        }
      } 
    );

    const rules = {
      tenantName: [
        { required: true, message: "请输入租户名称" },
        { max: 20, message: "租户名称不能超过20字符" },
      ],
      tenantParentId: [
        { required: true, message: "请选择上级租户", trigger: "change",},
      ],
      tenantEmail: [
        { required: true, message: "请输入邮箱",},
        { validator: validateEmail,},
        { max: 20,message: "邮箱地址长度不得超过20字符",},
      ],

      tenantAuthUrl: [
        {required: true, message: "请输入租户授权URL",},
      ],
      tenantEappid: [
        { required: true, message: "请输入小程序appid",},
      ],
      tenantEappsecret: [
        { required: true, message: "请输入小程序密钥",},
      ],
      tenantMobile: [
        { required: true, message: "请输入联系电话", },
        { validator: validateMobile,},
      ],
      tenantType: [
        { required: true, trigger: "change", message: "请选择租户类型",},
      ],
      tenantNature: [
        { required: true, trigger: "change", message: "请选择租户性质",},
      ],
      tenantCategory: [
        { required: true, trigger: "change", message: "请选择租户分类",},
      ],
      tenantExpirationTime: [
        { required: true, trigger: "change", message: "请选择有效期",},
      ],
      tenantOperName: [
        { max: 20, message: "操作员姓名不得超过20字符",},
      ],
    };
    const cancel = () => {
      formRef.value?.resetFields();
      context.emit("update:modelValue", false);
    };
    const submit = () => {
      formRef.value?.validate(async (valid) => {
        if (valid) {
          if (state.isReplaceTel && !state.mobileCode) {
            ElMessage.warning("请输入验证码");
            throw new Error("请输入验证码");
          }
          try {
            state.loading = true;
            const data = {
              ...state.model,
              tenantActivationTime: moment(state.model.tenantActivationTime).format("YYYY-MM-DD HH:mm:ss"),
              tenantExpirationTime: moment(state.model.tenantExpirationTime).format("YYYY-MM-DD HH:mm:ss"),
            };
            if (state.isReplaceTel) {
              data.mobileCode = state.mobileCode;
            }
            const fn = state.model.tenantId ? editTenant : addTenant;
            const { message,data:{tenantId},code } = await fn(data);
            if(code===0&&!state.model.tenantId){
              context.emit("addClose",tenantId);
            }
            ElMessage.success(message);
            context.emit("change");
            context.emit("update:modelValue", false);
          } catch (e) {
            throw new Error(e.message);
          } finally {
            state.loading = false;
          }
        }
      });
    };
    const attrs = computed(() => {
      // eslint-disable-next-line no-unused-vars
      const { modelValue, id, ...attrs } = props;
      return attrs;
    });
    const topTenants = computed(() => store.state.app.topTenants);
    const update = (v) => {
      context.emit("update:modelValue", v);
    };
    const handleDateChange = ([start, end]) => {
      state.model.tenantExpirationTime = end;
      state.model.tenantActivationTime = start;
    };
    const loadInfo = async () => {
      try {
        state.dataLoading = true;
        const { data } = await getTenantDetails(props.id);
        state.actualTimes = [
          moment(data.tenantActivationTime).toDate(),
          moment(data.tenantExpirationTime).toDate(),
        ];
        const temp = Object.assign(getDefaultModel(), data);
        state.model = {
          ...temp,
          tenantParentId: Number(temp.tenantParentId),
          tenantAuthUser: parseInt(temp.tenantAuthUser, 10),
          tenantAuthDevice: parseInt(temp.tenantAuthDevice, 10),
        };
      } catch (e) {
        throw new Error(e.message);
      } finally {
        state.dataLoading = false;
      }
    };
    const reduceTime = () => {
      if (timer.value) {
        clearTimeout(timer.value);
        timer.value = null;
      }
      timer.value = setTimeout(() => {
        state.times -= 1;
        if (state.times > 0) {
          reduceTime();
        } else {
          state.sending = false;
        }
      }, 1000);
    };
    const handleReplaceTel = async () => {
      if (!state.isReplaceTel) {
        state.isReplaceTel = true;
      } else {
        formRef.value.validateField("tenantMobile", async (msg) => {
          if (!msg) {
            try {
              state.sendLoading = true;
              await sendSms({ sendMobile: state.model.tenantMobile });
              state.sending = true;
              state.times = 60;
              reduceTime();
            } catch (e) {
              throw new Error(e.message);
            } finally {
              state.sendLoading = false;
            }
          }
        });
      }
    };
    const replaceText = computed(() => {
      if (!state.isReplaceTel) {
        return "更换手机号";
      } else {
        if (state.sending) {
          return `${state.times}s`;
        } else {
          return "发送验证码";
        }
      }
    });
    watch(
      () => props.modelValue,
      (v) => {
        if (v) {
          state.isReplaceTel = false;
          if (props.id) {
            loadInfo();
          } else {
            state.model = getDefaultModel();
          }
          state.actualTimes = "";
        }
      }
    );
    return {
      attrs,
      update,
      formRef,
      cancel,
      submit,
      rules,
      labelWidth: THEMEVARS.formLabelWidth,
      state,
      tenantTypes,
      categorys,
      natures,
      handleDateChange,
      topTenants,
      handleReplaceTel,
      replaceText,
    };
  },
};
</script>
<style lang="scss" scoped>
.tel-wrap {
  display: flex;
  justify-content: flex-start;
  &.is-edit {
    .el-input {
      flex-basis: 150px;
    }
    .el-button {
      margin: 0 10px;
    }
  }
}
</style>
