
<template>
  <div class="cashRecharge" v-loading="state.loading">
    <kade-route-card style="height: auto">
      <kade-table-filter @search="search()" @reset="reset()">
        <el-form inline size="mini" label-width="100px">
          <el-form-item label="操作员:">
            <el-select clearable filterable multiple collapse-tags v-model="state.form.operatorList" placeholder="请选择">
              <el-option v-for="(item, index) in state.systemUserList" :key="index" :label="item.userName" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="统计时间:">
            <el-date-picker v-model="state.requestDate" type="datetimerange" unlink-panels :default-time="state.defaultTime" range-separator="~" start-placeholder="请选择日期" end-placeholder="请选择日期" @change="changeDate">
            </el-date-picker>
          </el-form-item>
          <!-- <el-form-item label="按入账时间:">
            <el-checkbox v-model="state.timeType"></el-checkbox>
          </el-form-item> -->
        </el-form>
      </kade-table-filter>
      <kade-table-wrap title="出纳交款报表">
        <template #extra>
          <el-button @click="exportClick()" icon="el-icon-daoru" size="mini" class="btn-blue">导出查询明细</el-button>
          <el-button @click="printClick()" icon="el-icon-printer" size="mini" class="btn-purple">打印</el-button>
        </template>
        <el-table style="width: 100%" :data="state.detailList" ref="multipleTable" height="55vh" highlight-current-row border stripe>
          <el-table-column v-for="(item,index) in column" :key="index" :width="item.width" :label="item.label" :prop="item.prop" align="center" show-overflow-tooltip>
            <template v-if="item.render" #default="scope">
              {{item.render(scope.row[item.prop])}}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150" align="center">
            <template #default="scope">
              <el-button size="mini" type='text' class="green" v-if="!(scope.row.type === 1)" @click="detailClick(scope.row)">查看交款明细</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination">
          <el-pagination background :current-page="state.form.pageNum" :page-size="state.form.pageSize" layout="total, sizes, prev, pager, next, jumper" :page-sizes="[6, 10, 20, 50, 100]" :total="state.total" @current-change="handlePageChange" @size-change="handleSizeChange">
          </el-pagination>
        </div>
      </kade-table-wrap>
    </kade-route-card>
    <kade-summary-details v-model:modelValue="state.isShow" :data="state.rowData" :params="state.detailsParams" @update:modelValue="state.isShow=$event" />
  </div>
</template>
<script>
import {
  ElButton,
  ElForm,
  ElFormItem,
  ElSelect,
  ElOption,
  ElTable,
  ElTableColumn,
  ElPagination,
  ElDatePicker,
} from "element-plus";
import { timeStr } from "@/utils/date.js";
import { downloadXlsx, print } from "@/utils";
import { requestDate } from "@/utils/reqDefaultDate"
import { reactive } from "@vue/reactivity";
import {
  getSystemUser,
  cashierSummary,
  cashierSummaryDetail,
  cashierSummaryExport,
  cashierSummaryPrint
} from "@/applications/eccard-finance/api";
import { onMounted } from "@vue/runtime-core";
import details from "./components/details.vue";
const column = [
  { label: "操作员", prop: "operatorName", width: "" },
  {
    label: "收入金额", prop: "incomeAmount", width: "", render(val) {
      return Number(val).toFixed(2)
    }
  },
  {
    label: "支出金额", prop: "expendAmount", width: "", render(val) {
      return Number(val).toFixed(2)
    }
  },
  {
    label: "应缴金额", prop: "payableAmount", width: "", render(val) {
      return Number(val).toFixed(2)
    }
  },
];

export default {
  components: {
    "el-button": ElButton,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    "el-select": ElSelect,
    "el-option": ElOption,
    "el-table": ElTable,
    "el-table-column": ElTableColumn,
    "el-pagination": ElPagination,
    ElDatePicker,
    "kade-summary-details": details,
  },
  setup() {
    const state = reactive({
      loading: false,
      isShow: false,
      timeType: true,
      form: {
        pageNum: 1,
        pageSize: 6,
        startDateTime: requestDate()[0],
        endDateTime: requestDate()[1],
      },
      total: 0,
      requestDate: requestDate(),
      systemUserList: [],
      defaultTime: [
        new Date(new Date(new Date().toLocaleDateString()).getTime()),
        new Date(
          new Date(new Date().toLocaleDateString()).getTime() +
          24 * 60 * 60 * 1000 -
          1
        ),
      ],
      rowData: {},
      detailsParams: {},
    });
    //获取操作员
    const querySystemUser = () => {
      getSystemUser().then((res) => {
        state.systemUserList = res.data;
      });
    };
    const getList = async () => {
      state.loading = true;
      let params = { ...state.form }
      if (params.operatorList && params.operatorList.length) {
        params.operatorList = params.operatorList.join(",")
      } else {
        delete params.operatorList
      }
      try {
        let { code, data } = await cashierSummary(params);
        if (code === 0) {
          let {
            pageInfo: { total, list },
            generate: { expendAmount, incomeAmount, payableAmount },
          } = data;
          state.detailList = list;
          if (state.detailList.length) {
            state.detailList.push({
              expendAmount,
              incomeAmount,
              payableAmount,
              operatorName: "合计",
              type: 1
            });
          }
          state.total = total;
          state.loading = false;
        }
      }
      catch {
        state.loading = false;
      }
    };
    const changeDate = (val) => {
      if (val && val.length) {
        state.form.startDateTime = timeStr(val[0]);
        state.form.endDateTime = timeStr(val[1]);
      } else {
        console.log(123);
        state.form.startDateTime = requestDate()[0];
        state.form.endDateTime = requestDate()[1];
        console.log(state.form);
      }
    };
    const detailClick = async ({ expendAmount, incomeAmount, operatorId, payableAmount }) => {
      let params = {
        expendAmount, incomeAmount, operatorId, payableAmount, startDateTime: state.form.startDateTime, endDateTime: state.form.endDateTime
      }
      state.detailsParams = params
      try {
        state.loading = true;
        let { data } = await cashierSummaryDetail(params)
        state.rowData = data
        state.loading = false
        state.isShow = true
      }
      catch {
        state.loading = false;
      }
    }
    const search = () => {
      state.form.pageNum = 1;
      state.form.pageSize = 6;
      getList();
    };

    const exportClick = async () => {
      state.loading = true;
      try {
        let res = await cashierSummaryExport(state.form);
        downloadXlsx(res, '出纳汇总报表.xlsx')
        state.loading = false;
      }
      catch {
        state.loading = false;
      }
    };
    const printClick = async () => {
      let params = { ...state.form }
      if (params.operatorList && params.operatorList.length) {
        params.operatorList = params.operatorList.join(",")
      } else {
        delete params.operatorList
      }
      let { data, code } = await cashierSummaryPrint(params)
      if (code === 0) {
        print(data)

      }
    }
    const handlePageChange = (val) => {
      state.form.pageNum = val;
      getList();
    };
    const handleSizeChange = (val) => {
      state.form.pageNum = 1;
      state.form.pageSize = val;
      getList();
    };
    const handleClose = () => {
      state.dialogVisible = false;
    };
    const reset = () => {
      state.form = {
        pageNum: 1,
        pageSize: 6,
        startDateTime: requestDate()[0],
        endDateTime: requestDate()[1],
      };
      state.requestDate = requestDate();
    };
    onMounted(() => {
      getList();
      querySystemUser();
    });
    return {
      column,
      state,
      timeStr,
      handleClose,
      exportClick,
      printClick,
      getList,
      detailClick,
      search,
      changeDate,
      handlePageChange,
      handleSizeChange,
      reset,
    };
  },
};
</script>
<style lang="scss" scoped>
:deep(.el-divider--horizontal) {
  margin: 0 0 10px 0;
}
.cashRecharge {
  min-height: 680px;
  :deep(.search-box) {
    .el-input__inner {
      width: 193px;
    }
    .el-date-editor {
      width: 400px;
    }
  }
}
:deep(.el-dialog__header) {
  border-bottom: 1px solid #efefef;
}
:deep(.el-dialog__headerbtn) {
  font-size: 20px;
  top: 10px;
  right: 10px;
}
:deep(.el-dialog__header) {
  padding: 10px 20px;
}
:deep(.el-overlay) {
  position: absolute;
  background-color: rgba(0, 0, 0, 0.2);
}
:deep(.el-dialog__footer) {
  text-align: center;
}
:deep(.el-dialog__title) {
  font-size: 14px;
}
</style>