<template>
  <kade-modal v-bind="attrs" :modelValue="modelValue" @update:modelValue="update">
    <el-form label-width="100px" :rules="rules" :model="state.model" size="small" ref="formRef">
      <template v-if="!merchantOperatorId">
        <el-form-item label="选择用户:" prop="baseUserId">
          <el-input placeholder="请选择" v-model="state.model.baseUserName" readonly @click="state.isPersoner = true">
          </el-input>
          <select-person-dialog :isMultiple="false" :isShow="state.isPersoner" @close="closePersonSelect" />
        </el-form-item>
      </template>
      <template v-else>
        <el-form-item label="用户名:">
          {{ state.model.baseUserName }}
        </el-form-item>
      </template>
      <el-form-item label="绑定期限:" prop="beginDate">
        <el-date-picker v-model="state.dateLimit" type="daterange" style="width:100%" range-separator="至"
          start-placeholder="开始日期" end-placeholder="结束日期" @change="handleDateChange">
        </el-date-picker>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button icon="el-icon-circle-close" @click="cancel" size="small">取消</el-button>
      <el-button icon="el-icon-circle-check" @click="submit" :loading="state.btnLoading" size="small" type="primary">保存
      </el-button>
    </template>
  </kade-modal>
</template>
<script>
import { reactive, watch, computed, ref } from 'vue';
import {
  ElForm,
  ElFormItem,
  ElInput,
  ElDatePicker,
  ElMessage,
  ElButton,
} from 'element-plus';
import moment from 'moment';
import Modal from '@/components/modal';
import { getUserInfoListByPage } from '@/applications/eccard-basic-data/api';
import {
  addMerchantOperator,
  getMerchantOperatorById,
  updateMerchantOperator,
} from '@/applications/eccard-finance/api';
import selectPersonDialog from "@/components/table/selectPersonDialog.vue";
export default {
  emits: ['update:modelValue', 'change'],
  components: {
    'kade-modal': Modal,
    'el-form': ElForm,
    'el-form-item': ElFormItem,
    'el-date-picker': ElDatePicker,
    'el-button': ElButton,
    ElInput,
    "select-person-dialog": selectPersonDialog,
  },
  props: {
    modelValue: {
      type: Boolean,
      default: false,
    },
    merchantId: {
      type: [String, Number],
      required: true,
    },
    merchantOperatorId: {
      type: [String, Number],
    },
    isInfo: {
      type: Boolean,
      default: false,
    }
  },
  setup(props, context) {
    const formRef = ref(null)
    const state = reactive({
      isPersoner: false,
      model: {
        baseUserId: '',
        baseUserName: '',
        beginDate: '',
        endDate: '',
      },
      dateLimit: [],
      btnLoading: false,
      loading: false,
    });
    const rules = {
      baseUserId: [{
        required: true,
        message: '请选择用户',
        trigger: 'change',
      }],
      beginDate: [{
        required: true,
        message: '请选择绑定期限',
        trigger: 'change',
      }]
    };
    const cancel = () => {
      context.emit('update:modelValue', false);
    }
    const closePersonSelect = (user) => {
      state.model.baseUserId = user.id || null;
      state.model.baseUserName = user.userName || null;
      /* if (val) {
        state.model.areaDirector = val.userName
      } */
      state.isPersoner = false;
    };
    const submit =  () => {
      formRef.value.validate(async valid => {
        if (valid) {
          try {
            state.btnLoading = true;
            const fn = props.merchantOperatorId ? updateMerchantOperator : addMerchantOperator;
            // eslint-disable-next-line no-unused-vars
            const { baseUserName, ...fields } = state.model;
            const { message } = await fn({
              ...fields,
              beginDate: moment(state.model.beginDate).format('YYYY-MM-DD'),
              endDate: moment(state.model.endDate).format('YYYY-MM-DD'),
              merchantId: props.merchantId,
            });
            ElMessage.success(message);
            context.emit('change');
            cancel();
          } catch (e) {
            throw new Error(e.message);
          } finally {
            state.btnLoading = false;
          }
        }
      })

    }
    const loadInfo = async () => {
      try {
        state.loading = true;
        const { data } = await getMerchantOperatorById({
          merchantOperatorId: props.merchantOperatorId,
          merchantId: props.merchantId,
        });
        state.model = data;
      } catch (e) {
        throw new Error(e.message);
      } finally {
        state.loading = false;
      }
    }
    const handleDateChange = (v) => {
      state.model.beginDate = v[0] || '';
      state.model.endDate = v[1] || '';
    }
    const update = (v) => {
      context.emit('update:modelValue', v);
    }
    const attrs = computed(() => {
      // eslint-disable-next-line no-unused-vars
      const { modelValue, merchantId, merchantOperatorId, isInfo, ...attrs } = props;
      let title;
      if (props.isInfo) {
        title = '详情';
      } else if (props.merchantOperatorId) {
        title = '修改';
      } else {
        title = '绑定管理员';
      }
      return { ...attrs, title };
    });
    const selectUserChange = (user) => {
      state.model.baseUserId = user.id || null;
      state.model.baseUserName = user.userName || null;
    }
    watch(() => props.modelValue, (v) => {
      if (v && props.merchantOperatorId) {
        loadInfo();
      }
    });
    return {
      formRef,
      state,
      cancel,
      submit,
      update,
      rules,
      attrs,
      closePersonSelect,
      getUserInfoListByPage,
      handleDateChange,
      selectUserChange,
    }
  }
}
</script>