<template>
  <div class="header">
    <div class="inner">
      <div class="left">
        <slot name="title">
          <span class="title">{{ title }}</span>
        </slot>
      </div>
      <div class="right">
        <div class="control">
          <kade-user-control
            :menus="menus"
            :avatar="avatar"
            :label="userName"
            @menuClick="handleMenuClick"
          />
        </div>
        <!-- <div class="icon-btn" v-if="showMessage" @click="handleMessageClick">
          <el-badge size="small" :hidden="badge === 0" :value="badge" type="danger" :max="99">
            <i class="el-icon-xiaoxi1"></i>
          </el-badge>
        </div> -->
        <div class="icon-btn" @click="() => handleMenuClick('Loginout')">
          <i
            :class="isUnified ? 'el-icon-switch-button' : 'el-icon-guanbi'"
          ></i>
        </div>
      </div>
    </div>
  </div>
  <change-pass-modal v-model="showEditPass" title="修改密码" />
  <my-info-modal v-model="showEditInfo" title="个人信息" />
</template>
<script>
// 头部
import { mapState } from "vuex";
import { ElMessageBox, ElMessage } from "element-plus";
import UserControl from "@/components/userControl";
import { getMessageList } from "@/applications/unified_portal/api";
import ChangePass from "@/applications/unified_portal/views/changepass";
import MyInfo from "@/applications/unified_portal/views/myinfo";
export default {
  components: {
    "kade-user-control": UserControl,
    "change-pass-modal": ChangePass,
    "my-info-modal": MyInfo,
    // 'el-badge': ElBadge,
  },
  data() {
    return {
      badge: 0,
      showEditInfo: false,
      showEditPass: false,
      isUnified: CONFIG.NAME === "unified_portal",
    };
  },
  methods: {
    handleEditInfo() {
      this.showEditInfo = true;
    },
    handleEditPass() {
      this.showEditPass = true;
    },
    async handleMenuClick(action) {
      if (action === "Home") {
        window.location.href = "unified_portal";
      }
      if (action === "MyInfo") {
        this.handleEditInfo();
        return;
      }
      if (action === "MyMessage") {
        this.$router.push({ name: "MyMessage" });
        return;
      }
      if (action === "ChangePass") {
        this.handleEditPass();
        return;
      }
      if (action === "Loginout") {
        if (this.isUnified) {
          ElMessageBox.confirm(
            "确认要退出系统吗？",
            "提示",
            {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            }
          )
            .then(async () => {
              await this.$store.dispatch("user/loginout");
            })
            .catch(() => {
              ElMessage({
                type: "info",
                message: "已取消",
              });
            });

          
        } else {
          location.href = "/unified_portal";
        }
      }
    },
    handleMessageClick() {
      this.$router.push({ name: "MyMessage" });
    },
  },
  computed: {
    ...mapState({
      title: (state) => state.title,
      menus: (state) => state.user.userMenus,
      avatar: (state) => state.user.userInfo.userHeadUrl,
      userName: (state) => state.user.userInfo.userName,
    }),
    showMessage() {
      return CONFIG.NAME === "unified_portal";
    },
  },
  mounted() {
    if (CONFIG.NAME === "unified_portal") {
      getMessageList({
        rowNum: 1,
        beginPage: 1,
      }).then((res) => {
        this.badge = res.data?.totalCount || 0;
      });
    }
  },
};
</script>
<style lang="scss" scoped>
.header {
  width: 100%;
  height: 50px;
  position: relative;
  background-color: #fff;
  .inner {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 50px;
    padding: 0 20px;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .title {
      color: #666;
      font-size: 18px;
      font-weight: 500;
    }
    .left,
    .right {
      display: flex;
      align-items: center;
    }
    .left {
      flex: 1;
      justify-content: flex-start;
    }
    .right {
      flex-basis: 300px;
      justify-content: flex-end;
      .control {
        margin-right: 30px;
        display: flex;
        align-items: center;
        justify-content: flex-end;
      }
      i {
        font-size: 22px;
        color: #666;
        &:hover {
          color: $primary-color;
        }
      }
      .icon-btn {
        cursor: pointer;
      }
      .icon-btn + .icon-btn {
        margin-left: 40px;
      }
    }
  }
}
</style>