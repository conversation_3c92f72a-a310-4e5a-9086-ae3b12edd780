<template>
  <el-dialog :model-value="modelValue" title="绑定设备" width="90%" :before-close="beforeClose">
    <kade-table-wrap title="选择设备" style="margin-top:20px" v-loading="state.loading">
      <kade-select-table :isShow="modelValue" :value="[]" :reqFnc="noBindGatewayDeviceList" :selectCondition="state.selectCondition" :column="column" :params="state.params" @change="deviceChange" />
    </kade-table-wrap>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="beforeClose" size="mini">取消</el-button>
        <el-button type="primary" @click="submit()" size="mini">提交</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script>
import { ElDialog, ElButton, ElMessage } from "element-plus";
import { onMounted, reactive, watch } from "vue";
// import { objToArray } from "@/utils"
import { useDict } from "@/hooks/useDict";
import {
  noBindGatewayDeviceList,
  getModel,
  getWorkStationList,
  iotCfg,
  bindGateway
} from "@/applications/eccard-iot/api";
import selectTable from "@/components/table/selectTable.vue";
const column = [
  { label: "所属区域", prop: "areaName", isDict: false, width: "" },
  { label: "设备机号", prop: "deviceNo", isDict: false, width: "" },
  { label: "设备名称", prop: "deviceName", isDict: false, width: "" },
  { label: "终端型号", prop: "deviceModel", isDict: false, width: "" },
  { label: "设备状态", prop: "deviceStatus", isDict: true, width: "" },
  { label: "连接类型", prop: "deviceConnectType", isDict: true, width: "" },
  { label: "设备IP", prop: "deviceIp", isDict: false, width: "" },
];

export default {
  emits: ["update:modelValue"],
  components: {
    ElDialog,
    ElButton,
    "kade-select-table": selectTable,
  },
  props: {
    modelValue: {
      type: Boolean,
      default: false,
    },
    rowData: {
      type: Object,
      default: null,
    },
    TabModule: {
      type: Function,
      default: null,
    }
  },
  setup(props, context) {
    const state = reactive({
      loading: false,
      selectCondition: [
        {
          label: "设备类型", valueKey: "deviceType", placeholder: "请选择", isSelect: true,
          select: {
            list: [],
            option: { label: "cfgValue", value: "cfgKey", },
          },
        },
        {
          label: "终端型号", valueKey: "deviceModel", placeholder: "请选择", isSelect: true,
          select: {
            list: [],
            option: { label: "productMode", value: "productMode" }
          },
        },
        { label: "所属区域", valueKey: "areaId", placeholder: "请选择", isSelect: false, isTree: "area" },
        { label: "机号", valueKey: "deviceNo", placeholder: "请输入", isSelect: false, },
        {
          label: "设备状态", valueKey: "deviceStatus", placeholder: "请选择", isSelect: true,
          select: {
            list: useDict("SYS_DEVICE_STATICE"),
            option: { label: "label", value: "value" },
          },
        },
        { label: "设备名称", valueKey: "deviceName", placeholder: "请输入", isSelect: false, },
        {
          label: "所属工作站", valueKey: "workstationId", placeholder: "请选择", isSelect: true,
          select: {
            list: [],
            option: { label: "name", value: "id" },
          },
        },
        {
          label: "连接类型", valueKey: "deviceConnectType", placeholder: "请选择", isSelect: true,
          select: {
            list: useDict("SYS_DEVICE_CONNECT_TYPE"),
            option: { label: "label", value: "value" },
          },
        },
        { label: "设备IP", valueKey: "deviceIp", placeholder: "请输入", isSelect: false, },
      ],
      params: {
        currentPageKey: "pageNum",
        pageSizeKey: "pageSize",
        resListKey: "list",
        resTotalKey: "total",
        value: {
          gatewayType:  props.rowData.gatewayType,
          deviceType: props.rowData.deviceType
        },
        tagNameKey: "deviceName",
        valueKey: "id",
      },
      deviceList: [],
    });

    watch(() => props.modelValue, val => {
      if (val) {
        state.params.value.deviceType = props.rowData.deviceType
        state.params.value.gatewayType = props.rowData.gatewayType
      }
    })
    const getDeviceTypeList = async () => {
      let { data } = await iotCfg({ cfgType: props.TabModule().productType });
      state.selectCondition[0].select.list = data
    }
    //获取终端型号
    const queryModel = async () => {
      let { data } = await getModel();
      state.selectCondition[1].select.list = data;
    };
    //获取工作站
    const queryWorkStationList = async () => {
      let { data: { list } } = await getWorkStationList();
      state.selectCondition[6].select.list = list;
    };
    const deviceChange = (val) => {
      console.log(val);
      state.deviceList = val.list.map((item) => {
        return {
          rdeviceId: item.id,
          deviceType: item.deviceType,
          gatewayType: props.rowData.gatewayType,
          rgatewayId: props.rowData.id
        }
      });
    };
    const submit = async () => {
      state.loading = true
      try {
        let { code, message } = await bindGateway(state.deviceList)
        if (code === 0) {
          ElMessage.success(message)
          context.emit("update:modelValue", false);
        }
        state.loading = false
      }
      catch {
        state.loading = false
      }
    }
    const beforeClose = () => {
      context.emit("update:modelValue", false);
    };
    onMounted(async () => {
      await getDeviceTypeList()
      queryModel();
      queryWorkStationList();
    });

    return {
      noBindGatewayDeviceList,
      column,
      state,
      deviceChange,
      submit,
      beforeClose,
    };
  },
};
</script>

<style lang="scss" scoped>
.tab-warp {
  margin: 20px 0;
}
</style>
