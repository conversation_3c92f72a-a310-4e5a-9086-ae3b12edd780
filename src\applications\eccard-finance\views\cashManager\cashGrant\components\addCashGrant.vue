<template>
  <div class="cash-add">
    <el-dialog :model-value="isAdd" title="新建现金发放" width="90%" :before-close="beforeClose"
      :close-on-click-modal="false">
      <div>
        <div class="padding-form-box">
          <el-form ref="form" :model="state.form" :rules="state.rules" inline size="small" label-width="120px">
            <el-form-item label="项目名称:" prop="projectName">
              <el-input placeholder="请输入" v-model="state.form.projectName"></el-input>
            </el-form-item>
            <el-form-item label="充值类型:" prop="walletCode">
              <el-select disabled v-model="state.form.walletCode" placeholder="请选择">
                <el-option v-for="(item, index) in [{ label: '现金钱包', value: 1 }]" :key="index" :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="充值方式:" prop="rechargeMode">
              <el-select clearable v-model="state.form.rechargeMode" placeholder="请选择">
                <el-option v-for="(item, index) in rechargeType" :key="index" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-form>
          <el-form size="small" label-width="120px">
            <el-form-item label="发放说明:">
              <el-input placeholder="请输入" type="textarea" v-model="state.form.keyWord"></el-input>
            </el-form-item>
            <el-form-item label="发放方式:">
              <el-radio-group v-model="state.form.grantMode">
                <el-radio label="GENERATE">生成清单</el-radio>
                <el-radio label="IMPORT">导入清单</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-form>

          <el-form v-show="state.form.grantMode == 'GENERATE'" inline :model="state.grantForm" ref="grantForm"
            :rules="state.grantRules" size="small" label-width="120px">
            <el-form-item label=" 身份类别:" prop="userRole">
              <el-select clearable v-model="state.grantForm.userRole" placeholder="请选择">
                <el-option v-for="(item, index) in roleList" :key="index" :label="item.roleName" :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="每人充值金额:" prop="rechargeAmount">
              <el-input placeholder="请输入" v-model="state.grantForm.rechargeAmount"></el-input>
            </el-form-item>
          </el-form>
          <div class="select-input-lang">
            <el-form v-show="state.form.grantMode == 'GENERATE'" inline size="small" label-width="120px">
              <el-form-item>
                <template #label>
                  <el-dropdown trigger="click" @command="dropdownChange">
                    <span class="el-dropdown-link">
                      {{ state.dropdownType }}:<el-icon class="el-icon--right"></el-icon>
                    </span>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item command="组织机构">组织机构</el-dropdown-item>
                        <el-dropdown-item command="卡片类型">卡片类型</el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </template>

                <kade-dept-select-tree v-if="state.dropdownType === '组织机构'" style="width: 100%"
                  :value="state.grantForm.deptPaths" valueKey="deptPath" :multiple="true"
                  @valueChange="(val) => (state.grantForm.deptPaths = val)" />
                <el-select v-if="state.dropdownType === '卡片类型'" v-model="state.grantForm.cardTypes" multiple
                  collapse-tags placeholder="请选择">
                  <el-option v-for="item in cardTypeList" :key="item.id" :label="item.ctName" :value="item.id">
                  </el-option>
                </el-select>
              </el-form-item>

              <el-button size="small" type="primary" @click="defineGenerateListClick()">确认生成清单</el-button>
              <el-button size="small" @click="state.grantForm = {}">重置</el-button>
            </el-form>
          </div>
          <el-form v-show="state.form.grantMode == 'IMPORT'" inline size="small" label-width="120px">
            <el-form-item label=" 上传文件:">
              <el-upload class="upload-demo" :action="state.requestUrl"
                :headers="{ Authorization: `bearer ${state.requestHeader}` }" :on-success="handleSuccess"
                :on-remove="handleRemove" :on-exceed="handleExceed" :limit="1" :file-list="fileList" ref="uploadDom">
                <el-button size="small" type="primary">上传充值清单</el-button>
              </el-upload>
            </el-form-item>
            <el-form-item>
              <div class="green" style="margin-left: 20px" @click="download()">下载样例</div>
            </el-form-item>
          </el-form>
          <div style="margin-bottom: 10px" v-if="state.listData.errorCount > 0">
            <span class="red"> • </span>异常数据
            <span class="red"> {{ state.listData.errorCount }}</span>
            条，请修改确认
          </div>
          <el-table style="width: 100%" :data="state.personList" v-loading="false" border stripe>
            <el-table-column label="校检结果" prop="verifyResult" align="center">
              <template #default="scope">
                <div :style="scope.row.error && 'color:#f00'">
                  {{ scope.row.verifyResult }}
                </div>
              </template>
            </el-table-column>

            <el-table-column label="用户编号" prop="userCode" align="center"></el-table-column>
            <el-table-column label="姓名" prop="userName" align="center"></el-table-column>
            <el-table-column label="组织机构" prop="deptName" align="center"></el-table-column>
            <el-table-column label="身份类别" prop="userRole" align="center"></el-table-column>
            <el-table-column label="充值金额（元）" prop="rechargeAmount" align="center"></el-table-column>
            <el-table-column label="操作" prop="userName" align="center">
              <template #default="scope">
                <!-- <span @click="delClick(scope.row)" style="margin-right: 10px"
                  >编辑</span
                > -->
                <span @click="delClick(scope.row)" class="green">删除</span>
              </template>
            </el-table-column>
          </el-table>
          <div class="cash-add-pagination">
            <div>
              <span>合计：<span class="blue">{{ state.listData.totalCount }}</span>人，<span class="blue">{{
                  state.listData.totalAmount
              }}</span>元</span>
            </div>
            <el-pagination background :current-page="state.grantParam.currentPage"
              :page-size="state.grantParam.pageSize" layout="total, sizes, prev, pager, next, jumper"
              :page-sizes="[6, 10, 20, 50, 100]" :total="state.total" @current-change="handlePageChange"
              @size-change="handleSizeChange">
            </el-pagination>
          </div>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="beforeClose()" size="mini">关&nbsp;&nbsp;闭</el-button>
          <el-button @click="submitClick()" size="mini" type="primary">确&nbsp;&nbsp;认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script>
import { onMounted, reactive, computed, ref, watch, nextTick } from "vue";
import { useStore } from "vuex";
import { getToken, downloadXlsx } from "@/utils";
import { useDict } from "@/hooks/useDict";

import {
  generateRechargeList,
  getWaitRechargeListByPage,
  deleteWaitRecharge,
  saveImportRechargeList,
  exportCashExample,
} from "@/applications/eccard-finance/api";
import {
  ElDialog,
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElTable,
  ElRadio,
  ElRadioGroup,
  ElTableColumn,
  ElPagination,
  ElUpload,
  ElMessage,
  ElDropdown,
  ElDropdownMenu,
  ElDropdownItem,
} from "element-plus";
import deptSelectTree from "@/components/tree/deptSelectTree.vue";

export default {
  components: {
    "el-dialog": ElDialog,
    "el-button": ElButton,
    "el-input": ElInput,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    "el-select": ElSelect,
    "el-option": ElOption,
    "el-table": ElTable,
    "el-table-column": ElTableColumn,
    "el-pagination": ElPagination,
    ElRadio,
    ElRadioGroup,
    ElUpload,
    ElDropdown,
    ElDropdownMenu,
    ElDropdownItem,
    "kade-dept-select-tree": deptSelectTree,
  },
  setup() {
    const store = useStore();
    const uploadDom = ref(null);
    const form = ref(null);
    const grantForm = ref(null);
    const rechargeType = useDict("SYS_RECHARGE_MODE");
    const state = reactive({
      form: {
        walletCode: 1,
      },
      grantForm: {},
      dropdownType: "组织机构",
      rules: {
        projectName: [
          {
            required: true,
            message: "请输入项目名称",
            trigger: "blur",
          },
        ],
        rechargeMode: [
          {
            required: true,
            message: "请选择充值方式",
            trigger: "change",
          },
        ],
      },
      grantRules: {
        rechargeAmount: [
          {
            required: true,
            message: "请输入每人充值金额",
            trigger: "blur",
          },
        ],
        userRole: [
          {
            required: true,
            message: "请选择身份类别",
            trigger: "change",
          },
        ],
      },
      requestHeader: getToken(),
      requestUrl: `${CONFIG.BASE_API_PATH}eccard-finance/CashGrant/importRechargeList`,
      grantParam: {
        currentPage: 1,
        pageSize: 6,
      },
      total: 0,
      personList: [],
      listData: {
        totalCount: 0,
        totalAmount: 0,
      },
    });
    const isAdd = computed(() => {
      return store.state.cashData.isAdd;
    });
    const roleList = computed(() => {
      return store.state.cashData.roleList;
    });
    const cardTypeList = computed(() => {
      return store.state.cashData.cardTypeList;
    });
    //监听清单生成类型切换
    watch(
      () => state.form.grantMode,
      (val) => {
        reset();
        if (val === "GENERATE") {
          nextTick(() => {
            grantForm.value.clearValidate();
          });
        }
      }
    );

    //监听模态框开关
    watch(
      () => store.state.cashData.isAdd,
      (val) => {
        if (val) {
          reset();
          nextTick(() => {
            form.value.clearValidate();
            grantForm.value.clearValidate();
          });
        }
      }
    );

    watch(
      () => state.dropdownType,
      (val) => {
        if (val === "组织机构") {
          delete state.grantForm.cardTypes;
        } else if (val === "卡片类型") {
          delete state.grantForm.deptPaths;
        }
      }
    );


    const dropdownChange = (val) => {
      state.dropdownType = val;
    };

    //获取待充值清单列表
    const grantList = async () => {
      let {
        data: {
          generateRechargeDto: { errorCount, totalAmount, totalCount },
          pageInfo,
        },
      } = await getWaitRechargeListByPage(state.grantParam);
      state.personList = pageInfo.list;
      state.total = pageInfo.size;
      state.listData = { errorCount, totalAmount, totalCount };
    };
    //点击生成清单
    const defineGenerateListClick = () => {
      grantForm.value.validate(async (valid) => {
        if (valid) {
          let { data, code } = await generateRechargeList(state.grantForm);
          if (code === 0) {
            state.grantParam.projectNo = data.projectNo;
            grantList();
          }
        } else {
          return false;
        }
      });
    };

    //上传成功
    const handleSuccess = (res) => {
      if (res.code === 0) {
        state.grantParam.projectNo = res.data.projectNo;
        grantList();
      }
    };

    //上传文件个数超出限制提示
    const handleExceed = (files, fileList) => {
      if (fileList.length === 1) {
        ElMessage.error("最多上传一个文件！");
      }
    };

    //删除文件
    const handleRemove = () => {
      state.listData = {
        totalCount: 0,
        totalAmount: 0,
      };
      state.total = 0;
      state.personList = [];
      state.grantParam = {
        currentPage: 1,
        pageSize: 6,
      };
    };

    const handlePageChange = (val) => {
      state.grantParam.currentPage = val;
      grantList();
    };
    const handleSizeChange = (val) => {
      state.grantParam.currentPage = 1;
      state.grantParam.pageSize = val;
      grantList();
    };

    //删除人员
    const delClick = async (row) => {
      let { code } = await deleteWaitRecharge({
        projectNo: state.grantParam.projectNo,
        id: Number(row.id),
      });
      if (code === 0) {
        ElMessage.success("删除成功！");
        grantList();
      }
    };

    //确认生成清单
    const submitClick = () => {
      if (!state.total) {
        return ElMessage.error("请先生成清单！");
      }
      form.value.validate(async (valid) => {
        if (valid) {
          let param = { ...state.form, projectNo: state.grantParam.projectNo };
          let { code, message } = await saveImportRechargeList(param);
          if (code === 0) {
            ElMessage.success(message);
            store.commit("cashData/updateState", {
              key: "isAdd",
              payload: false,
            });
          }
        } else {
          return false;
        }
      });
    };

    //下载清单样例
    const download = async () => {
      let res = await exportCashExample();
      downloadXlsx(res, "现金充值导入样例.xlsx");
    };

    //关闭
    const beforeClose = () => {
      store.commit("cashData/updateState", {
        key: "isAdd",
        payload: false,
      });
    };

    const reset = () => {
      state.grantForm = {};
      nextTick(() => {
        uploadDom.value.clearFiles();
      });
      state.listData = {
        totalCount: 0,
        totalAmount: 0,
      };
      state.total = 0;
      state.personList = [];
      state.grantParam = {
        currentPage: 1,
        pageSize: 6,
      };
    };

    onMounted(() => { });
    return {
      rechargeType,
      uploadDom,
      form,
      grantForm,
      state,
      isAdd,
      delClick,
      roleList,
      cardTypeList,
      dropdownChange,
      defineGenerateListClick,
      handleSuccess,
      handleRemove,
      handleExceed,
      handlePageChange,
      handleSizeChange,
      submitClick,
      download,
      beforeClose,
    };
  },
};
</script>
<style lang="scss" scoped>
.cash-add {
  .cash-add-pagination {
    padding-left: 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}

:deep(.el-dialog__header) {
  border-bottom: 1px solid #efefef;
}

:deep(.el-overlay) {
  position: absolute;
  background-color: rgba(0, 0, 0, 0);
}

:deep(.el-dialog__footer) {
  text-align: center;
}

:deep(.select-input-lang) {
  .el-select {
    width: 500px;
  }
}

:deep(.el-popper.is-light) {
  width: 500px !important;
}
</style>