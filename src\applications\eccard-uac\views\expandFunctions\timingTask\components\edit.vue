<template>
  <el-dialog :model-value="modelValue" :title="title" width="1130px" :before-close="handleClose"
    :close-on-click-modal="false">
    <div class="padding-box">
      <el-form size="mini" label-width="130px" :model="state.form" ref="formRef"
        :rules="dialogType != 'details' && rules">
        <el-row>
          <el-col :span="8">
            <el-form-item label="任务编号：" prop="taskNo">
              <el-input v-if="dialogType == 'details'" :model-value="state.form.taskNo" redaonly />
              <el-input-number v-else v-model="state.form.taskNo" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item label="描述：" prop="description">
              <el-input v-if="dialogType == 'details'" :model-value="state.form.description" redaonly />
              <el-input v-else v-model="state.form.description" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="起始日期：" prop="beginDate">
              <el-input v-if="dialogType == 'details'" :model-value="state.form.beginDate" redaonly />
              <el-date-picker v-else v-model="state.form.beginDate" type="date" placeholder="请选择" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="截止日期：" prop="endDate">
              <el-input v-if="dialogType == 'details'" :model-value="state.form.endDate" redaonly />
              <el-date-picker v-else v-model="state.form.endDate" type="date" placeholder="请选择" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="动作时间：" prop="actionTime">
              <el-input v-if="dialogType == 'details'" :model-value="state.form.actionTime" redaonly />
              <el-time-picker v-else v-model="state.form.actionTime" placeholder="请选择时间" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="有效星期选项：">
              <template v-if="dialogType == 'details'">
                <el-checkbox disabled v-for="(item, index) in state.weekList" v-model="item.checked" :key="index">
                  {{ item.label }}
                </el-checkbox>
              </template>
              <template v-else>
                <el-checkbox v-for="(item, index) in state.weekList" v-model="item.checked" :key="index">
                  {{ item.label }}
                </el-checkbox>
              </template>

            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="控制方式：">
              <el-input v-if="dialogType == 'details'" :model-value="state.form.doorControlMode" redaonly />
              <el-select v-else v-model="state.form.doorControlMode">
                <el-option v-for="(item, index) in controlModeList" :key="index" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="适用于门：">
              <el-input v-if="dialogType == 'details'" :model-value="state.form.doorName" redaonly />
              <el-select v-else v-model="state.form.doorId">
                <el-option v-for="(item, index) in state.doorList" :key="index" :label="item.doorName" :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注：" prop="remarks">
              <el-input v-if="dialogType == 'details'" :model-value="state.form.remarks" redaonly />
              <el-input v-else type="textarea" v-model="state.form.remarks" placeholder="请输入" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button type="primary" @click="submit" size="mini" v-if="dialogType != 'details'">保存</el-button>
        <el-button @click="handleClose" size="mini">返回</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script>
import { ElDialog, ElRow, ElCol, ElButton, ElForm, ElFormItem, ElInput, ElSelect, ElOption, ElInputNumber, ElDatePicker, ElCheckbox, ElTimePicker, ElMessage } from "element-plus"
import { reactive } from '@vue/reactivity'
import { watch, ref, nextTick, computed } from 'vue'
import { dateStr, timeStr } from '@/utils/date.js'
import {addTask, editTask,getDoorList } from '@/applications/eccard-uac/api.js'
const rules = {
  taskNo: [{ required: true, message: '请输入任务编号', trigger: 'blur' }],
  beginDate: [{ required: true, message: '请选择起始日期', trigger: 'change' }],
  endDate: [{ required: true, message: '请选择截止日期', trigger: 'change' }],
  actionTime: [{ required: true, message: '请选择动作时间', trigger: 'change' }],
  description:[{ required:false, max: 200, message: '描述信息不能超过200' }],
  remarks:[{ required:false, max: 200, message: '备注信息不能超过200' }]
}
const controlModeList=[
  {label:'常开',value:0},
  {label:'常闭',value:1}
]
export default {
  components: {
    ElDialog, ElRow, ElCol, ElButton, ElForm, ElFormItem, ElInput, ElSelect, ElOption, ElInputNumber, ElDatePicker, ElCheckbox, ElTimePicker
  },
  emits: ["update:modelValue"],
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    dialogType: {
      type: String,
      default: ''
    },
    rowData: {
      type: Object,
      default: null
    }
  },
  setup(props, context) {
    const formRef = ref(null)
    const state = reactive({
      weekList: [
        { label: '星期一', checked: false, filed: 'monday' },
        { label: '星期二', checked: false, filed: 'tuesday' },
        { label: '星期三', checked: false, filed: 'wednesday' },
        { label: '星期四', checked: false, filed: 'thursday' },
        { label: '星期五', checked: false, filed: 'friday' },
        { label: '星期六', checked: false, filed: 'saturday' },
        { label: '星期日', checked: false, filed: 'sunday' }
      ],
      form: {},
      dataList: [],
      doorList: []
    })
    const handleClose = () => {
      context.emit("update:modelValue", false)
    }
    const title = computed(() => {
      if (props.dialogType == 'add') {
        return '新增定时任务'
      } else if (props.dialogType == 'edit') {
        return '编辑定时任务'
      } else {
        return '定时任务详情'
      }
    })
    const getDoor =() =>{
      getDoorList().then((res)=>{
        state.doorList=res.data
        state.doorList.unshift({doorName:'全部',id:0})
        console.log(res)
      })
    }
    watch(() => props.modelValue, val => {
      nextTick(() => {
        formRef.value.clearValidate()
      })
      if (val) {
        getDoor()
        if (props.dialogType != 'add') {
          let { id,deviceId, taskNo, description, beginDate, endDate, actionTime, doorControlMode, doorId, remarks } = JSON.parse(JSON.stringify(props.rowData))
          console.log(taskNo)
          state.form = { id,deviceId, taskNo: Number(taskNo), description, beginDate: dateStr(beginDate), endDate: dateStr(endDate), actionTime: timeStr(actionTime), doorControlMode, doorId, remarks }
          state.weekList.forEach((item) => {
            item.checked = { ...props.rowData }[item.filed] === 1 ? true : false
          })
        } else {
          state.form = {}
          state.weekList.forEach((item) => {
            item.checked = false
          })
        }
      }
    })
    const submit = () => {
      console.log(state.form.checkList)
      formRef.value.validate(async (valid) => {
        if (valid) {
          let param = { ...state.form }
          state.weekList.forEach((item) => {
            param[item.filed] = item.checked ? 1 : 0
          })
          param.beginDate = dateStr(param.beginDate)
          param.endDate = dateStr(param.endDate)
          param.actionTime = timeStr(param.actionTime)
          console.log(param)
          let fn = props.dialogType == 'add' ? addTask : editTask
          let { code, message } = await fn(param)
          if (code === 0) {
            ElMessage.success(message)
            context.emit('update:modelValue', true)
          }
        }
      })
    }
    return {
      title,
      rules,
      state,
      formRef,
      submit,
      controlModeList,
      handleClose
    }
  }
}
</script>
<style lang="scss" scoped>
:deep(.kade-table-wrap) {
  margin-bottom: 20px;
  box-sizing: border-box;
}

.padding-box {
  padding: 20px 20px 0
}

.time-select {
  margin-bottom: 10px;

  .time-label {
    margin-right: 10px;
  }
}

:deep(.el-input-number) {
  width: 220px
}

:deep(.el-select) {
  width: 220px
}
</style>