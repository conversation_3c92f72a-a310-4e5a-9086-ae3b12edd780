<template>
  <el-dialog v-loading="state.loading" :model-value="modelValue" title="房间物品配置" width="800px" :before-close="handleClose">
    <div class="btn-box">
      <el-button @click="handleEdit({})" size="small" class="btn-green" icon="el-icon-plus">新增</el-button>
      <el-button @click="batchDel()" size="small" class="btn-blue" icon="el-icon-close">批量删除</el-button>
      <el-button @click="exportGoods" size="small" class="btn-purple" icon="el-icon-download">导出</el-button>
    </div>
    <el-table style="width: 100%;margin-bottom:20px" :data="state.dataList" ref="multipleTable" v-loading="state.loading"  @selection-change="handleSelectChange" border stripe>
      <el-table-column prop="" show-overflow-tooltip type="selection" align="center"></el-table-column>
      <el-table-column show-overflow-tooltip label="物品名称" prop="goodsName" align="center"></el-table-column>
      <el-table-column show-overflow-tooltip label="物品规格" prop="goodsSpecs" align="center"></el-table-column>
      <el-table-column show-overflow-tooltip label="物品数量" prop="goodsCount" align="center"></el-table-column>
      <el-table-column width="150px" label="物品图片" prop="resourceUrl" align="center">
        <template #default="scope">
          <el-image style="width: 60px; height: 60px" :src="scope.row.resourceUrl" :preview-src-list="[scope.row.resourceUrl]" :initial-index="0" fit="cover"></el-image>
        </template>
      </el-table-column>
      <el-table-column show-overflow-tooltip label="备注" prop="goodsRemark" align="center"></el-table-column>
      <el-table-column width="200px" label="操作" align="center">
        <template #default="scope">
          <el-button size="mini" type="text" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button size="mini" type="text" @click="handleDel(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div style="height:1px"></div>
    <kade-room-goods-edit v-model:modelValue="state.isEdit" :selectRow="selectRow" :rowData="state.rowData" @update:modelValue="close" />
  </el-dialog>
</template>

<script>
import { ElDialog, ElButton, ElTable, ElTableColumn, ElImage, ElMessageBox, ElMessage } from "element-plus"
import { reactive } from '@vue/reactivity'
import { downloadXlsx } from "@/utils"
import { getRoomGoods, roomGoodsDelete,roomGoodsBatchDelete,exportRoomGoods } from "@/applications/eccard-dorm/api";
import goodsEdit from "./goodsEdit"
import { watch } from '@vue/runtime-core';
export default {
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    selectRow: {
      types: Object,
      default: null
    },
  },
  components: {
    ElDialog,
    ElButton,
    ElTable,
    ElTableColumn,
    ElImage,
    "kade-room-goods-edit": goodsEdit
  },
  setup(props, context) {
    const state = reactive({
      loading: false,
      isEdit: false,
      rowData: {},
      dataList: [],
      selectRowList:[]
    })
    watch(() => props.modelValue, val => {
      if (val) {
        getList()
      }
    })
    const getList = async () => {
      state.laoding = true
      try {
        let { data } = await getRoomGoods({ roomTypeId: props.selectRow.roomTypeId })
        state.dataList = data
        state.laoding = false
      }
      catch {
        state.laoding = false
      }
    }
    const handleEdit = (row) => {
      state.rowData = row
      state.isEdit = true
    }
    const handleDel = (row) => {
      ElMessageBox.confirm("确认删除?", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        let { code, message } = await roomGoodsDelete(row.roomGoodsId);
        if (code === 0) {
          ElMessage.success(message);
          getList();
        }
      });
    }
    const handleSelectChange = (val) => {
      state.selectRowList = val
    }
    const batchDel = () => {
      if (!state.selectRowList.length) {
        return ElMessage.error("请先选择需要删除的物品！")
      }
      ElMessageBox.confirm("确认删除已选择物品?", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        let param = state.selectRowList.map(item => item.roomGoodsId).join(",")
        let { code, message } = await roomGoodsBatchDelete(param);
        if (code === 0) {
          ElMessage.success(message);
          getList();
        }
      });
    }
    const exportGoods=async ()=>{
      let res=await exportRoomGoods({roomTypeId:props.selectRow.roomTypeId})
      downloadXlsx(res,`${props.selectRow.roomTypeName}房间物品列表.xlsx`)
    }
    const handleClose = () => {
      context.emit("update:modelValue", false)
    };
    const close = (val) => {
      if (val) {
        getList()
      }
      state.isEdit = false
    }
    return {
      state,
      handleEdit,
      handleSelectChange,
      handleDel,
      batchDel,
      exportGoods,
      handleClose,
      close
    }
  }
}
</script>

<style lang="scss" scoped>
.btn-box {
  text-align: right;
  padding: 20px 0 10px;
}
</style>