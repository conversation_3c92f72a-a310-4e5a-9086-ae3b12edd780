<template>
  <el-dialog v-loading="state.loading" :model-value="isRecord.isShow" :title="(isRecord.type=='add'?'新增':'修改')+'设备更换记录'" width="640px" :before-close="beforeClose" :close-on-click-modal="false">
    <el-form label-width="100px" size="mini" :model="state.form" :rules="rules" ref="formRef" style="margin-top:20px">
      <el-form-item label="更换原因：" prop="replaceReason">
        <el-input v-model="state.form.replaceReason" placeholder="请输入" maxlength="20"></el-input>
      </el-form-item>
      <el-form-item label="更换时间：" prop="replaceDate">
        <el-date-picker v-model="state.form.replaceDate" type="datetime" placeholder="请选择" />
      </el-form-item>
      <el-form-item label="更换人员：">
        <el-input v-model="state.form.operatorName" placeholder="请输入" maxlength="20"></el-input>
      </el-form-item>
      <el-form-item label="备注：">
        <el-input v-model="state.form.replaceRemarks" placeholder="请输入" maxlength="200"></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="submit()" type="primary" size="mini">保&nbsp;&nbsp;存</el-button>
        <el-button @click="beforeClose()" size="mini">返&nbsp;&nbsp;回</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script>
import { ElDialog, ElButton, ElForm, ElFormItem, ElInput, ElDatePicker, ElMessage } from "element-plus"
import { computed, reactive, ref ,nextTick,watch} from 'vue'
import { useStore } from "vuex"
import { timeStr } from "@/utils/date"
import { deviceReplaceAdd, deviceReplaceEdit } from "@/applications/eccard-iot/api";

export default {
  components: {
    ElDialog,
    ElButton,
    ElForm,
    ElFormItem,
    ElInput,
    ElDatePicker
  },
  props: {
    data: {
      type: Object,
      default: null
    }
  },
  setup(props) {
    const formRef = ref(null)
    const store = useStore()
    const state = reactive({
      loading: false,
      form: {}
    })
    const rules = {
      replaceReason: [
        {
          required: true,
          message: "请输入更换原因",
        },
      ],
      replaceDate: [
        {
          required: true,
          message: "请选择更换时间",
          trigger: "change",
        },
      ],
    }
    const isRecord = computed(() => {
      return store.state['identityDevice/accessControllerChannel'].isRecord
    })
    watch(() => store.state['identityDevice/accessControllerChannel'].isRecord.isShow, val => {
      if (val) {
        state.form = { ...store.state['identityDevice/accessControllerChannel'].isRecord.data }
        nextTick(() => {
          formRef.value.clearValidate()
        })
      }
    })
    const submit = () => {
      formRef.value.validate(async (valid) => {
        if (valid) {
          let { id, deviceModel, deviceName, deviceNo, deviceType } = props.data
          let params = { deviceId: id, deviceModel, deviceName, deviceNo, deviceType, ...state.form }
          params.replaceDate = timeStr(params.replaceDate)
          params.deviceType = "ACCESS_CONTROL_DEVICE"
          console.log(params);
          let fn = store.state['identityDevice/accessControllerChannel'].isRecord.type == 'add' ? deviceReplaceAdd : deviceReplaceEdit
          state.loading = true
          try {
            let { code, message } = await fn(params)
            if (code === 0) {
              ElMessage.success(message)
              beforeClose()
            }
            state.loading = false
          }
          catch {
            state.loading = false
          }
        } else {
          return false
        }
      })
    }
    const beforeClose = () => {
      store.commit("identityDevice/accessControllerChannel/updateState", {
        key: "isRecord",
        payload: {
          type: store.state['identityDevice/accessControllerChannel'].isRecord.type,
          data: {},
          isShow: false
        },
      });
    }
    return {
      formRef,
      rules,
      state,
      isRecord,
      submit,
      beforeClose
    }
  }
}
</script>
<style lang="scss" scoped>
:deep(.el-input__inner) {
  width: 100% !important;
}
:deep(.el-date-editor.el-input){
  width: 100%;
}
</style>