<template>
  <div class="tree-box" v-loading="state.loading">
    <el-tree
      show-checkbox
      :check-on-click-node="false"
      :check-strictly="state.checkStrictly"
      node-key="id"
      ref="treeRef"
      :props="treeProps"
      :data="areaList"
      @check-change="handleCheckChange"
    >
      <template #default="{ node }">
        <span class="custom-tree-node">
          <kade-icon name="iconarea" />
          <span style="margin-left: 5px">{{ node.label }}</span>
        </span>
      </template>
    </el-tree>
  </div>
</template>
<script>
import { reactive, ref } from "@vue/reactivity";
import { computed, nextTick, onMounted, watch } from "@vue/runtime-core";
import { useStore } from "vuex";
import {makeTree} from '@/utils'
import { ElTree } from "element-plus"
const treeProps = {
  children: "children",
  label: "areaName",
  isLeaf: "leaf",
};
export default {
  components:{
    ElTree
  },
  setup() {
    const treeRef=ref(null)
    const store = useStore();
    const state = reactive({
      checkStrictly:false,
      selectList:[]
    });

    const areaList = computed(() => {
      return makeTree(store.state["sys/role"].areaList,"id","areaParentId","children");
    });
    watch(()=>store.state["sys/role"].areaList,(val)=>{
      state.selectList=val.filter(item=>item.checked).map(item=>item.id)
      nextTick(()=>{
        treeRef.value?.getNode(areaList[0]?.id)?.expand();
          state.selectList.forEach(item=>{
             var node=treeRef.value.getNode(item)
             if(node.isLeaf){
               treeRef.value.setChecked(node,true)
             }
          })
          state.checkStrictly=false
      })
      
    })

    const handleCheckChange=()=>{
      /* if(b){
        state.selectList.push(a.id)
      }else{
        state.selectList=state.selectList.filter(item=>item!=a.id)
      }
      if(c){
        state.selectList.push(a.id)
      } */

      console.log(treeRef.value.getCheckedNodes());
      state.selectList = treeRef.value.getCheckedNodes().map(item => item.id)

      state.selected = Array.from(new Set(state.selectList));
      console.log(state.selected );
      store.commit("sys/role/updateState", {
        key: "areaAuthIdList",
        payload: state.selected,
      });
    }

    

    onMounted(() => {});
    return {
      treeProps,
      treeRef,
      state,
      areaList,
      handleCheckChange,
    };
  },
};
</script>