<template>
  <div class="tree-box" v-loading="state.loading">
    <el-tree
      show-checkbox
      :check-on-click-node="false"
      :expand-on-click-node="false"
      :check-strictly="state.checkStrictly"
      node-key="id"
      ref="treeRef"
      :props="treeProps"
      :data="menuList"
      highlight-current
      @check-change="handleCheckChange"
      @node-click="nodeClick"
    >
      <template #default="{ node }">
        <span class="custom-tree-node">
          <kade-icon name="iconmenu-line" />
          <span style="margin-left: 5px">{{ node.label }}</span>
        </span>
      </template>
    </el-tree>
  </div>
</template>
<script>
import { reactive, ref } from "@vue/reactivity";
import { computed, nextTick, onMounted, watch } from "@vue/runtime-core";
import { useStore } from "vuex";
import {makeTree} from '@/utils'
import { ElTree } from "element-plus"

const treeProps = {
  children: "children",
  label: "menuName",
  isLeaf: "leaf",
};
export default {
  components:{
    ElTree
  },
  setup() {
    const treeRef=ref(null)
    const store = useStore();
    const state = reactive({
      checkStrictly:false,
      selectList:[]
    });

    const menuList = computed(() => {
      return makeTree(store.state["sys/role"].menuList,"id","parentId","children");
    });
    watch(()=>store.state["sys/role"].menuList,(val)=>{
      // state.checkStrictly=true
      state.selectList=val.filter(item=>item.checked==true).map(item=>item.id)
      nextTick(()=>{
        // treeRef.value.setCheckedKeys(state.selectList,true)
        // state.checkStrictly=false
        treeRef.value?.getNode(menuList[0]?.id)?.expand();
          state.selectList.forEach(item=>{
             var node=treeRef.value.getNode(item)
             console.log(item,node);
             if(node.isLeaf){
               treeRef.value.setChecked(node,true)
             }
          })
          state.checkStrictly=false
      })
      
    })

    const handleCheckChange=()=>{
      state.selected=treeRef.value.getCheckedNodes(false, true).map(item=>item.id)

      store.commit("sys/role/updateState", {
        key: "moduleAuthIdList",
        payload: state.selected,
      });
    }
    const nodeClick=row=>{
      store.commit("sys/role/updateState", {
        key: "rowModuleMenu",
        payload:row,
      });
      store.dispatch("sys/role/getAuthMenuList")
    }
  
    onMounted(() => {});
    return {
      treeProps,
      treeRef,
      state,
      menuList,
      handleCheckChange,
      nodeClick,
    };
  },
};
</script>