<template>
  <el-dialog :model-value="modelValue" title="处理报修单" width="700px" :before-close="handleClose">
    <el-form v-loading="state.loading" :model="state.form" inline label-width="100px" size="mini">
      <el-form-item label="所属区域：">
        <el-input :model-value="state.form.areaName" readonly />
      </el-form-item>
      <el-form-item  label="楼栋：">
        <el-input :model-value="state.form.buildName" readonly />
      </el-form-item>
      <el-form-item  label="单元：">
        <el-input :model-value="state.form.unitName" readonly />
      </el-form-item>
      <el-form-item  label="楼层：">
        <el-input :model-value="state.form.floorName" readonly />
      </el-form-item>
      <el-form-item  label="房间：">
        <el-input :model-value="state.form.roomName" readonly />
      </el-form-item>
      <el-form-item label="报修人:" prop="repairPersonName">
        <el-input :model-value="state.form.repairPersonName" readonly />
        
      </el-form-item>
      <el-form-item label="报修人电话:" prop="repairPersonTel">
        <el-input :model-value="state.form.repairPersonTel" readonly />
      </el-form-item>
      <el-form-item label="报修项目:" prop="repairProjectName">
        <el-input :model-value="state.form.repairProjectName" readonly />
      </el-form-item>
      <el-form-item label="情况描述:">
        <el-input :model-value="state.form.remark" type="textarea" :rows="4" readonly />
      </el-form-item>
      <div class="url">
        <el-form-item label="附件：">
          <el-image style="width: 100px; height: 100px" :src="state.form.resourceUrl" :preview-src-list="[state.form.resourceUrl]" :initial-index="0" fit="cover"></el-image>
        </el-form-item>
      </div>
    </el-form>
    <template #footer>
      <span class="dialog-footer" v-if="type!=='details'">
        <el-button @click="handleClose" size="mini">取消</el-button>
        <el-button  type="primary" @click="submit" size="mini">完成</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { ElDialog, ElButton, ElForm, ElFormItem, ElInput, ElImage, ElMessage,  } from "element-plus"
import { reactive,watch } from 'vue'
import { handleRepair } from "@/applications/eccard-dorm/api.js"

export default {
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    rowData: {
      type: String,
      default: ""
    }
  },
  components: {
    ElDialog,
    ElButton,
    ElForm,
    ElFormItem,
    ElInput,
    ElImage,
  },
  setup(props, context) {
    const state = reactive({
      form: {
        resourceUrl: '',
      },
      loading: false
    })
    watch(()=>props.modelValue,val=>{
      if(val){
        let { id,repairProjectCode,repairPersonId,repairProjectName,areaId,unitName,areaName,buildId,buildName,unitNum,floorNum,floorName,roomId,roomName,repairPersonName,repairPersonTel,repairProjectId,remark,resourceUrl,repairStatus } = JSON.parse(JSON.stringify(props.rowData))
          state.form={ id,repairProjectCode,repairPersonId,repairProjectName,unitName,areaId,areaName,buildId,buildName,unitNum,floorNum,floorName,roomId,roomName,repairPersonName,repairPersonTel,repairProjectId,remark,resourceUrl,repairStatus }
        console.log(state.form,1)
      }
    })
    const handleClose = () => {
      context.emit('update:modelValue', false)
    }
    const submit = () => { 
      handleRepair(props.rowData.id).then((res)=>{
        console.log(res)
        if(res.code===0){
          ElMessage.success(res.message)
          context.emit('update:modelValue',true)
        }
      })
    }
    return {
      state,
      submit,
      handleClose,
    }
  }
}
</script>

<style lang="scss" scoped>
.url {
  width: 507px;
}
:deep(.el-textarea__inner) {
  width: 506px;
}
</style>