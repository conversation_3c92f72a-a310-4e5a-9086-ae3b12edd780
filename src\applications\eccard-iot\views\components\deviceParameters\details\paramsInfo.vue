<template>
  <component :is='component'></component>
</template>
<script>
import { reactive } from '@vue/reactivity'
import { useStore } from "vuex"
import machineParameters from "./components/machineParameters"
import terminalDeviceParameters from "./components/terminalDeviceParameters"
import waterParameters from "./components/waterParameters"
import faceParameters from "./components/faceParameters"
import saveWaterParameters from "./components/saveWaterParameters"
import infiniteWaterParams from "./components/infiniteWaterParameters"
import { computed } from '@vue/runtime-core'

export default {
  components: {
    machineParameters, terminalDeviceParameters, waterParameters, faceParameters,saveWaterParameters,infiniteWaterParams
  },
  setup() {
    const store = useStore()
    const state = reactive({

    })
    const component = computed(() => {
      return store.state.app.activeTab
    })
    /* onMounted(()=>{
      store.dispatch("deviceParameters/getParams",store.state.deviceParameters[store.state.app.activeTab].selectRow.id);
    }) */
    return {
      state,
      component
    }
  }
}
</script>