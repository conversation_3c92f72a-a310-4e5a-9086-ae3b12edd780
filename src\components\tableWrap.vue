<template>
  <div class="kade-table-wrap">
    <div class="table-wrap-head">
      <div class="title">
        <i v-if="icon != 'none'" :class="icon || 'el-icon-liebiaolist46'"></i>
        <span class="text">{{ title || $store.getters['app/currentTab']?.options?.menuName }}</span>
      </div>
      <div class="btns">
        <slot name="extra"></slot>
      </div>
    </div>
    <slot></slot>
    <div class="pagination" v-if="$slots.pagination">
      <slot name="pagination"></slot>
    </div>
  </div>
</template>
<script>
// 通用表格包裹组件
export default {
  name: 'kade-table-wrap',
  props: {
    title: String,
    icon: {
      type: String,
      deafult: ""
    }
  }
}
</script>
<style lang="scss">
.kade-table-wrap {
  position: relative;
  padding-bottom: 10px;
  box-sizing: border-box;

  .el-table {
    border-left: none;

    tr>th:last-child,
    tr>td:last-child {
      border-right: none !important;
    }

    .el-table--border,
    .el-table--group {
      border: none;
    }

    &::after {
      background-color: transparent;
    }
  }

  .pagination {
    padding-right: 20px;
    box-sizing: border-box;
  }

  .table-wrap-head {
    width: 100%;
    height: 50px;
    display: flex;
    justify-content: space-between;
    padding: 0 20px;
    box-sizing: border-box;

    .title {
      height: 50px;
      display: flex;
      justify-content: flex-start;
      align-items: center;

      i {
        margin-right: 10px;
        font-size: 17px;
        cursor: pointer;
      }
    }

    .btns {
      display: flex;
      justify-content: flex-end;
      align-items: center;
    }
  }

  border-radius: 5px;
  border: 1px solid $border-color;
}
</style>