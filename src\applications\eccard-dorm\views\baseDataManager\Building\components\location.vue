<template>
  <el-dialog :model-value="dialogLocation" title="位置选择" width="90%" :before-close="handleClose">
    <div id="localtion-box" style="height:70vh">
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose" size="small">取消</el-button>
        <el-button type="primary" @click="submit" size="small">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { ElDialog, ElButton } from "element-plus"
import { reactive, watch, nextTick } from 'vue'
export default {
  props: {
    dialogLocation: {
      type: Boolean,
      default: false,
    },
    latAndLng: {
      type: String,
      default: "",
    }
  },
  components: {
    ElDialog,
    ElButton
  },
  setup(props, context) {
    const state = reactive({
      point: { lng: 104.04263635868074, lat: 30.556100647961866 }
    });
    watch(() => props.dialogLocation, val => {
      if (val) {
        if (props.latAndLng.includes(",")) {
          state.point.lng = props.latAndLng.split(",")[0]
          state.point.lat = props.latAndLng.split(",")[1]
        } else {
          state.point = { lng: 104.04263635868074, lat: 30.556100647961866 }
        }

        nextTick(() => {
          loadMapScript(); // 加载百度地图资源
        })
      }
    })
    // 初始化地图
    const init = () => {
      let Bmap = window.BMap; // 注意要带window，不然会报错（注意官方api,会有改动，之前是Bmap,后面3.0版本改为了BMap,最好查文档或者打印一下window）
      var map = new Bmap.Map("localtion-box"); // allmap必须和dom上的id一直
      var cityCtrl = new Bmap.CityListControl();  // 添加城市列表控件
      map.addControl(cityCtrl);
      map.centerAndZoom(
        new Bmap.Point(state.point.lng, state.point.lat),
        13
      ); // 初始化地图,设置中心点坐标和地图级别
      map.setCurrentCity("成都");
      map.enableScrollWheelZoom(true);
      var geoc = new Bmap.Geocoder();
      var marker
      var label
      var point = new Bmap.Point(state.point.lng, state.point.lat)
      marker = new Bmap.Marker(point);// 创建标注
      geoc.getLocation(point, function (rs) {
        label = new Bmap.Label(`${rs.address},经度:${state.point.lng},纬度:${state.point.lat}`, {       // 创建文本标注
          position: state.point,                          // 设置标注的地理位置
          offset: new Bmap.Size(10, -20)           // 设置标注的偏移量
        })
        map.addOverlay(label);
        map.addOverlay(marker);
      })
      map.addEventListener('click', function (e) {
        if (marker) {
          map.removeOverlay(marker);
        }
        if (label) {
          map.removeOverlay(label);
        }
        point = new Bmap.Point(e.point.lng, e.point.lat);
        marker = new Bmap.Marker(point);// 创建标注
        geoc.getLocation(point, function (rs) {
          label = new Bmap.Label(`${rs.address},经度:${e.point.lng},纬度:${e.point.lat}`, {       // 创建文本标注
            position: point,                          // 设置标注的地理位置
            offset: new Bmap.Size(10, -20)           // 设置标注的偏移量
          })
          map.addOverlay(label);
        })
        map.addOverlay(marker);
        state.point = point
      });
    };
    const loadMapScript = () => {
      // 此处在所需页面引入资源就是，不用再public/index.html中引入
      var script = document.createElement("script");
      script.type = "text/javascript";
      script.className = "loadmap"; // 给script一个类名
      script.src =
        "https://api.map.baidu.com/getscript?v=3.0&ak=GwcDO9hqGccPhBxdQ3WGGL62ZEESTu5E";
      // 此处需要注意：申请ak时，一定要应用类别一定要选浏览器端，不能选服务端，不然地图会报ak无效
      script.onload = () => {
        // 使用script.onload，待资源加载完成，再初始化地图
        init();
      };
      let loadmap = document.getElementsByClassName("loadmap");
      if (loadmap) {
        // 每次append script之前判断一下，避免重复添加script资源标签
        for (var i = 0; i < loadmap.length; i++) {
          document.body.removeChild(loadmap[i]);
        }
      }

      document.body.appendChild(script);
    };
    const submit = () => {
      context.emit("close", state.point)
    }
    const handleClose = () => {
      context.emit("close", false)
    };
    return {
      state,
      submit,
      handleClose,
    }
  }
}
</script>
<style lang="scss" scoped>
:deep(.el-icon-close:before) {
  display: none;
}
:deep(.el-dialog__header) {
  border-bottom: 0;
}
:deep(.el-dialog) {
  border-radius: 0;
}
</style>