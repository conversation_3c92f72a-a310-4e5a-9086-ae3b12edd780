<template>
  <el-dialog :model-value="isShow" title="无线智能卡节水控制器详情" width="80%" :before-close="beforeClose" :close-on-click-modal="false">
    <kade-tab-wrap :tabs="tabs" style="margin-top: 20px" v-model="state.tab">
      <template #jbxx>
        <kade-basic-params :data="data" />
      </template>
      <template #sbcs>
        <kade-device-params :data="data" />
      </template>
      <template #ghjl>
        <kade-record :data="data" />
      </template>
    </kade-tab-wrap>
  </el-dialog>
</template>
<script>
import { ElDialog } from "element-plus"
import basicParams from "./basicParams"
import deviceParams from "./deviceParams"

import record from "./record"
import { reactive } from '@vue/reactivity'
const tabs = [
  {
    name: "jbxx",
    label: "基本信息",
  },
  {
    name: "sbcs",
    label: "设备参数",
  },
  {
    name: "ghjl",
    label: "更换记录",
  },
];
export default {
  components: {
    ElDialog,
    "kade-basic-params": basicParams,
    "kade-device-params": deviceParams,
    "kade-record": record,
  },
  props: {
    isShow: {
      type: Boolean,
      default: false
    },
    data: {
      type: String,
      default: ""
    }
  },
  setup(props, context) {
    const state = reactive({
      tab: "jbxx"
    })
    const beforeClose = () => {
      context.emit("close", false)
      state.tab = "jbxx"
    }
    
    return {
      tabs,
      state,
      beforeClose
    }
  }
}
</script>
<style lang="scss" scoped>
</style>