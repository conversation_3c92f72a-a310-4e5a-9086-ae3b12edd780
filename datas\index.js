const path = require('path');
const fs = require('fs');
const uriPrefix = '/eccard/test';
module.exports = function (app) {
    ['get', 'put', 'delete', 'post'].forEach(method => {
        app[method](`${uriPrefix}/*`, (req, res) => {
            const dataPath = path.resolve(__dirname, `./${req.url.replace(uriPrefix, '')}.json`);
            if(fs.existsSync(dataPath)) {
                res.json(JSON.parse(fs.readFileSync(dataPath, 'utf8')));
            } else {
                res.json({});
            }
        });
    });
}