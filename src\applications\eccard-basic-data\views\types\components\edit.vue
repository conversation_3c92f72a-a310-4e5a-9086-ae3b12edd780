<template>
  <kade-modal v-bind="attrs" :modelValue="modelValue" @update:modelValue="update" content-class="types">
    <el-form v-loading="state.loading" ref="formRef" :label-width="labelWidth" :rules="rules" :model="state.model" size="small">
      <el-form-item label="卡类名称:" prop="cardTypeName">
        <el-input placeholder="请输入" v-model="state.model.cardTypeName" />
      </el-form-item>
      <el-form-item label="启用状态:" prop="enableStatus">
        <el-switch
          v-model="state.model.enableStatus"
          :active-color="themes.primaryColor"
          :inactive-color="themes.dangerColor"
          active-value="ENABLE_TRUE"
          inactive-value="ENABLE_FALSE"
          inactive-text="停用"
          active-text="启用"
        >
        </el-switch>                
      </el-form-item>
      <el-form-item label="是否启用押金:" prop="pressMoneyEnable">
        <el-switch
          v-model="state.model.pressMoneyEnable"
          :active-color="themes.primaryColor"
          :inactive-color="themes.dangerColor"
          active-value="ENABLE_TRUE"
          inactive-value="ENABLE_FALSE"
          inactive-text="停用"
          active-text="启用"          
        >
        </el-switch>               
      </el-form-item>
      <el-form-item label="押金:" prop="pressMoney" v-if="state.model.pressMoneyEnable === 'ENABLE_TRUE'">
        <el-input-number :min="0" :max="9999999999" :precision="2" :step="0.1" v-model="state.model.pressMoney" />&nbsp;&nbsp;&nbsp;元
      </el-form-item> 
      <el-form-item label="是否启用有效期:">
        <el-switch
          v-model="state.model.timeEnable"
          :active-color="themes.primaryColor"
          :inactive-color="themes.dangerColor"
          active-value="ENABLE_TRUE"
          inactive-value="ENABLE_FALSE"
          inactive-text="停用"
          active-text="启用"          
        >
        </el-switch>               
      </el-form-item>
      <el-form-item label="有效期:" prop="timeCanUse" v-if="state.model.timeEnable === 'ENABLE_TRUE'">
        <el-input-number step-strictly :min="1" :max="50" :step="1" v-model="state.model.timeCanUse" />&nbsp;&nbsp;&nbsp;年
      </el-form-item>
      <el-form-item label="是否允许发副卡:">
        <el-switch
          v-model="state.model.allowSecondCard"
          :active-color="themes.primaryColor"
          :inactive-color="themes.dangerColor"
          active-value="TRUE"
          inactive-value="FALSE"
          inactive-text="否"
          active-text="是"          
        >
        </el-switch>               
      </el-form-item>
      <el-form-item label="工本费:" prop="ctFlatCost">
        <el-input-number :min="0" :max="9999999999" :precision="2" :step="0.1" v-model="state.model.ctFlatCost" />&nbsp;&nbsp;&nbsp;元
      </el-form-item>   
      <el-form-item label="折扣率:" prop="partnerRate">
        <el-input-number :min="0" :precision="2" :step="0.01" v-model="state.model.partnerRate" />
      </el-form-item>                   
      <el-form-item label="备注:" prop="remark">
        <el-input maxlength="140" type="textarea" placeholder="请输入" v-model="state.model.remark" show-word-limit />
      </el-form-item>      
    </el-form>
    <template #footer>
      <el-button icon="el-icon-circle-close" @click="cancel" size="small">取消</el-button>
      <el-button icon="el-icon-circle-check" :loading="state.btnLoading" @click="submit" size="small" type="primary">保存</el-button>
    </template>
  </kade-modal>
</template>
<script>
import Modal from '@/components/modal';
import { computed, reactive, ref, watch } from 'vue';
import { 
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElSwitch,
  ElInputNumber,
  ElMessage,
  } from 'element-plus';
import { getCardTypeDetailsByCardTypeID, updateCardTypeDetailsByCardTypeID } from '@/applications/eccard-basic-data/api';
const getDefaultModel = () => ({
  cardTypeName: '',
  enableStatus: 'ENABLE_FALSE',
  pressMoney: 0,
  pressMoneyEnable: 'ENABLE_FALSE',
  remark: '',
  timeCanUse: 1,
  timeEnable: 'ENABLE_FALSE',
});
export default {
  emits: ['update:modelValue', 'change'],
  props: {
    title: {
      type: String,
      default: ''
    },
    modelValue: {
      type: Boolean,
      default: false,
    },
    id: {
      type: [String, Number],
      default: null,
    }
  },
  components: {
    'kade-modal': Modal,
    'el-button': ElButton,
    'el-form': ElForm,
    'el-form-item': ElFormItem,
    'el-input': ElInput,
    'el-switch': ElSwitch,
    'el-input-number': ElInputNumber,
  },
  setup(props, context) {
    const formRef = ref(null);
    const state = reactive({
      model: getDefaultModel(),
      btnLoading: false,
      loading: false,
    });
    const validatePressMoney = (rule, value, callback) => {
      if (state.model.pressMoneyEnable === 'ENABLE_TRUE' && value !== 0 && !value) {
        callback('请输入押金');
      } else {
        callback();
      }
    }
    const validateTimeCanUse = (rule, value, callback) => {
      if (state.model.timeEnable === 'ENABLE_TRUE' && !value) {
        callback('请输入卡有效期');
      } else {
        callback();
      }      
    }    
    const rules = {
      cardTypeName: [
        { required: true, message: '请输入卡类型名称' },
        { max: 20, message: '卡类型名称不能超过20个字符' }
      ],
      pressMoney: [
        { validator: validatePressMoney }
      ],
      timeCanUse: [
        { validator: validateTimeCanUse }
      ],
      ctFlatCost:[
        { required: true, message: '请输入工本费' },
      ],
      partnerRate:[
        { required: true, message: '请输入折扣率' },
      ],
      remark: [
        { max: 140, message: '不能超过140字符' }
      ]
    };
    const cancel = () => {
      formRef.value?.resetFields();
      context.emit('update:modelValue', false);
    }
    const submit = () => {
      formRef.value?.validate(async (valid) => {
        if(valid) {
          try {
            state.btnLoading = true;
            const { message } = await updateCardTypeDetailsByCardTypeID(state.model);
            context.emit('change');
            context.emit('update:modelValue', false);
            ElMessage.success(message);            
          } catch(e) {
            throw new Error(e.message);
          } finally {
            state.btnLoading = false;
          }
        }
      });      
    }
    const attrs = computed(() => {
      // eslint-disable-next-line no-unused-vars
      const { modelValue, id, ...attrs } = props;
      return attrs;
    });
    const update = (v) => {
      context.emit('update:modelValue', v);
    }
    const loadInfo = async () => {
      try {
        state.loading = true;
        const { data } = await getCardTypeDetailsByCardTypeID({ cardTypeID: props.id });
        state.model = {
          cardTypeName: data.ctName,
          enableStatus: data.ctState,
          pressMoney: parseFloat(data.ctDeposit),
          pressMoneyEnable: data.ctDepositEnable,
          remark: data.ctRemark,
          ctFlatCost:parseFloat(data.ctFlatCost),
          timeCanUse: parseFloat(data.ctValidDate),
          timeEnable: data.ctValidDateEnable,
          cardTypeId: data.cardTypeId,
          allowSecondCard:data.allowSecondCard,
          partnerRate:data.partnerRate
        };
      } catch(e) {
        throw new Error(e.message);
      } finally {
        state.loading = false;
      }
    }
    watch(() => props.modelValue, (n) => {
      if(n) {
        if (props.id) {
          loadInfo();
        } else {
          state.model = getDefaultModel();
        }
      }
    });
    return {
      attrs,
      update,
      formRef,
      cancel,
      submit,
      rules,
      labelWidth: THEMEVARS.formLabelWidth,
      state,
      themes: THEMEVARS,
    }
  }
}
</script>

<style lang="scss" scoped>
.kade-dialog .modal-content.types{
  max-height: 420px;
}  
:deep(.el-input-number) {
  width: 180px
}
</style>