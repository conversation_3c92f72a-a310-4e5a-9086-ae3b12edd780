<template>
  <div class="wallet-manager">
    <kade-route-card style="height: auto">
      <kade-table-wrap title="钱包列表">
        <el-divider></el-divider>
        <div class="padding-box transaction-info-report">
          <el-table style="width: 100%" :data="state.walletList" v-loading="state.loading" highlight-current-row border stripe @row-click="rowClick">
            <el-table-column v-for="(item, index) in state.columns" :key="index" :label="item.label" :prop="item.prop" align="center">
              <template v-if="item.filter || item.isEdit" #default="scope">
                <div v-if="item.filter">
                  {{
                    filterDictionary(scope.row.walletType, state.walletTypeList)
                  }}
                </div>
                <el-switch @click="switchChange(scope.row)" v-if="item.isEdit" :model-value="scope.row.useStatus" active-value="TRUE" inactive-value="FALSE" />
              </template>
            </el-table-column>
          </el-table>
        </div>
      </kade-table-wrap>
      <kade-tab-wrap extra :tabs="tabs" v-model="state.tab" style="margin-top: 20px" @active="active">
        <template #cspz>
          <kade-params />
        </template>
        <template #czfs>
          <kade-recharge-type />
        </template>
        <template #xffs>
          <kade-consume-type />
        </template>

        <template #extra v-if="state.tab=='cspz'&&!isEditButton">
          <el-button :disabled="isDisabled" @click="editParams" type="primary" size="small" icon="el-icon-circle-plus-outline">编辑参数配置</el-button>
        </template>
      </kade-tab-wrap>
    </kade-route-card>
  </div>
</template>
<script>
import { computed, onMounted, reactive } from "vue";
import { useStore } from "vuex";

import {
  ElDivider,
  ElTable,
  ElTableColumn,
  ElSwitch,
  ElMessage,
  ElButton,
} from "element-plus";
import {
  getWalletList,
  upateWalletStatusEntity,
} from "@/applications/eccard-finance/api";
import { filterDictionary } from "@/utils/index";
import { useDict } from "@/hooks/useDict.js";
import walletParams from "./walletParams.vue";
import consumeType from "./consumeType.vue";
import rechargeType from "./rechargeType.vue";

const tabs = [
  {
    name: "cspz",
    label: "参数配置",
  },
  {
    name: "czfs",
    label: "充值方式",
  },
  {
    name: "xffs",
    label: "消费方式",
  },
];

export default {
  components: {
    ElDivider,
    ElTable,
    ElTableColumn,
    ElSwitch,
    ElButton,
    "kade-params": walletParams,
    "kade-consume-type": consumeType,
    "kade-recharge-type": rechargeType,
  },
  setup() {
    const store = useStore();
    const state = reactive({
      tab: "cspz",
      loading: false,
      columns: [
        {
          label: "钱包名称",
          prop: "walletName",
        },
        {
          label: "钱包类型",
          prop: "walletType",
          filter: true,
        },
        {
          label: "是否启用",
          prop: "useStatus",
          isEdit: true,
        },
      ],
      walletList: [],
      walletTypeList: useDict("PERSON_WALLET_TYPE"),
    });

    const isEditButton = computed(() => {
      return store.state.walletManagerData.isEditButton;
    });

    const isDisabled = computed(() => {
      return store.state.walletManagerData.walletParam ? false : true;
    });

    //获取钱包列表
    const queryWalletList = async () => {
      state.loading = true;
      try {
        let { data, code } = await getWalletList();
        if (code === 0) {
          state.walletList = data;
        }
      } finally {
        state.loading = false;
      }
    };

    //修改钱包状态
    const switchChange = async (row) => {
      let params = {
        useStatus: row.useStatus === "TRUE" ? "FALSE" : "TRUE",
        walletId: Number(row.walletId),
      };
      let { message, code } = await upateWalletStatusEntity(params);
      if (code === 0) {
        ElMessage.success(message);
        queryWalletList();
      }
    };

    const rowClick = (row) => {
      store.commit("walletManagerData/updateState", {
        key: "selectRow",
        payload: row,
      });
    };

    const editParams = () => {
      store.commit("walletManagerData/updateState", {
        key: "isEditButton",
        payload: true,
      });
    };

    const active = (e) => {
      store.commit("walletManagerData/updateState", {
        key: "isEditButton",
        payload: false,
      });
      if (e.props.name === "cspz") {
        store.dispatch("walletManagerData/getWalletParam");
      } else if (e.props.name === "czfs") {
        store.dispatch("walletManagerData/getWalletTrade", { tradeType: 101 });
      } else if (e.props.name === "xffs") {
        store.dispatch("walletManagerData/getWalletTrade", { tradeType: 201 });
      }
    };

    onMounted(() => {
      queryWalletList();
    });
    return {
      filterDictionary,
      tabs,
      state,
      isEditButton,
      isDisabled,
      editParams,
      switchChange,
      rowClick,
      active,
    };
  },
};
</script>
<style lang="scss" scoped>
.wallet-manager {
  .el-divider--horizontal {
    margin: 0;
  }
}
</style>
