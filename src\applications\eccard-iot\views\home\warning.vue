<template>
  <kade-route-card>
    <template #header>
      <span class="header-title">设备分类统计</span>
    </template>
    <el-table :data="[1, 2, 3, 4, 5, 6, 7, 8]" height="260px" style="width: 100%">
      <el-table-column show-overflow-tooltip align="center" prop="date" label="预警日期"></el-table-column>
      <el-table-column show-overflow-tooltip align="center" prop="date" label="设备名称"></el-table-column>
      <el-table-column show-overflow-tooltip align="center" prop="date" label="设备机号"></el-table-column>
      <el-table-column show-overflow-tooltip align="center" prop="date" label="设备类型"></el-table-column>
      <el-table-column show-overflow-tooltip align="center" prop="date" label="设备状态"></el-table-column>
      <el-table-column show-overflow-tooltip align="center" prop="date" label="预警原因"></el-table-column>
    </el-table>
  </kade-route-card>
</template>
<script>
import { ElTable, ElTableColumn } from "element-plus"
export default {
  components: {
    ElTable, ElTableColumn
  },
  setup() {
    return {

    }
  }
}
</script>
<style lang="scss" scoped>
:deep(.el-table) {

  th,
  td {
    padding: 10px 0;
    color: #999999;
    border: none;
  }
  tr{
    background-color: #ffecec;
  }
  .el-table__body-wrapper {
    background: #ffecec;

  }
}
</style>