<template>
  <el-dialog :model-value="isShow" width="90%" :before-close="beforeClose" :close-on-click-modal="false">
    <template #title>
      <div class="dialog-title">
        <div class="detail-title">新闻详情</div>
        <div
          class="detail-status"
          :style="{
            color: details.status == 'FALSE' ? '#02D200' : '#3399FF',
            'border-color': details.status == 'FALSE' ? '#02D200' : '#3399FF',
          }"
        >
          {{ details.status == "FALSE" ? "即将发布" : "已发布成功" }}
        </div>
      </div>
    </template>
    <div style="padding-top: 20px">
      <el-form size="mini" label-width="120px">
        <el-row>
          <el-col :span="8">
            <el-form-item label="消息类型:">
              <!-- <el-input readonly :model-value="filterDictionary(details.messType, messTypeList)"></el-input> -->
              <el-input readonly :model-value="details.messType"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label=" 发布人:">
              <el-input readonly :model-value="details.publishUser"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="发布时间:">
              <el-input readonly :model-value="details.publishTime"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="消息标题:">
          <el-input readonly :model-value="details.messTitle" :maxlength="30"></el-input>
        </el-form-item>
        <el-form-item label="消息内容:">
          <div class="news-text" v-html="details.messContent"></div>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button type="primary" v-if="details.status == 'FALSE'" @click="edit()" size="mini">编辑</el-button>
        <el-button @click="beforeClose" size="mini">返回</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script>
import { reactive } from "vue";

import { filterDictionary } from "@/utils/index.js";
import { useDict } from "@/hooks/useDict";
import {
  ElDialog,
  ElRow,
  ElCol,
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
} from "element-plus";

export default {
  components: {
    ElDialog,
    ElRow,
    ElCol,
    ElButton,
    ElForm,
    ElFormItem,
    ElInput,
  },
  props:{
    isShow:{
      type:Boolean,
      default:false,
    },
    details:{
      type:Object,
      default:null
    }
  },
  setup(props,context) {
    const messTypeList = useDict("SYS_MESSAGE_TYPE"); //消息类型
    const state = reactive({
      isTailor: false,
      imgFile: "",
      form: {
        newsReceiver: [],
      },
    });


    const beforeClose = () => {
      context.emit("close",false)
    };

    return {
      filterDictionary,
      messTypeList,
      state,
      beforeClose,
    };
  },
};
</script>
<style lang="scss" scoped>
.news-text {
  box-sizing: border-box;
  width: 100%;
  height: 300px;
  padding: 20px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow-y: scroll;
}
.dialog-title {
  display: flex;
  align-items: center;
  .detail-status {
    border: 1px solid #000;
    border-radius: 4px;
    margin-left: 20px;
    padding: 3px;
    font-size: 12px;

  }
}
</style>