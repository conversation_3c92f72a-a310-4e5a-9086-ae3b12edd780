<template>
  <div class="AddBatchTerminal">
    <el-dialog :model-value="isShow" :title="title" width="70%" :before-close="beforeClose">
      <div class="dialog-box">
        <div class="title">设备基本信息</div>
        <el-divider></el-divider>
        <div class="padding-form-box">
          <el-form inline size="mini" ref="ruleForm" :model="state.form" label-width="120px" :rules="state.rules">
            <el-form-item label="终端型号：" prop="deviceModel">
              <el-select clearable v-model="state.form.deviceModel" placeholder="请选择">
                <el-option v-for="(item, index) in listData.modelList" :key="index" :label="item.productMode"
                  :value="item.productMode" @change="deviceModelChange">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="所属区域：" prop="areaId">
              <kade-area-select-tree style="width: 100%" :value="state.form.areaId" valueKey="id" :multiple="false"
                @valueChange="(val) => (state.form.areaId = val.id)" />
            </el-form-item>

            <el-form-item label="开始机号：" prop="startNo">
              <el-input placeholder="请输入" v-model="state.form.startNo" maxlength="9"></el-input>
            </el-form-item>
            <el-form-item label="结束机号：" prop="endNo">
              <el-input placeholder="请输入" v-model="state.form.endNo" maxlength="9"></el-input>
            </el-form-item>
            <el-form-item label="设备使用状态：" prop="deviceStatus">
              <el-select clearable v-model="state.form.deviceStatus" placeholder="请选择">
                <el-option v-for="(item, index) in listData.deviceStaticList" :key="index" :label="item.dictValue"
                  :value="item.dictCode">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="所属工作站：">
              <el-select clearable v-model="state.form.workstationId" placeholder="请选择">
                <el-option v-for="(item, index) in listData.workStationList" :key="index" :label="item.name"
                  :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="连接类型：">
              <el-select clearable v-model="state.form.deviceConnectType" placeholder="请选择">
                <el-option v-for="(item, index) in listData.deviceConnectTypeList" :key="index" :label="item.dictValue"
                  :value="item.dictCode">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="设备IP：">
              <el-input placeholder="请输入" v-model="state.form.deviceIp" maxlength="20"></el-input>
            </el-form-item>
            <el-form-item label="固件版本：">
              <el-select clearable v-model="state.form.deviceVersion" placeholder="请选择">
                <el-option v-for="(item, index) in state.deviceVersion" :key="index" :label="item.deviceVersionName"
                  :value="item.deviceVersionCode">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="参数名称：">
              <el-select clearable v-model="state.form.paramId" placeholder="请选择">
                <el-option v-for="(item, index) in listData.deviceParamList" :key="index" :label="item.name"
                  :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="支付方式：">
              <el-select multiple clearable v-model="state.form.devicePayType" placeholder="请选择">
                <el-option v-for="(item, index) in listData.tradeModeList"  :key="index" :label="item.name"
                  :value="item.code">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="设备SN号：">
              <el-input placeholder="请输入" v-model="state.form.deviceSn" maxlength="20"></el-input>
            </el-form-item>
          </el-form>
          <el-form size="mini" label-width="120px">
            <el-form-item label="设备位置：">
              <el-input style="width: 100%" placeholder="请输入" v-model="state.form.devicePosition" maxlength="30">
              </el-input>
            </el-form-item>
            <el-form-item label="备注：">
              <el-input type="textarea" placeholder="请输入备注" v-model="state.form.deviceRemarks" maxlength="200">
              </el-input>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <div class="dialog-box">
        <div class="title">设备个性化信息</div>
        <el-divider></el-divider>
        <div class="padding-form-box">
          <el-form inline size="mini" label-width="120px">
            <el-form-item label="所属商户：">
              <el-select clearable v-model="state.form.merchantId" placeholder="请选择">
                <el-option v-for="(item, index) in listData.merchantList" :key="index" :label="item.merchantName"
                  :value="item.merchantId">
                </el-option>
              </el-select>
            </el-form-item>
            <!-- <el-form-item label="终端类型：">
              <el-select
                clearable
                v-model="state.form.deviceType"
                placeholder="请选择"
              >
                <el-option
                  v-for="(item, index) in listData.typelList"
                  :key="index"
                  :label="item.name"
                  :value="item.code"
                >
                </el-option>
              </el-select>
            </el-form-item> -->
            <el-form-item label="设备验证码：">
              <el-input placeholder="请输入" v-model="state.form.verifyCode" maxlength="20"></el-input>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="submit()" type="primary" size="mini">保&nbsp;&nbsp;存</el-button>
          <el-button @click="beforeClose()" size="mini">返&nbsp;&nbsp;回</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script>
import { reactive, ref, watch,nextTick } from "vue";
import {
  addBatch,
  //  getDeviceVersion
} from "@/applications/eccard-iot/api";
import {
  ElDialog,
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElDivider,
  ElMessage,
} from "element-plus";
import areaSelectTree from "@/components/tree/areaSelectTree.vue";
export default {
  components: {
    "el-dialog": ElDialog,
    "el-button": ElButton,
    "el-input": ElInput,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    "el-select": ElSelect,
    "el-option": ElOption,
    ElDivider,
    "kade-area-select-tree": areaSelectTree,
  },
  props: {
    title: {
      types: String,
      default: "",
    },
    isShow: {
      types: Boolean,
      default: false,
    },
    listData: {
      types: Object,
      default: {},
    },
  },
  setup(props, context) {
    let ruleForm = ref(null);
    const state = reactive({
      form: {},
      rules: {
        deviceModel: [
          {
            required: true,
            message: "请选择终端型号",
            trigger: "change",
          },
        ],
        areaId: [
          {
            required: true,
            message: "请选择所属区域",
            trigger: "change",
          },
        ],
        startNo: [
          {
            required: true,
            message: "请输入开始机号",
            trigger: "blur",
          },
          {
            pattern: /^[0-9]*$/,
            message: "请输入数字",
          },
        ],
        endNo: [
          {
            required: true,
            message: "请输入结束机号",
            trigger: "blur",
          },
          {
            pattern: /^[0-9]*$/,
            message: "请输入数字",
          },
        ],
        deviceStatus: [
          {
            required: true,
            message: "请选择设备使用状态",
            trigger: "change",
          },
        ],
      },
    });
    watch(
      () => props.isShow,
      (val) => {
        if (val) {
          nextTick(() => {
            ruleForm.value.clearValidate()
          })
        }
      }
    );

    const beforeClose = () => {
      context.emit("off", false);
      state.form = {};
    };
    /*     const deviceModelChange = async (e) => {
          let { data } = await getDeviceVersion({ deviceModelCode: e });
          state.deviceVersion = data;
        }; */
    const submit = () => {
      ruleForm.value.validate(async (valid) => {
        if (valid) {
          state.form.deviceType = "智能消费终端";
          let data = { ...state.form }
          data.devicePayType = data.devicePayType.join(",")
          let { code, message } = await addBatch(data);
          if (code === 0) {
            ElMessage.success(message);
            context.emit("success", true);
          }
        } else {
          return false;
        }
      });
    };
    return {
      state,
      ruleForm,
      submit,
      beforeClose,
      // deviceModelChange,
    };
  },
};
</script>
<style lang="scss" scoped>
.el-divider--horizontal {
  margin: 0 0 10px;
}

.dialog-box {
  border: 1px solid #dcdfe6;
  border-radius: 5px;
  margin: 10px;

  .title {
    padding: 10px;
  }
}

:deep(.el-dialog__header) {
  border-bottom: 1px solid #efefef;
}

:deep(.el-overlay) {
  position: absolute;
  background-color: rgba(0, 0, 0, 0.3);
}

:deep(.el-dialog__footer) {
  text-align: center;
}

:deep(.el-form-item__content) {
  .el-select {
    width: 178px;
  }

  .el-textarea {
    width: 100%;

    .el-textarea__inner {
      width: 100%;
    }
  }
}
</style>