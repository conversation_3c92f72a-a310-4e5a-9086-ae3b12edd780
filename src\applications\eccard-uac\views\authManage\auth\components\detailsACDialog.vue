<template>
  <el-dialog :model-value="modelValue" title="门禁详情" width="1700px" :before-close="handleClose"
    :close-on-click-modal="false">
    <div style="padding:20px 0 20px">
      <kade-table-wrap title="门禁信息">
        <el-divider></el-divider>
        <div class="padding-box box">
          <el-table :data="rowList" border>
            <el-table-column label="门禁分组" prop="groupName" align="center"></el-table-column>
            <el-table-column label="门禁区域" prop="areaName" align="center"></el-table-column>
            <el-table-column label="设备类型" prop="deviceTypeName" align="center"></el-table-column>
            <el-table-column label="设备名称" prop="deviceName" align="center"></el-table-column>
          </el-table>
        </div>
      </kade-table-wrap>
      <kade-table-filter @search="handleSearch" @reset="handleReset">
        <el-form inline label-width="100px" size="mini">
          <el-form-item label="组织机构">
            <kade-dept-select-tree style="width: 100%" :value="state.form.deptPath" valueKey="deptPath"
              :multiple="false" @valueChange="(val) => (state.form.deptPath = val.deptPath)" />
          </el-form-item>
          <el-form-item label="身份类别">
            <el-select clearable v-model="state.form.userRole">
              <el-option v-for="(item, index) in state.roleList" :key="index" :label="item.roleName" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="卡类">
            <el-select clearable v-model="state.form.cardType">
              <el-option v-for="(item, index) in state.cardTypeList" :key="index" :label="item.ctName"
                :value="item.ctCode"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="卡片类别">
            <el-select clearable v-model="state.form.cardCategory">
              <el-option v-for="(item, index) in cardList" :key="index" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="关键字">
            <el-input clearable v-model="state.form.keyWord" placeholder="请输入"></el-input>
          </el-form-item>
        </el-form>
      </kade-table-filter>
      <kade-table-wrap title="已授权用户" v-loading="state.loading">
        <template #extra>
          <el-button @click="delAuth" class="btn-purple" icon="el-icon-delete-solid" size="mini">删除权限</el-button>
        </template>
        <el-table :data="state.dataList" @selection-change="selectionChange" border>
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column show-overflow-tooltip label="用户编号" prop="userCode" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip label="用户名称" prop="userName" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip label="性别" prop="" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip label="组织机构" prop="deptName" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip label="身份类别" prop="" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip label="卡类" prop="ctCode" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip label="卡片类别" prop="cardCategory" align="center">
            <template #default="scope">
              {{ dictionaryFilter(scope.row.cardCategory) }}
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip label="卡号" prop="cardNo" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip label="时段" prop="authPeriod" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip label="是否启用一次刷卡限制" prop="oneCard" align="center">
            <template #default="scope">
              {{ dictionaryFilter(scope.row.oneCard + '') }}
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip label="授权时间" prop="authBeginTime" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip label="过期时间" prop="authEndTime" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip label="权限状态" prop="downStatus" align="center">
            <template #default="scope">
              {{ dictionaryFilter(scope.row.downStatus) }}
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination">
          <el-pagination v-model:currentPage="state.form.pageNum" v-model:page-size="state.form.pageSize"
            :page-sizes="[10, 20, 30, 40]" background layout="total, sizes, prev, pager, next, jumper"
            :total="state.total" @size-change="handleSizeChange" @current-change="handleCurrentChange">
          </el-pagination>
        </div>
      </kade-table-wrap>
    </div>
  </el-dialog>
</template>
<script>
import { ElDialog, ElButton, ElDivider, ElTable, ElTableColumn, ElForm, ElFormItem, ElInput, ElSelect, ElOption, ElPagination, ElMessageBox, ElMessage } from "element-plus"
import { reactive, watch, onMounted } from 'vue'
import { useDict } from "@/hooks/useDict.js";
import {
  getRolelist
} from "@/applications/eccard-basic-data/api";
import { getCardTypeList } from "@/applications/eccard-iot/api";
import { authManageDeviceDetails, authManageDel } from "@/applications/eccard-uac/api";
import deptSelectTree from "@/components/tree/deptSelectTree.vue";
export default {
  components: {
    ElDialog, ElButton, ElDivider, ElTable, ElTableColumn, ElForm, ElFormItem, ElInput, ElSelect, ElOption, ElPagination,
    "kade-dept-select-tree": deptSelectTree,
  },
  emits: ["update:modelValue"],
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    rowList: {
      type: Array,
      default: null
    }
  },
  setup(props, context) {
    const cardList = useDict("PERSON_CARD_CATEGORY")
    const state = reactive({
      loading: false,
      roleList: [],
      cardTypeList: [],
      selectList: [],
      form: {
        currentPage: 1,
        pageSize: 10,
      },
      dataList: [],
      total: 0
    })
    watch(() => props.modelValue, val => {
      if (val) {
        state.dataList = []
        state.total = 0
        state.form = {
          currentPage: 1,
          pageSize: 10,
        }
        getList()
      }
    })
    const getList = async () => {
      state.loading = true
      let params = {
        ...state.form,
        deviceId: props.rowList[0].deviceId
      }
      let { data: { list, total } } = await authManageDeviceDetails(params)
      state.dataList = list
      state.total = total
      state.loading = false
    }
    //获取卡片类型
    const queryCardTypeList = () => {
      getCardTypeList().then((res) => {
        state.cardTypeList = res.data;
      });
    };
    const queryRolelist = async () => {
      let { data } = await getRolelist();
      state.roleList = data;
    };
    const selectionChange = val => {
      state.selectList = val
    }
    const delAuth = () => {
      if (!state.selectList.length) {
        return ElMessage.error("请选择人员！")
      }
      ElMessageBox.confirm("确认删除?", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        let { code, message } = await authManageDel({ authIds: state.selectList.map(item => item.id) });
        if (code === 0) {
          ElMessage.success(message);
          state.form.currentPage = 1
          getList();
        }
      });
    }
    const handleSearch = () => {
      getList();
    }
    const handleReset = () => {
      state.form = {
        currentPage: 1,
        pageSize: 10,
      }
    }
    const handleClose = () => {
      context.emit("update:modelValue", false)
    }
    const handleCurrentChange = val => {
      state.form.currentPage = val
      getList()
    }
    const handleSizeChange = val => {
      state.form.currentPage = 1
      state.form.pageSize = val
      getList()
    }
    onMounted(() => {
      queryCardTypeList()
      queryRolelist()
    })
    return {
      cardList,
      state,
      selectionChange,
      delAuth,
      handleSearch,
      handleReset,
      handleClose,
      handleCurrentChange,
      handleSizeChange
    }
  }
}
</script>
<style lang="scss" scoped>
:deep(.kade-table-wrap) {
  margin-bottom: 20px;
}

.padding-box {
  padding: 10px 10px 0;
}

.box {
  .el-table {
    border-left: 1px solid #EBEEF5;
    border-right: 1px solid #EBEEF5;
  }
}

.el-divider--horizontal {
  margin: 0
}

:deep(.box) {
  padding: 10px 10px 0;

  .el-form {
    .el-select {
      width: 150px;
    }

    .el-input {
      width: 150px
    }
  }
}
</style>