<template>
  <div>
    <el-dialog
      :model-value="isPerson"
      title="人员清单"
      width="70%"
      :before-close="beforeClose"
      :close-on-click-modal="false"
    >
      <div>
        <div class="padding-form-box">
          <el-form inline size="small" label-width="100px">
            <el-form-item label="关键字">
              <el-input placeholder="请输入" v-model="state.form.keyWord"></el-input>
            </el-form-item>
            <el-form-item label="组织机构:">
              <kade-dept-select-tree
                style="width: 100%"
                :value="state.form.deptPath"
                valueKey="deptPath"
                :multiple="false"
                @valueChange="(val) => (state.form.deptPath = val.deptPath)"
              />
            </el-form-item> 
            <el-form-item label="身份类别:">
              <el-select clearable v-model="state.form.roleId" placeholder="请选择">
                <el-option v-for="(item, index) in roleList" :key="index" :label="item.roleName" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
            <el-button @click="getDataList()" size="small" type="primary" icon="el-icon-search">搜索</el-button>
          </el-form>
          <el-table
            style="width: 100%"
            :data="state.personList"
            v-loading="false"
            highlight-current-row
            border
            stripe
          >
            <el-table-column label="用户编号" prop="userCode" align="center"></el-table-column>
            <el-table-column label="姓名" prop="userName" align="center"></el-table-column>
            <el-table-column label="组织结构" prop="deptName" align="center"></el-table-column>
            <el-table-column label="身份类别" prop="roleName" align="center"></el-table-column>
            <el-table-column label="收费金额" prop="payAmount" align="center"></el-table-column>
            <el-table-column label="收费状态" prop="payStatus" align="center">
              <template #default="scope">
              {{dictionaryFilter(scope.row.payStatus)}}
              </template>
            </el-table-column>
          </el-table>
          <div class="pagination">
            <el-pagination
              background
              :current-page="state.form.currentPage"
              :page-size="state.form.pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :page-sizes="[6, 10, 20, 50, 100]"
              :total="state.total"
              @current-change="handlePageChange"
              @size-change="handleSizeChange"
            >
            </el-pagination>
          </div>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="beforeClose()" size="mini">关&nbsp;&nbsp;闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script>
import { reactive, computed, watch } from "vue";
import { useStore } from "vuex";
import { getUniListByPage } from "@/applications/eccard-finance/api";
import deptSelectTree from "@/components/tree/deptSelectTree.vue";
import {
  ElDialog,
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElTable,
  ElTableColumn,
  ElPagination,
} from "element-plus";
export default {
  components: {
    "el-dialog": ElDialog,
    "el-button": ElButton,
    "el-input": ElInput,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    "el-select": ElSelect,
    "el-option": ElOption,
    "el-table": ElTable,
    "el-table-column": ElTableColumn,
    "el-pagination": ElPagination,
    "kade-dept-select-tree": deptSelectTree,
  },
  setup() {
    const store = useStore();
    const state = reactive({
      form: {
        currentPage: 1,
        pageSize: 6,
        // projectId: store.state.chargeData.selectRow.projectId,
      },
      total: 0,
    });
    const isPerson = computed(() => {
      return store.state.chargeData.isPerson;
    });
    const roleList=computed(()=>{
      return store.state.chargeData.roleList
    })
    watch(
      () => store.state.chargeData.isPerson,
      (val) => {
        if (val) {
          state.form = {
            currentPage: 1,
            pageSize: 6,
            projectId: store.state.chargeData.selectRow.id,
          };
          getDataList();
        }
      }
    );
    const getDataList = () => {
      getUniListByPage(state.form).then((res) => {
        state.personList = res.data.list;
        state.total = res.data.total;
      });
    };

    const handlePageChange = (val) => {
      state.form.currentPage = val;
      getDataList();
    };
    const handleSizeChange = (val) => {
      state.form.currentPage = 1;
      state.form.pageSize = val;
      getDataList();
    };
    const beforeClose = () => {
      store.commit("chargeData/updateState", {
        key: "isPerson",
        payload: false,
      });
    };
    return {
      state,
      isPerson,
      roleList,
      getDataList,
      beforeClose,
      handlePageChange,
      handleSizeChange,
    };
  },
};
</script>
<style lang="scss" scoped> 
:deep(.el-dialog__header) {
  border-bottom: 1px solid #efefef;
}
:deep(.el-overlay) {
  position: absolute;
  background-color: rgba(0, 0, 0, 0);
}
:deep(.el-dialog__footer) {
  text-align: center;
}
</style>