<template>
  <div class="box">
    <el-table style="width: 100%" :data="state.personFamilyList" v-loading="state.loading" border stripe>
      <el-table-column label="姓名" prop="userName" align="center"></el-table-column>
      <el-table-column prop="userCode" label="用户编号" align="center">
      </el-table-column>
      <el-table-column label="关系" prop="userRelation" align="center"></el-table-column>
      <el-table-column label="账户状态" align="center">
        <template #default="scope">
          <el-popover placement="right" v-if="scope.row.acctStatus == null" :width="200" v-model="state.visible">
            <template #reference>
              <div class="green" @click="acctStatusEdit(scope.row, 'active')">激活</div>
            </template>
            <div style="margin-bottom: 10px">选择卡片类别</div>
            <el-select v-model="state.activeForm.acctType" placeholder="请选择" @change="activeChange">
              <el-option v-for="(item, index) in state.cardTypeList" :key="index" :label="item.ctName"
                :value="item.ctCode">
              </el-option>
            </el-select>
          </el-popover>
          <div @click="acctStatusEdit(scope.row, 'edit')" v-else>
            <el-switch v-model="scope.row.acctStatus" inline-prompt active-color="#13ce66" inactive-color="#ff4949"
              :active-value="'NORMAL_ACCOUNT'" :inactive-value="'FREEZE_ACCOUNT'" />
          </div>
        </template>
      </el-table-column>
      <el-table-column label="卡片类别" prop="acctTypeName" align="center"></el-table-column>
      <el-table-column label="卡片状态" align="center">
        <template #default="scope">
          {{ filterDictionary(scope.row.cardStatus, state.cardStatusList) }}
        </template>
      </el-table-column>
      <el-table-column label="联系方式" prop="userTel" align="center"></el-table-column>
    </el-table>
  </div>
</template>
<script>
import {
  ElTable,
  ElTableColumn,
  ElSwitch,
  ElPopover,
  ElSelect,
  ElOption,
  ElMessage,
} from "element-plus";

import {
  personFamilyAccountActive,
  updatePersonFamilyAcctStatus,
  getCardTypeList,
} from "@/applications/eccard-finance/api";
import { useStore } from "vuex";
import { reactive } from "@vue/reactivity";
import { onMounted, watch } from "@vue/runtime-core";

export default {
  components: {
    ElTable,
    ElTableColumn,
    ElSwitch,
    ElPopover,
    ElSelect,
    ElOption,
  },
  setup() {
    const store = useStore();
    const state = reactive({
      visible: false,
      loading: false,
      nowPerson: {},
      editForm: {
        userId: "",
      },
      activeForm: {
        userId: "",
      },
      personFamilyList: [],
      cardTypeList: [],
      accountStatusList: JSON.parse(
        localStorage.getItem("kade_cache_dictionary")
      ).filter((item) => item.dictType == "PERSON_ACCOUNT_STATUS"), //账户状态
      cardStatusList: JSON.parse(
        localStorage.getItem("kade_cache_dictionary")
      ).filter((item) => item.dictType == "PERSON_CARD_STATUS"), //卡片状态
    });
    const filterDictionary = (val, dictionaryList) => {
      for (let i of dictionaryList) {
        if (val == i.dictCode) {
          return i.dictValue;
        }
      }
    };
    watch(
      () => store.state.data.personFamilyList,
      (val) => {
        state.personFamilyList = val;
      }
    );
    //获取卡片类型
    const queryCardTypeList = () => {
      getCardTypeList().then((res) => {
        state.cardTypeList = res.data;
      });
    };
    const acctStatusEdit = (val, type) => {
      if (type == "active") {
        state.nowPerson = val;
      } else if (type == "edit") {
        state.editForm.userId = store.state.data.selectPerson.userId;
        state.editForm.familyId = val.familyId;
        state.editForm.acctStatus = val.acctStatus;
        state.loading = true;
        updatePersonFamilyAcctStatus(state.editForm)
          .then(({ code, message }) => {
            if (code === 0) {
              ElMessage.success(message);
            }
            store.dispatch("data/queryPersonFamilyList", {
              userId: store.state.data.selectPerson.userId,
            });
            state.loading = false;
          })
          .catch(() => (state.loading = false));
      }
    };

    const activeChange = () => {
      state.activeForm.userId = store.state.data.selectPerson.userId;
      state.activeForm.familyId = state.nowPerson.familyId;
      state.loading = true;
      personFamilyAccountActive(state.activeForm)
        .then(({ code, message }) => {
          if (code === 0) {
            ElMessage.success(message);
          }
          state.activeForm.acctType=''
          store.dispatch("data/queryPersonFamilyList", {
            userId: store.state.data.selectPerson.userId,
          });
          state.loading = false;
        })
        .catch(() => (state.loading = false));
    };
    onMounted(() => {
      queryCardTypeList();
      store.dispatch("data/queryPersonFamilyList", {
        userId: store.state.data.selectPerson.userId,
      });
    });
    return {
      state,
      filterDictionary,
      acctStatusEdit,
      activeChange,
    };
  },
};
</script>
<style lang="scss" scoped>
.box {
  min-height: 650px;

  // overflow-y: scroll;
  .el-table tr {
    height: 48px;
  }
}

:deep(.el-popper.is-light) {
  width: 200px !important;
}
</style>
