﻿import { createRouter, createWebHistory, createWebHashHistory } from 'vue-router';
import View from '@/components/view';

const routes = [
  {
    path: '/',
    redirect: '/main',
    meta: {
      noauth: true,
    }
  },
  {
    path: '/404',
    name: 'NotFound',
    component: () => import('@/components/404'),
    meta: {
      noauth: true,
    }
  },
  {
    path: '/main',
    name: 'Main',
    component: View,
    redirect: {
      name: 'Panel',
    },
    children: [{
        path: 'panel',
        name: 'Panel',
        component: () => import(/* webpackChunkName: "main" */ '@/components/portalView'),
        redirect: { name: 'Home' },
        children: [{
          path: 'home',
          name: 'Home',
          component: () => import(/* webpackChunkName: "home" */ '../views/home'),
          meta: {
            label: 'POS管理系统',
            icon: 'iconsongmenhuwangzhan'
          }
        },{
          path: 'message',
          name: 'MyMessage',
          component: () => import(/* webpackChunkName: "personal" */ '../views/message'),
          meta: {
            label: '我的消息',
            icon: 'iconxiaoxi'
          }
        },{
          path: 'calendar',
          name: 'Calendar',
          component: () => import(/* webpackChunkName: "personal" */ '../views/home/<USER>/calendar.vue'),
          meta: {
            label: '待办事项',
            icon: 'iconxiaoxi'
          }
        },{
          path: 'messageList',
          name: 'MessageList',
          component: () => import(/* webpackChunkName: "personal" */ '../views/home/<USER>/messageList.vue'),
          meta: {
            label: '系统通知',
            icon: 'iconxiaoxi'
          }
        }]
    }]
  },
  {
    path: "/:pathMatch(.*)*",
    redirect: '/404'
  }
]
const fn = CONFIG.ROUTE_TYPE === 'hash' ? createWebHashHistory: createWebHistory;
const router = createRouter({
  history: fn(CONFIG.IS_ROUTE_BASE_REWRITE ? CONFIG.ROUTE_BASE : '/unified_portal'),
  scrollBehavior: () => ({ y: 0 }),
  routes
})

export function resetRouter() {
  const newRouter = createRouter();
  router.matcher = newRouter.matcher;
}

export default router;
