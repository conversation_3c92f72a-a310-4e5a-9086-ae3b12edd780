<template>
  <kade-modal v-bind="attrs" :modelValue="modelValue" @update:modelValue="update">
    <el-form ref="formRef" :label-width="labelWidth" :rules="rules" :model="state.model" size="small">
      <el-form-item label="类别名称" prop="roleName">
        <el-input placeholder="请输入" v-model="state.model.roleName" />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-switch v-model="state.model.status" :active-color="themes.primaryColor" :inactive-color="themes.dangerColor" active-value="ENABLE_TRUE" inactive-value="ENABLE_FALSE" inactive-text="禁用" active-text="启用">
        </el-switch>
      </el-form-item>
      <el-form-item label="备注">
        <el-input type="textarea" placeholder="请输入" v-model="state.model.remarks" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button icon="el-icon-circle-close" @click="cancel" size="small">取消</el-button>
      <el-button icon="el-icon-circle-check" :loading="btnLoading" @click="submit" size="small" type="primary">保存</el-button>
    </template>
  </kade-modal>
</template>
<script>
import Modal from "@/components/modal";
import { computed, reactive, ref, watch } from "vue";
import {
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElMessage,
  ElSwitch,
} from "element-plus";
import { addRole, updateRole } from "@/applications/eccard-basic-data/api";
const getDefaultModel = () => ({
  roleName: "",
  status: "ENABLE_TRUE",
});
export default {
  emits: ["update:modelValue", "change"],
  props: {
    title: {
      type: String,
      default: "",
    },
    modelValue: {
      type: Boolean,
      default: false,
    },
    role: {
      type: Object,
      default: () => ({}),
    },
  },
  components: {
    "kade-modal": Modal,
    "el-button": ElButton,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    "el-input": ElInput,
    "el-switch": ElSwitch,
  },
  setup(props, context) {
    const formRef = ref(null);
    const btnLoading = ref(false);
    const state = reactive({
      model: getDefaultModel(),
    });
    const rules = {
      roleName: [
        { required: true, message: "请输入类别名称" },
        { max: 20, message: "类别名称不能超过20个字符" },
      ],
    };
    const cancel = () => {
      formRef.value?.resetFields();
      context.emit("update:modelValue", false);
    };
    const submit = () => {
      formRef.value?.validate(async (valid) => {
        if (valid) {
          try {
            btnLoading.value = true;
            const fn = props.role?.id ? updateRole : addRole;
            const { message, code } = await fn(state.model);
            if (code === 0) {
              ElMessage.success(message);
            } else {
              ElMessage.error(message);
            }
            context.emit("update:modelValue", false);
            context.emit("change");
          } catch (e) {
            throw new Error(e.message);
          } finally {
            btnLoading.value = false;
          }
        }
      });
    };
    const attrs = computed(() => {
      // eslint-disable-next-line no-unused-vars
      const { modelValue, role, ...attrs } = props;
      return attrs;
    });
    const update = (v) => {
      context.emit("update:modelValue", v);
    };
    watch(
      () => props.modelValue,
      (n) => {
        if (n) {
          if (props.role?.id) {
            state.model = Object.assign(getDefaultModel(), props.role);
          } else {
            state.model = getDefaultModel();
          }
        }
      }
    );
    return {
      attrs,
      update,
      formRef,
      cancel,
      submit,
      rules,
      labelWidth: THEMEVARS.formLabelWidth,
      state,
      btnLoading,
      themes: THEMEVARS,
    };
  },
};
</script>