<template>
  <kade-modal v-bind="attrs" :modelValue="modelValue" @update:modelValue="update">
    <div v-loading="state.dataLoading">
      <el-checkbox-group v-model="state.submitAuthList">
        <el-checkbox
          v-for="item in state.authList"
          :key="item.id"
          :label="item.id"
          >{{ item.menuName }}</el-checkbox
        >
      </el-checkbox-group>
    </div>
    <template #footer>
      <el-button size="small" @click="cancel">取消</el-button>
      <el-button size="small" @click="submit" type="primary">提交</el-button>
    </template>
  </kade-modal>
</template>
<script>
import Modal from '@/components/modal';
import { computed, reactive, ref, watch } from 'vue';
import { 
  ElCheckbox,
  ElCheckboxGroup,
  ElButton,
  ElMessage,
} from 'element-plus';
import {
  getTenantAuthAppList,
  saveTenantSupperRoleMenu
} from "@/applications/eccard-ops/api";
export default {
  emits: ['update:modelValue', 'change'],
  props: {
    title: {
      type: String,
      default: ''
    },
    modelValue: {
      type: Boolean,
      default: false,
    },
    appList:{
      type:Array
    },
    id: {
      type: [String, Number],
      default: null
    },
    type: String,
  },
  components: {
    'kade-modal': Modal,
    'el-button': ElButton,
    ElCheckbox,
    ElCheckboxGroup
  },
  setup(props, context) {
    const formRef = ref(null);
    const state = reactive({
      dataLoading:false,
      authList:[],
      submitAuthList:[],
    });
    const cancel = () => {
      context.emit('update:modelValue', false);
    }
    const submit = async () => {
      let params={
        menuIds:state.submitAuthList.map(item=>Number(item)),
        tenantId:props.id,
      }
      state.dataLoading = true;
      let {code,message}=await saveTenantSupperRoleMenu(params)
      
      if(code===0){
        ElMessage.success(message)
        context.emit("update:modelValue", false);
      }
      state.dataLoading = false;

    }
    const attrs = computed(() => {
      // eslint-disable-next-line no-unused-vars
      const { modelValue, id, type, ...attrs } = props;
      return attrs;
    });
    const update = (v) => {
      context.emit('update:modelValue', v);
    }
    watch(() => props.modelValue, async (n) => {
      if(n){
        try {
          state.dataLoading = true;
          const {data} = await getTenantAuthAppList(props.id);
          state.authList = data
          state.submitAuthList=data.filter(item=>item.checked).map(item=>item.id)
        } catch (e) {
          // throw new Error(e.message);
        } finally {
          state.dataLoading = false;
        }
      }
    });

    return {
      attrs,
      update,
      formRef,
      cancel,
      submit,
      labelWidth: THEMEVARS.formLabelWidth,
      state,
    }
  }
}
</script>