<template>
  <div class="deviceDetail">
    <el-dialog
      :model-value="isShowDeviceDetail"
      :title="title"
      width="70%"
      :before-close="beforeClose"
    >
      <kade-tab-wrap :tabs="tabs" style="margin-top: 20px" v-model="state.tab">
        <template #jbxx><kade-information /> </template>
        <template #sbcs><kade-parameter /> </template>
        <template #ghjl> <kade-record /></template>
      </kade-tab-wrap>
    </el-dialog>
  </div>
</template>
<script>
import { reactive, ref, computed, watch } from "vue";
import { ElDialog } from "element-plus";
import { useStore } from "vuex";
import information from "./components/information.vue";
import parameter from "./components/parameter.vue";
import record from "./components/record.vue";
const tabs = [
  {
    name: "jbxx",
    label: "基本信息",
  },
  {
    name: "sbcs",
    label: "设备参数",
  },
  {
    name: "ghjl",
    label: "更换记录",
  },
];

export default {
  components: {
    "el-dialog": ElDialog,
    "kade-information": information,
    "kade-parameter": parameter,
    "kade-record": record,
  },
  props: {
    title: {
      types: String,
      default: "",
    },
    isShow: {
      types: Boolean,
      default: false,
    },
  },
  setup() {
    const store = useStore();
    let ruleForm = ref(null);
    const state = reactive({
      tab: "jbxx",
      form: {},
    });
    watch(
      () => store.state.cardDeviceData.isShowDeviceDetail,
      (val) => {
        if (!val) {
          state.tab = "jbxx";
        }
      }
    );
    const isShowDeviceDetail = computed(() => {
      return store.state.cardDeviceData.isShowDeviceDetail;
    });
    const beforeClose = () => {
      store.commit("cardDeviceData/updateState", {
        key: "isShowDeviceDetail",
        payload: false,
      });
    };

    return {
      tabs,
      state,
      ruleForm,
      isShowDeviceDetail,
      beforeClose,
    };
  },
};
</script>
<style lang="scss" scoped>
.el-divider--horizontal {
  margin: 0 0 10px;
}
:deep(.el-dialog__header) {
  border-bottom: 1px solid #efefef;
}

:deep(.el-overlay) {
  position: absolute;
  background-color: rgba(0, 0, 0, 0.3);
}

:deep(.el-dialog__footer) {
  text-align: center;
}

:deep(.el-form-item__content) {
  .el-select {
    width: 178px;
  }

  .el-textarea {
    width: 100%;

    .el-textarea__inner {
      width: 100%;
    }
  }
}
</style>