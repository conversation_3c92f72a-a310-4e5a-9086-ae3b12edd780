<template>
  <div class="news">
    <kade-route-card>
      <kade-table-filter @search="handleSearch" @reset="handleReset">
        <el-form inline label-width="120px" size="small">
          <el-form-item label="关键字">
            <el-input
              size="small"
              v-model="state.form.keyWord"
              clearable
              placeholder="请输入"
            />
          </el-form-item>
          <el-form-item label="新闻公告类型">
            <el-select v-model="state.form.newsType" clearable placeholder="请选择">
                <el-option
                  v-for="item in newsType"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
          </el-form-item>
          <el-form-item label="发布状态">
            <el-select v-model="state.form.status" clearable placeholder="请选择">
                <el-option
                  v-for="item in newsStatus"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
          </el-form-item>
          <el-form-item label="发布时间">
            <el-date-picker
              v-model="state.date"
              type="daterange"
              range-separator="-"
              clearable
              unlink-panels
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              @change="dateChange"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="发布人">
            <el-input v-model="state.form.newsPublisher" clearable placeholder="请输入" />
          </el-form-item>
        </el-form>
      </kade-table-filter>
      <kade-table-wrap>
        <template #extra>
          <el-button icon="el-icon-plus" @click="handleAdd"  size="small" type="success">发布</el-button>
<!--           <el-button type="success" @click="edit" icon="el-icon-daorutupian" size="small">编辑</el-button>
          <el-button class="btn-import" icon="el-icon-daoru" size="small">删除</el-button> -->
        </template>
        <el-table style="width: 100%" v-loading="state.loading" :data="state.newsList" border stripe>
          <el-table-column v-for="(item,index) in column" :key="index" :label="item.label" align="center" :prop="item.prop" :width="item.width" show-overflow-tooltip>
            <template #default="scope" v-if="item.type=='text'">
              <div class="table-text" v-html="scope.row[item.prop]"></div>
            </template>
            <template #default="scope" v-else-if="item.render">
              {{item.render(scope.row[item.prop])}}
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" fixed="right" width="200">
            <template #default="scope">
              <el-button @click="handleInfo(scope.row)" class="green" type="text" size="mini">查看详情</el-button>
              <el-button @click="handleEdit(scope.row)" class="blue" type="text" size="mini">编辑</el-button>
              <el-button @click="handleDel(scope.row)" class="red" type="text" size="mini">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination">
          <el-pagination
            background
            :current-page="state.form.currentPage"
            layout="total, sizes, prev, pager, next, jumper"
            :page-sizes="[10, 20, 50, 100, 200]"
            :total="state.total"
            :page-size="state.form.pageSize"
            @current-change="currentChange"
            @size-change="sizeChange"
          >
          </el-pagination>
        </div>
      </kade-table-wrap>
    </kade-route-card>
    <kade-news-add/>
    <kade-news-edit/>
    <kade-news-details/>
  </div>
</template>
<script>
import { onMounted, reactive, watch } from "vue";
import { useStore } from "vuex";
import {
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElTable,
  ElTableColumn,
  ElButton,
  ElDatePicker,
  ElPagination,
  ElMessage,
  ElMessageBox
} from "element-plus";
import { useDict } from "@/hooks/useDict";
import { dateStr } from "@/utils/date.js";
import { filterDictionary } from "@/utils/index.js";
import add from "./add.vue"
import edit from "./edit.vue"
import details from "./details.vue"
import { getNewsList,delNews } from "@/applications/eccard-basic-data/api";


export default {
  components: {
    ElForm,
    ElFormItem,
    ElInput,
    ElSelect,
    ElOption,
    ElTable,
    ElTableColumn,
    ElButton,
    ElDatePicker,
    ElPagination,
    "kade-news-add":add,
    "kade-news-edit":edit,
    "kade-news-details":details

  },
  setup() {
    const store=useStore()
    const newsType = useDict("SYS_NEWS_TYPE"); //新闻公告类型
    const newsReceiver = useDict("SYS_NEWS_RECEIVER"); //新闻接收
    const newsStatus = useDict("SYS_BOOL_STRING"); //发布状态
    const column=[
      {label:"新闻公告类型",prop:"newsType",width:"120px",render:(val)=>filterDictionary(val,newsType)},
      {label:"发布状态",prop:"status",width:"100px",render:(val)=>val=="FALSE"?'即将发布':'已发布成功'},
      {label:"接收端",prop:"newsReceiver",render:(val)=>{
        let arr=val.split(",")
        return (arr.map(item=>filterDictionary(item,newsReceiver))).join(",")
      }},
      {label:"消息标题",prop:"newsTitle",width:"290px",type:"text"},
      // {label:"消息内容",prop:"newsContent",width:"440px",type:"text"},
      {label:"发布人",prop:"newsPublisher",},
      {label:"发布时间",prop:"newsPublishTime",width:"200px"},
    ]
    const state = reactive({
      loading:false,
      form: {
        currentPage:1,
        pageSize:10,
      },
      date:[],
      newsList:[],
      total:0,
    });

    watch(()=>store.state.news.isAdd,val=>{
      if(!val){
        getList()
      }
    })
    watch(()=>store.state.news.isEdit,val=>{
      if(!val){
        getList()
      }
    })
    const getList=async ()=>{
      state.loading=true
      let {data:{list,total}}=await getNewsList(state.form)
      state.newsList=list
      state.total=total
      console.log(state.newsList)
      state.loading=false

    }
    const dateChange=val=>{
      if(val){
        state.form.startTime=dateStr(val[0])
        state.form.endTime=dateStr(val[1])
      }else{
        delete state.form.startTime
        delete state.form.endTime
      }
    }
    const handleSearch=()=>{
      getList()
    }

    const handleReset=()=>{
      state.form={
        currentPage:1,
        pageSize:10,
      }
    }
    const handleAdd=()=>{
      store.commit("news/updateState", {
        key: "isAdd",
        payload: true
      });
    }
    const handleInfo=row=>{
      store.commit("news/updateState", {
        key: "selectRow",
        payload: row
      });
      store.commit("news/updateState", {
        key: "isDetails",
        payload: true
      });
    }
    const handleEdit=row=>{
      store.commit("news/updateState", {
        key: "selectRow",
        payload: row
      });
      store.commit("news/updateState", {
        key: "isEdit",
        payload: true
      });
    }
    const handleDel= row=>{
      ElMessageBox.confirm('确认删除？','提示',{
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(async () => {
        let {code,message}=await delNews(row.id)
        if(code===0){
          ElMessage.success(message)
          getList()
        }
      }).catch(() => {
        ElMessage({
          type: 'info',
          message: '已取消！',
        })
      })
    }

    const currentChange=val=>{
      state.form.currentPage=val
      getList()
    }
    const sizeChange=val=>{
      state.form.currentPage=1
      state.form.pageSize=val
      getList()
    }

    onMounted(()=>{
      getList()
    })
    return {
      column,
      newsType,
      newsStatus,
      state,
      dateChange,
      handleSearch,
      handleReset,
      handleAdd,
      handleInfo,
      handleEdit,
      handleDel,
      currentChange,
      sizeChange
    };
  },
};
</script>
<style lang="scss" scoped>
  .table-text{
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
:deep(.el-divider--horizontal) {
  margin: 0;
}
:deep(.el-dialog__header) {
  border-bottom: 1px solid #efefef;
}

:deep(.el-overlay) {
  position: absolute;
  background-color: rgba(0, 0, 0, 0.3);
}

:deep(.el-dialog__footer) {
  text-align: center;
}
</style>