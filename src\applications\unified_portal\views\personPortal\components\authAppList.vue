<template>
    <kade-list @change="handleToggle" v-if="dataList.length" :data="dataList" />
    <el-empty v-else description="您还没有应用" />
</template>
<script>
import { computed, reactive, onMounted } from 'vue';
import List from '@/components/list';
import { ElEmpty, ElMessageBox, ElMessage } from 'element-plus';
import {
  getUserApplyList,
  setAuthAppInfoById,
} from '@/applications/unified_portal/api';
export default {
  components: {
    'el-empty': ElEmpty,
    'kade-list': List,
  },
  setup() {
    const states = reactive({
      dataList: [],
    });
    const loadData = async () => {
      const { data } = await getUserApplyList();
      states.dataList = data;
    }    
    const handleToggle = (id, { applyName, state, appId }) => {
        ElMessageBox.confirm(`是否${state === 'ENABLE_TRUE' || state == 1 ? '停止' : '启用'}应用'${applyName}'?`, {
            type: 'warning',
            closeOnPressEscape: false,
            closeOnClickModal: false,            
        }).then(async () => {
            try{
                const { message } = await setAuthAppInfoById({ appID: appId, status: state === 'ENABLE_TRUE' || state == 1 ? 'ENABLE_FALSE' : 'ENABLE_TRUE' });
                const target = states.dataList.find(it => it.appId === appId);
                target.state = state === 'ENABLE_TRUE' || state == 1 ? '0' : '1';
                ElMessage.success(message);
            }catch(e) {
                throw new Error(e.message);
            }
        });
    }  
    const dataList = computed(() => {
      return states.dataList.map(it => ({
        ...it,
        label: it.applyName,
        value: it.state === 'ENABLE_TRUE' || it.state == 1,
        image: it.applyLogo,
      }))
    });
    onMounted(() => {
      loadData();
    });
    return {
      handleToggle,
      dataList,
    }    
  }
}
</script>