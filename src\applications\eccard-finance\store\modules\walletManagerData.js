import {
  getWalletParamConfig,
  getWalletTradeMode
} from "@/applications/eccard-finance/api";
const state = {
  selectRow: '',
  walletParam: "",
  walletTradeList: "",
  isEditButton: false,
};
const mutations = {
  updateState(state, { key, payload }) {
    state[key] = payload;
  },
};
const actions = {
  async getWalletParam({ commit }) {
    if (!state.selectRow) return
    let { data, code } = await getWalletParamConfig({ walletId: state.selectRow.walletId });
    if (code === 0) {
      if (data == "参数内容为空") {
        let params = {
          allowClearing: true,
          allowOverpayment: true,
          allowPersonRecharge: true,
          allowRefund: true,
          allowTransferIn: true,
          balanceUpperLimit: 10,
          dailyConsumeLimit: 500,
          dailyRechargeLimit: 500,
          refundRatio: 50,
          singleTradeUpperLimit: 1000,
          tradeNotifyAmount: 5,
          transferMinAmount: 10,
          warnAmount: 50,
        }
        commit("updateState", { key: 'walletParam', payload: params })
      } else {
        commit("updateState", { key: 'walletParam', payload: data })

      }
    }
  },
  async getWalletTrade({ commit }, params) {
    if (!state.selectRow) return
    let { data, code } = await getWalletTradeMode({ walletId: state.selectRow.walletId, ...params });
    if (code === 0) {
      commit("updateState", { key: 'walletTradeList', payload: data })
    }
  },
};
export default {
  namespaced: true,
  state,
  mutations,
  actions
}