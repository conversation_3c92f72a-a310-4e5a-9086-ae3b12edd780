<template>
  <el-dialog v-loading="state.loading" :model-value="modelValue" :title="rowData.id?'编辑网关':'新增网关'" width="800px" :before-close="beforeClose">
    <div class="padding-form-box">
      <div class="width-box">
        <el-form size="mini" ref="formRef" :model="state.form" label-width="120px" :rules="rules">
          <el-row>
            <el-col :md="24" :lg="12">
              <el-form-item label="所属区域：" prop="areaId">
                <kade-area-select-tree style="width: 100%" :value="state.form.areaId" valueKey="id" :multiple="false" @valueChange="(val) => (state.form.areaId = val.id)" />
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="所属设备类型：" prop="deviceType">
                <el-select v-model="state.form.deviceType">
                  <el-option v-for="(item, index) in state.deviceTypeList" :key="index" :label="item.cfgValue" :value="item.cfgKey"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="网关机号：" prop="gatewayNo">
                <el-input placeholder="请输入" v-model="state.form.gatewayNo"></el-input>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="网关名称：" prop="gatewayName">
                <el-input placeholder="请输入" v-model="state.form.gatewayName"></el-input>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="网关IP：" prop="gatewayIp">
                <el-input placeholder="请输入校检" v-model="state.form.gatewayIp"></el-input>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="网关端口：" prop="gatewayPort">
                <el-input placeholder="请输入" v-model="state.form.gatewayPort"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="备注：">
                <el-input type="textarea" placeholder="请输入备注" v-model="state.form.remarks"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="submit()" type="primary" size="mini">保&nbsp;&nbsp;存</el-button>
        <el-button @click="beforeClose()" size="mini">返&nbsp;&nbsp;回</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script>
import { reactive, ref, watch, nextTick, onMounted } from "vue";
// import { objToArray } from "@/utils"
import { iotCfg, gatewayEdit } from "@/applications/eccard-iot/api";
import areaSelectTree from "@/components/tree/areaSelectTree.vue";
import {
  ElDialog,
  ElButton,
  ElRow,
  ElCol,
  ElForm,
  ElFormItem,
  ElSelect,
  ElOption,
  ElInput,
  ElMessage,
} from "element-plus";
export default {
  components: {
    ElRow,
    ElCol,
    ElSelect,
    ElOption,
    "el-dialog": ElDialog,
    "el-button": ElButton,
    "el-input": ElInput,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    "kade-area-select-tree": areaSelectTree,
  },
  props: {
    modelValue: {
      type: Boolean,
      default: false,
    },
    rowData: {
      type: Object,
      default: null,
    },
    TabModule: {
      type: Function,
      default: null,
    }
  },
  setup(props, context) {
    const formRef = ref(null)
    const state = reactive({
      loading: false,
      form: {},
      deviceTypeList: []

    });
    watch(
      () => props.modelValue,
      (val) => {
        if (val) {
          console.log(props.rowData);
          state.form = { ...props.rowData }
        }
        nextTick(() => {
          formRef.value.clearValidate()
        })
      }
    );
    const rules = {
      areaId: [
        {
          required: true,
          message: "请输入所属区域",
          trigger: "change",
        },
      ],
      deviceType: [
        {
          required: true,
          message: "请选择设备类型",
          trigger: "change",
        },
      ],
      gatewayNo: [
        {
          required: true,
          message: "请输入网关机号",
          trigger: "blur",
        },
      ],
      gatewayName: [
        {
          required: true,
          message: "请输入网关名称",
          trigger: "blur",
        },
      ],
      gatewayIp: [
        /* {
          pattern: /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/,
          message: 'ip格式不对！',
          trigger: 'blur'
        }, */
        {
          required: true,
          message: "请输入网关IP",
          trigger: "blur",
        },
      ],
      gatewayPort: [
        {
          required: true,
          message: "请输入网关端口",
          trigger: "blur",
        },
      ],
    }
    const queryDeviceType = async () => {
      let { data } = await iotCfg({ cfgType: props.TabModule().productType });
      state.deviceTypeList = data
    };
    const beforeClose = () => {
      context.emit("update:modelValue", false);
    };
    const submit = () => {
      formRef.value.validate(async (valid) => {
        if (valid) {
          let fn = state.form.id ? gatewayEdit : props.TabModule().addFnc
          state.loading = true
          state.form.gatewayType = props.TabModule().gatewayType
          try {
            let { code, message } = await fn(state.form)
            if (code === 0) {
              ElMessage.success(message)
              context.emit("update:modelValue", true);
              state.loading = false
            }
          }
          catch {
            state.loading = false
          }
        } else {
          return false;
        }
      });
    };
    onMounted(() => {
      queryDeviceType()
    })
    return {
      state,
      formRef,
      rules,
      submit,
      beforeClose,
    };
  },
};
</script>
<style lang="scss" scoped>
:deep(.el-form-item__content) {
  .el-select {
    width: 178px;
  }

  .el-textarea {
    width: 100%;

    .el-textarea__inner {
      width: 100%;
    }
  }
}
</style>