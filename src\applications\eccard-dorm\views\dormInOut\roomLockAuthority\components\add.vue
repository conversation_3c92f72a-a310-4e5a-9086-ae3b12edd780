<template>
  <el-dialog :model-value="modelValue" title="添加授权" width="1550px" :before-close="handleClose">
    <el-row :gutter="10">
      <el-col :span="9">
        <div class="select-access">
          <div class="title">选择门禁</div>
          <el-divider></el-divider>
          <kade-select-table :isShow="true" :value="[]" :column="lockColumn"
            :selectCondition="ACSelection" :params="lockParams" :reqFnc="getAuthorityAreaInfo" :isMultiple="true"
            :isCurrentSelect="true" @change="accessChange" />
        </div>
      </el-col>
      <el-col :span="15">
        <div class="select-user">
          <div class="title">选择用户</div>
          <el-divider></el-divider>
          <kade-select-table :isShow="true" :value="[]" :column="userColumn"
            :selectCondition="userSelection" :params="userParams" :reqFnc="getUserInfoListByPage" :isMultiple="true"
            :isCurrentSelect="true" @change="personChange" />
        </div>
      </el-col>
    </el-row>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose" size="mini">取消</el-button>
        <el-button type="primary" @click="submit" size="mini">确认</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { onMounted, reactive,  } from 'vue'
import selectTable from '@/components/table/selectTable'
import { useDict } from "@/hooks/useDict.js"
import { ElDialog, ElButton, ElRow, ElCol, ElDivider, ElMessage } from "element-plus"
import { addDeviceAuthority,getAuthorityAreaInfo } from '@/applications/eccard-dorm/api';
import { getRolelist, getUserInfoListByPage } from '@/applications/eccard-basic-data/api';
const lockColumn = [
  { label: '区域', isRow:true,render:(val)=>{
    return `${val.areaName}>${val.buildName}>${val.unitName}>${val.roomName}`
  } }
]
const userColumn = [
  { label: '组织机构', prop: 'deptName' },
  { label: '身份类别', prop: 'roleName' },
  { label: '用户编号', prop: 'userCode' },
  { label: '用户名称', prop: 'userName' },
  { label: '性别', prop: 'userSex', isDict: true }
]
const lockParams = {
  currentPageKey: "currentPage",
  pageSizeKey: "pageSize",
  resListKey: "dataList",
  resTotalKey: "totalCount",
  value: {
  },
  tagNameKey: "roomString",
  valueKey: "id",
}
const userParams = {
  currentPageKey: "beginPage",
  pageSizeKey: "rowNum",
  resListKey: "dataList",
  resTotalKey: "totalCount",
  value: {},
  tagNameKey: "userName",
  valueKey: "id",
}
export default {
  props: {
    modelValue: {
      type: Boolean,
      default: false
    }
  },
  components: {
    ElDialog,
    ElButton,
    ElRow,
    ElCol,
    ElDivider,
    "kade-select-table": selectTable
  },
  setup(props, context) {
    const state = reactive({
      form: {
        userIds: [],
        deviceList: []
      },
    })
    const userSelection = [
      { label: '组织机构', valueKey: 'deptId',dataKey: "id", placeholder: '请选择', isTree: 'dept' },
      { label: '身份类别', valueKey: 'userRole', placeholder: '请选择', isSelect: true, select: { list: [], option: { label: 'roleName', value: 'id' } } },
      { label: '关键字', valueKey: 'keyWord', placeholder: '姓名或编号关键字搜索', isSelect: false },
      { label: '性别', valueKey: 'userSex', placeholder: '请选择', isSelect: true, select: { list: useDict('SYS_SEX'), option: { label: 'label', value: 'value' } } },
      { label: '是否住宿', valueKey: '', placeholder: '请选择', isSelect: true, select: { list: [], option: { label: '', value: '' } } }
    ]
    const ACSelection = [
      { label: '区域', valueKey: 'areaId',dataKey: "id", placeholder: '请选择', isTree: 'area' },
    ]
    //获取身份类别
    const queryRolelist = () => {
      getRolelist().then((res) => {
        userSelection[1].select.list = res.data
      });
    };
    const personChange=(val)=>{
      console.log(val)
      state.form.userIds=val.list.map(item=>item.id)
    }
    const accessChange=(val)=>{
      console.log(val)
      state.form.deviceList=val.list.map(item=>{
        return {
          buildId:item.buildId,
          floorNum:item.floorNum,
          roomId:item.roomId,
          unitNum:item.unitNum
        }
      })
      console.log(state.form.deviceList)
    }
    const handleClose = () => {
      context.emit("update:modelValue", false)
    }
    // watch(()=>props.modelValue,val=>{
    //   if(val){
    //     state.form={
    //       userIds: [],
    //     deviceList: []
    //     }
    //   }
    // })
    const submit = async () => {
      if (!state.form.userIds.length) {
        return ElMessage.error("请选择人员！")
      }
      console.log(state.form);
      if (!state.form.deviceList.length) {
        return ElMessage.error("请选择门禁！")
      }
      // let param = {...state.form}
      // console.log(param)
      let { code,message } = await addDeviceAuthority(state.form)
      if(code===0){
        ElMessage.success(message)
        context.emit('update:modelValue',true)
      }
    }
    onMounted(() => {
      queryRolelist()
    })
    return {
      getUserInfoListByPage,
      getAuthorityAreaInfo,
      state,
      submit,
      handleClose,
      lockColumn,
      userColumn,
      userSelection,
      ACSelection,
      lockParams,
      userParams,
      personChange,
      accessChange
    }
  }
}
</script>

<style lang="scss" scoped>
.el-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin: 20px 0;
  width: 100%;

  .title {
    margin: 10px 20px;
  }

  :deep(.select-access) {
    border: 1px solid #eeeeee;
    height: 526px;
    border-radius: 3px;

    .el-table__empty-block {
      height: 300px !important;
    }

    // .el-input__inner{
    //   width: 240px;
    //   height: 40px;
    // }
    // .el-icon-arrow-up:before{
    //   height: 25px;
    //   width: 25px;
    //   line-height: 40px;
    // }
    // .el-input__inner{
    //   width: 240px;
    //   height: 40px;
    // }
    .el-form-item__content {
      width: 170px !important;
    }

    .select-trigger {
      width: 140px;
    }

    .el-select .el-select--mini {
      width: 140px !important;
    }

    .el-form-item__label {
      width: 70px !important;
    }

    .pagination {
      width: 100%;
      height: 52px;
      position: relative;
      .select-trigger{
        width: 100px;
      }

      .el-pagination {
        position: absolute;
        left: 70px;
      }
    }

  }

  :deep(.select-user) {
    border: 1px solid #eeeeee;
    border-radius: 3px;

    .el-input__inner {
      width: 180px;
    }

    .select-trigger {
      width: 180px;
    }

    .el-select .el-select--mini {
      width: 180px !important;
    }

    .el-input__inner {
      width: 192px;
    }

    .pagination {
      width: 100%;
      height: 52px;
      position: relative;
      .select-trigger{
        width: 100px;
      }

      .el-pagination {
        position: absolute;
        left: 400px;
      }
    }

  }
}
</style>