<template>
  <el-form inline size="mini" :label-width="`${labelWidth}px`">
    <kade-linkage-select v-if="linkage" :value="state.form" :data="linkage.linkageData" @change="linkageChange" />
    <el-form-item v-for="(item, index) in selectCondition" :key="index" :label="item.label">
      <el-select v-if="item.isSelect" clearable v-model="state.form[item.valueKey]"
        :value-key="item.select.isValueObj&&item.select.option.value" :collapse-tags-tooltip="item.select.isMultiple"
        :collapse-tags="item.select.isMultiple" :multiple="item.select.isMultiple" placeholder="请选择">
        <el-option v-for="(v, i) in item.select.list" :key="i" :label="v[item.select.option.label]"
          :value="item.select.isValueObj?v:v[item.select.option.value]">
        </el-option>
      </el-select>
      <kade-area-select-tree v-else-if="item.isTree == 'area'" :value="state.form[item.valueKey]"
        :valueKey="item.dataKey" :multiple="false"
        @valueChange="(val) => (state.form[item.valueKey] = val[item.dataKey])" />
      <kade-dept-select-tree v-else-if="item.isTree == 'dept'" :value="state.form[item.valueKey]"
        :valueKey="item.dataKey" :multiple="false"
        @valueChange="(val) => (state.form[item.valueKey] = val[item.dataKey])" />
      <el-input v-else placeholder="请输入" v-model="state.form[item.valueKey]"></el-input>
    </el-form-item>
    <el-form-item label="&nbsp;" v-if="selectCondition.length || linkage">
      <el-button @click="reset()" icon="el-icon-refresh-right" size="mini" class="shop-upload">重置</el-button>
      <el-button @click="getList()" icon="el-icon-sousuo" size="mini" class="shop-upload" type="primary">搜索</el-button>
    </el-form-item>
  </el-form>
  <template v-if="!state.selectAll">
    <el-tag class="tag" v-for="(item, index) in state.tagList" :key="index" @close="closeTag(item, index)" closable>{{
    item[params.tagNameKey]
    }}</el-tag>
  </template>
  <template v-else>
    <el-tag class="tag">全选</el-tag>
  </template>
  <el-table :data="state.dataList" v-loading="state.loading" @selection-change="selectionChange"
    :highlight-current-row="!isMultiple" @row-click="rowClick" ref="multipleTable" height="300px" border stripe>
    <el-table-column v-if="isMultiple" type="selection" width="55" align="center"></el-table-column>
    <el-table-column show-overflow-tooltip v-for="(item, index) in column" :width="item.width" :key="index"
      :label="item.label" :prop="item.prop" align="center">
      <template v-if="item.isDict" #default="scope">
        {{ dictionaryFilter(scope.row[item.prop]) }}
      </template>
      <template v-else-if="item.isRow" #default="scope">
        {{item.render(scope.row)}}
      </template>
      <template v-else-if="item.render" #default="scope">
        {{item.render(scope.row[item.prop])}}
      </template>

    </el-table-column>
  </el-table>
  <div class="pagination" :style="{ display: isMultiple && !isCurrentSelect ? 'flex' : 'block' }">
    <el-checkbox :disabled="!state.dataList.length" v-if="isMultiple && !isCurrentSelect" v-model="state.selectAll"
      label="全选" @change="valueChange"></el-checkbox>
    <el-pagination background :current-page="state.form[params.currentPageKey]"
      :page-size="state.form[params.pageSizeKey]" layout="total, sizes, prev, pager, next, jumper"
      :page-sizes="[5, 10, 20, 50, 100]" :total="state.total" @current-change="currentChange" @size-change="sizeChange">
    </el-pagination>
  </div>
</template>
<script>
import { reactive, nextTick, ref, onMounted, watch } from "vue";
import {
  ElForm,
  ElFormItem,
  ElSelect,
  ElOption,
  ElInput,
  ElTable,
  ElTableColumn,
  ElCheckbox,
  ElPagination,
  ElButton,
  ElTag
} from "element-plus";
import areaSelectTree from "@/components/tree/areaSelectTree.vue";
import deptSelectTree from "@/components/tree/deptSelectTree.vue";
import linkageSelect from "@/applications/eccard-dorm/components/linkageSelect.vue"
export default {
  components: {
    ElForm,
    ElFormItem,
    ElSelect,
    ElOption,
    ElInput,
    ElTable,
    ElTableColumn,
    ElCheckbox,
    ElPagination,
    ElButton,
    ElTag,
    "kade-dept-select-tree": deptSelectTree,
    "kade-area-select-tree": areaSelectTree,
    "kade-linkage-select": linkageSelect,
  },
  props: {
    isShow: {
      type: Boolean,
      default: false
    },
    value: {
      type: Array,
      default: null
    },
    column: {
      types: Array,
      default: [],
    },
    linkage: {
      type: Object,
      default: null
    },
    selectCondition: {
      types: Array,
      default: [],
    },
    params: {
      type: Object,
      default: null
    },
    reqFnc: {
      type: Function,
      default: null
    },
    isMultiple: {
      type: Boolean,
      default: true
    },
    isCurrentSelect: {
      type: Boolean,
      default: false,
    },
    labelWidth: {
      type: Number,
      default: 100
    }
  },
  setup(props, context) {
    const multipleTable = ref(null)
    const state = reactive({
      loading: false,
      dataList: [],
      total: 0,
      currentSelected: [],
      allSelected: [],
      form: {},
      cloneForm: {},
      selectAll: false,
      tagList: [],
    });

    watch(() => props.isShow, (val) => {
      if (val) {
        console.log(1, props.value);
        state.dataList = []
        state.total = 0
        state.currentSelected = []
        state.form = { ...props.params.value }
        state.form[props.params.currentPageKey] = 1
        state.form[props.params.pageSizeKey] = 10
        state.selectAll = false
        state.tagList = [...props.value]
        state.allSelected = [...props.value]
        getList()

      }
    })

    const getList = async () => {
      for (let key in state.form) {
        if (!state.form[key]) {
          delete state.form[key]
        }
      }
      state.cloneForm = { ...state.form }
      state.loading = true
      try {
        let { data } = await props.reqFnc(state.form)
        state.dataList = data[props.params.resListKey]
        state.total = data[props.params.resTotalKey]
        if (!state.selectAll) {
          nextTick(() => {
            multipleTable.value.clearSelection()
            alreadySelected()
          })
        } else {
          state.dataList.forEach(item => {
            nextTick(() => {
              multipleTable.value.toggleRowSelection(item, true)
            })
          })
        }
        state.loading = false

      }
      catch {
        state.loading = false
      }
    }

    const rowClick = (row) => {
      context.emit("rowChange", row)
    }

    const linkageChange = (val) => {
      state.form = { ...state.form, ...val }
      console.log(state.form);
    }
    //获取列表自动勾选已选中row
    const alreadySelected = () => {
      //筛选当前页选中row
      let arr = []
      for (let item of state.dataList) {
        for (let val of state.allSelected) {
          if (item[props.params.valueKey] == val[props.params.valueKey]) {
            arr.push(item)
          }
        }
      }
      arr.forEach(item => {
        nextTick(() => {
          multipleTable.value.toggleRowSelection(item, true)
        })
      })
    }
    const valueChange = val => {
      if (val) {
        state.dataList.forEach(item => {
          nextTick(() => {
            multipleTable.value.toggleRowSelection(item, true)
          })
        })
      } else {
        nextTick(() => {
          multipleTable.value.clearSelection()
        })
        state.allSelected = []

      }
      context.emit("change", {
        list: state.allSelected,
        params: state.cloneForm,
        isSelectAll: state.selectAll
      }
      )
    }
    const selectionChange = (selected) => {
      console.log("selected",selected);
      //先去除总选中列表在当前页的选中项
      selected.forEach(item => {
        state.allSelected.forEach((v, i) => {
          if (item[props.params.valueKey] == v[props.params.valueKey]) {
            state.allSelected.splice(i, 1)
          }
        })
      })

      state.currentSelected = selected
      console.log(state.currentSelected, state.allSelected );
      context.emit("change", {
        list: state.allSelected.concat(state.currentSelected),
        params: state.cloneForm,
        isSelectAll: state.selectAll
      }
      )
      state.tagList = state.allSelected.concat(state.currentSelected)
    }


    const closeTag = (item, index) => {
      state.tagList.splice(index, 1)
      state.allSelected = state.allSelected.filter(v => v[props.params.valueKey] !== item[props.params.valueKey])
      state.currentSelected = state.currentSelected.filter(v => v[props.params.valueKey] !== item[props.params.valueKey])
      multipleTable.value.toggleRowSelection(item, false)
      context.emit("change", {
        list: state.allSelected.concat(state.currentSelected),
        params: state.cloneForm,
        isSelectAll: state.selectAll
      }
      )
    }

    const reset = () => {
      state.form = { ...props.params.value }
      state.form[props.params.currentPageKey] = 1
      state.form[props.params.pageSizeKey] = 10
    }
    const currentChange = val => {
      state.allSelected = state.allSelected.concat(state.currentSelected)
      state.form[props.params.currentPageKey] = val
      getList()
    }
    const sizeChange = val => {
      state.allSelected = state.allSelected.concat(state.currentSelected)
      state.form[props.params.currentPageKey] = 1
      state.form[props.params.pageSizeKey] = val
      getList()
    }
    onMounted(() => {
      state.tagList = props.value
      state.allSelected = props.value
      state.form = { ...props.params.value }
      state.form[props.params.currentPageKey] = 1
      state.form[props.params.pageSizeKey] = 10
      if(props.isShow){
        getList()
      }
    })
    return {
      multipleTable,
      state,
      getList,
      rowClick,
      linkageChange,
      reset,
      valueChange,
      alreadySelected,
      selectionChange,
      closeTag,
      currentChange,
      sizeChange
    };
  },
};
</script>
<style lang="scss" scoped>
.pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  text-align: right;
  padding: 10px 20px;
}

:deep(.el-form-item__content) {
  .el-select {
    width: 192px;
  }

  .el-input {
    width: 192px;
  }

  .el-textarea {
    width: 100%;

    .el-textarea__inner {
      width: 100%;
    }
  }
}

.tag {
  margin-left: 10px;
  margin-bottom: 10px;
}
</style>