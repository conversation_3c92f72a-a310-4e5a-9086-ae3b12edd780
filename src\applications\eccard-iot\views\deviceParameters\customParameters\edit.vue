<template>
  <el-dialog :model-value="dialogVisible" :title="title + '参数'" width="800px" :before-close="handleClose"
    :close-on-click-modal="false">
    <el-form label-width="140px" size="small" :rules="rules" :model="state.form" ref="formRef">
      <el-row>
        <el-col :span="12">
          <el-form-item label="参数名称：" prop="paramsName">
            <el-input placeholder="请输入" v-model="state.form.paramsName"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="参数编号：" prop="paramsCode">
            <el-input placeholder="请输入" v-model="state.form.paramsCode"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="设备类型：" prop="deviceType">
            <el-select v-model="state.form.deviceType" @change="deviceChange">
              <el-option v-for="(item, index) in deviceTypelist" :key="index" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="参数模板名称：" prop="paramsTemplate">
            <el-select v-model="state.form.paramsTemplate">
              <el-option v-for="(item, index) in state.paramsTemplateList" :key="index" :label="item.templateName"
                :value="item.templateName">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="是否启用：">
            <el-switch v-model="state.form.status"></el-switch>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="备注：">
            <el-input placeholder="请输入" type="textarea" v-model="state.form.remarks"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose" size="mini">取消</el-button>
        <el-button type="primary" @click="submit" size="mini">保存</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import {
  ElDialog,
  ElButton,
  ElRow,
  ElCol,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  elSwitch,
} from "element-plus";
import { reactive, computed, watch, ref, nextTick } from "vue";

const rules = {
  paramsName: [
    { required: true, message: "请输入参数名称", },
    { max: 20, message: "参数名称长度不能超过20字符", },
  ],
  paramsCode: [
    { required: true, message: "请输入参数编号", },
    { pattern: /^[0-9a-zA-Z]+$/, message: "请输入字母+数字", },
    { max: 20, message: "参数编号长度不能超过20字符", },
  ],
  deviceType: [
    { required: true, message: "请选择设备类型", trigger: "change", },
  ],
  paramsTemplate: [
    { required: true, message: "请选择参数模板", trigger: "change", },
  ],
}
export default {
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Object,
      default: null
    },
    type: {
      type: String,
      default: ""
    },
    deviceTypelist: {
      type: Array,
      default: () => []
    },
  },
  components: {
    ElDialog,
    ElButton,
    ElRow,
    ElCol,
    ElForm,
    ElFormItem,
    ElInput,
    ElSelect,
    ElOption,
    elSwitch,
  },
  setup(props, context) {
    const formRef = ref(null)
    const state = reactive({
      paramsTemplateList: JSON.parse(sessionStorage.getItem('templateList')),
      form: {
        status: true
      },
    });
    watch(() => props.dialogVisible, val => {
      if (val) {
        nextTick(() => {
          formRef.value.clearValidate()
        })
        state.form = { ...props.data }
        deviceChange(state.form.deviceType)

      }
    })
    const title = computed(() => {
      if (props.type == 'add') {
        return '新增'
      } else if (props.type == 'edit') {
        return '编辑'
      } else {
        return ''
      }
    })
    const deviceChange = val => {
      if (val) {
        state.paramsTemplateList = JSON.parse(sessionStorage.getItem('templateList')).filter(item => item.deviceType == val)
      } else {
        state.paramsTemplateList = JSON.parse(sessionStorage.getItem('templateList'))
      }
    }
    const submit = () => {
      formRef.value.validate(async (valid) => {
        if (valid) {
          let list = JSON.parse(sessionStorage.getItem('customParamsList'))
          if (props.data.id) {
            list.forEach((item, index) => {
              console.log(item.id, props.data.id);
              if (item.id == props.data.id) {
                list[index] = { ...state.form }
              }
            })
            console.log(list);
            sessionStorage.setItem('customParamsList', JSON.stringify(list))
          } else {
            if (list.length) {
              state.form.id = list[list.length - 1].id + 1
            } else {
              state.form.id = 1
            }
            state.form.dateTime = new Date()
            list.push(state.form)
            sessionStorage.setItem('customParamsList', JSON.stringify(list))
          }
          context.emit("close", true);
        } else {
          return false
        }
      })
    }
    const handleClose = () => {
      context.emit("close", false);
    };
    return {
      rules,
      formRef,
      state,
      title,
      deviceChange,
      submit,
      handleClose,
    };
  },
};
</script>

<style lang="scss" scoped>
.el-form {
  margin-top: 10px;
}

.table-box {
  border: 1px solid #eeeeee;
  border-radius: 0 0 8px 8px;

  .pagination {
    margin: 10px;
  }
}

.footer-box {
  margin-top: 10px;
  border: 1px solid #eeeeee;
  border-radius: 4px;

  .text-box {
    margin: 15px;
  }

  .el-form {
    margin-top: 20px;
  }
}

:deep(.el-select) {
  width: 100%;
}
</style>