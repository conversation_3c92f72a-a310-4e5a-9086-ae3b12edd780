<template>
  <div class="padding-box">
    <div v-if="details" class="mask-box">
      <div class="header-box">
        <div class="header-item">计费模式</div>
        <div class="header-item">最小计费单位(1秒/次)</div>
        <div class="header-item">预扣费金额(元)</div>
      </div>
      <div class="main-box">
        <div class="main-item">{{dictListFnc().billModel.filter(item => item.value == details.billModel)[0]?.label}}</div>
        <div class="main-item">{{details.minbillUnit}}</div>
        <div class="main-item">{{details.billNum}}</div>
      </div>
    </div>
    <el-empty v-else description="当前设备参数未设定"></el-empty>
  </div>
</template>
<script>
import { computed } from "@vue/runtime-core";
import { useStore } from "vuex";
import { ElEmpty } from "element-plus"
export default {
  components: {
    ElEmpty,
  },
  setup() {
    const store = useStore();
    const details = computed(() => {
      let data = "";
      for (let item of store.state['hydropowerDevice/waterController'].params) {
        if (item.paramType == "BILL") {
          data = JSON.parse(item.paramContent);
          break;
        }
      }
      if (!data) {
        data = ""
      }
      return data;
    });
    const dictListFnc = () => {
      return store.state['hydropowerDevice/waterController'].dict
    }
    return {
      details,
      dictListFnc
    };
  },
};
</script>
<style lang="scss" scoped>
.mask-box {
  border: 1px solid #eeeeee;
  line-height: 50px;
  min-width: 750px;
  .header-box {
    display: flex;
    align-items: center;
    justify-content: space-around;
    border-bottom: 1px solid #eeeeee;
    .header-item {
      text-align: center;
      width: 100%;
      min-width: 250px;
      background: #f6f6f6;
      border-right: 1px solid #eeeeee;
      &:last-child {
        border-right: 0;
      }
    }
  }
  .main-box {
    display: flex;
    align-items: center;
    justify-content: space-around;
    .main-item {
      text-align: center;
      width: 100%;
      min-width: 250px;
      border-right: 1px solid #eeeeee;
      &:last-child {
        border-right: 0;
      }
    }
  }
}
</style>