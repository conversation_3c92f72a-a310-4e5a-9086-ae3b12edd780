<template>
  <div class="panel-left-menu" :style="styles" ref="box">
    <div class="panel-left-menu-wrap">
      <Menu v-bind="menuProps" />
    </div>
  </div>
</template>
<script>
import { computed } from 'vue';
import Menu from './menu';
export default {
  props: {
    toggle: {
      type: Boolean,
      default: true,
    },
    isWideDevice: {
      type: Boolean,
      default: true,
    }
  },  
  components: {
    Menu,
  },
  setup(props){
    const menuProps = computed(() => props);
    const styles = computed(() => {
      if(!props.isWideDevice) {
        return {
          width: !props.toggle ? '0' : '210px',
          transition: 'width .2s linear',
        }
      }
      return {
        width: props.toggle ? '65px' : '210px',        
      }
    });
    return {
      menuProps,
      styles
    }
  }
}
</script>
<style lang="scss" scoped>
.panel-left-menu-wrap{
  height: 100%;
  width: 100%;
  overflow-y: auto;
  background-color: $panel-menu-bg;
}
.panel-left-menu{
  position: absolute;
  height: 100%;
  left: 0;
  top: 0;
  transition: all .2s linear;
}
@media screen and(max-width: 998px) {
  .panel-left-menu{
    position: fixed;
    z-index: 999;
    background-color: #fff;
  }
}
</style>