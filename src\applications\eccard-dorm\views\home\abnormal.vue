<template>
  <kade-route-card>
    <template #header>
      <span class="header-title">宿舍设备监控异常情况</span>
    </template>
    <div id="abnormalCharts" class="chartline"></div>
  </kade-route-card>
</template>
<script>
import * as echarts from "echarts";
import { onMounted } from "vue";
export default {
  setup() {
    const echartInit = () => {
      var chartDom = document.getElementById('abnormalCharts');
      var myChart = echarts.init(chartDom);
      var option;

      option = {
        xAxis: {
          type: 'category',
          data: ['门锁不在线', '门锁电量不足', '水表余额不足', '电表余额不足']
        },
        grid: {
          top: 20,
          bottom: 20,
          left: 40,
          right: 0
        },
        yAxis: [
          {
            show: true,
            type: "value",
            splitLine: {
              show: true,
              lineStyle: {
                type: "solid",
              },
            },
            axisLabel: {
              margin: 20,

            },
            minInterval :1
          }
        ],
        series: [
          {
            data: [120, 200, 150, 80],
            type: 'bar',
            barWidth: 50,
            label: {
              show: true,
              distance: 0,
              formatter: '{c}'
            },
          }
        ]
      };

      option && myChart.setOption(option);
    };
    onMounted(() => {
      echartInit();
    });
    return {
      echartInit,
    };
  }
}
</script>
<style scoped lang="scss">
.chartline {
  height: 260px;
  width: 100%;
}
</style>