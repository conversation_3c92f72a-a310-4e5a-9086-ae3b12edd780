<template>
  <el-dialog :model-value="modelValue" title="编辑时段" width="500px" :before-close="handleClose">
    <el-form label-width="100px" size="mini" ref="formRef" :model="state.form" :rules="rules">
      <el-form-item label="时段名称：">
        <el-input :model-value="rowData.name" disabled></el-input>
      </el-form-item>
      <el-form-item label="开始时间：" prop="beginTime">
        <el-time-select v-model="state.form.beginTime" :max-time="state.form.endTime" placeholder="请选择开始时间"
          start="00:00" step="00:15" end="23:59" />
      </el-form-item>
      <el-form-item label="结束时间：" prop="endTime">
        <el-time-select v-model="state.form.endTime" :min-time="state.form.beginTime" placeholder="请选择结束时间"
          start="00:00" step="00:15" end="23:59" />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button type="primary" @click="handleSave" :loading="state.loading" size="mini">保存</el-button>
        <el-button @click="handleClose" size="mini">返回</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { ElDialog, ElButton, ElForm, ElFormItem, ElInput, ElTimeSelect, ElMessage, } from "element-plus"
import { reactive, ref, watch, nextTick } from 'vue'
import { mealPeriodEdit, } from "@/applications/eccard-supermarket/api.js";

export default {
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    rowData: {
      type: Object,
      default: null
    },
  },
  components: {
    ElDialog,
    ElButton,
    ElForm,
    ElFormItem,
    ElInput,
    ElTimeSelect
  },
  setup(props, context) {
    const formRef = ref(null)
    const state = reactive({
      loading: false,
      form: {}
    });
    watch(() => props.modelValue, val => {
      if (val) {
        state.form = { ...props.rowData }
        nextTick(() => {
          formRef.value.clearValidate()
        })
      }
    })
    const rules = {
      beginTime: [
        {
          required: true,
          message: "请选择开始时间",
          trigger: "change",
        },
      ],
      endTime: [
        {
          required: true,
          message: "请选择结束时间",
          trigger: "change",
        },
      ],
    }
    const handleSave = () => {
      formRef.value.validate(async valid => {
        if (valid) {
          try {
            state.loading = true
            let { code, message } = await mealPeriodEdit(state.form)
            if (code === 0) {
              ElMessage.success(message)
              context.emit("update:modelValue", true)
            }
            state.loading = false
          }
          catch {
            state.loading = false
          }
        } else {
          return false
        }
      })
    }
    const handleClose = () => {
      context.emit("update:modelValue", false)
    };
    return {
      formRef,
      state,
      rules,
      handleSave,
      handleClose,
    }
  }
}
</script>

<style lang="scss" scoped>
.el-form {
  margin-top: 20px;
}
</style>