<template>
  <el-dialog :model-value="modelValue" :title="title(type)" width="650px" :before-close="handleClose">
    <el-form label-width="100px" size="mini" ref="formRef" :model="state.form" :rules="rules">
      <el-row>
        <el-col :md="24" :lg="12">
          <el-form-item label="类型名称：" prop="typeName">
            <span v-if="type=='details'">{{state.form.typeName}}</span>
            <el-input v-else v-model="state.form.typeName" placeholder="请输入"></el-input>
          </el-form-item>
        </el-col>
        <el-col :md="24" :lg="12">
          <el-form-item label="最大值：" prop="maxValue">
            <span v-if="type=='details'">{{state.form.maxValue}}</span>
            <el-input v-else v-model="state.form.maxValue" placeholder="请输入"></el-input>
          </el-form-item>
        </el-col>
        <el-col :md="24" :lg="12">
          <el-form-item label="生产厂家：" prop="makeFactory">
            <span v-if="type=='details'">{{state.form.makeFactory}}</span>
            <el-input v-else v-model="state.form.makeFactory" placeholder="请输入"></el-input>
          </el-form-item>
        </el-col>
        <el-col :md="24" :lg="12">
          <el-form-item label="规格：" prop="deviceSpecifications">
            <span v-if="type=='details'">{{state.form.deviceSpecifications}}</span>
            <el-input v-else v-model="state.form.deviceSpecifications" placeholder="请输入"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="厂家地址：" prop="factoryAddress">
            <span v-if="type=='details'">{{state.form.factoryAddress}}</span>
            <el-input v-else v-model="state.form.factoryAddress" type="textarea" placeholder="请输入"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer v-if="type!=='details'">
      <span class="dialog-footer">
        <el-button type="primary" @click="handleSave" size="mini">保存</el-button>
        <el-button @click="handleClose" size="mini">返回</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { ElDialog, ElRow, ElCol, ElButton, ElForm, ElFormItem, ElInput, ElMessage } from "element-plus"
import { reactive, ref, watch, nextTick } from 'vue'
import { waterTypeAdd, waterTypeEdit } from "@/applications/eccard-iot/api";
export default {
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    rowData: {
      types: Object,
      default: null
    },
    type: {
      types: String,
      default: ''
    }
  },
  components: {
    ElDialog,
    ElRow, ElCol,
    ElButton,
    ElForm,
    ElFormItem,
    ElInput,
  },
  setup(props, context) {
    const formRef = ref(null)
    const state = reactive({
      isPersoner: false,
      form: {
        receiveMessage: 'TRUE'
      }
    });
    watch(() => props.modelValue, val => {
      if (val) {
        if (props.editType == 'add') {
          state.form = {}
        } else {
          state.form = { ...props.rowData }
        }
        nextTick(() => {
          formRef.value.clearValidate()
        })
      }
    })
    const title = (val) => {
      if (val == 'add') {
        return '新增水表类型'
      } else if (val == 'edit') {
        return '编辑水表类型'
      } else if (val == 'details') {
        return '水表类型详情'
      }
    }
    const rules = {
      typeName: [
        {
          required: true,
          message: "请输入类型名称",
          trigger: "blur",
        },
        {
          max: 50,
          message: "类型名称长度不得超过50字符",
        },
      ],
      maxValue: [
        {
          required: true,
          message: "请输入最大值",
          trigger: "blur",
        },
        {
          pattern: /^[+]{0,1}(\d+)$/,
          message: "请输入正整数",
        },
      ],
      makeFactory: [
        {
          required: true,
          message: "请输入生产厂家",
          trigger: "blur",
        },
        {
          max: 200,
          message: "生产厂家长度不得超过200字符",
        },
      ],
      deviceSpecifications: [
        {
          required: true,
          message: "请输入规格",
          trigger: "blur",
        },
        {
          max: 200,
          message: "规格长度不得超过200字符",
        },
      ],
      factoryAddress: [
        {
          required: true,
          message: "请输入厂家地址",
          trigger: "blur",
        },
        {
          max: 200,
          message: "厂家地址长度不得超过200字符",
        },
      ],
    }
    const handleSave = () => {
      formRef.value.validate(async valid => {
        if (valid) {
          let fn = props.type == 'add' ? waterTypeAdd : waterTypeEdit
          state.loading = true
          try {
            let { code, message } = await fn(state.form)
            if (code === 0) {
              ElMessage.success(message)
              context.emit("update:modelValue", true)
            }
            state.loading = false
          }
          catch {
            state.loading = false
          }
        } else {
          return false
        }
      })
    }
    const handleClose = () => {
      context.emit("update:modelValue", false)
    };
    return {
      formRef,
      state,
      title,
      rules,
      handleSave,
      handleClose,
    }
  }
}
</script>

<style lang="scss" scoped>
.el-form {
  margin-top: 20px;
}
.el-input {
  width: 211px;
}
:deep(.single-image-uploader) {
  width: 100px;
  height: 100px;
  .el-upload {
    width: 100px;
    height: 100px;
  }
  .element-icons {
    font-size: 40px !important;
  }
}
</style>