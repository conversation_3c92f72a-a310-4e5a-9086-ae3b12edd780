<template>
  <div class="padding-box">
    <el-table :data="state.dataList" v-loading="state.loading" border>
      <el-table-column show-overflow-tooltip v-for="(item, index) in column" :key="index" :label="item.label" :prop="item.prop" align="center">
        <template #default="scope" v-if="item.render">
          {{ item.render(scope.row[item.prop]) }}
        </template>
      </el-table-column>

      <el-table-column label="操作" align="center">
        <template #default="scope">
          <!-- <span @click="details(scope.row)">详情</span> -->
          <span @click="unbind(scope.row)" class="green">取消绑定</span>
        </template>
      </el-table-column>
    </el-table>
    <div style="text-align: right; margin-top: 10px">
      <el-pagination background :current-page="state.form.currentPage" :page-size="state.form.pageSize" layout="total, sizes, prev, pager, next, jumper" :page-sizes="[5, 10, 20, 50, 100]" :total="state.total" @current-change="handlePageChange" @size-change="handleSizeChange">
      </el-pagination>
    </div>
  </div>
</template>
<script>
import {
  ElTable,
  ElTableColumn,
  ElPagination,
  ElMessageBox,
  ElMessage,
} from "element-plus";
import { reactive } from "@vue/reactivity";
// import { bindInfo, unbindDevice } from "@/applications/eccard-iot/api";
import { onMounted } from "@vue/runtime-core";
import { filterDictionary } from "@/utils";
import { useDict } from "@/hooks/useDict";

const column = [
  { label: "所属区域", prop: "areaName" },
  { label: "所属商户", prop: "merchantName" },
  {
    label: "设备状态",
    prop: "deviceStatus",
    render: (val) => {
      return filterDictionary(val, useDict("SYS_DEVICE_STATICE"));
    },
  },
  { label: "机号", prop: "deviceNo" },
  { label: "设备名称", prop: "deviceName" },
  { label: "设备型号", prop: "deviceModel" },
  {
    label: "连接类型",
    prop: "deviceConnectType",
    render: (val) => {
      return filterDictionary(val, useDict("SYS_DEVICE_CONNECT_TYPE"));
    },
  },
  { label: "设备IP", prop: "deviceIp" },
];
export default {
  components: {
    ElTable,
    ElTableColumn,
    ElPagination,
  },
  props: {
  },
  setup(props) {
    const state = reactive({
      loading: false,
      form: {
        currentPage: 1,
        pageSize: 10,
      },
      dataList: [],
      total: 0,
    });

    const getList = async () => {
    };
    const unbind = async (row) => {
      ElMessageBox.confirm("确认取消绑定?", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        state.loading = true
        try {
          let { code, message } = await props.TabModule().cancelBindFnc(row.relationId);
          if (code === 0) {
            ElMessage.success(message);
            state.loading = false
            getList();
          }
        }
        catch {
          state.loading = false
        }
      });
    };
    const handlePageChange = (val) => {
      state.form.currentPage = val;
      getList();
    };
    const handleSizeChange = (val) => {
      state.form.currentPage = 1;
      state.form.pageSize = val;
      getList();
    };
    onMounted(() => {
      getList();
    });
    return {
      column,
      state,
      unbind,
      handlePageChange,
      handleSizeChange,
    };
  },
};
</script>
<style lang="scss" scoped>
</style>