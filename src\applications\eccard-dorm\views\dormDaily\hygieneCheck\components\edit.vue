<template>
  <el-dialog :model-value="modelValue" :title="title" width="1340px" :before-close="handleClose">
    <el-row class="table" :gutter="10" v-loading="state.loading">
      <el-col :span="12">
        <div class="check-info">
          <div class="title">检查信息</div>
          <el-divider></el-divider>
          <el-form inline label-width="100px" size="mini" :model="state.form" ref="formRef"
            :rules="dialogType !== 'details' && rules">
            <el-form-item v-if="dialogType == 'details'" label="所属区域：">
              <el-input :model-value="state.form.areaName" readonly />
            </el-form-item>
            <el-form-item v-if="dialogType == 'details'" label="楼栋：">
              <el-input :model-value="state.form.buildName" readonly />
            </el-form-item>
            <el-form-item v-if="dialogType == 'details'" label="单元：">
              <el-input :model-value="state.form.unitName" readonly />
            </el-form-item>
            <el-form-item v-if="dialogType == 'details'" label="楼层：">
              <el-input :model-value="state.form.floorName" readonly />
            </el-form-item>
            <el-form-item v-if="dialogType == 'details'" label="房间：">
              <el-input :model-value="state.form.roomName" readonly />
            </el-form-item>
            <kade-linkage-select v-if="dialogType !== 'details'" :isEdit="modelValue ? true : false" :data="linkageData"
              :value="state.form" @change="linkageChange" />
            <el-form-item label="检查日期：" prop="checkTime">
              <el-input v-if="dialogType == 'details'" :model-value="state.form.checkTime" readonly />
              <el-date-picker v-else v-model="state.form.checkTime" type="date" placeholder="请选择日期" />
            </el-form-item>
            <el-form-item label="检查人：" prop="checkPerson">
              <el-input clearable v-if="dialogType == 'details'" :model-value="state.form.checkPerson" readonly>
              </el-input>
              <el-input clearable v-else v-model="state.form.checkPerson" maxlength="10" placeholder="请输入检查人"></el-input>
            </el-form-item>
            <el-form-item label="检查结果：" prop="checkResult">
              <el-input clearable v-if="dialogType == 'details'" :model-value="dictionaryFilter(state.form.checkResult)" readonly>
              </el-input>
              <el-select v-else v-model="state.form.checkResult" placeholder="请选择">
                <el-option v-for="(item, index) in checkResultList" :key="index" :label="item.label"
                  :value="item.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="是否通报：" prop="whetherNotify">
              <el-input clearable v-if="dialogType == 'details'" :model-value="dictionaryFilter(state.form.whetherNotify)" readonly>
              </el-input>
              <el-select v-else v-model="state.form.whetherNotify" placeholder="请选择">
                <el-option v-for="(item, index) in whetherNotifyList" :key="index" :label="item.label"
                  :value="item.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="得分：" prop="checkScore">
              <el-input v-if="dialogType == 'details'" :model-value="state.form.checkScore" readonly></el-input>
              <el-input v-else v-model="state.form.checkScore" placeholder="请输入检查得分"></el-input>
            </el-form-item>
            <el-form-item label="检查情况：">
              <el-input v-if="dialogType == 'details'" type="textarea" :rows="5" :model-value="state.form.checkRemark"
                readonly></el-input>
              <el-input v-else type="textarea" :rows="5" v-model="state.form.checkRemark" placeholder="请输入检查情况">
              </el-input>
            </el-form-item>
            <el-form-item label="附件：">
              <el-image v-if="dialogType == 'details'" style="width: 100px; height: 100px" :src="state.form.resourceUrl"
                :preview-src-list="[state.form.resourceUrl]" :initial-index="0" fit="cover"></el-image>
              <kade-single-image-upload v-else v-model="state.form.resourceUrl" :action="uploadApplyLogo"
                icon="el-icon-plus" />
            </el-form-item>
          </el-form>
        </div>
      </el-col>
      <el-col :span="12">
        <div class="person-grade">
          <div class="title">个人打分</div>
          <el-divider></el-divider>
          <el-table :header-cell-style="{ background: '#f9fafc' }" :data="state.form.scoreList" height="355px"  :key="1">
            <el-table-column show-overflow-tooltip v-for="(item, index) in column" :key="index" :prop="item.prop"
              :label="item.label" align="center"></el-table-column>
            <el-table-column prop="checkScore" label="检查得分" align="center" max-width="200" width="200px">
              <template #default="scope">
                <el-input v-model="scope.row.checkScore" placeholder="请输入分数" size="mini"
                  onkeyup="value=value.replace(/\D|^0/g,'')"
                  oninput="if(value>10)value=10;if(isNaN(Number(value)))value=0;if(value<0)value=0"
                  :readonly="dialogType == 'details'" @change="handleChange(scope.$index,scope.row)"></el-input>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-col>
    </el-row>
    <template #footer>
      <span class="dialog-footer" v-if="dialogType != 'details'">
        <el-button @click="handleClose" size="mini">取消</el-button>
        <el-button type="primary" @click="submit" size="mini">确认</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { reactive, } from "@vue/reactivity";
import { useStore } from "vuex"
import { useDict } from "@/hooks/useDict"
import SingleImageUpload from "@/components/singleImageUpload";
import linkageSelect from '@/applications/eccard-dorm/components/linkageSelect'
import { getRoomStayInfo, editHygieneCheck, addHygieneCheck } from "@/applications/eccard-dorm/api"
import { dateStr } from "@/utils/date.js"
import {
  ElDialog,
  ElButton,
  ElRow,
  ElCol,
  ElForm,
  ElFormItem,
  ElDivider,
  ElSelect,
  ElOption,
  ElInput,
  ElDatePicker,
  ElTable,
  ElImage,
  ElTableColumn,
  ElMessage,
} from "element-plus";
import {
  computed, ref,
  watch,
  nextTick,
} from "vue";
import { uploadApplyLogo } from "@/applications/unified_portal/api";

const column = [
  { label: "床位号", prop: "bedNum" },
  { label: "人员编号", prop: "userCode" },
  { label: "人员姓名", prop: "userName" },
  { label: "组织机构", prop: "deptName" },
];
const linkageData = {
  area: { label: '所属区域：', valueKey: 'areaId', key: 'id' },
  building: { label: '楼栋：', valueKey: 'buildId' },
  unit: { label: '单元：', valueKey: 'unitNum' },
  floor: { label: '楼层：', valueKey: 'floorNum' },
  room: { label: '房间：', valueKey: 'roomId' },
}
const rules = {
  roomId: [{ required: true, message: "请选择房间", trigger: "blur" }],
  checkTime: [{ required: true, message: "请选择检查日期", trigger: "blur" }],
  checkPerson: [{ required: true, message: "请输入检查人", trigger: 'blur' }],
  checkResult: [{ required: true, message: "请选择检查结果", trigger: "blur" }],
  whetherNotify: [{ required: true, message: "请选择是否通报", trigger: "blur" }],
  checkScore: [{ required: true, message: "请输入检查得分", trigger: "blur" }, { pattern:  /^([0-9]|10)$/, message: "检查得分值范围为0~10" }],
};
export default {
  props: {
    modelValue: {
      type: Boolean,
      default: false,
    },
    dialogType: {
      type: String,
      default: "",
    },
    selectRow: {
      type: Object,
      default: null
    }
  },
  components: {
    ElDialog,
    ElButton,
    ElRow,
    ElCol,
    ElForm,
    ElFormItem,
    ElDivider,
    ElSelect,
    ElOption,
    ElInput,
    ElDatePicker,
    ElTable,
    ElTableColumn,
    ElImage,
    "kade-linkage-select": linkageSelect,
    "kade-single-image-upload": SingleImageUpload,
  },
  setup(props, context) {
    const stroe = useStore()
    const checkResultList = useDict('DORM_DAILY_CHECK_RESULT')//检查结果
    const whetherNotifyList = useDict('SYS_BOOL_STRING')//是否通报
    const formRef = ref(null);
    const state = reactive({
      form: {
        resourceUrl: "",
        scoreList: [],
      },
      loading: false,
    })
    const handleClose = () => {
      context.emit("update:modelValue", false);
      formRef.value.clearValidate()
    }
    const linkageChange = (val) => {
      state.form = { ...state.form, ...val }
      if (val.roomId) {
        getRoomList(val.roomId)
      }
    }
    const title = computed(() => {
      if (props.dialogType === 'add') {
        return '新建卫生检查'
      } else if (props.dialogType === 'details') {
        return '查看卫生检查'
      } else {
        return '编辑卫生检查'
      }
    })
    watch(() => props.modelValue, val => {
      if (val) {
        if (props.dialogType == 'add') {
          state.form = {
            resourceUrl: "",
            scoreList: []
          }
        } else {
          let { areaId, buildId, unitNum, floorNum, floorName, unitName, roomId, checkTime, roomName, checkPerson, checkResult, whetherNotify, checkScore, checkRemark, resourceUrl, id, scoreList, areaName, buildName, } = JSON.parse(JSON.stringify(props.selectRow))
          state.form = { areaId, buildId, unitNum, floorNum, floorName, unitName, roomId, roomName, checkTime, checkPerson, checkResult, whetherNotify, checkScore, checkRemark, resourceUrl, id, scoreList, areaName, buildName, }
          console.log(state.form.checkTime);
        }
        nextTick(() => {
          formRef.value.clearValidate()
        })
      }
    })
    const isReadOnly = computed(() => {
      return props.dialogType === "details" ? false : false
    })

    const getRoomList = async (roomId) => {
      let { data } = await getRoomStayInfo({ roomId })
      state.form.scoreList = data.map(item => {
        return {
          ...item,
          checkScore: 10
        }
      })
    }
    const handleChange=(index,row)=>{
      state.form.scoreList[index].checkScore=row.checkScore
    }
    const submit = () => {
      formRef.value.validate(async (valid) => {
        if (valid) {
          let params = { ...state.form }
          params.checkTime = dateStr(params.checkTime)
          let fn = props.dialogType == 'add' ? addHygieneCheck : editHygieneCheck
          state.loading = true
          try {
            let { code, message } = await fn(params)
            if (code === 0) {
              ElMessage.success(message)
              context.emit('update:modelValue', true)
            }
            state.loading = false
          }
          catch {
            state.loading = false
          }
        }
      })
    }
    return {
      state,
      formRef,
      submit,
      linkageData,
      linkageChange,
      rules,
      title,
      stroe,
      checkResultList,
      handleChange,
      column,
      uploadApplyLogo,
      isReadOnly,
      handleClose,
      whetherNotifyList,
    };
  }

}
</script>

<style lang="scss" scoped>
.table {
  width: 100%;
  margin: 10px 15px 30px 10px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;

  .title {
    margin: 10px;
  }

  .check-info {
    border: 1px solid #eeeeee;
    border-radius: 5px;
    padding-bottom: 14px;
  }

  .person-grade {
    width: 100%;
    border: 1px solid #eeeeee;
    border-radius: 5px;

    .el-table {
      width: 100%;
      padding: 15px;
    }
  }
}

:deep(.el-table) {

  .el-input,
  .el-input__inner {
    width: 100px !important;
  }
}

:deep(.single-image-uploader) {
  width: 100px;
  height: 100px;

  .el-upload {
    width: 100px;
    height: 100px;
  }

  .element-icons {
    font-size: 40px !important;
  }
}
</style>