<template>
  <kade-route-card>
    <kade-table-filter @search="handleSearch" @reset="handleReset">
      <el-form :model="state.form" inline label-width="100px" size="mini">
        <el-form-item label="访客姓名">
          <el-input clearable v-model="state.form.visitorName" placeholder="输入访客姓名搜索"></el-input>
        </el-form-item>
        <kade-linkage-select :data="linkageData" :value="state.form" @change="linkageChange" />
        <el-form-item label="被访人">
          <el-input v-model="state.form.intervieweeName" placeholder="输入访客姓名搜索"></el-input>
        </el-form-item>
        <el-form-item label="访问时间">
          <el-date-picker v-model="state.requestDate" :defaultTime="defaultTime" type="datetimerange" range-separator="~" start-placeholder="开始时间"
            end-placeholder="结束时间" />
          <!-- <el-date-picker v-model="state.requestDate" type="daterange" range-separator="~" start-placeholder="开始时间"
            end-placeholder="结束时间" /> -->
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="访客列表">
      <template #extra>
        <el-button type="success" icon="el-icon-plus" size="mini" @click="edit('add')">新增</el-button>
        <el-button type="primary" icon="el-icon-delete-solid" size="mini" @click="batchDel()">批量删除</el-button>
      </template>
      <el-table :data="state.data" border v-loading="state.loading" @selection-change="selectChange" height="55vh">
        <el-table-column type="selection" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip v-for="(item, index) in column" :key="index" :label="item.label"
          :prop="item.prop" :width="item.width" align="center">
          <template #default="scope">
            {{ dictionaryFilter(scope.row[item.prop]) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="200px">
          <template #default="scope">
            <el-button type="text" class="green" size="mini" @click="edit('details', scope.row)">详情</el-button>
            <el-button type="text" class="green" size="mini" @click="edit('edit', scope.row)">编辑</el-button>
            <el-button type="text" class="green" size="mini" @click="handleDel(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination v-model:currentPage="state.form.currentPage" v-model:page-size="state.form.pageSize"
          :page-sizes="[10, 20, 30, 40]" background layout="total, sizes, prev, pager, next, jumper"
          :total="state.total" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      </div>
    </kade-table-wrap>
    <kade-visitor-edit v-model:modelValue="state.isShow" :type="state.type" :rowData="state.rowData"
      @update:modelValue="close" />
  </kade-route-card>
</template>

<script>
import { reactive, onMounted } from 'vue'
import { getVisitor, delVisitor, batchVisitor } from "@/applications/eccard-dorm/api.js"
import edit from './components/edit'
import linkageSelect from '@/applications/eccard-dorm/components/linkageSelect'
import { ElForm, ElFormItem, ElInput, ElDatePicker, ElPagination, ElTable, ElTableColumn, ElButton, ElMessage, ElMessageBox } from "element-plus"
import { timeStr } from "@/utils/date.js"
const linkageData = {
  area: { label: '所属区域', valueKey: 'areaPath', key: 'areaPath' },
  building: { label: '楼栋', valueKey: 'buildId' },
  unit: { label: '单元', valueKey: 'unitNum' },
  floor: { label: '楼层', valueKey: 'floorNum' },
  room: { label: '房间', valueKey: 'roomId' }
}
const column = [
  { label: '访客姓名', prop: 'visitorName' },
  { label: '访客类型', prop: 'visitorType' },
  { label: '被访人编号', prop: 'intervieweeCode' },
  { label: '被访人姓名', prop: 'intervieweeName' },
  { label: '被访人组织', prop: 'intervieweeDeptName' },
  { label: '所属区域', prop: 'areaName' },
  { label: '房间', prop: 'roomString', width: '200px' },
  { label: '登记时间', prop: 'createTime', width: '200px' }
]
const defaultTime=[
    new Date(new Date(new Date().toLocaleDateString()).getTime()),
        new Date(
            new Date(new Date().toLocaleDateString()).getTime()+
    24 * 60 * 60 * 1000 - 1)
  ]
export default {
  components: {
    ElForm, ElFormItem, ElInput, ElDatePicker, ElPagination, ElTable, ElTableColumn, ElButton,
    "kade-linkage-select": linkageSelect,
    "kade-visitor-edit": edit
  },
  setup() {
    const state = reactive({
      form: {
        pageSize: 10,
        currentPage: 1
      },
      data: [],
      isShow: false,
      loading: false,
      type: '',
      total: 0,
      rowData: '',
      requestDate: [],
      selectData: ''
    })
    const handleSearch = () => {
      getList()
    }
    const handleReset = () => {
      state.form = {
        currentPage: 1,
        pageSize: 10
      }
      state.requestDate = []
      getList()
    }
    const linkageChange = (val) => {
      state.form = { ...state.form, ...val }
    }
    const handleCurrentChange = (val) => {
      state.form.currentPage = val
      getList()
    }
    const handleSizeChange = (val) => {
      state.form.currentPage = 1
      state.form.pageSize = val
      getList()
    }
    const getList = async () => {
      if (state.requestDate && state.requestDate.length) {
        state.form.visitorBeginDate = timeStr(state.requestDate[0])
        state.form.visitorEndDate = timeStr(state.requestDate[1])
      } else {
        delete state.form.visitorBeginDate
        delete state.form.visitorEndDate
      }
      state.loading = true
      try {
        let { data: { list, total } } = await getVisitor(state.form)
        state.data = list
        state.total = total
        state.loading = false
      } catch {
        state.loading = false
      }
    }
    const handleDel = (row) => {
      ElMessageBox.confirm(`确认删除？`, {
        type: 'warning',
        confirmButtonText: '确认',
        cancelButtonText: '取消'
      }).then(async () => {
        let { code, message } = await delVisitor(row.id)
        if (code === 0) {
          ElMessage.success(message)
          getList()
        }
      })
    }
    const batchDel = () => {
      if (!state.selectData.length) {
        return ElMessage.error('请先选择要删除信息!')
      }
      ElMessageBox.confirm(`确认删除已选择信息`, `提示`, {
        type: 'warning',
        confirmButtonText: '确认',
        cancelButtonText: '取消'
      }).then(async () => {
        let param = state.selectData.map((item) => item.id).join(',')
        let { code, message } = await batchVisitor(param)
        if (code === 0) {
          ElMessage.success(message)
          getList()
          state.loading = false
        }
      })

    }
    const selectChange = (val) => {
      state.selectData = val
    }
    const edit = (type, row) => {
      state.isShow = true
      state.type = type
      state.rowData = row
    }
    const close = (val) => {
      if (val) {
        getList()
      }
      state.isShow = false
    }
    onMounted(() => {
      getList()
    })
    return {
      state,
      edit,
      close,
      column,
      linkageData,
      selectChange,
      defaultTime,
      handleDel,
      batchDel,
      handleReset,
      handleSearch,
      linkageChange,
      handleCurrentChange,
      handleSizeChange,
    }
  }
}
</script>

<style lang="scss" scoped>
:deep(.el-input--mini .el-input__inner) {
  width: 198px;
}

:deep(.el-pagination .el-select .el-input .el-input__inner) {
  width: 100px;
}

:deep(.el-pagination__editor.el-input .el-input__inner) {
  width: 46px;
}

.green :hover {
  text-decoration: underline;
}

:deep(.el-dialog) {
  border-radius: 6px;
  padding-bottom: 40px;
}

:deep(.el-dialog__header) {
  border-bottom: 1px solid #eeeeee;
  margin-bottom: 10px;
}

:deep(.el-dialog__footer) {
  border: none;
  text-align: center;
  margin-top: 10px;
}

:deep(.el-textarea__inner) {
  width: 516px;
}

:deep(.el-divider--horizontal) {
  margin: 0 0 20px;
}
</style>
