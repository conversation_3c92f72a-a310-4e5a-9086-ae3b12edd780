<template>
  <kade-modal v-bind="attrs" :modelValue="modelValue" @update:modelValue="update">
    <el-form
      ref="formRef"
      :label-width="labelWidth"
      :rules="rules"
      :model="model"
      size="small"
    >
      <el-form-item label="截止日期" prop="endDay">
        <el-datepicker placeholder="请选择" v-model="model.endDay" />
      </el-form-item>
      <el-form-item label="待办事项" prop="taskMsg">
        <el-input show-word-limit maxlength="200" type="textarea" :rows="5" placeholder="请输入" v-model="model.taskMsg" />
      </el-form-item>      
    </el-form>
    <template #footer>
      <el-button icon="el-icon-circle-close" @click="cancel" size="small">取消</el-button>
      <el-button icon="el-icon-circle-check" :loading="loading" @click="submit" size="small" type="primary">保存</el-button>
    </template>
  </kade-modal>
</template>
<script>
import Modal from '@/components/modal';
import { computed, reactive, ref } from 'vue';
import { ElButton, ElForm, ElFormItem, ElInput, ElDatePicker } from 'element-plus';
import moment from 'moment';
import { createTodo } from '@/applications/unified_portal/api';
export default {
  emits: ['update:modelValue', 'change'],
  props: {
    title: {
      type: String,
      default: ''
    },
    modelValue: {
      type: Boolean,
      default: false,
    },
  },
  components: {
    'kade-modal': Modal,
    'el-button': ElButton,
    'el-form': ElForm,
    'el-form-item': ElFormItem,
    'el-input': ElInput,
    'el-datepicker': ElDatePicker,
  },
  setup(props, context) {
    const formRef = ref(null);
    const loading = ref(false);
    const model = reactive({
      endDay: moment().toDate(),
      taskMsg: ''
    });
    const rules = {
      endDay: [
        { required: true, message: '请选择截止日期', trigger: 'change' },
      ],
      taskMsg: [
        { required: true, message: '请输入待办事项内容' },
        { max: 200, message: '内容不能超过200字符' }
      ]
    };
    const cancel = () => {
      formRef.value?.resetFields();
      context.emit('update:modelValue', false);
    }
    const submit = () => {
      formRef.value?.validate(async (valid) => {
        if(valid) {
          try {
            loading.value = true;
            const data = await createTodo({
              ...model,
              endDay: moment(model.endDay).format('YYYY-MM-DD'),
            });
            context.emit('update:modelValue', false);
            context.emit('change', data);
          }catch(e) {
            throw new Error(e.message);
          }finally{
            loading.value = false;
          }
        }
      });      
    }
    const attrs = computed(() => {
      // eslint-disable-next-line no-unused-vars
      const { modelValue, ...attrs } = props;
      return attrs;
    });
    const update = (v) => {
      context.emit('update:modelValue', v);
    }
    return {
      attrs,
      update,
      formRef,
      cancel,
      submit,
      rules,
      labelWidth: THEMEVARS.formLabelWidth,
      model,
      loading,
    }
  }
}
</script>