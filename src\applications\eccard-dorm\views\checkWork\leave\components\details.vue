<template>
  <el-dialog :model-value="dialogVisible" title="学生请假记录详情" width="680px" :before-close="handleClose">
    <el-form inline label-width="110px" size="mini">
      <el-form-item label="人员编号：">
        <el-input disabled size="mini" :model-value="state.form.userCode"></el-input>
      </el-form-item>
      <el-form-item label="人员姓名：">
        <el-input disabled size="mini" :model-value="state.form.userName"></el-input>
      </el-form-item>
      <el-form-item label="组织机构">
        <el-input disabled size="mini" :model-value="state.form.deptName"></el-input>
      </el-form-item>
      <el-form-item label="请假原因">
        <el-input disabled size="mini" :model-value="state.form.reason"></el-input>
      </el-form-item>
       <el-form-item label="楼栋">
        <el-input disabled size="mini" :model-value="state.form.buildName"></el-input>
      </el-form-item>
      <el-form-item label="单元号">
        <el-input disabled size="mini" :model-value="state.form.unitNum"></el-input>
      </el-form-item>
      <el-form-item label="楼层号">
        <el-input disabled size="mini" :model-value="state.form.floorNum"></el-input>
      </el-form-item>
      <el-form-item label="房间号">
        <el-input disabled size="mini" :model-value="state.form.roomName"></el-input>
      </el-form-item>
      <el-form-item label="请假时间：">
        <el-input class="leave-time" disabled :model-value="state.form.leavePeriod"></el-input>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script>
import { ElDialog, ElForm, ElFormItem, ElInput } from "element-plus";
import { reactive } from "@vue/reactivity";
import { watch } from 'vue';
export default {
  components: {
    ElDialog,
    ElForm,
    ElFormItem,
    ElInput,
  },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    rowData: {
      type: String,
      default: ''
    }
  },
  setup(props, context) {
    const state = reactive({
      form: {},
    });
    const handleClose = () => {
      context.emit('close', false)
    };
    watch(() => props.rowData, val => {
      state.form = val
    })
    return {
      state,
      handleClose,
    };
  },
};
</script>
<style lang="scss" scoped>
.el-form-item {
  margin-bottom: 5px;
}
:deep(.leave-time){
    width: 507px;
    .el-input__inner{
        width: 507px !important;
    }
}
</style>