<template>
  <el-dialog
    title="打印"
    :model-value="true"
    width="100%"
    fullscreen
    custom-class="print-dialog"
    append-to-body
    destroy-on-close 
    :before-close="handleClose">
    <div class="print-table">
      <el-table :data="dataList" border stripe>
        <template v-for="item in columns" :key="item.prop">
          <el-table-column v-bind="item" v-if="item.show">
            <template #default="scope" v-if="item.template">
              <component :is="item.template" v-bind="scope"></component>
            </template>
          </el-table-column>
        </template>                                  
      </el-table>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button size="small" icon="el-icon-circle-close" @click="handleClose">取消</el-button>
        <el-button size="small" icon="el-icon-circle-check" type="primary" @click="handleSure">确定</el-button>
      </div>
    </template>
  </el-dialog>  
</template>
<script>
// 表格打印预览组件，传入二次封装表格组件的columns
import {
  ElButton,
  ElDialog,
  ElTable,
  ElTableColumn,
} from 'element-plus';
export default {
  components: {
    'el-dialog': ElDialog,
    'el-button': ElButton,
    'el-table': ElTable,
    'el-table-column': ElTableColumn,
  },
  props: {
    dataList: {
      type: Array,
      default: () => ([])
    },
    columns: {
      type: Array,
      default: () => ([]),
    },
    close: {
      type: Function,
      default: () => (() => {})
    }
  },
  setup(props) {
    const handleSure = () => {
      window.print();
    }
    const handleClose = () => {
      props.close();
    }
    return {
      handleSure,
      handleClose,
    }
  }
}
</script>
<style lang="scss">
.print-dialog{
  .el-dialog__header{
    border-bottom: 1px solid $border-color;
    padding-bottom: 20px;
  }
  .dialog-footer{
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .el-dialog__body{
    height: calc(100% - 138px);
    overflow-y: auto;
  }
  .el-table{
    width: 800px;
    margin: 20px auto;
  }
}
@media print{
  .print-dialog{
    .el-dialog__header{
      display: none;
    }
    .el-dialog__footer{
      display: none;
    }
  }
}
</style>