<template>
  <div class="box">
    <div class="box-left">
      <div class="card" v-if="personCardInfo.userName">
        <div class="card-top">
          <div class="school-name">{{ personCardInfo.tenantName }}</div>
          <div class="card-number">{{ personCardInfo.cardNo }}</div>
        </div>
        <div class="student-name">{{ personCardInfo.userName }}</div>
        <div class="card-date">
          有效期：{{ timeStrDate(personCardInfo.cardEffectiveDate) }}
        </div>
      </div>
      <div class="card" v-else>
        <div style="color: #fff; text-align: center; line-height: 170px">
          未发卡
        </div>
      </div>
      <el-card v-if="personCardInfo.userName">
        <div class="card-status">
          <div>卡片状态</div>
          <div :class="
            personCardInfo.cardStatus == 'NORMAL_CARD' ? 'green' : 'red'
          ">
            <span>• &nbsp;</span>{{
                filterDictionary(personCardInfo.cardStatus, state.cardStatusList)
            }}
          </div>
        </div>
      </el-card>
      <el-button v-if="
        personCardInfo.userName && personCardInfo.cardStatus == 'NORMAL_CARD'
      " style="width: 100%; margin-top: 50px" type="primary" round @click="loss()">挂失</el-button>
      <el-button v-if="
        personCardInfo.userName && personCardInfo.cardStatus == 'LOSS_CARD'
      " style="width: 100%; margin-top: 50px" type="primary" round @click="relieveLoss()">解除挂失</el-button>
    </div>
    <div class="box-right">
      <kade-route-card style="height: auto">
        <kade-tab-wrap :tabs="tabs" style="margin-top: 20px" v-model="state.tab">
          <template #czjl>
            <el-form inline size="small" label-width="100px">
              <el-form-item label="选择日期:">
                <el-date-picker v-model="state.requestDate" type="datetimerange" :default-time="state.defaultTime"
                  range-separator="~" start-placeholder="请选择日期" end-placeholder="请选择日期" @change="changeDate">
                </el-date-picker>
              </el-form-item>
              <el-form-item label="操作类型:">
                <el-select clearable v-model="state.form.operationType" placeholder="请选择">
                  <el-option v-for="(item, index) in state.operationTypeList" :key="index" :label="item.dictValue"
                    :value="item.dictCode">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-button @click="search()" size="small" type="primary" icon="el-icon-search">搜索</el-button>
              <el-button icon="el-icon-refresh-right" @click="reset()" size="small">重置</el-button>
            </el-form>
            <el-table style="width: 100%" :data="cardLogList" v-loading="state.cardLogListLoading" highlight-current-row
              border stripe>
              <el-table-column label="操作时间" width="200" prop="tradeNo" align="center">
                <template #default="scope">
                  <div>
                    {{ timeStrDate(scope.row.operationTime) }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="operationUser" label="操作人" align="center">
              </el-table-column>

              <el-table-column label="操作类型" align="center">
                <template #default="scope">
                  <div>
                    {{
                        filterDictionary(
                          scope.row.operationType,
                          state.operationTypeList
                        )
                    }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="操作端" prop="operationSourceName" align="center"></el-table-column>
            </el-table>
            <div class="pagination">
              <el-pagination background :current-page="state.form.currentPage"
                layout="total, sizes, prev, pager, next, jumper" :page-size="state.form.pageSize"
                :page-sizes="[6, 10, 20, 50, 100]" :total="cardLogTotal" @current-change="handlePageChange"
                @size-change="handleSizeChange">
              </el-pagination>
            </div>
          </template>
        </kade-tab-wrap>
      </kade-route-card>
    </div>
  </div>
</template>

<script>
import { getPersonCardLog, lossPersonCard } from "@/applications/eccard-finance/api";
import { computed, onMounted, reactive } from "vue";
import { timeStr } from "@/utils/date.js";
import { useStore } from "vuex";
import { requestDefaultTime, requestDate } from "@/utils/reqDefaultDate.js";
import {
  ElCard,
  ElButton,
  ElForm,
  ElFormItem,
  ElDatePicker,
  ElSelect,
  ElOption,
  ElTable,
  ElTableColumn,
  ElPagination,
  ElMessage,
} from "element-plus";
const tabs = [
  {
    name: "czjl",
    label: "操作记录",
  },
];
export default {
  components: {
    ElCard,
    ElButton,
    ElForm,
    ElFormItem,
    ElDatePicker,
    ElSelect,
    ElOption,
    ElTable,
    ElTableColumn,
    ElPagination,
  },
  setup() {
    const store = useStore();
    const state = reactive({
      tab: "czjl",
      departCheckList: [],
      costTypelist: [],
      cardLogListLoading: false,
      form: {
        beginDate: requestDate()[0],
        endDate: requestDate()[1],
        currentPage: 1,
        pageSize: 6,
        physicalcardId: store.state.data.personCardInfo.physicalcardId,
        userId: store.state.data.personCardInfo.userId,
        operationType: "",
      },
      requestDate: requestDate(),
      defaultTime: requestDefaultTime(),
      cardStatusList: JSON.parse(
        localStorage.getItem("kade_cache_dictionary")
      ).filter((item) => item.dictType == "PERSON_CARD_STATUS"), //卡片状态
      operationTypeList: JSON.parse(
        localStorage.getItem("kade_cache_dictionary")
      ).filter((item) => item.dictType == "PERSON_CARD_OPERATION_TYPE"), //操作类型
    });
    //日期格式
    const timeStrDate = computed(() => {
      return timeStr;
    });

    const cardLogList = computed(() => {
      return store.state.data.cardLogList;
    });

    const cardLogTotal = computed(() => {
      return store.state.data.cardLogTotal;
    });

    const filterDictionary = (val, dictionaryList) => {
      for (let i of dictionaryList) {
        if (val == i.dictCode) {
          return i.dictValue;
        }
      }
    };

    const getUserInfo = () => {
      let data2 = {
        beginDate: requestDate()[0],
        endDate: requestDate()[1],
        currentPage: 1,
        pageSize: 6,
      };
      store.dispatch("data/queryPersonCardInfo", {
        data1: { userId: store.state.data.selectPerson.userId },
        data2,
      });
    };

    const getCardLogList = () => {
      state.cardLogListLoading = true;
      state.form.physicalcardId =
        store.state.data.personCardInfo.physicalcardId;
      state.form.userId = store.state.data.personCardInfo.userId;
      getPersonCardLog(state.form)
        .then((res) => {
          store.commit("data/updateState", {
            key: "cardLogList",
            payload: res.data.list,
          });
          store.commit("data/updateState", {
            key: "cardLogTotal",
            payload: res.data.total,
          });
          state.cardLogListLoading = false;
        })
        .catch(() => {
          state.cardLogListLoading = false;
        });
    };

    const personCardInfo = computed(() => {
      return store.state.data.personCardInfo;
    });

    const loss = async () => {
      let data = {
        physicalcardIds: [store.state.data.personCardInfo.physicalcardId],
        type: "CARD_REPORT_LOSS",
      };
      let { code, message } = await lossPersonCard(data);
      if (code === 0) {
        ElMessage.success(message);
        getUserInfo();
      } else {
        ElMessage.error(message);
      }
    };

    const relieveLoss = async () => {
      let data = {
        physicalcardIds: [store.state.data.personCardInfo.physicalcardId],
        type: "CARD_RESOLVE_LOSS",
      };
      let { code, message } = await lossPersonCard(data);
      if (code === 0) {
        ElMessage.success(message);
        getUserInfo();
      } else {
        ElMessage.error(message);
      }
    };

    const changeDate = (val) => {
      state.form.beginDate = timeStr(val[0]);
      state.form.endDate = timeStr(val[1]);
    };
    const handlePageChange = (val) => {
      state.form.currentPage = val;
      getCardLogList(state.form).then((res) => {
        console.log(res);
      });
    };
    const search = () => {
      getCardLogList();
    };

    const handleSizeChange = (val) => {
      state.form.currentPage = 1;
      state.form.pageSize = val;
      getCardLogList();
    };
    const reset = () => {
      state.form = {
        beginDate: requestDate()[0],
        endDate: requestDate()[1],
        currentPage: 1,
        pageSize: 6,
        physicalcardId: store.state.data.personCardInfo.physicalcardId,
        userId: store.state.data.personCardInfo.userId,
        operationType: "",
      };
      state.requestDate = requestDate()
    };

    onMounted(() => {
      getUserInfo();
    });
    return {
      state,
      tabs,
      personCardInfo,
      cardLogList,
      timeStrDate,
      cardLogTotal,
      loss,
      relieveLoss,
      handlePageChange,
      handleSizeChange,
      filterDictionary,
      changeDate,
      search,
      reset,
    };
  },
};
</script>
<style lang="scss" scoped>
.box {
  min-height: 650px;
  // overflow-y: scroll;
  display: flex;

  .box-left {
    width: 335px;
    // background: #f5f5f5;
    padding: 10px;

    .card {
      width: 335px;
      box-sizing: border-box;
      padding: 15px;
      background-image: url(../../../../../../../assets/card_bg.png);
      background-repeat: no-repeat;
      background-size: 335px 180px;
      margin-bottom: 20px;
      height: 180px;
      border-radius: 10px;

      .card-top {
        display: flex;
        justify-content: space-between;
        height: 22px;

        .school-name {
          font-family: "PingFangSC-Semibold", "PingFang SC Semibold",
            "PingFang SC", sans-serif;
          font-weight: 650;
          font-style: normal;
          color: #ffffff;
          font-size: 18px;
        }

        .card-number {
          font-family: "PingFangSC-Regular", "PingFang SC", sans-serif;
          font-weight: 400;
          font-style: normal;
          font-size: 16px;
          color: #ffffff;
        }
      }

      .student-name {
        text-align: center;
        margin: 45px 0 45px;
        font-family: "PingFangSC-Regular", "PingFang SC", sans-serif;
        font-weight: 400;
        font-style: normal;
        font-size: 16px;
        color: #ffffff;
      }

      .card-date {
        text-align: right;
        font-family: "PingFangSC-Regular", "PingFang SC", sans-serif;
        font-weight: 400;
        font-style: normal;
        color: #ffffff;
      }
    }

    .card-status {
      display: flex;
      justify-content: space-between;
    }
  }

  .box-right {
    flex: 1;
    margin-left: 50px;
  }
}
</style>