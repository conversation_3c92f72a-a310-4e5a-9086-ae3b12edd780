<template>
  <div :class="['kade-tab-wrap', 'border-box', { 'is-extra': extra }]">
    <el-tabs :model-value="modelValue" :type="type" :tab-position="tabPosition" @tab-click="handleTabClick" class="new-tabs-header">
      <el-tab-pane
        v-for="tab in tabs"
        :key="tab.name"
        :label="tab.label"
        :name="tab.name"
      >
        <slot :name="tab.name" v-if="modelValue === tab.name" />   
      </el-tab-pane>
    </el-tabs>
    <div class="kade-tab-extra">
      <slot name="extra"></slot>
    </div>
  </div>
</template>
<script>
// 通用Tab标签布局组件
import { ElTabs, ElTabPane } from 'element-plus';
export default {
  components: {
    'el-tabs': ElTabs,
    'el-tab-pane': ElTabPane,
  },
  emits: ['update:modelValue'],
  props: {
    modelValue: {
      type: String,
      default: null,
    },
    tabs: {
      type: Array,
      required: true,
    },
    extra: {
      type: [Boolean, String],
      default: false,
    },
    tabPosition:{
      type:String,
      default:"top"
    },
    type:{
      type:String,
      default:""
    }
  },
  setup(props, context) {
    const handleTabClick = (e) => {
      context.emit('update:modelValue', e.props.name);
      context.emit('active', e);
    }
    return {
      handleTabClick,
    }
  }
}
</script>
<style lang="scss" scopesd>
.kade-tab-wrap{
  position: relative;
  &.is-extra{
    padding-top: 13px;
  }
  .el-tabs__nav-wrap{
    padding:  0 20px;
    box-sizing: border-box;
  }
  .el-tabs__content{
    padding:  5px 20px 20px 20px;
    box-sizing: border-box;
    
    .el-tab-pane{
      transition:all 2s linear;
    }
  }
  .kade-tab-extra{
    position: absolute;
    right: 20px;
    top: 10px
  }
}
/* .el-tabs__item{
  // width: 150px;
  line-height: 50px;
  height: 50px;
  text-align: center;
  // padding: 0;
}
.el-tabs__active-bar {
  width: 150px;
  height: 5px;
} */
</style>