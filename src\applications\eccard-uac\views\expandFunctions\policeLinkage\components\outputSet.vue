<template>
  <el-dialog :model-value="modelValue" title="输出设置" width="1200px" :before-close="handleClose"
    :close-on-click-modal="false">
    <div style="padding:20px 0 10px">
      <el-form label-width="140px" size="mini">
        <el-row>
          <el-col :span="6">
            <el-form-item label="控制器编号：">
              <el-input placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="产品序列SN号：">
              <el-input placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="控制的门：">
              <el-input placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-tabs type="card" @tab-click="handleClick">
              <el-tab-pane v-for="(item, index) in state.form.outputList" :key="index" :label="item.label">
                <div class="padding-box">

                  <el-checkbox v-model="item.checked" size="large">{{`启用${item.label}`}}</el-checkbox>
                  <el-row :gutter="20" style="margin-top:20px" v-if="item.checked">
                    <el-col :span="8">
                      <kade-table-wrap title="触发源" icon="none" style="height:100%">
                        <el-divider></el-divider>
                        <div style="padding:20px 20px 0">
                          <el-radio-group v-model="item.trigger">
                            <el-radio v-for="(it, idx) in triggerList" :key="idx" :label="it.value">{{ it.label }}
                            </el-radio>
                          </el-radio-group>
                        </div>
                      </kade-table-wrap>
                    </el-col>
                    <el-col :span="16" v-if="item.trigger==1">
                      <kade-table-wrap title="触发事件" icon="none" style="height:100%">
                        <el-divider></el-divider>
                        <div style="padding:20px 20px 0">
                          <el-checkbox-group v-model="item.eventList">
                            <el-checkbox v-for="(it, idx) in eventList" :key="idx" :label="it.value">{{ it.label }}
                            </el-checkbox>
                          </el-checkbox-group>
                        </div>
                      </kade-table-wrap>
                    </el-col>
                  </el-row>
                </div>
              </el-tab-pane>
            </el-tabs>
          </el-col>
          <el-col :span="6">
            <el-form-item label="报警时长(秒)：">
              <el-input-number placeholder="请输入"></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="6" style="margin-left:20px">
            <el-form-item label="更改开门超时(秒)：">
              <el-input-number placeholder="请输入"></el-input-number>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button type="primary" @click="submit" size="mini">保存</el-button>
        <el-button @click="handleClose" size="mini">返回</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script>
import { ElDialog, ElRow, ElCol, ElDivider, ElButton, ElForm, ElFormItem, ElInput, ElRadioGroup, ElRadio, ElCheckboxGroup, ElCheckbox, ElInputNumber, ElTabs, ElTabPane } from "element-plus"
import { reactive } from '@vue/reactivity'
const triggerList = [
  { label: "3110-1号", value: 1 },
  { label: "防盗", value: 2 },
]
const eventList = [
  { label: "门长时间未关报警", value: 1 },
  { label: "强行闯入报警", value: 2 },
  { label: "无效刷卡", value: 3 },
  { label: "火警", value: 4 },
  { label: "胁迫报警", value: 5 },
  { label: "强制锁门", value: 6 },

]
export default {
  components: {
    ElDialog, ElRow, ElCol, ElDivider, ElButton, ElForm, ElFormItem, ElInput, ElRadioGroup, ElRadio, ElCheckboxGroup, ElCheckbox, ElInputNumber, ElTabs, ElTabPane
  },

  emits: ["update:modelValue"],
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    rowList: {
      type: Array,
      default: null
    }
  },
  setup(props, context) {
    const state = reactive({
      form: {
        outputList: [
          { label: "1号扩展输出口", checked: false, trigger: 1, eventList: [], },
          { label: "2号扩展输出口", checked: false, trigger: 2, eventList: [], },
          { label: "3号扩展输出口", checked: false, trigger: 1, eventList: [], },
          { label: "4号扩展输出口", checked: false, trigger: 1, eventList: [], },
        ]
      },
    })
    const handleClose = () => {
      context.emit("update:modelValue", false)
    }
    return {
      triggerList,
      eventList,
      state,
      handleClose
    }
  }
}
</script>
<style lang="scss" scoped>
:deep(.kade-table-wrap) {
  margin-bottom: 20px;

  .table-wrap-head {
    height: 40px;
  }

  .title {
    height: 40px
  }
}

:deep(.el-tabs) {
  margin-bottom: 20px;

  .el-tabs__header {
    margin: 0;
  }

  .el-tabs__content {
    border: 1px solid #E4E7ED;
    border-top: 0;
  }
}

:deep(.el-radio-group) {
  display: block;

  .el-radio {
    display: block;

    &:first-child {
      margin-bottom: 20px;
    }
  }
}

:deep(.el-checkbox-group) {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-around;

  .el-checkbox {
    width: 33%;
    margin: 0;

    &:nth-child(n+4) {
      margin-top: 20px;

    }
  }
}

:deep(.el-input-number) {
  width: 100%
}


.el-divider--horizontal {
  margin: 0
}


.el-table {
  border-left: 1px solid #EBEEF5;
  border-right: 1px solid #EBEEF5;
}
</style>