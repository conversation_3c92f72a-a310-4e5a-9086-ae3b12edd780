<template>
    <el-dialog v-model="dialog.show" v-bind="attrs">
      <template #title>
        <div class="modal-title">
          <slot name="title">{{ title }}</slot>
          <span class="close" @click="dialog.show = false">
            <kade-icon :size="closeSize" name="iconbiaoqing"></kade-icon>
          </span>
        </div>
      </template>
      <template #default>
        <div :class="['modal-content', contentClass]">
          <slot></slot>
        </div>
      </template>
      <template #footer>
        <slot name="footer"></slot>
      </template>
    </el-dialog>
</template>
<script>
// 通用弹窗组件二次封装
import { ElDialog } from 'element-plus';
import { computed } from 'vue';
import Icon from '@/components/icon';
export default {
  components: {
    "el-dialog": ElDialog,
    "kade-icon": Icon,
  },
  emits: ['update:modelValue'],
  props: {
    title: {
      type: String,
      default: ''
    },
    modelValue: {
      type: Boolean,
      default: false
    },
    contentClass: {
      type: String,
      default: ''
    }
  },
  setup(props, context) {
    const dialog = computed(() => {
      return {
        set show(v) {
          context.emit('update:modelValue', v);
        },
        get show() {
          return props.modelValue;
        },
      }
    });
    const attrs = computed(() => {
      // eslint-disable-next-line no-unused-vars
      const { modelValue, contentClass, ...attr } = props;
      return {
        width: 650,
        appendToBody: true,
        customClass: `kade-dialog`,
        showClose: false,
        closeOnPressEscape: false,
        closeOnClickModal: false,
        destroyOnClose: true,
        ...attr,
      };
    });
    const closeSize = computed(() => {
      return THEMEVARS.dialogCloseIconSize;
    });
    return {
      dialog,
      attrs,
      closeSize,
    }
  }
}
</script>