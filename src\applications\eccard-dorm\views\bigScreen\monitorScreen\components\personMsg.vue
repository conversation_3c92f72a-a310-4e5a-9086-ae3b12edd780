<template>
  <div class="person-msg">
    <div class="statistics">
      <kade-label-box v-for="(item, index) in 8" :key="index" class="label-box">
        <div class="label">房间数量</div>
        <div class="value">1292</div>
      </kade-label-box>
    </div>
    <div class="monitor">
      <div class="monitor-title">监控预警</div>
      <div class="monitor-box1">
        <kade-border-box v-for="(item, index) in 4" :key="index" class="monitor-box1-item">
          <img src="" alt="">
          <div class="msg-box">
            <div class="name">王世龙</div>
            <div class="msg">房间：101</div>
            <div class="msg">班级：一年级三班</div>
            <div class="msg">方向：归寝</div>
            <div class="msg">时间：2022-01-01 21:00:00</div>
            <div class="msg">体温：38℃</div>
          </div>
        </kade-border-box>
      </div>
      <div class="monitor-box2">
        <kade-label-box v-for="(item, index) in 6" :key="index" class="label-box">
          <img src="" alt="" />
          <div class="msg-box">
            <div class="msg">王世龙</div>
            <div class="msg">方向：归寝</div>
            <div class="msg">时间：2022-01-01 21:00:00</div>
            <div class="msg">状态：陌生人</div>
          </div>
        </kade-label-box>
      </div>
    </div>
  </div>
</template>
<script>
import labelBox from "../../components/labelBox.vue"
import borderBox from "../../components/borderBox.vue"

export default {
  components: {
    "kade-label-box": labelBox,
    "kade-border-box": borderBox,

  },
}
</script>
<style scoped lang="scss">
.person-msg {
  padding: 10px 0;

  .statistics {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-around;
    box-sizing: border-box;

    .label-box {
      box-sizing: border-box;
      width: 23%;
      margin-bottom: 10px;
      padding: 10px;

      .label {
        font-weight: 400;
        font-size: 14px;
        color: #0EE4F9;
      }

      .value {
        font-weight: 700;
        font-size: 30px;
        color: #FE7007;
        text-align: center;
      }
    }
  }

  .monitor {
    padding:0px 10px;

    .monitor-title {
      font-weight: 700;
      font-size: 18px;
      color: #0EE4F9;
      margin: 10px 0 10px;
    }

    .monitor-box1 {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;

      .monitor-box1-item {
        width: 48%;
        margin-bottom: 20px;
        display: flex;
        padding: 10px;

        img {
          width: 137px;
          height: 164px;
          margin-right: 20px;
        }

        .msg-box {
          font-size: 16px;
          color: #FFFFFF;
          line-height: 28px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;

          .name {
            margin-bottom: 5px;
            font-weight: 700;
            font-size: 28px;
            color: #50FFFF;
          }
        }
      }
    }

    .monitor-box2 {
      display: flex;
      justify-content: space-between;

      .label-box {
        text-align: center;
        padding:10px 5px;
        width: 130px;

        img {
          width: 107px;
          height: 120px;
        }

        .msg-box {
          font-size: 12px;
          color: #FFFFFF;
          line-height: 18px;
          text-align: left;
          padding:0 10px;
        }
      }
    }
  }
}
</style>