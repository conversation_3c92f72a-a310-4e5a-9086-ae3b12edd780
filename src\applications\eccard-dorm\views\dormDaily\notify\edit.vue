<template>
    <el-dialog :model-value="isShow" title="编辑消息通知" width="1100px" :before-close="beforeClose"
        :close-on-click-modal="false">
        <div style="padding-top: 20px">
            <el-form size="mini" label-width="120px" ref="formDom" :rules="rules" :model="state.form">
                <el-row>
                    <el-col :span="24" style="display:flex">
                        <el-form-item label="消息类型:" prop="msgType">
                            <el-select v-model="state.form.msgType" placeholder="请选择">
                                <el-option v-for="item in msgType" :key="item.value" :label="item.label"
                                    :value="item.value"></el-option>
                            </el-select>
                        </el-form-item>
                        <kade-linkage-select :isEdit="isShow ? true : false" :data="linkageData" :value="state.form"
                            @change="linkageChange" />
                    </el-col>
                </el-row>
                <el-form-item label="消息标题:" prop="msgTitle" style="width:100%">
                    <el-input placeholder="请输入不超过50字" v-model="state.form.msgTitle" :maxlength="50"></el-input>
                </el-form-item>

                <el-form-item label="消息内容:" prop="msgContent" style="width:100%">
                    <kade-wangeditor :htmlData="state.form.msgContent"
                        @change="(val) => (state.form.msgContent = val)" />
                </el-form-item>
            </el-form>
        </div>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="beforeClose" size="mini">返回</el-button>
                <el-button type="primary" @click="submit()" size="mini">保存</el-button>
            </span>
        </template>
    </el-dialog>
    <kade-tailor :isShow="state.isTailor" :file="state.imgFile" @close="close" />
</template>
<script>
import { computed, ref, watch } from "vue";
import { reactive, nextTick } from "vue";
import { useStore } from "vuex";
import { useDict } from "@/hooks/useDict";
import { editMsg } from "@/applications/eccard-dorm/api";
import linkageSelect from "@//applications/eccard-dorm/components/linkageSelect.vue";
import {
    ElDialog,
    ElRow,
    ElCol,
    ElButton,
    ElForm,
    ElFormItem,
    ElInput,
    ElSelect,
    ElOption,
    ElMessage,
} from "element-plus";
import wangeditor from "@/components/wangeditor.vue";
const linkageData = {
    area: { label: "接收区域：", valueKey: "areaId", key: "id" },
    building: { label: "接收楼栋：", valueKey: "buildId" },
    unit: { label: "接收单元：", valueKey: "unitNum", },
};
const rules = {
    msgType: [
        {
            required: true,
            message: "请选择消息类型",
            trigger: "change",
        },
    ],
    areaId: [
        {
            required: true,
            message: "请选择接收区域",
            trigger: "change",
        },
    ],
    buildId: [
        {
            required: true,
            message: "请选择接收楼栋",
            trigger: "blur",
        },
    ],
    unitNum: [
        {
            required: true,
            message: "请选择接收单元",
            trigger: "blur",
        },
    ],
    msgTitle: [
        {
            required: true,
            message: "请输入消息标题",
            trigger: "blur",
        },
    ],
    msgContent: [
        {
            required: true,
            message: "请输入消息内容",
            trigger: "blur",
        },
        { min: 0, max: 500, message: '消息内容不能超过500字符', trigger: 'blur' },
    ],
};

export default {
    components: {
        ElDialog,
        ElRow,
        ElCol,
        ElButton,
        ElForm,
        ElFormItem,
        ElInput,
        ElSelect,
        ElOption,
        "kade-wangeditor": wangeditor,
        "kade-linkage-select": linkageSelect,
    },
    setup(props, context) {
        const store = useStore();
        const formDom = ref(null);
        const msgType = useDict("SYS_MESSAGE_TYPE"); //消息类型
        // const msgReceiver = useDict("SYS_NEWS_RECEIVER"); //新闻接收
        const state = reactive({
            isTailor: false,
            imgFile: "",
            form: {},
        });
        const isShow = computed(() => {
            console.log(store.state.msg.isEdit);
            return store.state.msg.isEdit ? true : false;
        });
        watch(
            () => store.state.msg.isEdit,
            (val) => {
                if (val) {
                    nextTick(() => {

                    })
                    state.form = { ...store.state.msg.selectRow };
                    // state.form.newsReceiver=state.form.newsReceiver.split(",")
                }
            }
        );
        const linkageChange = (val) => {
            state.form = { ...state.form, ...val }
        }
        const submit = () => {
            formDom.value.validate(async (valid) => {
                if (valid) {
                    let params = { ...state.form };
                    console.log(params);
                    let { code, message } = await editMsg(params);
                    if (code === 0) {
                        ElMessage.success(message);
                        beforeClose();
                        context.emit("success")
                    }
                } else {
                    return false;
                }
            });
        };
        const beforeClose = () => {
            store.commit("msg/updateState", {
                key: "isEdit",
                payload: false,
            });
            formDom.value.clearValidate();
        };
        return {
            linkageData,
            store,
            rules,
            formDom,
            msgType,
            state,
            isShow,
            linkageChange,
            submit,
            beforeClose,
        };
    },
};
</script>
<style lang="scss" scoped>
:deep(.avatar-uploader .el-upload) {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

:deep(.avatar-uploader .el-upload:hover) {
    border-color: #409eff;
}

:deep(.avatar-uploader-icon) {
    font-size: 28px;
    color: #8c939d;
    width: 240px;
    line-height: 160px;
    text-align: center;
}

.avatar {
    width: 240px;
    height: 160px;
    display: block;
}

:deep(.el-form-item__content) {
    .el-select {
        width: 100%;
    }

    .el-textarea {
        width: 100%;

        .el-textarea__inner {
            width: 100%;
        }
    }

    .el-date-editor.el-input {
        width: 100%;
    }
}

:deep(.el-form-item) {
    width: 25%;
}
</style>