<template>
  <kade-route-card>
    <kade-table-filter @search="handleSearch" @reset="handleReset">
      <el-form inline label-width="100px" size="small">
        <el-form-item label="终端型号">
          <el-select v-model="state.form.deviceModel" clearable>
            <el-option v-for="(item, index) in state.listData.modelList" :key="index" :label="item.productMode" :value="item.productMode">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="所属区域">
          <kade-area-select-tree style="width: 100%" :value="state.form.areaId" valueKey="id" :multiple="false" @valueChange="(val) => (state.form.areaId = val.id)" />
        </el-form-item>
        <el-form-item label="机号">
          <el-input v-model="state.form.deviceNo" placeholder="请输入" clearable />
        </el-form-item>
        <el-form-item label="所属商户">
          <el-select v-model="state.form.merchantId" clearable>
            <el-option :label="item.merchantName" :value="item.merchantId" v-for="(item,index) in state.listData.merchantList" :key="index"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="设备状态">
          <el-select v-model="state.form.deviceStatus" clearable>
            <el-option v-for="(item, index) in deviceStaticList" :key="index" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="设备名称" clearable>
          <el-input v-model="state.form.deviceName" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="设备IP" clearable>
          <el-input v-model="state.form.deviceIp" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="所属工作站" clearable>
          <el-select v-model="state.form.workstationId">
            <el-option :label="item.name" :value="item.id" v-for="(item,index) in state.listData.workStationList" :key="index"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="连接类型" clearable>
          <el-select v-model="state.form.deviceConnectType">
            <el-option :label="item.label" :value="item.value" v-for="(item,index) in deviceConnectTypeList" :key="index"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="无线智能卡节水控制器列表">
      <template #extra>
        <el-button icon="el-icon-plus" @click="edit({},'add')" size="small" class="btn-green">新增</el-button>
        <el-button icon="el-icon-daorutupian" @click="state.isBatchAdd=true" size="small" class="btn-purple">批量新增</el-button>
        <el-button icon="el-icon-daorutupian" @click="addRecord()" size="small" class="btn-yellow">更换设备</el-button>
        <el-button icon="el-icon-daoru" size="small" class="btn-deep-blue" @click="handleImport">导入</el-button>
        <el-button icon="el-icon-daochu" size="small" class="btn-deep-blue" @click="handleExport">导出</el-button>
      </template>
      <el-table style="width: 100%" height="55vh" v-loading="state.loading" :data="state.dataList" @rowClick="rowClick" highlight-current-row border stripe>
        <el-table-column v-for="(item,index) in column" :key="index" :width="item.width" :prop="item.prop" :label="item.label" align="center" show-overflow-tooltip>
          <template #default="scope" v-if="item.isDict">
            {{dictionaryFilter(scope.row[item.prop])}}
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="180px">
          <template #default="scope">
            <el-button class="green" size="mini" @click="detail(scope.row)" type="text">详情</el-button>
            <el-button class="green" size="mini" @click="edit(scope.row,'edit')" type="text">编辑</el-button>
            <el-button class="green" size="mini" @click="del(scope.row)" type="text">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination background :current-page="state.form.currentPage" layout="total, sizes, prev, pager, next, jumper" :page-sizes="[10, 20, 50, 100, 200]" :total="state.total" :page-size="state.form.pageSize" @current-change="handlePageChange" @size-change="handleSizeChange">
        </el-pagination>
      </div>
    </kade-table-wrap>
    <kade-device-edit :isShow="state.isEdit" :type="state.editType" :data="state.selectRow" :listData="state.listData" @close="editClose" />
    <kade-batch-add :isShow="state.isBatchAdd" :listData="state.listData" @close="batchClose" />
    <kade-device-details :isShow="state.isDetails" :data="state.selectRow" @close="state.isDetails=false" />
    <kade-replace-record :data="state.selectRow" />
    <kade-device-type-manger v-model="state.isTypeManger" @update:modelValue="typeMangerClose" :listFnc="queryWaterTypeList" />
    <kade-import-device v-model="state.isImport" @close="importClose" />
  </kade-route-card>
</template>
<script>
import { ElButton, ElForm, ElFormItem, ElInput, ElSelect, ElOption, ElTable, ElTableColumn, ElPagination, ElMessageBox, ElMessage } from "element-plus";
import { onMounted, reactive } from 'vue';
import { useStore } from 'vuex';
import { useDict } from "@/hooks/useDict.js";
import { downloadXlsx } from "@/utils"
import {
  infiniteWaterDict,
  hydropowerGatewayListNoPage,
  getModel,
  getWorkStationList,
  deviceFactoryList,
  waterTypeListNoPage,
  infiniteWaterParamListNoPage,
  getMerchantList,
  infiniteWaterDeviceList,
  infiniteWaterDeviceDel,
  infiniteWaterDeviceExport
} from "@/applications/eccard-iot/api";
import areaSelectTree from "@/components/tree/areaSelectTree.vue";
import edit from "./components/edit.vue"
import batchAdd from "./components/batchAdd.vue"
import details from "./components/details"
import replaceRecord from "./components/replaceRecord"
import importDevice from "./components/import"
const column = [
  { prop: "deviceModel", label: "终端型号", width: "" },
  { prop: "merchantName", label: "所属商户", width: "" },
  { prop: "areaName", label: "所属区域", width: "" },
  { prop: "deviceNo", label: "设备机号", width: "" },
  { prop: "deviceName", label: "设备名称", width: "" },
  { prop: "paramName", label: "参数名称", width: "" },
  { prop: "deviceStatus", label: "设备状态", width: "", isDict: true },
  { prop: "deviceConnectType", label: "连接类型", width: "", isDict: true },
  { prop: "deviceIp", label: "设备IP", width: "" },
]
export default {
  components: {
    ElButton,
    ElForm,
    ElFormItem,
    ElInput,
    ElSelect,
    ElOption,
    ElTable,
    ElTableColumn,
    ElPagination,
    "kade-area-select-tree": areaSelectTree,
    'kade-device-edit': edit,
    'kade-device-details': details,
    "kade-replace-record": replaceRecord,
    "kade-batch-add": batchAdd,
    "kade-import-device": importDevice,

  },
  setup() {
    const deviceStaticList = useDict("SYS_DEVICE_STATICE")
    const deviceConnectTypeList = useDict("SYS_DEVICE_CONNECT_TYPE")
    const store = useStore()
    const state = reactive({
      isEdit: false,
      isDetails: false,
      isBind: false,
      isBatchAdd: false,
      isTypeManger: false,
      isImport: false,
      form: {
        pageNum: 1,
        pageSize: 10
      },
      dataList: [],
      total: 0,
      editType: "",
      selectRow: "",
      listData: {
        modelList: [],
        workStationList: [],
        deviceFactoryList: [],
        gatewayList: [],
        paramsList: [],
        merchantList: [],
        waterTypeList: []
      },
    })
    //获取终端型号
    const queryModel = async () => {
      let { data } = await getModel({ productClass: "WIRELESS_WATER_CONTROLLER" });
      state.listData.modelList = data;
    };
    //获取工作站
    const queryWorkStationList = async () => {
      let { data: { list } } = await getWorkStationList();
      state.listData.workStationList = list;
    };
    //获取网关
    const queryGatewayList = async () => {
      let { data } = await hydropowerGatewayListNoPage();
      state.listData.gatewayList = data;
    };
    //获取厂家
    const getDeviceFactoryList = async () => {
      let { data: { list } } = await deviceFactoryList();
      state.listData.deviceFactoryList = list;
    };
    //获取参数列表
    const getParamsList = async () => {
      let { data } = await infiniteWaterParamListNoPage();
      state.listData.paramsList = data;
    };
    //获取商户列表
    const queryMerchantList = async () => {
      let { data } = await getMerchantList();
      state.listData.merchantList = data;
    };
    //获取水表类型
    const queryWaterTypeList = async () => {
      let { data } = await waterTypeListNoPage();
      state.listData.waterTypeList = data;
    };
    const getLictList = async () => {
      let { data } = await infiniteWaterDict()
      let dict = {}
      for (let key in data) {
        let dictList = []
        for (let k in data[key]) {
          dictList.push({ label: data[key][k], value: String(k) })
        }
        dict[key] = dictList
      }
      store.commit("hydropowerDevice/infiniteWater/updateState", {
        key: "dict",
        payload: dict
      });
    }
    const getList = async () => {
      let { data: { list, total } } = await infiniteWaterDeviceList(state.form)
      state.dataList = list
      state.total = total
    }
    const rowClick = row => {
      state.selectRow = row
    }
    const edit = (row, type) => {
      state.selectRow = row
      state.editType = type
      state.isEdit = true
    }
    const detail = async (row) => {
      state.selectRow = row
      await getLictList()
      state.isDetails = true
    }
    const addRecord = () => {
      if (!state.selectRow.id) {
        return ElMessage.error("请选择设备！")
      }
      store.commit("hydropowerDevice/infiniteWater/updateState", {
        key: "isRecord",
        payload: {
          type: "add",
          isShow: true,
          rowData: {}
        },
      });
    }
    const del = (row) => {
      ElMessageBox.confirm("确认删除?", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        let { code, message } = await infiniteWaterDeviceDel(row.id);
        if (code === 0) {
          ElMessage.success(message);
          getList();
        }
      });
    }
    const handleImport = () => {
      state.isImport = true
    }
    const handleExport = async () => {
      let res = await infiniteWaterDeviceExport(state.form)
      downloadXlsx(res, "无线智能卡节水控制器列表.xlsx")
    }
    const handleSearch = () => {
      getList()
    }
    const handleReset = () => {
      state.form = {
        pageNum: 1,
        pageSize: 10
      }
    }
    const handlePageChange = (val) => {
      state.form.pageNum = val
      getList()
    }
    const handleSizeChange = (val) => {
      state.form.pageSize = val
      state.form.pageNum = 1
      getList()
    }
    const editClose = (val) => {
      if (val) {
        getList()
      }
      state.isEdit = false
    }
    const batchClose = (val) => {
      if (val) {
        getList()
      }
      state.isBatchAdd = false
    }
    const importClose = val => {
      if (val) {
        getList()
      }
      state.isImport = false
    }
    onMounted(() => {
      getList()
      queryModel()
      queryWorkStationList()
      getDeviceFactoryList()
      queryGatewayList()
      getParamsList()
      queryMerchantList()
      queryWaterTypeList()
    })
    return {
      column,
      deviceStaticList,
      deviceConnectTypeList,
      state,
      queryWaterTypeList,
      edit,
      detail,
      rowClick,
      addRecord,
      del,
      handleExport,
      handleImport,
      handleSearch,
      handleReset,
      handlePageChange,
      handleSizeChange,
      editClose,
      batchClose,
      importClose
    }
  }
};
</script>
<style lang="scss" scoped>
:deep(.el-form-item__content) {
  .el-select {
    width: 200px;
  }
  .el-input__inner {
    width: 200px;
  }
}
:deep(.el-dialog__header) {
  border-bottom: 1px solid #efefef;
}

:deep(.el-overlay) {
  position: absolute;
  background-color: rgba(0, 0, 0, 0.3);
}

:deep(.el-dialog__footer) {
  text-align: center;
}
</style>