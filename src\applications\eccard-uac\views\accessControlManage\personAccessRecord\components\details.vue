<template>
  <el-dialog :model-value="modelValue" title="有效记录详情" width="1100px" :before-close="handleClose">
    <div class="details-box">
      <kade-table-wrap title="图片" icon="none" style="width: 380px;">
        <el-divider></el-divider>
        <div class="padding-box img-box">
          <div class="img-item">
            <div class="img-text">系统照片</div>
            <img :src="state.detailsData.sysPhotoUrl" alt="">
          </div>
          <div class="img-item">
            <div class="img-text">抓拍照片</div>
            <img :src="state.detailsData.snapPhotoUrl" alt="">
          </div>
        </div>
      </kade-table-wrap>
      <kade-table-wrap title="记录信息" icon="none" style="width:650px">
        <el-divider></el-divider>
        <el-form size="mini" style="width:100%;padding: 0 10px;box-sizing: border-box;" label-width="100px">
          <el-row>
            <el-col :span="12">
              <el-form-item label="用户编号:">
                <el-input readonly :model-value="state.detailsData.userCode"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="用户姓名:">
                <el-input readonly :model-value="state.detailsData.userName"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="组织机构:">
                <el-input readonly :model-value="state.detailsData.deptName"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="身份类别:">
                <el-input readonly :model-value="state.detailsData.userRole"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="通行区域:">
                <el-input readonly :model-value="state.detailsData.areaName"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="门禁名称:">
                <el-input readonly :model-value="state.detailsData.deviceName"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="进出方向:">
                <el-input readonly :model-value="state.detailsData.passDirection"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="通行时间:">
                <el-input readonly :model-value="timeFilter(state.detailsData.passTime) "></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="识别方式:">
                <el-input readonly :model-value="state.detailsData.passModeName"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="是否通过:">
                <el-input readonly :model-value=" dictionaryFilter(state.detailsData.isPass) "></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="描述:">
                <el-input readonly type="textarea" :model-value="state.detailsData.remarks"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </kade-table-wrap>
    </div>
  </el-dialog>
</template>

<script>
import { ElDialog, ElDivider, ElRow, ElCol, ElForm, ElFormItem, ElInput } from "element-plus"
import { reactive, ref, watch } from 'vue'
import { acsDeviceRecordDetails } from "@/applications/eccard-uac/api";

export default {
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    rowData: {
      types: Object,
      default: null
    },
  },
  components: {
    ElDialog,
    ElDivider, ElRow, ElCol, ElForm, ElFormItem, ElInput
  },
  setup(props, context) {
    const formRef = ref(null)
    const state = reactive({
      isPersoner: false,
      detailsData:{},
      form: {
        receiveMessage: 'TRUE'
      }
    });
    watch(() => props.modelValue,async val => {
      if (val) {
        let {data}=await acsDeviceRecordDetails(props.rowData.id)
        state.detailsData=data
      }
    })
    const handleClose = () => {
      context.emit("update:modelValue", false)
    };
    return {
      formRef,
      state,
      handleClose,
    }
  }
}
</script>

<style lang="scss" scoped>
.el-form {
  margin-top: 20px;
}

:deep(.el-divider--horizontal) {
  margin: 0;
}

.details-box {
  display: flex;
  justify-content: space-between;
  height: 400px;
  padding: 20px 0;
}

.img-box {
  display: flex;
  justify-content: space-between;

  .img-item {
    width: 160px;

    img {
      margin-top: 10px;
      width: 160px;
      height: 224px;
    }
  }
}
</style>