/* 
  富文本编辑器
 */

<template>
  <div ref="wangeditor"></div>
</template>
<script>
import { onMounted, reactive, ref, watch } from "@vue/runtime-core";
import { getToken } from "@/utils";
import E from "wangeditor";

export default {
  props: {
    htmlData: {
      types: String,
      default: "",
    },
  },

  setup(props, context) {
    const wangeditor=ref(null)
    const state = reactive({
      requestHeader: getToken(),
      editor: "",
    });

    watch(
      () => props.htmlData,
      (val) => {
        if (state.editor) {
          state.editor.txt.html(val);
        }
      }
    );

    onMounted(() => {
      console.log(1);
      console.log(state.editor);
      state.editor = new E(wangeditor.value);
        //配置菜单
      state.editor.config.menus = [
        "head", //标题
        "bold", //加粗
        "fontSize", //字体大小
        "fontName", //字体
        "italic", //斜体
        "underline", //下划线
        "strikeThrough", //删除线
        "indent", //缩进
        "lineHeight", //行高
        "foreColor", //字体颜色
        "backColor", //背景颜色
        // "link",//链接
        "list", //序列
        // "todo",//待办事项
        "justify", //对齐
        // "quote",
        // "emoticon",
        "image", //图片
        // "video",//视频
        "table", //表格
        // "code",//代码
        "splitLine", //分割线
        "undo", //撤销
        "redo", //恢复
      ];
      state.editor.config.uploadImgMaxSize = 0.5 * 1024 * 1024 // 2M
      state.editor.config.uploadImgAccept = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp']
      state.editor.config.uploadImgShowBase64 = true;
      /* editor.config.uploadImgServer = `${CONFIG.BASE_API_PATH}eccard-partal-manage/applicationManage/uploadApplyLogo`;
      editor.config.uploadImgHeaders = {
        Authorization: `bearer ${state.requestHeader}`,
      };
      editor.config.uploadFileName = "file";
      
      editor.config.uploadImgHooks = {
        // 图片上传并返回了结果，图片插入已成功
        success: function (xhr) {
          console.log("success", xhr);
        },
        // 图片上传并返回了结果，但图片插入时出错了
        fail: function (xhr, editor, resData) {
          console.log("fail", resData);
        },
        // 图片上传并返回了结果，想要自己把图片插入到编辑器中
        customInsert: function (insertImgFn, result) {
          // result 即服务端返回的接口
          console.log("customInsert", result.data);
          // insertImgFn 可把图片插入到编辑器，传入图片 src ，执行函数即可
          insertImgFn(result.data);
        },
      }; */
      state.editor.create();
      state.editor.txt.html(props.htmlData);
      state.editor.config.onchange = function (newHtml) {
        console.log("change 之后最新的 html", newHtml);
        context.emit("change", newHtml);
      };
    });
    return {
      wangeditor,
      state,
    };
  },
};
</script>