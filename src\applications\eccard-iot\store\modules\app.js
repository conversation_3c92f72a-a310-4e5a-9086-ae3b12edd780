import { markRaw, defineAsyncComponent } from 'vue';
import { getDictionary } from '@/applications/unified_portal/api';
import {
	actions as mixinActions,
	state as mixinState,
	getters as mixinGettgers,
} from '@/utils/tab.mixin';
const dictionary = localStorage.getItem(`kade_cache_dictionary`);
const state = {
	...mixinState,
	dictionary: dictionary ? JSON.parse(dictionary) : [],
	applyTypes: [],
	componentMap: {
		factoryManager: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "basicInfoManager" */ '../../views/basicInfoManager/factoryManager'))),
		workstationManager: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "basicInfoManager" */ '../../views/basicInfoManager/workstationManager'))),
		workTypeManager: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "basicInfoManager" */ '../../views/basicInfoManager/workTypeManager'))),
		deviceFaultManage: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "extensionDeviceManager" */ '../../views/basicInfoManager/deviceFaultManage'))),
		// machineParameters: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "deviceParameters" */ '../../views/deviceParameters/machineParameters'))),
		// terminalDeviceParameters: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "deviceParameters" */ '../../views/deviceParameters/terminalDeviceParameters'))),

		machineParameters: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "deviceParameters" */ '../../views/components/deviceParameters'))),	
		terminalDeviceParameters: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "deviceParameters" */ '../../views/components/deviceParameters'))),
		faceParameters: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "deviceParameters" */ '../../views/components/deviceParameters'))),
		waterParameters: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "deviceParameters" */ '../../views/components/deviceParameters'))),
		saveWaterParameters: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "deviceParameters" */ '../../views/components/deviceParameters'))),

		// faceParameters: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "hydropowerDevice" */ '../../views/deviceParameters/faceParameters'))),
		// waterParameters: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "hydropowerDevice" */ '../../views/deviceParameters/waterParameters'))),
		cardDevice: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "financePayDevice" */ '../../views/financePayDevice/CardDevice'))),
		device: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "financePayDevice" */ '../../views/financePayDevice/Device'))),
		deviceRealTimeMonitor: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "shopmanager" */ '../../views/deviceMonitor/deviceRealTimeMonitor'))),
		
		faceDevice: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "identityDevice" */ '../../views/identityDevice/faceDistingDevice'))),
		accessControllerChannel: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "identityDevice" */ '../../views/identityDevice/accessControllerChannel'))),
		passagewayController: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "identityDevice" */ '../../views/identityDevice/passagewayController'))),

		attendanceDevice: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "identityDevice" */ '../../views/components/device'))),
		camera: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "identityDevice" */ '../../views/components/device'))),
		bigScreenDevice: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "identityDevice" */ '../../views/components/device'))),
		faceAnalysisDevice: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "identityDevice" */ '../../views/components/device'))),
		accessAIO: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "identityDevice" */ '../../views/components/device'))),
		visitorDevice:markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "identityDevice" */ '../../views/components/device'))),
		doorLock:markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "identityDevice" */ '../../views/components/device'))),
		leaveDevice:markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "identityDevice" */ '../../views/components/device'))),
		meetingSignInDevice:markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "identityDevice" */ '../../views/components/device'))), 
		electronicClassDevice:markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "identityDevice" */ '../../views/components/device'))), 


		powerController: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "hydropowerDevice" */ '../../views/components/device'))),
		waterController: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "hydropowerDevice" */ '../../views/hydropowerDevice/waterController'))),
		remoteWater: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "hydropowerDevice" */ '../../views/hydropowerDevice/remoteWater'))),

		financePayGateway: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "hydropowerDevice" */ '../../views/gatewayManager/gatewayDevice'))),
		identifyGateway: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "hydropowerDevice" */ '../../views/gatewayManager/gatewayDevice'))),
		energyWaterPowerGateway: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "hydropowerDevice" */ '../../views/gatewayManager/gatewayDevice'))),

		deviceDataDistribute: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "deviceDataManger" */ '../../views/deviceDataManger/deviceDataDistribute'))),


		customParametersTemplate: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "deviceParameters" */ '../../views/deviceParameters/customParametersTemplate'))),
		customParameters: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "deviceParameters" */ '../../views/deviceParameters/customParameters'))),

		extensionDevice: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "extensionDeviceManager" */ '../../views/extensionDeviceManager/extensionDevice'))),
		
		//系统辅助设备
		icCardDevice:markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "identityDevice" */ '../../views/components/device'))), 
		beforeMachine:markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "identityDevice" */ '../../views/components/device'))), 
		passwordServer:markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "identityDevice" */ '../../views/components/device'))), 
		financeEncryption:markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "identityDevice" */ '../../views/components/device'))), 


		//首页
		iotStatisticsHome: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "extensionDeviceManager" */ '../../views/home/<USER>'))),

		infiniteWater: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "hydropowerDevice" */ '../../views/hydropowerDevice/infiniteWater'))),
		infiniteWaterParams: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "deviceParameters" */ '../../views/components/deviceParameters'))),

		deviceRepairManage: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "deviceParameters" */ '../../views/deviceMonitor/deviceRepairManage'))),
		deviceWaterMonitor: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "deviceParameters" */ '../../views/deviceMonitor/deviceWaterMonitor'))),


	},
	customMenus: [],
};
const mutations = {
	updateState(state, { key, payload }) {
		state[key] = payload;
	},
};
const actions = {
	...mixinActions,
	async loadDictionary({ commit }) {
		const { data } = await getDictionary();
		localStorage.setItem(`kade_cache_dictionary`, JSON.stringify(data));
		commit('updateState', { key: 'dictionary', payload: data });
	},
};

const getters = {
	...mixinGettgers,
};

export default {
	namespaced: true,
	state,
	mutations,
	actions,
	getters,
}
