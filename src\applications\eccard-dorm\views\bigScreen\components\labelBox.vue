<template>
  <div class="label-box">
    <slot />
    <div class="horn"></div>
  </div>
</template>
<style scoped lang="scss">
.label-box {
  border: 1px solid #0e94eb;
  position: relative;
  .horn {
    position: absolute;
    top: 0;
    right: 0;
    box-sizing: border-box;
    width: 16px;
    height: 16px;
    border-right: 16px solid #f00;
    border-top: 16px solid rgba(255,255,255,0);
    transform: rotate(-90deg);
  }
}
</style>