<template>
  <div class="padding-box">
    <kade-table-filter @search="handleSearch" @reset="handleReset">
      <el-form inline label-width="100px" size="mini">
        <el-form-item label="区域">
          <el-input clearable v-model="state.form.name" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="所属组">
          <el-input clearable v-model="state.form.ip" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="所属控制器">
          <el-input clearable v-model="state.form.ip" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="门号">
          <el-input clearable v-model="state.form.ip" placeholder="请输入"></el-input>
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="首卡开门列表">
      <template #extra>
        <el-button class="btn-purple" size="mini" icon="el-icon-edit" @click="handleSetAuth">首卡设置</el-button>
      </template>
      <el-table border height="55vh" v-loading="state.loading" :data="state.dataList">
        <el-table-column show-overflow-tooltip prop="name" label="所属区域" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="ip" label="所属组" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="ip" label="所属控制器编号" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="ip" label="控制器SN号" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="ip" label="门名称" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="ip" label="是否启用首卡开门" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip label="操作" align="center">
          <template #default="scope">
            <el-button type="text" size="mini" @click="handleDetails(scope.row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination v-model:currentPage="state.form.pageNum" v-model:page-size="state.form.pageSize"
          :page-sizes="[10, 20, 30, 40]" background layout="total, sizes, prev, pager, next, jumper"
          :total="state.total" @size-change="handleSizeChange" @current-change="handleCurrentChange">
        </el-pagination>
      </div>
    </kade-table-wrap>
    <kade-first-card-set-dialog v-model="state.isSet" @update:modelValue="state.isSet = false" />
    <kade-first-card-details-dialog v-model="state.isDetails" @update:modelValue="state.isShow = false" />
  </div>
</template>
<script>
import { reactive } from "vue"
import { ElForm, ElFormItem, ElInput, ElTable, ElTableColumn, ElPagination, ElButton } from "element-plus"
import firstCardSet from "./components/firstCardSet.vue"
import details from "./components/details.vue"
export default {
  components: {
    ElForm, ElFormItem, ElInput, ElTable, ElTableColumn, ElPagination, ElButton,
    "kade-first-card-set-dialog": firstCardSet,
    "kade-first-card-details-dialog": details,
  },
  setup() {
    const state = reactive({
      loading: false,
      isSet: false,
      isDetails:false,
      form: {
        pageNum: 1,
        pageSize: 10
      },
      dataList: [{id:1}],
      total: 0,

    })
    const handleSetAuth = () => {
      state.isSet = true
    }
    const handleDetails = () => {
      state.isDetails = true
    }
    const handleCurrentChange = val => {
      state.form.pageNum = val
    }
    const handleSizeChange = val => {
      state.form.pageNum = 1
      state.form.pageSize = val
    }
    return {
      state,
      handleSetAuth,
      handleDetails,
      handleCurrentChange,
      handleSizeChange
    }
  }
}
</script>
<style lang="scss" scoped>
:deep(.el-dialog__footer) {
  text-align: center;
}
</style>