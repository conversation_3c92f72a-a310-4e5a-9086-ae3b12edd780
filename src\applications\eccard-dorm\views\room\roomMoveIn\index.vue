<template>
  <kade-route-card>
    <kade-table-filter @search="handleSearch" @reset="handleReset">
      <el-form label-width="100px" inline size="mini">
        <kade-linkage-select :data="linkageData" :value="state.form" @change="linkageChange" />
        <el-form-item label="是否入住">
          <el-select v-model="state.form.isStay">
            <el-option :label="item.label" :value="item.value" v-for="(item,index) in isStayList" :key="index" ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否住满">
          <el-select v-model="state.form.isFull">
            <el-option :label="item.label" :value="item.value" v-for="(item,index) in isFullList" :key="index" ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="组织机构">
          <kade-dept-select-tree style="width: 100%" :value="state.form.deptPath" valueKey="deptPath" :multiple="false"
            @valueChange="(val) => (state.form.deptPath = val.deptPath)" />
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="宿舍房间入住列表">
      <template #extra>
        <el-button icon="el-icon-daochu" size="small" class="btn-purple" @click="handleExport">导出</el-button>
      </template>
      <el-table style="width: 100%" :data="state.data" height="55vh" border stripe>
        <el-table-column v-for="(item,index) in column" :key="index" :width="item.width" :prop="item.prop" :label="item.label" align="center" show-overflow-tooltip>
        <template #default="scope">
          {{dictionaryFilter(scope.row[item.prop])}}
        </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination
          background
          :current-page="state.form.currentPage"
          layout="total, sizes, prev, pager, next, jumper"
          :page-sizes="[10, 20, 50, 100, 200]"
          :total="state.total"
          :page-size="state.form.pageSize"
          @current-change="handlePageChange"
          @size-change="handleSizeChange"
        >
        </el-pagination>
      </div>
    </kade-table-wrap>
  </kade-route-card>
</template>
<script>
import {ElForm,ElFormItem,ElSelect,ElOption,ElTable,ElTableColumn,ElButton,ElPagination} from "element-plus"
import { reactive,onMounted } from 'vue'
import linkageSelect from "../../../components/linkageSelect.vue";
import deptSelectTree from "@/components/tree/deptSelectTree.vue";
import { dormStayInfoList,exportDormStayInfo } from '@/applications/eccard-dorm/api.js'
import { useDict } from "../../../../../hooks/useDict";
const linkageData={
  area: { label: "区域", valueKey: "areaPath", key: "areaPath" },
  buildingType: { label: "楼栋类型", valueKey: "buildType" },
  building: { label: "楼栋", valueKey: "buildId" },
  unit: { label: "单元", valueKey: "unitNum" },
  floor: { label: "楼层", valueKey: "floorNum" },
  room: { label: "房间", valueKey: "roomId" },
}
const column=[
  {label:"区域",prop:"areaName"},
  {label:"楼栋类型",prop:"buildTypeName"},
  {label:"房间",prop:"roomString",width:"200px"},
  {label:"组织机构",prop:"deptName"},
  {label:"床位数",prop:"bedCount"},
  {label:"是否入住",prop:"isStay",isDict:true},
  {label:"是否住满",prop:"isFull",isDict:true},
  {label:"入住人数",prop:"checkPersonCount"},
  {label:"空余床位",prop:"surplusBedCount"},
]
export default {
  components:{
    ElForm,
    ElFormItem,
    ElSelect,
    ElOption,
    ElTable,
    ElTableColumn,
    ElButton,
    ElPagination,
    "kade-linkage-select": linkageSelect,
    "kade-dept-select-tree": deptSelectTree,
  },
  setup(){
    const isFullList = useDict('SYS_BOOL_STRING')
    const isStayList = useDict('SYS_BOOL_STRING')
    const state=reactive({
      form:{
        currentPage:1,
        pageSize:10
      },
      total:0,
      data:[],
      loading:false
    })
    const handlePageChange=(val)=>{
      state.form.currentPage=val
      getList()
    }
    const handleSizeChange=(val)=>{
      state.form.currentPage=1
      state.form.pageSize=val
      getList()
    }
    const linkageChange=(val)=>{
      state.form={...state.form,...val}
    }
    const handleSearch=()=>{
      getList()
    }
    const handleReset=()=>{
      state.form={
        pageSize:10,
        currentPage:1
      }
      getList()
    }
    const getList= async()=>{
      state.loading=true
      try{
        let { data:{dataList,totalCount }} = await dormStayInfoList(state.form)
        state.data=dataList
        state.total=totalCount
        state.loading=false
      }
      catch{
        state.loading=false
      }
    }
    const handleExport= async()=>{
      let res = await exportDormStayInfo(state.form)
      let url = window.URL.createObjectURL(
        new Blob([res],{ type: "application/vnd.ms-excel" })
      )
      let link = document.createElement('a')
      link.href=url
      link.style.display='none'
      link.setAttribute('download','宿舍入住列表.xlsx')
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }
    onMounted(()=>{
      getList()
    })
    return {
      column,
      state,
      linkageData,
      isFullList,
      isStayList,
      handlePageChange,
      handleSizeChange,
      linkageChange,
      handleSearch,
      handleReset,
      handleExport,
    }
  }
}
</script>