<template>
  <kade-tab-wrap :tabs="tabs" v-model="state.tab">
    <template #jbcs>
      <kade-basic-params />
    </template>
    <template #zzcs>
      <kade-transfer-params />
    </template>
    <template #sdcs>
      <kade-time-params />
    </template>
    <template #klqxcs>
      <kade-card-auth-params />
    </template>
    <template #pjdycs>
      <kade-bill-print-params />
    </template>
  </kade-tab-wrap>
</template>
<script>
import { reactive } from "@vue/reactivity";
import { useStore } from "vuex";
import { getdeviceParamDetail } from "@/applications/eccard-iot/api";
import { onMounted } from "@vue/runtime-core";

import basicParams from "./basicParams";
import timeParams from "./timeParams";
import cardAuthParams from "./cardAuthParams";
import billPrintParams from "./billPrintParams";
import transferParams from "./transferParams";
const tabs = [
  { name: "jbcs", label: "基本参数" },
  { name: "zzcs", label: "转账参数" },
  { name: "sdcs", label: "时段参数" },
  { name: "klqxcs", label: "卡类权限参数" },
  { name: "pjdycs", label: "票据打印参数" },
];
export default {
  components: {
    "kade-basic-params": basicParams,
    "kade-time-params": timeParams,
    "kade-card-auth-params": cardAuthParams,
    "kade-bill-print-params": billPrintParams,
    "kade-transfer-params": transferParams,
  },
  setup() {
    const store = useStore();
    const state = reactive({
      tab: "jbcs",
    });
    const getDetails = async () => {
      let { data } = await getdeviceParamDetail(
        store.state.deviceParameters[store.state.app.activeTab].selectRow.id
      );
      store.commit("deviceParameters/updateState", {
        key: store.state.app.activeTab,
        childKey: "detailsParams",
        payload: data,
      });
    };
    onMounted(() => {
      getDetails();
    });
    return {
      tabs,
      state,
    };
  },
};
</script>
<style lang="scss" scoped>
</style>