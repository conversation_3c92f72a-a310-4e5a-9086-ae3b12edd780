<template>
  <el-dialog
    :model-value="dialogVisible"
    title="审核现金发放"
    width="30%"
    :before-close="off"
     :close-on-click-modal="false"
  >
    <el-form
      label-width="120px"
      ref="form"
      :rules="state.formRules"
      :model="state.form"
       size="small"
    >
      <el-form-item label="是否通过："  prop="auditStatus">
        <el-radio-group v-model="state.form.auditStatus">
          <el-radio label="SYS_AUDIT_PASSED">通过</el-radio>
          <el-radio label="SYS_AUDIT_FAIL">不通过</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="操作员：">
        <el-input v-model="state.form.operator" placeholder="当前登录账号姓名"></el-input>
      </el-form-item>
      <el-form-item label="备注：" prop="auditRemark">
        <el-input
          v-model="state.form.auditRemark"
          type="textarea"
        ></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="off()" size="small">取消</el-button>
        <el-button type="primary" @click="submit()" size="small">提交</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script>
import {
  ElDialog,
  ElForm,
  ElFormItem,
  ElButton,
  ElRadioGroup,
  ElRadio,
  ElInput,
  ElMessage,
} from "element-plus";
import { reactive, ref, watch } from "vue";
import { auditRechargeImport } from "@/applications/eccard-finance/api";
import { useStore } from "vuex";

export default {
  components: {
    ElDialog,
    ElButton,
    ElForm,
    ElFormItem,
    ElRadioGroup,
    ElRadio,
    ElInput,
  },
  props: {
    dialogVisible: {
      types: Boolean,
      default: false,
    },
  },
  setup(props, context) {
    const form = ref(null);
    const store = useStore();
    const state = reactive({
      form: {},
      formRules: {
        auditStatus: [
          {
            required: true,
            message: "请选择是否通过",
            trigger: "change",
          },
        ],
        auditRemark: [
          {
            required: true,
            message: "请输入审核说明",
            trigger: "blur",
          },
        ],
      },
    });
    
    watch(()=>props.dialogVisible,val=>{
      if(!val){
        state.form={}
      }
    })

    const off = () => {
      context.emit("off", false);
    };

    const submit = () => {
      form.value.validate(async (valid) => {
        if (valid) {
          state.form.projectId = store.state.cashData.selectRow.projectId;
          let {code,message}=await auditRechargeImport(state.form)
          if(code===0){
            ElMessage.success(message)
            context.emit("off", true);
          }else{
            ElMessage.error(message)
          }
        } else {
          return false;
        }
      });
    };
    return {
      form,
      state,
      submit,
      off,
    };
  },
};
</script>