<template>
    <div class="role">
        <kade-route-card>            
            <kade-table-wrap>
                <template #extra>
                    <!-- <el-button icon="el-icon-plus" @click="handleAdd" size="small" type="primary">新增</el-button> -->
                </template>                
                <el-table style="width: 100%" :data="options.dataList" v-loading="options.loading" border stripe>
                    <el-table-column label="类别名称" prop="roleName" width="150" align="center"></el-table-column>
                    <el-table-column label="状态" align="center">
                        <template #default="scope">
                            <el-switch :model-value="scope.row.status" active-value="ENABLE_TRUE" inactive-value="ENABLE_FALSE" />
                        </template>
                        
                    </el-table-column>
                    <el-table-column label="编辑时间" prop="lastModifyTime" align="center"></el-table-column>  
                    <el-table-column label="备注" prop="remarks" align="center"></el-table-column>                                                                                             
                    <el-table-column label="操作" align="center" width="120">
                        <template #default="scope">
                            <el-button @click="handleEdit(scope.row)" size="mini" type="text">编辑</el-button>
                            <!-- <el-button @click="handleDel(scope.row)" type="text" size="mini">删除</el-button> -->
                        </template>
                    </el-table-column>                                    
                </el-table>
                <div class="pagination">
                    <el-pagination
                        background
                        :current-page="options.currPage"
                        layout="total, sizes, prev, pager, next, jumper"
                        :page-sizes="[10, 20, 50, 100, 200]"        
                        :total="options.total"
                        :page-size="options.pageSize"
                        @current-change="(val) => pageChange(val)"
                        @size-change="(val) => sizeChange(val)"
                    >
                    </el-pagination>           
                </div>                 
            </kade-table-wrap>          
        </kade-route-card>
        <kade-role-edit v-model="showCreateModal" :role="state.role" :title="state.role.id ? '编辑' : '新增'" @change="loadData"/>
    </div>
</template>
<script>
import { ref, reactive } from 'vue';
import {
    ElSwitch,
    ElTable,
    ElTableColumn,
    ElButton,
    ElPagination,
    ElMessageBox,
    ElMessage,
} from 'element-plus';
import { getRolelistByPage, delRole } from '@/applications/eccard-basic-data/api';
import { usePagination } from '@/hooks/usePagination';
import Edit from './components/edit';
export default {
    components: {
        ElSwitch,
        'el-table': ElTable,
        'el-button': ElButton,
        'el-table-column': ElTableColumn,
        'el-pagination': ElPagination,
        'kade-role-edit': Edit,
    },
    setup() {
        const showCreateModal = ref(false);
        const state = reactive({
            role: {}
        });
        const { options, loadData, querys,pageChange,sizeChange } = usePagination(getRolelistByPage, {}, {}, {
            currentPage: 'currPage',
            pageSize: 'pageSize'
        });
        const handleAdd = () => {
            state.role = {};
            showCreateModal.value = true;
        }
        const handleDel = ({ id, roleName }) => {
            ElMessageBox.confirm(`确认删除身份类别"${roleName}"?`, {
                type: 'warning',
                closeOnPressEscape: false,
                closeOnClickModal: false,                
            }).then(async () => {
                try{
                    const { message } = await delRole({ id });
                    ElMessage.success(message);
                    loadData();
                }catch(e) {
                    throw new Error(e.message);
                }
            });          
        }
        const handleEdit = (role) => {
            state.role = role;
            showCreateModal.value = true;
        }
        return {
            showCreateModal,
            options,
            querys,
            pageChange,
            sizeChange,
            handleAdd,
            loadData,
            handleDel,
            handleEdit,
            state,
        }      
    }
}
</script>
<style lang="scss" scoped>
.role{
    width: 100%;
    height: 100%;
}
</style>