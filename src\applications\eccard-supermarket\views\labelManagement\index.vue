<template>
  <div class="personnelInformation">
    <kade-route-card>
      <kade-table-filter @search="search" @reset="reset">
        <el-form inline label-width="120px" size="small">
          <el-form-item label="标签名称">
            <el-input size="small" v-model="state.form.labelName" placeholder="请输入标签名称" />
          </el-form-item>
          <!--           <el-form-item label="状态">
            <el-select v-model="state.form.label">
              <el-option v-for="(item, index) in statusList " :key="index" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item> -->
        </el-form>
      </kade-table-filter>
      <kade-table-wrap title="菜品标签列表">
        <template #extra>
          <el-button icon="el-icon-plus" size="mini" type="success" @click="handleEdit({})">新增标签</el-button>
        </template>
        <el-table style="width: 100%" height="55vh" :data="state.dataList" v-loading="state.loading" highlight-current-row border
          stripe @row-click="rowClick" @selection-change="selectionChange">
          <!-- <el-table-column type="selection" width="55" align="center" /> -->
          <el-table-column label="标签名称" prop="labelName" align="center" show-overflow-tooltip></el-table-column>
          <!-- <el-table-column label="状态" prop="userName" align="center" show-overflow-tooltip></el-table-column> -->
          <el-table-column label="操作" align="center" width="200" show-overflow-tooltip>
            <template #default="scope">
              <el-button size="mini" type="text" @click="handleEdit(scope.row)">编辑</el-button>
              <el-button size="mini" type="text" @click="handleDel(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination">
          <el-pagination background v-model:current-page="state.form.currentPage"
            layout="total, sizes, prev, pager, next, jumper" :page-sizes="[10, 20, 50, 100, 200]" :total="state.total"
            v-model:page-size="state.form.pageSize" @current-change="handlePageChange" @size-change="handleSizeChange">
          </el-pagination>
        </div>
      </kade-table-wrap>
    </kade-route-card>
    <kade-food-label-edit v-model="state.isEdit" :rowData="state.rowData" @update:modelValue="close" />
  </div>
</template>
<script>
import {
  ElTable,
  ElTableColumn,
  ElForm,
  ElFormItem,
  ElInput,
  /*   ElSelect,
    ElOption, */
  ElPagination,
  ElButton,
  ElMessageBox,
  ElMessage,
} from "element-plus";
import { onMounted, reactive } from "vue";
import { useDict } from "@/hooks/useDict.js";
import { labelList, deleteLabel } from "@/applications/eccard-supermarket/api.js";
import foodLabelEdit from "./components/edit.vue"
export default {
  components: {
    "el-table": ElTable,
    "el-table-column": ElTableColumn,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    "el-input": ElInput,
    "el-pagination": ElPagination,
    "el-button": ElButton,
    /*     ElSelect,
        ElOption, */
    "kade-food-label-edit": foodLabelEdit
  },
  setup() {
    const statusList = useDict("SYS_ENABLE")
    const state = reactive({
      isEdit: false,
      loading: false,
      form: {
        pageNum: 1,
        pageSize: 10,
      },
      dataList: [],
      total: 0,
      rowData: {},
    });

    const getList = async () => {
      state.loading = true
      try {
        let { data: { list, total } } = await labelList(state.form)
        state.dataList = list
        state.total = total
        state.loading = false
      }
      catch {
        state.loading = false
      }
    }

    const handleEdit = (row) => {
      state.rowData = row
      state.isEdit = true
    }
    const handleDel = row => {
      ElMessageBox.confirm("确认删除?", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        let { code, message } = await deleteLabel([row.id]);
        if (code === 0) {
          ElMessage.success(message);
          getList();
        }
      });
    }
    const search = () => {
      getList()
    }
    const reset = () => {
      state.form = {
        pageNum: 1,
        pageSize: 10,
      }
    }
    const handlePageChange = val => {
      state.form.pageNum = val
      getList()
    }
    const handleSizeChange = val => {
      state.form.pageNum = 1
      state.form.pageSize = val
      getList()
    }
    const close = val => {
      if (val) {
        getList()
      }
      state.isEdit = false
    }
    onMounted(() => {
      getList()
    })

    return {
      statusList,
      state,
      handleEdit,
      handleDel,
      search,
      reset,
      handlePageChange,
      handleSizeChange,
      close
    };
  },
};
</script>
<style lang="scss" scoped>

</style>
