<template>
  <el-dialog :model-value="isShow" :title="'选择人员'" width="60%" :before-close="beforeClose"
    :close-on-click-modal="false">
    <div style="padding:20rpx 10rpx">
      <kade-select-table :isMultiple="isMultiple" :isShow="isShow" :value="[]"
        :reqFnc="getUserInfoListByPage" :selectCondition="state.selectCondition" :column="column" :params="params"
        @rowChange="rowChange" @change="personChange" />
    </div>
    <template #footer v-if="isMultiple">
      <span class="dialog-footer">
        <el-button @click="beforeClose()" size="mini">返&nbsp;&nbsp;回</el-button>
        <el-button @click="submit()" type="primary" size="mini">保&nbsp;&nbsp;存</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script>
import { ElDialog, ElButton } from "element-plus";
import { reactive } from "@vue/reactivity";
import { useDict } from "@/hooks/useDict.js";
import { onMounted } from "@vue/runtime-core";
import {
  getUserInfoListByPage,
  getRolelist,
} from "@/applications/eccard-basic-data/api";
import selectTable from "@/components/table/selectTable.vue";
const column = [
  { label: "用户编号", prop: "userCode", isDict: false, width: "" },
  { label: "姓名", prop: "userName", isDict: false, width: "" },
  { label: "组织机构", prop: "deptName", isDict: false, width: "" },
  { label: "身份类别", prop: "roleName", isDict: false, width: "" },
];
const params = {
  currentPageKey: "beginPage",
  pageSizeKey: "rowNum",
  resListKey: "dataList",
  resTotalKey: "totalCount",
  value: {},
  tagNameKey: "userName",
  valueKey: "id",
};
export default {
  emits: ["close"],
  components: {
    ElDialog,
    ElButton,
    "kade-select-table": selectTable,
  },
  props: {
    isShow: {
      type: Boolean,
      default: false,
    },
    isMultiple: {
      type: Boolean,
      default: true
    }
  },
  setup(props, context) {
    const statusList = useDict("BASE_USER_STATE");
    const state = reactive({
      selectCondition: [
        {
          label: "关键字",
          valueKey: "userName",
          placeholder: "姓名或编号关键字搜索",
          isSelect: false,
        },
        {
          label: "组织机构",
          valueKey: "userDept",
          dataKey: "deptPath",
          placeholder: "请选择",
          isSelect: false,
          isTree: "dept",
        },
        {
          label: "身份类别",
          valueKey: "userRole",
          placeholder: "请选择",
          isSelect: true,
          select: {
            list: [],
            option: {
              label: "roleName",
              value: "id",
            },
          },
        },
      ],
      form: {},
    });
    const queryRolelist = async () => {
      let { data } = await getRolelist();
      state.selectCondition[2].select.list = data;
    };
    const personChange = (val) => {
      state.form = val;
    };
    const rowChange = row => {
      if (!props.isMultiple) {
        context.emit("close", row);

      }
    }
    const submit = async () => {
      context.emit("close", state.form);
    };

    const beforeClose = () => {
      context.emit("close", false);
      state.form.userIds = [];
    };
    onMounted(() => {
      queryRolelist();
    });
    return {
      getUserInfoListByPage,
      column,
      params,
      statusList,
      state,
      rowChange,
      personChange,
      submit,
      beforeClose,
    };
  },
};
</script>
<style lang="scss" scoped>
.el-divider--horizontal {
  margin: 0px 0px 10px;
}

.kade-table-wrap {
  padding-bottom: 0;
}

:deep(.el-form) {
  margin-top: 20px;
}
</style>