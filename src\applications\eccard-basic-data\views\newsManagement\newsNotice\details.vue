<template>
  <el-dialog :model-value="isShow" width="90%" :before-close="beforeClose" :close-on-click-modal="false">
    <template #title>
      <div class="dialog-title">
        <div class="detail-title">新闻详情</div>
        <div
          class="detail-status"
          :style="{
            color: details.status == 'FALSE' ? '#02D200' : '#3399FF',
            'border-color': details.status == 'FALSE' ? '#02D200' : '#3399FF',
          }"
        >
          {{ details.status == "FALSE" ? "即将发布" : "已发布成功" }}
        </div>
      </div>
    </template>
    <div style="padding-top: 20px">
      <el-form size="mini" label-width="120px">
        <el-row>
          <el-col :span="6">
            <el-form-item label="新闻公告类型:">
              <el-input
                readonly
                :model-value="filterDictionary(details.newsType, newsType)"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="接收端:">
              <el-input
                readonly
                :model-value="
                  details.newsReceiver
                    .split(',')
                    .map((item) => filterDictionary(item, newsReceiver))
                    .join(',')
                "
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label=" 发布人:">
              <el-input
                readonly
                :model-value="details.newsPublisher"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="发布时间:">
              <el-input
                readonly
                :model-value="details.newsPublishTime"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="新闻公告标题:">
          <el-input
            readonly
            :model-value="details.newsTitle"
            :maxlength="30"
          ></el-input>
        </el-form-item>
        <el-form-item label="封面图片:">
          <el-image
            style="width: 240px; height: 160px"
            :src="details.newsImgUrl"
            fit="none"
          ></el-image>
        </el-form-item>
        <el-form-item label="新闻正文:">
          <div class="news-text" v-html="details.newsContent"></div>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button type="primary" v-if="details.status == 'FALSE'" @click="edit()" size="mini">编辑</el-button>
        <el-button @click="beforeClose" size="mini">返回</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script>
import { computed, reactive } from "vue";
import { useStore } from "vuex";
import { filterDictionary } from "@/utils/index.js";
import { useDict } from "@/hooks/useDict";
import {
  ElDialog,
  ElRow,
  ElCol,
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElImage,
} from "element-plus";

export default {
  components: {
    ElDialog,
    ElRow,
    ElCol,
    ElButton,
    ElForm,
    ElFormItem,
    ElInput,
    ElImage,
  },
  setup() {
    const store = useStore();
    const newsType = useDict("SYS_NEWS_TYPE"); //新闻公告类型
    const newsReceiver = useDict("SYS_NEWS_RECEIVER"); //新闻接收

    const state = reactive({
      isTailor: false,
      imgFile: "",
      form: {
        newsReceiver: [],
      },
    });

    const isShow = computed(() => {
      return store.state.news.isDetails;
    });
    const details = computed(() => {
      return store.state.news.selectRow;
    });

    const edit = () => {
      store.commit("news/updateState", {
        key: "isDetails",
        payload: false,
      });
      store.commit("news/updateState", {
        key: "isEdit",
        payload: true,
      });
    };

    const beforeClose = () => {
      store.commit("news/updateState", {
        key: "isDetails",
        payload: false,
      });
    };

    return {
      filterDictionary,
      newsType,
      newsReceiver,
      state,
      isShow,
      details,
      edit,
      beforeClose,
    };
  },
};
</script>
<style lang="scss" scoped>
.news-text {
  box-sizing: border-box;
  width: 100%;
  height: 300px;
  padding: 20px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow-y: scroll;
}
.dialog-title {
  display: flex;
  align-items: center;
  .detail-status {
    border: 1px solid #000;
    border-radius: 4px;
    margin-left: 20px;
    padding: 3px;
    font-size: 12px;

  }
}
</style>