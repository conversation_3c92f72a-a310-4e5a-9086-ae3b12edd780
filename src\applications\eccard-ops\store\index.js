﻿import { createStore } from 'vuex'

const moduleFiles = require.context('./modules', true, /\.js$/);
const modules = moduleFiles.keys().reduce((modules, modulePath) => {
	const moduleName = modulePath.replace(/^\.\/(.*)\.\w+$/, '$1')
	const value = moduleFiles(modulePath);
	modules[moduleName] = value.default;
	return modules;
}, {});

export default createStore({
  state: {
		title: CONFIG.TITLE,
  },
  modules
})
