<template>
  <kade-route-card>
    <kade-table-filter @search="() => handleSearch(true)" @reset="() => handleSearch(false)">
      <el-form inline size="small" label-width="100px">
        <el-form-item label="租户编号:">
          <el-input v-model="querys.tenantNo" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="租户名称:">
          <el-input v-model="querys.tenantName" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="租户状态:">
          <el-select clearable placeholder="请选择" v-model="querys.tenantStatus">
            <el-option v-for="item in status" :label="item.label" :value="item.value" :key="item.value"></el-option>
          </el-select>
        </el-form-item>
         <el-form-item label="联系电话:">
          <el-input v-model="querys.tenantMobile" placeholder="请输入"></el-input>
        </el-form-item>
      </el-form> 
    </kade-table-filter>
    <kade-table-wrap title="租户信息">
      <template #extra>
        <el-button size="small" icon="el-icon-plus" type="primary" @click="() => handleEdit()">新建</el-button>
      </template>      
      <el-table style="width: 100%" :data="options.dataList" v-loading="options.loading" border stripe>
          <el-table-column
            label="序号"
            type="index"
            align="center"
            width="50"
          >
          </el-table-column>
          <el-table-column
            label="租户编号"
            prop="tenantNo"
            align="center"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            label="租户名称"
            prop="tenantName"
            align="center"
            show-overflow-tooltip
          ></el-table-column> 
          <el-table-column
            label="状态"
            align="center"
            show-overflow-tooltip
          >
          <template #default="scope">
            <span :class="['tenant-status', { frozen: scope.row.tenantStatus === 'FROZEN' }]">{{ dictionaryFilter(scope.row.tenantStatus) }}</span>
          </template>
          </el-table-column>
          <el-table-column
            label="租户类型"
            align="center"
          >
          <template #default="scope">
            {{ dictionaryFilter(scope.row.tenantType) }}
          </template>                
          </el-table-column> 
          <el-table-column
            label="租户类别"
            align="center"
            show-overflow-tooltip
          >
          <template #default="scope">
            {{ dictionaryFilter(scope.row.tenantCategory) }}
          </template>                
          </el-table-column> 
          <el-table-column
            label="租户性质"
            align="center"
            show-overflow-tooltip
          >
          <template #default="scope">
            {{ dictionaryFilter(scope.row.tenantNature) }}
          </template>                
          </el-table-column>
          <el-table-column
            label="操作员姓名"
            align="center"
            prop="tenantOperName"
            show-overflow-tooltip
          >               
          </el-table-column> 
          <el-table-column
            label="手机号"
            align="center"
            prop="tenantMobile"
            show-overflow-tooltip
          >               
          </el-table-column>  
          <el-table-column
            label="失效时间"
            align="center"
            prop="tenantExpirationTime"
            show-overflow-tooltip
          >               
          </el-table-column>                                                                                                                                                                           
          <el-table-column
            label="操作"
            align="center"
            fixed="right"
            width="200"
          >
            <template #default="scope">
              <el-button v-if="scope.row.tenantStatus === 'FROZEN'" @click="handleFrozen(scope.row, 'thraw')" type="text" size="mini">解冻</el-button>
              <el-button v-else @click="handleFrozen(scope.row, 'frozen')" type="text" size="mini">冻结</el-button>
              <el-button @click="handleInfo(scope.row)" type="text" size="mini">详情</el-button>
              <el-button @click="handleEdit(scope.row)" type="text" size="mini">编辑</el-button>
              <el-button @click="handleAppAuth(scope.row)" type="text" size="mini">应用授权</el-button>
            </template>
          </el-table-column>                                    
      </el-table>
      <div class="pagination">
        <el-pagination
          background
          :current-page="options.pageNum"
          layout="total, sizes, prev, pager, next, jumper"
          :page-sizes="[10, 20, 50, 100, 200]"        
          :total="options.total"
          :page-size="options.pageSize"
          @current-change="(val) => pageChange(val)"
          @size-change="(val) => sizeChange(val)"
        >
        </el-pagination>             
      </div>      
    </kade-table-wrap>
    <kade-edit-modal
      @change="loadData"
      @addClose="addClose"
      :title="state.tenantId ? '编辑租户' : '新建租户'"
      :id="state.tenantId"
      v-model="state.showCreateModal"
    />
    <kade-info-modal
      title="详情"
      :id="state.tenantId"
      v-model="state.showInfoModal"
      @edit="handleLinkEdit"
    />
    <kade-frozen-modal
      :type="state.tenantStatus"
      @change="loadData"
      :title="state.tenantStatus === 'frozen' ? '冻结' : '解冻'"
      :id="state.tenantId"
      v-model="state.showFrozenModal"
    />
    <kade-app-auth-modal title="应用授权" :appList="state.appList" :id="state.tenantId" v-model="state.showAppAuthModal" />
  </kade-route-card>
  
</template>
<script>
import {
  ElTable,
  ElTableColumn,
  ElButton,
  ElPagination,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
} from 'element-plus';
import { reactive, onMounted } from 'vue';
import { usePagination } from '@/hooks/usePagination';
import { useDict } from '@/hooks/useDict';
import { getTenantListPage} from '@/applications/eccard-ops/api';
import Edit from './components/edit';
import Frozen from './components/frozen';
import Info from './components/info';
import appAuth from './components/appAuth';
import { useStore } from 'vuex';
export default {
  components: {
    'el-table': ElTable,
    'el-table-column': ElTableColumn,
    'el-button': ElButton,
    'el-pagination': ElPagination,
    'el-form': ElForm,
    'el-form-item': ElFormItem,
    'el-input': ElInput,
    'el-select': ElSelect,
    'el-option': ElOption,
    'kade-edit-modal': Edit,
    'kade-frozen-modal': Frozen,
    'kade-info-modal': Info,
    'kade-app-auth-modal': appAuth,
  },
  setup() {
    const status = useDict('TENANT_STATUS');
    const state = reactive({
      showCreateModal: false,
      showInfoModal: false,
      showFrozenModal: false,
      showAppAuthModal:false,
      tenantId: null,
      tenantStatus: '',
      appList:[],
    });
    const store = useStore();
    const defaultQuerys = () => ({
      tenantNo: '',
      tenantName: '',
      tenantStatus: '',      
    });
    const { 
      options,
      loadData,
      querys,
      search,
      pageChange,
      sizeChange
    } = usePagination(getTenantListPage, defaultQuerys(), {}, { currentPage: 'pageNum', pageSize: 'pageSize' });


    const handleSearch = (type) => {
      if (type) {
        search();
      } else {
        Object.assign(querys, defaultQuerys());
      }
    }
    const handleEdit = (row) => {
      if (row) {
        state.tenantId = row.tenantId;
      } else {
        state.tenantId = null;
      }
      state.showCreateModal = true;
    }
    const handleFrozen = (row, type) => {
      state.tenantId = row.tenantId;
      state.tenantStatus = type;
      state.showFrozenModal = true;
    }
    const handleInfo = (row) => {
      state.tenantId = row.tenantId;
      state.showInfoModal = true;
    }
    const handleLinkEdit = (id) => {
      state.tenantId = id;
      state.showInfoModal = false;
      state.showCreateModal = true;
    }

    const handleAppAuth=async (row)=>{
      state.tenantId = row.tenantId;
      state.showAppAuthModal = true;
    }

    const addClose=tenantId=>{
      state.tenantId = tenantId;
      state.showAppAuthModal = true;
    }

    onMounted(() => {
      store.dispatch('app/loadTopTenants')
    });
    return {
      state,
      options,
      loadData,
      querys,
      pageChange,
      sizeChange,
      status,
      handleSearch,
      handleEdit,
      handleFrozen,
      handleInfo,
      handleLinkEdit,
      handleAppAuth,
      addClose
    };
  }
}
</script>
<style lang="scss" scoped>
.tenant-status{
  position: relative;
  padding-left: 15px;
  &::before{
    content: '';
    display: block;
    width: 7px;
    height: 7px;
    border-radius: 50%;
    background-color: $success-color;
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    margin: auto;
  }
  &.frozen::before{
    background-color: #afafaf;
  }
}
</style>