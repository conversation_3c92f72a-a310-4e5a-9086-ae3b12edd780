<template>
  <div class="cashierdetails">
    <kade-route-card style="height: auto">
      <kade-table-filter @search="search()" @reset="reset()">
        <el-form inline label-width="100px" size="mini">
          <el-form-item label="文件名称">
            <el-input v-model="state.form.fileName" placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="模块名称">
            <el-input v-model="state.form.moduleName" placeholder="请输入"></el-input>
          </el-form-item>
        </el-form>
      </kade-table-filter>
      <kade-table-wrap title="数据文件列表" v-loading="state.loading">
        <!--         <template #extra>
          <el-button class="btn-blue" icon="el-icon-daochu" size="mini" @click="exportClick()">导出</el-button>
        </template> -->
        <el-table :data="state.dataList" border height="55vh">
          <el-table-column show-overflow-tooltip prop="fileName" label="文件名称" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip prop="filePath" label="文件路径" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip prop="status" label="状态" align="center">
            <template #default="scope">
              {{ scope.row.status == 1 ? '已删除' : '未删除' }}
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip prop="userName" label="创建人" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip prop="createTime" label="创建时间" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip prop="status" label="状态" align="center">
            <template #default="scope">
              <el-button type="text" size="mini" @click="exportClick(scope.row)">下载</el-button>
              <el-button type="text" size="mini" @click="delClick(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination">
          <el-pagination :currentPage="state.form.pageNum" :page-size="state.form.pageSize"
            :page-sizes="[10, 20, 30, 40]" :small="small" background layout="total, sizes, prev, pager, next, jumper"
            :total="state.total" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
        </div>
      </kade-table-wrap>
    </kade-route-card>
  </div>
</template>

<script>
import { reactive } from "@vue/reactivity";
import {
  ElForm,
  ElFormItem,
  ElInput,
  ElButton,
  ElTable,
  ElTableColumn,
  ElPagination,
  ElMessageBox,
  ElMessage
} from "element-plus";
import {
  getFileRecordList,
  fileRecordDownload,
  fileRecordDel
} from "@/applications/eccard-finance/api";
import { downloadXlsx } from "@/utils";
import { onMounted } from "@vue/runtime-core";
export default {
  components: {
    ElForm,
    ElFormItem,
    ElInput,
    ElButton,
    ElTable,
    ElTableColumn,
    ElPagination,
  },
  setup() {
    const state = reactive({
      loading: false,
      form: {
        pageNum: 1,
        pageSize: 10,
      },
      dataList: [],
      total: 0,
    });

    const getList = () => {
      state.loading = true
      getFileRecordList(state.form).then((res) => {
        let { data: { list, total } } = res;
        state.dataList = list;
        state.total = total;
        state.loading = false
      }).catch(() => {
        state.loading = false
      });
    };

    const search = () => {
      getList();
    };
    const reset = () => {
      state.form = {
        pageNum: 1,
        pageSize: 10,
      };
    };
    const handleSizeChange = (val) => {
      state.form.pageNum = 1;
      state.form.pageSize = val;
      getList();
    };
    const handleCurrentChange = (val) => {
      state.form.pageNum = val;
      getList();
    };
    const exportClick = async (row) => {
      state.loading = true
      try {
        let res = await fileRecordDownload({ filePath: row.filePath });
        downloadXlsx(res, row.fileName);
        state.loading = false
      }
      catch {
        state.loading = false
      }
    };
    const delClick = row => {
      ElMessageBox.confirm("确认删除?", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        let { code, message } = await fileRecordDel([row]);
        if (code === 0) {
          ElMessage.success(message);
          getList();
        }
      });
    }
    onMounted(() => {
      getList();
    });
    return {
      state,
      search,
      exportClick,
      delClick,
      reset,
      handleSizeChange,
      handleCurrentChange,
    };
  },
};
</script>

<style lang="scss" scoped>
// :deep(.el-range-editor--mini.el-input__inner){
//     width: 205px;
// }
:deep(.el-select .el-input__inner) {
  height: 28px;
  width: 205px;
}

:deep(.el-input--mini .el-input__inner) {
  width: 205px;
}

:deep(.el-pagination .el-select .el-input .el-input__inner) {
  width: 100px;
}

:deep(.el-pagination__editor.el-input .el-input__inner) {
  width: 46px;
}
</style>