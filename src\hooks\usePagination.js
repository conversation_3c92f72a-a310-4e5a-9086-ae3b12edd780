import { reactive, onMounted } from 'vue';

const formatQuery = (queryObj) => {
  const data = JSON.parse(JSON.stringify(queryObj));
  let obj = {};
  for(let key in data) {
    let temp = data[key];
    if (typeof temp === 'string') {
      temp = temp.replace(/^\s+|\s+$/gim, '');
    }
    if(temp !== '') {
      obj[key] = temp;
    }
  }
  return obj;
}

// 分页hooks
export function usePagination(
    fetch, 
    query = {},
    opts = {}, 
    pageKey = { currentPage: 'beginPage', pageSize: 'rowNum' },
    isNotMountedLoad = false,
  ) {
  const options = reactive(Object.assign({
    pageSize: 10,
    total: 0,
    currentPage: 1,
    loading: false,
    dataList: [],
    onLoad: () => {}    
  }, opts));
  const querys = reactive(query);
  const loadData = async (isInfinte = false) => {
    try{
      options.loading = true;
      const { currentPage, pageSize } = options;
      const { data: { dataList, totalCount, total, list } } = await fetch({
        [pageKey['currentPage']]: currentPage,
        [pageKey['pageSize']]: pageSize,
        ...formatQuery(querys),
      });
      if(isInfinte) {
        options.dataList = [ ...options.dataList, ...(dataList || list) ];
      } else {
        options.dataList = dataList || list;
      }
      options.total = totalCount ?? total;
    } catch(e) {
      throw new Error(e.message);
    } finally {
      options.loading = false;
      options.onLoad?.(options.dataList);
    }
  };
  const pageChange = (page) => {
    options.currentPage = page;
    loadData();
  }
  const sizeChange = (page) => {
    options.pageSize = page;
    loadData();
  }
  const search = () => {
    options.dataList = [];
    options.currentPage = 1;
    loadData();
  }
  onMounted(async () => {
    if(!isNotMountedLoad) {
      loadData();
    }
  });
  return {
    options,
    querys,
    loadData,
    search,
    pageChange,
    sizeChange,
  }
}