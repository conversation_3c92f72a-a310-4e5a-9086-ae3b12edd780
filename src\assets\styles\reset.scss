﻿@font-face{
    font-family: 'blacktitle'; 
    src:url('../font/blacktitle.ttf') format('truetype'),
  }
html,
body,
nav,
ul,
ol,
li,
img,
input,
button,
span,
section,
p,
div {
    margin: 0;
    padding: 0;
}
html,
body {
    font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial,
        sans-serif,"blacktitle";
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: $font-size;
    color: $font-color;
    width: 100%;
    height: 100%;
    overflow: hidden;
    background-color: $bg-color;
    user-select: text;
    -webkit-text-size-adjust: none;
}
#app {
    width: 100%;
    height: 100%;
}
::-webkit-scrollbar {
    width: 7px;
    height: 7px;
    background-color: #f5f5f5;
}

/*定义滚动条轨道 内阴影+圆角*/
::-webkit-scrollbar-track {
    box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    background-color: #f5f5f5;
}

/*定义滑块 内阴影+圆角*/
::-webkit-scrollbar-thumb {
    border-radius: 10px;
    box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
    background-color: #c8c8c8;
}
.kade-dialog {
    .el-dialog__header {
        padding: 15px 20px 15px 20px;
        border-bottom: 1px solid $dialog-header-border-color;
        .modal-title {
            font-size: $dialog-header-title-size;
            position: relative;
        }
        .close {
            position: absolute;
            right: 0;
            top: 0;
            bottom: 0;
            margin: auto;
        }
    }
    .modal-content {
        width: 100%;
        max-height: 400px;
        overflow-y: auto;
        padding: 20px 10px 20px 0;
        box-sizing: border-box;
        .el-select {
            width: 100%;
        }
    }
}
.custom-calendar {
    .el-calendar-table .el-calendar-day {
        height: 40px !important;
        line-height: 25px;
        text-align: center;
    }
    .el-calendar__body {
        padding: 0px 20px 20px !important;
    }
    .el-calendar__header {
        padding: 10px 20px !important;
    }
}
.el-dialog__footer {
    border-top: 1px solid $border-color;
    padding: 15px 20px !important;
}
.el-dialog__header {
    border-bottom: 1px solid $border-color;
  }
.el-dialog__body {
    padding: 0 20px !important;
}

.el-table__body tr.current-row > td,
.el-table__body tr.current-row:hover > td {
    background-color: $primary-color;
    color: #fff;
    .el-button--text {
        color: #fff;
    }
}


.el-table--striped .el-table__body tr.el-table__row--striped.current-row td,
.el-table--striped .el-table__body tr.el-table__row--striped.current-row:hover td {
    background-color: $primary-color;
    color: #fff;
}

.el-switch {
    border: 1px solid #fff;
    border-radius: 10px;
}
.el-switch__core {
    height: 18px;
}
.el-switch__action {
    line-height: 18px;
}

.el-cascader-menu {
    background-color: #fff;
}

.el-cascader-menu {
    height: 200px !important;
    overflow-y: scroll !important;
}

.green {
    color: #1abc9c !important;
}
.red {
    color: #ff3f3f !important;
}
.blue {
    color: #3399ff !important;
}
.ash {
    color: #bbbbbb !important ;
}

.btn-yellow {
    background: #ff8726 !important;
    color: #fff !important;
}
.btn-yellow:hover {
    background: #e68a00 !important;
}

.btn-green {
    background: #02d200 !important;
    color: #fff !important;
}
.btn-green:hover {
    background: #04b200 !important;
}

.btn-purple {
    background: #955fff !important;
    color: #fff !important;
}
.btn-purple:hover {
    background: #7714a6 !important;
}

.btn-blue {
    background: #29bbf4 !important;
    color: #fff !important;
}
.btn-blue:hover {
    background: #3399ff !important;
}

.btn-deep-blue {
    background: #3399ff !important;
    color: #fff !important;
}
.btn-deep-blue:hover {
    background: #127cdd !important;
}
.btn-pink {
    background: #ff7171 !important;
    color: #fff !important;
}
.btn-pink:hover {
    background: #ff6060 !important;
}
.btn-ash {
    background: #d7d7d7 !important;
    color: #fff !important;
}
