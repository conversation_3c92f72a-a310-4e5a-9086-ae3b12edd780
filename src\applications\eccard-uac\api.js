import request from '@/service';
/* 楼栋管理 */
//楼栋列表分页查询
export function getBuildingList(params) {
  return request.get('/eccard-access-control/baseBuilding/page', { params });
}
//添加楼栋信息
export function buildingAdd(params) {
  return request.post('/eccard-access-control/baseBuilding/addBuilding', params);
}
//修改楼栋信息
export function buildingEdit(params) {
  return request.put('/eccard-access-control/baseBuilding/updateBuilding', params);
}
//删除楼栋信息
export function buildingDelete(params) {
  return request.delete(`/eccard-access-control/baseBuilding/deleteBuildingById/${params}`);
}
//批量删除楼栋信息
export function buildingBatchDelete(params) {
  return request.delete(`/eccard-access-control/baseBuilding/batchDeleteBuilding/${params}`);
}

//楼栋列表导出
export function exportBuilding(params) {
  return request.get('/eccard-access-control/baseBuilding/exportBuilding', {
    params,
    headers: {
      'Content-Type': 'application/octet-stream',
      Accept: "*/*"
    },
    responseType: 'blob',
  });
}

//楼栋列表下载导入模板
export function downImportTemplate(params) {
  return request.get('/eccard-access-control/baseBuilding/downImportTemplate', {
    params,
    headers: {
      'Content-Type': 'application/octet-stream',
      Accept: "*/*"
    },
    responseType: 'blob',
  });
}

//获取导入楼栋信息列表数据-分页
export function getImportBuildingList(params) {
  return request.get('/eccard-access-control/baseBuilding/getImportBuildingListByPage', { params });
}
//删除待保存楼栋信息
export function deleteWaitBuilding(params) {
  return request.delete(`/eccard-access-control/baseBuilding/deleteWaitBuildingById/${params.projectNo}/${params.id}`);
}
//保存导入楼栋数据
export function saveImportBuilding(params) {
  return request.post('/eccard-access-control/baseBuilding/saveImportBuilding', params);
}

/* 楼栋类型 */
//楼栋类型列表
export function buildingTypeList(params) {
  return request.get('/eccard-access-control/baseBuildingType/list', { params });
}
//楼栋类型表分页列表
export function baseBuildingTypeList(params) {
  return request.get('/eccard-access-control/baseBuildingType', { params });
}
//添加楼栋类型
export function baseBuildingTypeAdd(params) {
  return request.post('/eccard-access-control/baseBuildingType', params);
}
//修改楼栋类型
export function baseBuildingTypeEdit(params) {
  return request.put('/eccard-access-control/baseBuildingType', params);
}
//删除楼栋类型
export function baseBuildingTypeDel(params) {
  return request.delete(`/eccard-access-control/baseBuildingType/${params}`);
}
//批量删除楼栋类型
export function baseBuildingTypeBatchDelete(params) {
  return request.delete(`/eccard-access-control/baseBuildingType/deleteBatch/${params}`);
}




//楼栋选择列表查询
export function queryBuildingList(params) {
  return request.get(`/eccard-access-control/baseBuilding/list`, { params });
}
//获取楼栋单元选择列表
export function queryBuildingUnitList(params) {
  return request.get(`/eccard-access-control/baseBuilding/getBuildingUnitList/${params}`);
}
//获取楼栋楼层选择列表
export function queryBuildingFloorList(params) {
  return request.get(`/eccard-access-control/baseBuilding/getBuildingFloorList/${params.buildId}/${params.unitId}`);
}
//获取房间选择列表
export function queryRoomList(params) {
  return request.get(`/eccard-access-control/RoomInfo/list`, { params });
}



/* 房间场所类型 */

//房间场所类型选择列表查询
export function getRoomTypeSelectList(params) {
  return request.get('/eccard-access-control/baseBuildRoomType/list', { params });
}

//房间场所类型分页查询
export function getRoomTypeList(params) {
  return request.get('/eccard-access-control/baseBuildRoomType', { params });
}
//添加房间场所类型
export function roomTypeAdd(params) {
  return request.post('/eccard-access-control/baseBuildRoomType', params);
}
//修改房间场所类型
export function roomTypeEdit(params) {
  return request.put('/eccard-access-control/baseBuildRoomType', params);
}
//删除房间场所类型
export function roomTypeDelete(params) {
  return request.delete(`/eccard-access-control/baseBuildRoomType/${params}`);
}

//获取房间物品配置
export function getRoomGoods(params) {
  return request.get(`/eccard-access-control/baseBuildRoomType/RoomGoods/list`, { params });
}
//添加房间物品设置
export function addRoomGoodsAdd(params) {
  return request.post('/eccard-access-control/baseBuildRoomType/RoomGoods/addRoomGoods', params);
}
//修改房间物品设置
export function addRoomGoodsEdit(params) {
  return request.post('/eccard-access-control/baseBuildRoomType/RoomGoods/editRoomGoods', params);
}
//删除房间物品设置
export function roomGoodsDelete(params) {
  return request.delete(`/eccard-access-control/baseBuildRoomType/RoomGoods/deleteRoomGoods/${params}`);
}
//批量删除房间物品设置
export function roomGoodsBatchDelete(params) {
  return request.delete(`/eccard-access-control/baseBuildRoomType/RoomGoods/deleteRoomGoodsBatch/${params}`);
}
//导出房间物品配置
export function exportRoomGoods(params) {
  return request.get('/eccard-access-control/baseBuildRoomType/RoomGoods/exportRoomGoods', {
    params,
    headers: {
      'Content-Type': 'application/octet-stream',
      Accept: "*/*"
    },
    responseType: 'blob',
  });
}


/* 房间管理 */
//房间分页查询
export function getRoomList(params) {
  return request.get('/eccard-access-control/baseRoomInfo/page', { params });
}
//添加房间
export function roomAdd(params) {
  return request.post('/eccard-access-control/baseRoomInfo/addRoomInfo', params);
}
//修改房间
export function roomEdit(params) {
  return request.put('/eccard-access-control/baseRoomInfo/updateRoomInfo', params);
}
//删除房间
export function roomDelete(params) {
  return request.delete(`/eccard-access-control/baseRoomInfo/deleteRoomInfo/${params}`);
}

//批量添加房间
export function roomBatchAdd(params) {
  return request.post('/eccard-access-control/baseRoomInfo/addRoomInfoBatch', params);
}
//批量删除房间
export function roomBatchDelete(params) {
  return request.delete(`/eccard-access-control/baseRoomInfo/batchDeleteBuilding/${params}`);
}

//房间列表下载导入模板
export function roomDownImportTemplate(params) {
  return request.get('/eccard-access-control/baseRoomInfo/downImportTemplate', {
    params,
    headers: {
      'Content-Type': 'application/octet-stream',
      Accept: "*/*"
    },
    responseType: 'blob',
  });
}

//获取导入房间信息列表数据-分页
export function getImportRoomList(params) {
  return request.get('/eccard-access-control/baseRoomInfo/getImportRoomInfoListByPage', { params });
}
/* //删除待保存房间信息
export function deleteWaitBuilding(params) {
  return request.delete(`/eccard-dorm/Building/deleteWaitBuildingById/${params.projectNo}/${params.id}`);
} */
//保存导入房间数据
export function saveImportRoom(params) {
  return request.post('/eccard-access-control/baseRoomInfo/saveImport', params);
}

//导出房间列表
export function exportRoom(params) {
  return request.get('/eccard-access-control/baseRoomInfo/exportBaseRoom', {
    params,
    headers: {
      'Content-Type': 'application/octet-stream',
      Accept: "*/*"
    },
    responseType: 'blob',
  });
}


/* 设备管理 */
//房间信息选择列表
export function baseRoomInfoList(params) {
  return request.get('/eccard-access-control/baseRoomInfo/list', { params });
}
//状态设备数量统计
export function getOnlineCount(params) {
  return request.get('/eccard-access-control/accessInfo/acsDeviceConsole/getOnlineCount', { params });
}
//获取房间设备列表数据
export function acsDeviceList(params) {
  return request.get('/eccard-access-control/accessInfo/acsDeviceInfo/list', { params });
}
//分页获取待绑定设备列表
export function getUnBindAccessDeviceList(params) {
  return request.post('/eccard-access-control/accessInfo/acsDeviceInfo/getUnBindAccessDeviceList', params);
}
//添加房间设备信息
export function addRoomDevice(params) {
  return request.post('/eccard-access-control/accessInfo/acsDeviceInfo/addRoomDevice', params);
}
//删除房间设备信息
export function deleteRoomDevice(params) {
  return request.delete(`/eccard-access-control/accessInfo/acsDeviceInfo/deleteRoomDevice/${params}`);
}
//房间设备统计
export function acsDeviceInfoStatic(params) {
  return request.get('/eccard-access-control/accessInfo/acsDeviceInfo/static', { params });
}

/* 门禁控制台 */
//获取电子地图
export function acsDeviceConsoleList(params) {
  return request.get(`/eccard-access-control/accessInfo/acsDeviceConsole/page`, { params });
}
//删除权限
export function deleteAuth(params) {
  return request.post('/eccard-access-control/accessInfo/acsDeviceConsole/deleteAuth', params);
}
//设备校时
export function deviceCheckTime(params) {
  return request.post('/eccard-access-control/accessInfo/acsDeviceConsole/checkTime', params);
}
//远程开门
export function openDoor(params) {
  return request.post('/eccard-access-control/accessInfo/acsDeviceConsole/openDoor', params);
}
//上传权限
export function uploadAuth(params) {
  return request.post('/eccard-access-control/accessInfo/acsDeviceConsole/uploadAuth', params);
}


/* 电子地图 */
//获取电子地图
export function acsAreaMap(params) {
  return request.get(`/eccard-access-control/accessInfo/acsAreaMap/${params}/detail`);
}

//更新区域电子地图
export function updateAcsAreaMap(params) {
  return request.post('/eccard-access-control/accessInfo/acsAreaMap', params);
}

//获取区域设备列表
export function getAreaDeviceList(params) {
  return request.get('/eccard-access-control/accessInfo/acsAreaMap/getAreaDeviceList', { params });
}
//分页获取待绑定电子地图设备列表
export function getUnBindMapDeviceList(params) {
  return request.post('/eccard-access-control/accessInfo/acsAreaMap/getUnBindMapDeviceList', params);
}
//保存设备信息
export function saveAreaDevice(params) {
  return request.post('/eccard-access-control/accessInfo/acsAreaMap/saveAreaDevice', params);
}


/* 人员通行记录 */
//人员通行记录分页列表
export function acsDeviceRecordList(params) {
  return request.get(`/eccard-access-control/accessInfo/acsDeviceRecord/page`, { params });
}
//通行记录详情
export function acsDeviceRecordDetails(params) {
  return request.get(`/eccard-access-control/accessInfo/acsDeviceRecord/${params}/detail`);
}
//导出人员通行记录列表
export function exportRecord(params) {
  return request.get('/eccard-access-control/accessInfo/acsDeviceRecord/exportRecord', {
    params,
    headers: {
      'Content-Type': 'application/octet-stream',
      Accept: "*/*"
    },
    responseType: 'blob',
  });
}

/* 通行人次 */
//通行人次分页列表
export function getStaticPage(params) {
  return request.get(`/eccard-access-control/accessInfo/acsDeviceRecordStatic/getStaticPage`, { params });
}
//导出通行人次
export function exportStatic(params) {
  return request.get('/eccard-access-control/accessInfo/acsDeviceRecordStatic/exportStatic', {
    params,
    headers: {
      'Content-Type': 'application/octet-stream',
      Accept: "*/*"
    },
    responseType: 'blob',
  });
}

//获取车辆通行记录列表
export function getAcsVehicleRecord(params) {
  return request.get('/eccard-access-control/authInfo/acsVehicleRecord/page', { params })
}

/* 分组管理 */
//分组分页列表
export function acsGroupingInfoList(params) {
  return request.get('/eccard-access-control/authInfo/acsGroupingInfo', { params });
}
//添加分组
export function acsGroupingInfoAdd(params) {
  return request.post('/eccard-access-control/authInfo/acsGroupingInfo', params);
}
//修改分组
export function acsGroupingInfoEdit(params) {
  return request.put('/eccard-access-control/authInfo/acsGroupingInfo', params);
}
//删除分组
export function acsGroupingInfoDel(params) {
  return request.delete(`/eccard-access-control/authInfo/acsGroupingInfo/${params}`);
}

//导出车辆通行记录表
export function exportAcsVehicleRecord(params) {
  return request.get('/eccard-access-control/authInfo/acsVehicleRecord/exportRecord', {
    params,
    headers: {
      'Content-Type': 'application/octet-stream',
      Accept: "*/*"
    },
    responseType: 'blob',
  });
}


//设备分组分页列表
export function acsDeviceGroupList(params) {
  return request.get('/eccard-access-control/authInfo/acsDeviceGroup/page', { params });
}
//批量新增分组设备
export function acsDeviceGroupBatchAdd(params) {
  return request.post('/eccard-access-control/authInfo/acsDeviceGroup', params);
}
//批量删除分组设备
export function acsDeviceGroupBatchDel(params) {
  return request.delete(`/eccard-access-control/authInfo/acsDeviceGroup/deleteBatch/${params}`);
}
//删除分组设备
export function acsDeviceGroupDel(params) {
  return request.delete(`/eccard-access-control/authInfo/acsDeviceGroup/${params}`);
}
//分页查询待分组设备列表
export function getUnGroupDeviceListByPage(params) {
  return request.get('/eccard-access-control/authInfo/acsDeviceGroup/getUnGroupDeviceListByPage', { params });
}


//人员分组分页列表
export function acsUserGroupList(params) {
  return request.get('/eccard-access-control/authInfo/acsUserGroup/page', { params });
}
//批量新增分组人员
export function acsUserGroupBatchAdd(params) {
  return request.post('/eccard-access-control/authInfo/acsUserGroup', params);
}
//全选新增分组人员
export function addAllUserListAdd(params) {
  return request.post('/eccard-access-control/authInfo/acsUserGroup/addAllUserList', params);
}
//批量删除分组人员
export function acsUserGroupBatchDel(params) {
  return request.delete(`/eccard-access-control/authInfo/acsUserGroup/deleteBatch/${params}`);
}
//删除分组人员
export function acsUserGroupDel(params) {
  return request.delete(`/eccard-access-control/authInfo/acsUserGroup/${params}`);
}
//分页查询待分组人员列表
export function getUnGroupUserListByPage(params) {
  return request.get('/eccard-access-control/authInfo/acsUserGroup/getUnGroupUserListByPage', { params });
}


/** 
 * 权限管理
*/

/* 权限策略 */
//分页列表
export function acsPolicyAuthorizeList(params) {
  return request.get('/eccard-access-control/authInfo/acsPolicyAuthorize/list', { params });
}
//新增策略授权信息
export function acsPolicyAuthorizeAdd(params) {
  return request.post('/eccard-access-control/authInfo/acsPolicyAuthorize/batchInsert', params);
}
//删除权限策略
export function acsPolicyAuthorizeDel(params) {
  return request.delete(`/eccard-access-control/authInfo/acsPolicyAuthorize/${params}`);
}
//批量删除权限策略
export function acsPolicyAuthorizeDeleteBatch(params) {
  return request.delete(`/eccard-access-control/authInfo/acsPolicyAuthorize/deleteBatch/${params}`);
}

/* 授权管理 */
//设备权限列表
export function getAuthManageList(params) {
  return request.post('/eccard-access-control/authManage/device/page', params);
}
//人员查询列表
export function authManageUserList(params) {
  return request.post('/eccard-access-control/authManage/userPage', params);
}
//新增权限
export function authManageAdd(params) {
  return request.post('/eccard-access-control/authManage', params);
}
//删除人员授权信息
export function authManageDel(params) {
  return request.post('/eccard-access-control/authManage/deleteAuth', params);
}
//设备授权信息详情查询
export function authManageDeviceDetails(params) {
  return request.post('/eccard-access-control/authManage/doorAuthInfo/page', params);
}
//人员授权信息详情查询
export function authManageUserDetails(params) {
  return request.post('/eccard-access-control/authManage/userAuthInfo/page', params);
}

/* 权限明细 */
//获取权限明细列表
export function getAuthDetailList(params) {
  return request.post('/eccard-access-control/authDetail/page', params);
}

//导出权限明细列表
export function exportAuthDetail(params) {
  return request.post('/eccard-access-control/authDetail/export', params,
    {
      headers: {
        'Content-Type': 'application/octet-stream',
        Accept: "*/*"
      },
      responseType: 'blob',
    });
}

/* 权限申请 */
//权限申请列表分页查询
export function authApplyList(params) {
  return request.post('/eccard-access-control/authApply/page', params);
}
//权限申请新增
export function authApplyAdd(params) {
  return request.post('/eccard-access-control/authApply', params);
}
//权限申请编辑
export function authApplyEdit(params) {
  return request.put('/eccard-access-control/authApply', params);
}
//权限申请区域查询
export function authApplyAreaList(params) {
  return request.post('/eccard-access-control/authApply/areaList', params);
}
//删除定时任务信息
export function authApplyDel(params) {
  return request.delete(`/eccard-access-control/authApply/${params}`);
}


//获取权限操作日志列表
export function getOperateLogList(params) {
  return request.post('/eccard-access-control/operateLog/page', params);
}

//导出权限操作日志列表
export function exportOperateLog(params) {
  return request.post('/eccard-access-control/operateLog/export', params,
    {
      headers: {
        'Content-Type': 'application/octet-stream',
        Accept: "*/*"
      },
      responseType: 'blob',
    });
}

//获取定时任务列表
export function getTaskList(params) {
  return request.post('/eccard-access-control/task/page', params)
}

//新增定时任务信息
export function addTask(params) {
  return request.post('/eccard-access-control/task', params)
}

//修改定时任务信息
export function editTask(params) {
  return request.put('/eccard-access-control/task', params)
}

//删除定时任务信息
export function deleteTask(params) {
  return request.delete(`/eccard-access-control/task/${params}`);
}

//获取门列表
export function getDoorList(params) {
  return request.get('/eccard-access-control/task/doorList', { params })
}

//获取时段管理列表
export function getDevicePeriodList(params) {
  return request.post('/eccard-access-control/devicePeriod/page', params)
}

//获取时段管理列表(不分页)
export function getDevicePeriod(params) {
  return request.post('/eccard-access-control/devicePeriod/list', params)
}

//新增定时任务信息
export function addDevicePeriod(params) {
  return request.post('/eccard-access-control/devicePeriod', params)
}

//修改定时任务信息
export function editDevicePeriod(params) {
  return request.put('/eccard-access-control/devicePeriod', params)
}

//删除定时任务信息
export function deleteDevicePeriod(params) {
  return request.delete(`/eccard-access-control/devicePeriod/${params}`);
}

//获取权限审核列表
export function getAuthCheckList(params) {
  return request.post('/eccard-access-control/authCheck/page', params)
}

//权限审核
export function authCheck(params) {
  return request.put('/eccard-access-control/authCheck', params)
}