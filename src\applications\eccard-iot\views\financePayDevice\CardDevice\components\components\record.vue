<template>
  <div class="padding-box">
    <el-table border :data="state.replaceList" style="width: 100%">
      <el-table-column align="center" v-for="(item, index) in column" :key="index" :prop="item.prop"
        :label="item.label" />
      <el-table-column align="center" label="操作">
        <template #default="scope">
          <span class="green" style="margin-right: 10px" @click="edit(scope.row)">编辑</span>
          <span class="green" @click="del(scope.row)">删除</span>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<script>
import { useStore } from "vuex";

import { ElTable, ElTableColumn, ElMessageBox, ElMessage } from "element-plus";
import { getRepalceRecord, delRecords } from "@/applications/eccard-iot/api";
import { onMounted, reactive } from "@vue/runtime-core";

const column = [
  { label: "记录时间", prop: "replaceDate" },
  { label: "更换原因", prop: "replaceReason" },
  { label: "更换人员", prop: "operatorName" },
  { label: "备注", prop: "replaceRemarks" },
];
export default {
  components: {
    ElTable,
    ElTableColumn,
  },
  setup() {
    const store = useStore();
    const state = reactive({
      replaceList: [],
    });
    const getList = async () => {
      let { data:{list} } = await getRepalceRecord({
        deviceId: store.state.cardDeviceData.deviceDetail.id,
        pageNum:1,
        pageSize:100000,
      });
      state.replaceList = list
    };
    const edit = (row) => {
      store.commit("cardDeviceData/updateState", {
        key: "isreplace",
        payload: {
          isShow: true,
          data: {...row}
        }
      });
    };
    const del = (row) => {
      ElMessageBox.confirm("确认删除?", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        let { code, message } = await delRecords(row.id);
        if (code === 0) {
          ElMessage.success(message);
          getList();
        }
      });
    };
    onMounted(() => {
      getList();
    });
    return {
      column,
      state,
      edit,
      del,
    };
  },
};
</script>
<style lang="scss" scoped>
</style>