<template>
  <el-table style="width: 100%" :data="state.dataList" border stripe>
    <el-table-column label="门号" prop="doorNo" align="center"></el-table-column>
    <el-table-column label="门名称" prop="doorName" align="center">
      <template #default="scope">
        <div v-if="!state.flag">{{ scope.row.doorName }}</div>
        <el-input v-else v-model="scope.row.doorName" size="mini" maxlength="20" />
      </template>
    </el-table-column>
    <el-table-column label="控制方式" prop="doorControl" align="center">
      <template #default="scope">
        <div v-if="!state.flag">{{ doorControlList.find(item => item.value ===
            scope.row.doorControl)?.label || scope.row.doorControl
        }}</div>
        <el-select v-else v-model="scope.row.doorControl" size="mini">
          <el-option v-for="(item, index) in doorControlList" :key="index" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </template>
    </el-table-column>
    <el-table-column label="开门延迟(秒)" prop="doorDelay" align="center">
      <template #default="scope">
        <div v-if="!state.flag">{{ scope.row.doorDelay }}</div>
        <el-input-number v-else v-model="scope.row.doorDelay" :min="1" :max="10" size="mini"
          controls-position="right" />
      </template>
    </el-table-column>
    <el-table-column label="排序" prop="doorSort" align="center">
      <template #default="scope">
        <div v-if="!state.flag">{{ scope.row.doorSort }}</div>
        <el-input-number v-else v-model="scope.row.doorSort" :min="1" :max="10" size="mini" controls-position="right" />
      </template>
    </el-table-column>
  </el-table>
  <el-form label-width="150px" style="margin-top:20px">
    <el-form-item label="是否启用一次刷卡：">
      <el-select v-model="state.rowData.onceCreditcard" size="mini" v-if="state.flag == true">
        <el-option v-for="(item, index) in boolList" :key="index" :label="item.label" :value="item.value"></el-option>
      </el-select>
      <el-input v-else
        :model-value="dictionaryFilter(state.rowData.onceCreditcard && state.rowData.onceCreditcard + '')" size="mini"
        readonly></el-input>
    </el-form-item>
  </el-form>
  <div style="text-align:center">
    <el-button size="mini" v-if="state.flag" @click="canel()" style="margin-right:10px">取消</el-button>
    <el-button size="mini" v-if="state.flag" @click="save()" type="success">保存</el-button>
    <el-button size="mini" v-if="!state.flag" @click="handleEdit" type="primary">编辑</el-button>

  </div>
</template>
<script>
import { ElTable, ElTableColumn, ElForm, ElFormItem, ElInputNumber, ElSelect, ElOption, ElButton, ElInput, ElMessage } from "element-plus"
import { reactive, onMounted } from 'vue'
import { useDict } from "@/hooks/useDict.js";
import { accessControlDoorList, accessControlDeviceDetails, accessControlDoorEdit } from "@/applications/eccard-iot/api";
const doorControlList = [
  { label: "在线", value: 3 },
  { label: "常开", value: 1 },
  { label: "常闭", value: 2 },
]
export default {
  components: {
    ElTable,
    ElTableColumn,
    ElForm,
    ElFormItem,
    ElSelect,
    ElOption,
    ElButton,
    ElInput,
    ElInputNumber
  },
  props: {
    data: {
      type: Object,
      default: null,
    }
  },
  setup(props) {
    const boolList = useDict("SYS_BOOL_INT")
    const state = reactive({
      loading: false,
      flag: false,
      open: "",
      editDataList: [],
      dataList: [],
      dataListCopy: [],
      rowData: {},
      rowDataCopy: {},
    })
    const getList = async () => {
      let params = {
        accessControlDeviceId: props.data.id,
        pageNum: 1,
        pageSize: 1000
      }
      state.loading = true
      let { data: { list } } = await accessControlDoorList(params)
      state.dataList = JSON.parse(JSON.stringify(list))
      state.dataListCopy = JSON.parse(JSON.stringify(list))
      state.loading = false


    }
    const getDetails = async () => {
      let { code, data } = await accessControlDeviceDetails(props.data.id)
      if (code === 0) {
        state.rowData = JSON.parse(JSON.stringify(data))
        state.rowDataCopy = JSON.parse(JSON.stringify(data))
        state.rowData.onceCreditcard = state.rowData.onceCreditcard === null ? '' : state.rowData.onceCreditcard + ""
        state.rowDataCopy.onceCreditcard = state.rowDataCopy.onceCreditcard === null ? '' : state.rowDataCopy.onceCreditcard + ""
      }
    }
    const handleChange = (val, index) => {
      console.log(state.editDataList, state.dataList);
      state.editDataList[index].doorName = val
    }
    const handleEdit = () => {
      state.editDataList = JSON.parse(JSON.stringify(state.dataListCopy))
      state.flag = true;
    }
    const save = async () => {
      state.loading = true
      let params = {
        accessControlDoorVos: state.dataList,
        id: props.data.id,
        onceCreditcard: state.rowData.onceCreditcard
      }
      try {
        let { code, message } = await accessControlDoorEdit(params)
        if (code === 0) {
          ElMessage.success(message)
          await getList()
          await getDetails()
          state.flag = false
        }
        state.loading = false
      }
      catch {
        state.loading = false
      }
    }
    const canel = () => {
      console.log(state.dataList, state.dataListCopy);
      state.dataList = JSON.parse(JSON.stringify(state.dataListCopy))
      state.rowData = JSON.parse(JSON.stringify(state.rowDataCopy))
      state.flag = false

    }
    onMounted(() => {
      getList()
      getDetails()
      state.rowData = { ...props.data }
    })
    return {
      doorControlList,
      boolList,
      state,
      handleEdit,
      handleChange,
      save,
      canel
    }
  }
}
</script>
<style lang="scss" scoped>
</style>