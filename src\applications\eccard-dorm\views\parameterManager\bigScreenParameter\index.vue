<template>
        <kade-route-card>
            <kade-table-wrap title="大屏监控参数列表">
                <el-table :data="state.data" border v-loading="state.loading">
                  <el-table-column show-overflow-tooltip prop="paramName" label="参数名称" align="center"></el-table-column>
                  <el-table-column show-overflow-tooltip prop="paramValue" label="参数值" align="center"></el-table-column>
                  <el-table-column show-overflow-tooltip prop="lastModifyTime" label="修改时间" align="center"></el-table-column>
                  <el-table-column show-overflow-tooltip prop="lastModifyUserName" label="修改人" align="center"></el-table-column>
                  <el-table-column show-overflow-tooltip prop="remarks" label="备注" align="center" width="500px"></el-table-column>
                  <el-table-column  label="操作" align="center" width="80px">
                      <template #default="scope">
                          <el-button type="text" class="green" size="mini" @click="edit(scope.row)">编辑</el-button>
                      </template>
                  </el-table-column>
                </el-table>
            </kade-table-wrap>
        <kade-bigscreen-parameter-edit :dialogVisible="state.dialogVisible" :rowData="state.rowData" @close="close" />
        </kade-route-card>
</template>

<script>
import { ElTable,ElTableColumn,ElButton, } from "element-plus"
import { reactive } from '@vue/reactivity'
import edit from "./components/edit"
import { onMounted } from '@vue/runtime-core'
import { largeScreenParamList } from '@/applications/eccard-dorm/api.js'
export default {
    components:{
        ElTable,
        ElTableColumn,
        ElButton,
        "kade-bigscreen-parameter-edit":edit
    },
    setup(){
        const state=reactive({
            data:[],
            dialogVisible:false,
            rowData:{},
            loading:false
        });
        const getList=async()=>{
            state.loading=true
            try{
            let {data} = await largeScreenParamList()
            state.data=data
            state.loading=false
            }
            catch{
                state.loading=false
            }
        };
        const edit=(row)=>{
            state.dialogVisible=true
            state.rowData=row
        };
        const close=(val)=>{
            if(val){
                getList()
            }
            state.dialogVisible=false
        }
        onMounted(()=>{
            getList();
        });
        return{
            state,
            edit,
            close
        }
    }
}
</script>

<style lang="scss" scoped>
.kade-table-wrap{
    padding: 0;
    border-bottom: none;
}
.green :hover{
    text-decoration: underline;
}
:deep(.el-dialog__footer){
    border-top: none;
    text-align: center;
}
:deep(.el-dialog__header){
    border-bottom: 1px solid #eeeeee;
    margin-bottom: 20px;
}
</style>