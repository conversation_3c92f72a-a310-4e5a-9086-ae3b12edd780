<template>
  <div class="home padding-box">
    <div class="summary-box">
      <div class="summary-item" v-for="(item, index) in list" :key="index">
        <div class="left" :style="{ backgroundColor: item.background }">
          <img :src="item.icon" alt="">
        </div>
        <div class="right">
          <div class="value" :style="{ color: item.background }">{{ item.value }}</div>
          <div class="label">{{ item.label }}</div>
        </div>
      </div>
    </div>
    <el-row :gutter="20">
      <el-col :span="15" style="margin-bottom:20px">
        <kade-device-num />
      </el-col>
      <el-col :span="9" style="margin-bottom:20px">
        <kade-device-type />
      </el-col>
      <el-col :span="24">
        <kade-warning />
      </el-col>
    </el-row>
  </div>
</template>
<script>
import { reactive } from '@vue/reactivity'
import { ElCol, ElRow } from "element-plus"
import deviceIcon from "@/assets/iot_img/device.svg"
import onlineIcon from "@/assets/iot_img/online.svg"
import offlineIcon from "@/assets/iot_img/offline.svg"
import gatewayIcon from "@/assets/iot_img/gateway.svg"
import deviceNum from "@/applications/eccard-iot/views/home/<USER>"
import deviceType from "@/applications/eccard-iot/views/home/<USER>"
import warning from "@/applications/eccard-iot/views/home/<USER>"

export default {
  components: {
    ElCol, ElRow,
    "kade-device-num": deviceNum,
    "kade-device-type": deviceType,
    "kade-warning": warning,
  },
  setup() {
    const list = [
      { label: "设备总数", value: 6542, icon: deviceIcon, background: "#3399ff" },
      { label: "设备在线数量", value: 6542, icon: onlineIcon, background: "#3c6" },
      { label: "设备离线数量", value: 6542, icon: offlineIcon, background: "#ff8726 " },
      { label: "网关数量", value: 6542, icon: gatewayIcon, background: "#e64444" },
    ]
    const state = reactive({

    })
    return {
      state,
      list,
    }
  }
}
</script>
<style lang="scss" scoped>
.home {
  .summary-box {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;

    .summary-item {
      box-sizing: border-box;

      width: 24%;
      border-radius: 5px;
      box-shadow: 1px 3px 5px rgb(0 0 0 / 12%);
      overflow: hidden;
      display: flex;

      .left {
        width: 100px;
        // height: 100px;
        display: flex;
        justify-content: center;
        align-items: center;

        img {
          width: 40px;
          height: 40px;
        }
      }

      .right {
        flex: 1;
        margin-left: 20px;
        padding: 20px 0;

        .value {
          font: 36px arial;
          // height: 60px;
        }

        .label {
          font-size: 14px;
          color: #999999;
        }
      }
    }
  }
}

:deep(.el-card__header) {
  padding: 0;

  .header-title {
    display: inline-block;
    padding: 16px;
    border-bottom: 2px solid #3399ff;
    font-size: 16px;
    color: #1890FF;
  }
}
</style>