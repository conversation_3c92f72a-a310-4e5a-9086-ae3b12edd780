<template>
  <el-dialog :model-value="dialogTable" title="楼栋数据导入" width="1000px" :before-close="handleClose">
    <div style="padding-bottom:30px" v-loading="state.loading">
      <div class="top-button">
        <el-button type="text" size="small" @click="exportModel" style="text-decoration: underline">导入模板下载</el-button>
        <div style="display:flex">
          <el-upload style="margin:0 10px" ref="uploadRef" :headers="{ Authorization: `bearer ${state.uploadHeader}` }" class="upload-demo" :show-file-list="false" :action="state.uploadUrl" :before-upload="beforeUpload" :on-success="uploadSuccess" :on-error="uploadError">
            <el-button type="success" icon="el-icon-daoru" size="small">导入数据</el-button>
          </el-upload>
          <el-button @click="save" type="primary" size="small" :disabled="!state.ImportBuildingRes.projectNo">保存</el-button>
        </div>
      </div>
      <div class="table-box">
        <el-table :data="state.dataList" border>
          <!-- <el-table-column show-overflow-tooltip prop="" type="selection" align="center"></el-table-column> -->
          <el-table-column show-overflow-tooltip prop="buildNo" label="楼栋号" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip prop="buildName" label="楼栋名称" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip prop="buildTypeName" label="楼栋类型" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip prop="areaName" label="所属区域" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip prop="unitCount" label="总单元数" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip prop="floorCount" label="总楼层数" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip label="经纬度" align="center">
            <template #default="scope">
              {{`经度：${scope.row.lngPoint}；纬度：${scope.row.latPoint}`}}
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip prop="address" label="详细地址" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip width="200px" prop="resultMsg" label="校验结果" align="center">
            <template #default="scope">
              <div :style="{color:scope.row.error?'red':'green'}">
                {{scope.row.resultMsg}}
              </div>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center">
            <template #default="scope">
              <el-button type="text" size="mini" @click="handleDel(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination">
          <el-pagination :currentPage="state.form.currentPage" :page-size="state.form.pageSize" :page-sizes="[10, 20, 30, 40]" background layout="total, sizes, prev, pager, next, jumper" :total="state.total" @size-change="handleSizeChange" @current-change="handleCurrentChange">
          </el-pagination>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { ElDialog, ElButton, ElTable, ElTableColumn, ElPagination, ElUpload, ElMessage, ElMessageBox } from "element-plus"
import { reactive,watch } from 'vue'
import { getToken, downloadXlsx } from "@/utils";
import { downImportTemplate, getImportBuildingList, deleteWaitBuilding, saveImportBuilding } from "@/applications/eccard-dorm/api";

export default {
  props: {
    dialogTable: {
      type: Boolean,
      default: false
    }
  },
  components: {
    ElDialog,
    ElButton,
    ElTable,
    ElTableColumn,
    ElPagination,
    ElUpload,
  },
  setup(props, context) {
    const state = reactive({
      loading: false,
      uploadHeader: getToken(),
      uploadUrl: `${CONFIG.BASE_API_PATH}eccard-dorm/Building/importBuilding`,
      form: {
        currentPage: 1,
        pageSize: 10
      },
      dataList: [],
      total: 0,
      ImportBuildingRes: {}
    });
    watch(() => props.modelValue, val => {
      if (val) {
        state.dataList = []
        state.total = 0
        state.ImportBuildingRes = {}
      }
    })
    const getList = async () => {
      state.loading = true
      try {
        let { data: { pageInfo: { list, total }, ImportBuildingRes } } = await getImportBuildingList(state.form)
        state.dataList = list
        state.total = total
        state.ImportBuildingRes = ImportBuildingRes
        state.loading = false
      }
      catch {
        state.loading = false
      }
    }
    const beforeUpload = () => {
      state.loading = true
    }
    const uploadSuccess = ({ code, data, message }) => {
      if (code === 0) {
        state.form.projectNo = data.projectNo
        getList();
      } else {
        ElMessage.error(message);
      }
      state.loading = false
    };
    const uploadError = () => {
      state.loading = false
    }
    const handleDel = row => {
      ElMessageBox.confirm("确认删除?", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        let { code, message } = await deleteWaitBuilding({ id: row.id, projectNo: state.ImportBuildingRes.projectNo });
        if (code === 0) {
          ElMessage.success(message);
          getList();
        }
      });
    }
    const save = async () => {
      if (!state.dataList.length) {
        return ElMessage.error("导入列表为空，请重新导入！")
      }
      if (state.ImportBuildingRes.errorCount !== 0) {
        return ElMessage.error('当前楼栋数据有错误信息，请检查后重新上传！')
      }
      state.loading = true
      try {
        let { code, message } = await saveImportBuilding({ projectNo: state.ImportBuildingRes.projectNo })
        if (code === 0) {
          ElMessage.success(message)
          context.emit("close", true)
        }
        state.loading = false
      }
      catch {
        state.loading = false
      }

    }
    const exportModel = async () => {
      state.loading = true
      let res = await downImportTemplate()
      downloadXlsx(res, "楼栋列表.xlsx")
      state.loading = false
    }
    const handleSizeChange = (val) => {
      state.form.currentPage = 1
      state.form.pageSize = val
      getList()
    }
    const handleCurrentChange = (val) => {
      state.form.currentPage = val
      getList()
    }
    const handleClose = () => {
      context.emit("close", false)
    };
    return {
      state,
      beforeUpload,
      uploadSuccess,
      uploadError,
      handleDel,
      exportModel,
      save,
      handleSizeChange,
      handleCurrentChange,
      handleClose,
    }
  }
}
</script>

<style lang="scss" scoped>
.top-button {
  display: flex;
  justify-content: space-between;
  align-content: center;
  margin: 20px 0;
}
.table-box {
  // border: 1px solid #eeeeee;
  .pagination {
    padding: 10px;
    border: 1px solid #eeeeee;
    border-top: 0;
  }
}
</style>