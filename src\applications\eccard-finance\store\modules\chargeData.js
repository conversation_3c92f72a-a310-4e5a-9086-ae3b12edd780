import {

} from "@/applications/eccard-finance/api";
const state = {
  isEditData:{
    isShow:false,
    isEdit:false
  },
  isPerson:false,
  isDetails:false,
  ChargeTypeList:[],
  roleList:[],
  cardTypeList:[],
  selectRow:"",
  isReq:false,
};
const mutations = {
  updateState(state, { key, payload }) {
    state[key] = payload;
  },
};
const actions = {


};
export default {
  namespaced: true,
  state,
  mutations,
  actions
}