<template>
  <kade-tab-wrap :tabs="tabs" style="margin-top: 20px" v-model="state.tab">
    <template #jbcs>
      <kade-basic-params />
    </template>
    <template #sfcs>
      <kade-water-charge-params />
    </template>
    <template #fjfcs>
      <kade-attach-params />
    </template>
  </kade-tab-wrap>
</template>
<script>
import { reactive, ref } from "@vue/reactivity";
import { useStore } from "vuex";
import { watch } from "@vue/runtime-core";
import basicParams from "./basicParams"
import waterChargeParams from "./waterChargeParams"
import attachParams from "./attachParams"
const tabs = [
  { name: "jbcs", label: "基本参数" },
  { name: "sfcs", label: "水费参数" },
  { name: "fjfcs", label: "附加费参数" },
];
export default {
  components: {
    "kade-basic-params": basicParams,
    "kade-water-charge-params": waterChargeParams,
    "kade-attach-params": attachParams,
  },
  setup() {
    const store = useStore();
    const formDom = ref(null);
    const state = reactive({
      tab: "jbcs",
      form: {},
    });
    watch(() => store.state.deviceParameters[store.state.app.activeTab] && store.state.deviceParameters[store.state.app.activeTab].isSetParams, val => {
      if (val) {
        state.tab = "jbcs"
      }else{
        state.tab=""
      }
    })
    return {
      tabs,
      formDom,
      state,
    };
  },
};
</script>
<style lang="scss" scoped>
</style>