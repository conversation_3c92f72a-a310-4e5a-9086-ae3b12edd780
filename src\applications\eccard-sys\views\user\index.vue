<template>
  <kade-route-card>
    <kade-table-filter @search="search" @reset="handleReset">
      <el-form inline size="small" label-width="100px">
        <el-form-item label="用户账号:">
          <el-input style="width: 215px" v-model="state.form.userAccount" placeholder="请输入" clearable></el-input>
        </el-form-item>
        <el-form-item label="用户名称:">
          <el-input style="width: 215px" v-model="state.form.userName" placeholder="请输入" clearable></el-input>
        </el-form-item>
        <el-form-item label="用户类型:">
          <el-select placeholder="请选择" v-model="state.form.userSex" style="width: 100%" clearable>
            <el-option v-for="item in sexList" :label="item.label" :value="item.value" :key="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="电话:">
          <el-input style="width: 215px" v-model="state.form.userTel" placeholder="请输入" clearable></el-input>
        </el-form-item>
        <el-form-item label="用户状态:">
          <el-select placeholder="请选择" v-model="state.form.status" style="width: 100%" clearable>
            <el-option v-for="item in statusList" :label="item.label" :value="item.value" :key="item.value"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="用户">
      <template #extra>
        <el-button icon="el-icon-plus" size="small" type="primary" @click="addUser">添加人员</el-button>
      </template>
      <!-- <el-divider></el-divider> -->
      <el-table style="width: 100%" :data="state.dataList" border stripe>
        <el-table-column label="用户账号" prop="userAccount" align="center"></el-table-column>
        <el-table-column label="邮箱" prop="userEmail" align="center"></el-table-column>
        <el-table-column label="用户名称" align="center" prop="userName"></el-table-column>
        <el-table-column label="性别" align="center">
          <template #default="scope">
            {{ dictionaryFilter(scope.row.userSex) }}
          </template>
        </el-table-column>
        <el-table-column label="电话" align="center" prop="userTel"></el-table-column>
        <el-table-column label="状态" align="center">
          <template #default="scope">
            <el-switch :model-value="scope.row.status" active-value="ENABLE_TRUE" inactive-value="ENABLE_FALSE"
              @click="statusChange(scope.row)" />
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="120">
          <template #default="scope">
            <el-button type="text" style="margin-right: 10px" @click="editUser(scope.row)" size="mini">编辑</el-button>
            <el-dropdown trigger="click" @command="(c) => handleCommand(c, scope.row)">
              <el-button size="mini" type="text">
                更多<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="reset">重置密码</el-dropdown-item>
                  <el-dropdown-item command="operation">删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination background v-model:current-page="state.form.pageNum"
          layout="total, sizes, prev, pager, next, jumper" :page-sizes="[10, 20, 50, 100, 200]" :total="state.total"
          v-model:page-size="state.form.pageSize" @current-change="currentChange" @size-change="sizeChange">
        </el-pagination>
      </div>
    </kade-table-wrap>
    <kade-user-edit @change="getList()" :user="state.user" :title="state.user.id ? '编辑' : '新增'"
      v-model="showCreateModal" />
  </kade-route-card>
</template>
<script>
import { onMounted, reactive, ref } from "vue";
import {
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElSwitch,
  ElTable,
  ElTableColumn,
  ElPagination,
  ElDropdown,
  ElDropdownMenu,
  ElDropdownItem,
  ElMessageBox,
  ElMessage,
} from "element-plus";
import { useDict } from "@/hooks/useDict.js";
import {
  getSystemUserForPage,
  reSetPwd,
  delSysUser,
  editSysUser
} from "@/applications/eccard-sys/api";
import EditModal from "./components/edit";

const treeProps = {
  children: "children",
  label: "deptName",
};
export default {
  components: {
    ElButton,
    ElForm,
    ElFormItem,
    ElInput,
    ElSelect,
    ElOption,
    ElSwitch,
    ElTable,
    ElTableColumn,
    ElPagination,
    ElDropdown,
    ElDropdownMenu,
    ElDropdownItem,
    "kade-user-edit": EditModal,
  },
  setup() {
    const sexList = useDict("SYS_SEX")
    const statusList = useDict("SYS_ENABLE")
    const showCreateModal = ref(false);
    const state = reactive({
      user: {},
      form: {
        pageNum: 1,
        pageSize: 10,
      },
      total: 0,
      dataList: [],
    });

    const getList = async () => {
      let {
        data: { list, total },
      } = await getSystemUserForPage(state.form);
      state.dataList = list;
      state.total = total;
    };
    const search = () => {
      getList()
    }
    const handleReset = () => {
      state.form = {
        pageNum: 1,
        pageSize: 10,
      }
    }
    const addUser = () => {
      state.user = {};
      showCreateModal.value = true;
    };
    const editUser = (user) => {
      state.user = user;
      showCreateModal.value = true;
    };
    const statusChange = row => {
      console.log(row);
      ElMessageBox.confirm(`确认${row.status=='ENABLE_TRUE'?'停用':'启用'}该用户?`, "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        console.log(1);
        let { code, message } = await editSysUser({...row,status:row.status=='ENABLE_TRUE'?'ENABLE_FALSE':'ENABLE_TRUE',roleIds:row.roleIds?row.roleIds.split(','):[]});
        if (code === 0) {
          ElMessage.success(message);
          getList()
        }
      });
    }
    const resetPass = ({ id }) => {
      ElMessageBox.prompt(`请输入密码`, "重置密码", {
        inputPattern: /^[^\u4E00-\u9FA5]{6,18}$/,
        inputErrorMessage: "密码格式错误，请输入6~18位非中文字符",
      }).then(async ({ value }) => {
        try {
          const { message } = await reSetPwd({ id, newPwd: value });
          ElMessage.success(message);
        } catch (e) {
          throw new Error(e.message);
        }
      });
    };
    const del = ({ id }) => {
      ElMessageBox.confirm("确认删除?", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        console.log(1);
        let { code, message } = await delSysUser(id);
        if (code === 0) {
          ElMessage.success(message);
          getList()
        }
      });
    };

    const handleCommand = (c, row) => {
      switch (c) {
        case "operation":
          del(row);
          break;
        case "reset":
          resetPass(row);
          break;
      }
    };

    const currentChange = (val) => {
      state.form.startPage = val;
      getList();
    };
    const sizeChange = (val) => {
      state.form.rowNum = val;
      getList();
    };
    onMounted(() => {
      getList();
    });
    return {
      sexList,
      statusList,
      treeProps,
      showCreateModal,
      state,
      getList,
      addUser,
      editUser,
      search,
      handleReset,
      statusChange,
      handleCommand,
      currentChange,
      sizeChange,
    };
  },
};
</script>
<style lang="scss" scoped>
:deep(.el-tabs__header) {
  height: 80vh !important;
  border: 1px solid #eeeeee;
  border-right: none;
}

.scroll-box {
  height: 80vh;
}

.el-divider--horizontal {
  margin: 0;
}
</style>