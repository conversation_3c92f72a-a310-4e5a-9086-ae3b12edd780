<template>
  <div class="screen-box">
    <slot />
    <div class="border-r-b"></div>
    <div class="horn-h-l-t horn horn-h"></div>
    <div class="horn-v-l-t horn horn-v"></div>
    <div class="horn-h-r-t horn horn-h"></div>
    <div class="horn-v-r-t horn horn-v"></div>
    <div class="horn-h-l-b horn horn-h"></div>
    <div class="horn-v-l-b horn horn-v"></div>
    <div class="horn-h-r-b horn horn-h"></div>
    <div class="horn-v-r-b horn horn-v"></div>
  </div>
</template>
<style lang="scss" scoped>
.screen-box {
  // height: 100%;
  border: 1.5px solid #03c4db;
  position: relative;
  padding: 10px;
  box-sizing: border-box;

  .border-r-b {
    width: 100%;
    height: 100%;
    border-right: 1.5px solid #03c4db;
    border-bottom: 1.5px solid #03c4db;
    position: absolute;
    bottom: -6px;
    right: -6px;

  }

  .horn {
    position: absolute;
    background: #5986df;
  }

  .horn-v {
    height: 15px;
    width: 5px;

  }

  .horn-h {
    height: 5px;
    width: 20px;
  }

  .horn-h-l-t {
    top: -3px;
    left: -3px;
  }

  .horn-v-l-t {
    top: 2px;
    left: -3px;
  }

  .horn-h-r-t {
    top: -3px;
    right: -3px;
  }

  .horn-v-r-t {
    top: 2px;
    right: -3px;
  }

  .horn-h-l-b {
    bottom: -3px;
    left: -3px;
  }

  .horn-v-l-b {
    bottom: 2px;
    left: -3px;
  }

  .horn-h-r-b {
    bottom: -3px;
    right: -3px;
  }

  .horn-v-r-b {
    bottom: 2px;
    right: -3px;
  }
}
</style>