<template>
  <div class="login-details">
    <el-dialog :model-value="isShow" :title="rowData.id ? '编辑故障类型' : '新增故障类型'" width="600px" :before-close="handleClose">
      <el-form size="mini" label-width="120px" ref="formRef" :model="state.form" :rules="state.rules">
        <el-form-item label="设备类型：">
          <el-select v-model="state.form.deviceType" clearable style="width: 100%;" :disabled="disable()">
            <el-option v-for="(item, index) in state.deviceTypeList" :key="index" :label="item.cfgValue"
              :value="item.cfgKey"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="故障类型：" prop="failureName">
          <el-input v-model.trim="state.form.failureName" maxlength="20" clearable placeholder="请输入故障类型"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleClose" size="small">取消</el-button>
          <el-button type="primary" @click="handleSubmit" size="small" :loading="state.loading">确认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script>
import { reactive, ref, watch, nextTick } from "vue";
import { onMounted } from "@vue/runtime-core";
import {
  ElButton,
  ElDialog,
  ElForm,
  ElFormItem,
  ElInput,
  ElMessage,
  ElSelect,
  ElOption,
} from "element-plus";
import {
  deviceFaultUpdate,
  deviceFaultAdd,
  iotCfg
} from "@/applications/eccard-iot/api";
export default {
  components: {
    ElDialog,
    ElForm,
    ElFormItem,
    ElInput,
    ElButton,
    ElSelect,
    ElOption,
  },
  props: {
    isShow: {
      types: Boolean,
      default: false,
    },
    rowData: {
      type: Object,
      default: () => { },
    },
  },
  setup(props, context) {
    const formRef = ref(null);
    const state = reactive({
      rules: {
        failureName: [{ required: true,message: "请输入故障类型" }],
      },
      loading: false,
      form: {

      },
      deviceTypeList:[]
    });
    watch(
      () => props.isShow,
      (val) => {
        if (val) {
          state.form = {
            ...props.rowData,
          };
          nextTick(() => {
            formRef.value.clearValidate();
          });
        }
      }
    );
    const handleClose = () => {
      context.emit("close", false);
    };
    const handleSubmit = () => {
      formRef.value.validate(async (valid) => {
        if (valid) {
          let fn = props.rowData.id ? deviceFaultUpdate : deviceFaultAdd;
          state.loading = true;
          try {
            console.log(state.form);
            let { code, message } = await fn(state.form);
            if (code === 0) {
              ElMessage.success(message);
              context.emit("close", true);
            }
            state.loading = false;
          } catch {
            state.loading = false;
          }
        }
      });
    };
    //获取设备类型
    const queryDeviceType = async () => {
      let { data } = await iotCfg();
      state.deviceTypeList = data;
    };
    const disable = () => {
      return state.form.id?true:false
    }
    onMounted(() => {
      queryDeviceType()
    });
    return {
      formRef,
      state,
      handleClose,
      handleSubmit,
      queryDeviceType,
      disable
    };
  },
};
</script>
<style lang="scss" scoped>
.el-form {
  margin-top: 20px;
}
</style>