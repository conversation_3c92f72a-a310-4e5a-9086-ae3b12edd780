<template>
  <el-input
    :model-value="text"
    readonly
    suffix-icon="el-icon-arrow-down"
    :placeholder="placeholder"
    @click="handleToggle"
    class="origin-select-tree-input"
  >
    <template #suffix v-if="text">
      <i class="el-icon-circle-close" @click.stop="handleClear"></i>
    </template>  
  </el-input>
  <el-dialog
    :title="placeholder"
    v-model="state.visible"
    :width="600"
    custom-class="kade-dialog"
    append-to-body
  >
    <div style="padding: 20px 0">
      <div class="checkedbox" v-if="multipe">
        <el-tag
          v-for="item in state.selected"
          :key="item[valueKey]"
          closable
          size="small"
          @close="handleTagClose(item)"
        >
          {{item[labelKey]}}
        </el-tag>
      </div>
      <el-table
        ref="tableRef"
        style="width: 100%"
        :data="state.dataList"
        v-loading="state.loading" 
        border
        @row-click="handleSelect"
        @select="selectClick"
      >
        <el-table-column
          type="selection"
          v-if="multipe"
          width="55">
        </el-table-column>    
        <el-table-column v-for="item in columns" :key="item.prop" v-bind="item" />                               
      </el-table>
      <div class="pagination" v-if="state.total > 6">
        <el-pagination
          background
          v-model:current-page="state.query.beginPage"
          layout="prev, pager, next"
          :total="state.total"
          v-model:page-size="state.query.rowNum"
          @current-change="pageChange"
        >
        </el-pagination>              
      </div>       
    </div>   
    <template v-if="multipe" #footer>
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSure">确定</el-button>
    </template>
  </el-dialog>  
</template>
<script>
// 远程搜索下拉组件
import { 
  ElInput,
  ElDialog,
  ElButton,
  ElTable,
  ElTableColumn,
  ElPagination,
  ElTag,
} from 'element-plus';
import { reactive, watch, ref, nextTick } from 'vue';
export default {
  emits: ['update:modelValue', 'change'],
  components: {
    'el-input': ElInput,
    'el-dialog': ElDialog,
    'el-button': ElButton,
    'el-table': ElTable,
    'el-table-column': ElTableColumn,
    'el-pagination': ElPagination,
    'el-tag': ElTag,
  },
  props: {
    placeholder: {
      type: String,
      default: '请选择'
    },
    multipe: {
      type: Boolean,
      default: false,
    },
    text: {
      type: String,
      default: ''
    },
    defaultValue: {
      type: Array,
      default: () => []
    },
    action: {
      type: Function,
      default: () => {}
    },
    columns: {
      type: Array,
      default: () => ([])
    },
    labelKey: {
      type: String,
      default: 'name'
    },
    valueKey: {
      type: String,
      default: 'id'
    },
    pageKeys: {
      type: Object,
      default: () => ({
        currentPage: 'beginPage',
        pageSize: 'rowNum'
      })
    }
  },
  setup(props, context) {
    const tableRef = ref(null);
    const state = reactive({
      visible: false,
      selected: [],
      memorySelect:[],
      loading: false,
      dataList: [],
      total: 0,
      query: {
        beginPage: 1,
        rowNum: 6,
      },
    });
    const handleCancel = () => {
      state.visible = false;
    }
    const handleSure = () => {
      state.visible = false;
      context.emit('change', [ ...state.selected ]);
    }
    const loadData = async () => {
      try {
        state.loading = true;
        const { data: { dataList, totalCount } } = await props.action({
          ...state.query,
          [props.pageKeys['currentPage']]: state.query.beginPage,
          [props.pageKeys['pageSize']]: state.query.rowNum,
        });
        state.dataList = dataList;
        if(props.multipe && props.defaultValue.length) {
          nextTick(() => {
            const values = props.defaultValue.map(n => n[props.valueKey]);
            dataList.forEach(it => {
              if(values.includes(it[props.valueKey])) {
                console.log(1)
                tableRef.value.toggleRowSelection(it, true);
              }
            });
          });
        }
        state.total = totalCount;
      } catch(e) {
        throw new Error(e.message);
      } finally {
        state.loading = false;
      }
    }
    const handleToggle = () => {
      state.visible = true;
      loadData();
      // if(!state.dataList.length) {
      //   loadData();
      // }
    }
    const pageChange = (page) => {
      state.query.beginPage = page;
      console.log(state.selected);
      loadData();
    }
    const handleSelect = (row) => {
      if(!props.multipe) {
        state.visible = false;
        context.emit('change', row);        
      }
    }
/*     const handleSelectionChange = (v) => {
      console.log(v);
      state.selected = v;
    } */

    const selectClick=(row)=>{
      console.log(row);
      state.selected=row
    }


    const handleTagClose = (item) => {
      const index = state.selected.findIndex(it => it[props.valueKey] === item[props.valueKey]);
      state.selected.splice(index, 1);
    }

    const handleClear = () => {
      if(!props.multipe) {
        context.emit('change', null);        
      } else {
        context.emit('change', []);
      }
    }    

    watch(() => props.defaultValue, () => {
      if(props.multipe && props.defaultValue.length) {
        state.selected = [ ...props.defaultValue ];
      }
    });

    return {
      handleCancel,
      handleSure,
      handleClear,
      state,
      handleToggle,
      pageChange,
      handleSelect,
      selectClick,
      handleTagClose,
      // handleSelectionChange,
      tableRef,
    }
  }
}
</script>
<style lang="scss" scoped>
.checkedbox{
  .el-tag{
    margin-right: 10px;
    margin-bottom: 10px;
  }
}
.origin-select-tree-input{
  .el-icon-circle-close{
    display: none;
  }
  &:hover{
    .el-icon-circle-close{
      display: inline-block;
    }
  }
}
</style>