<template>
  <el-dialog :model-value="modelValue" :title="title" width="1700px" :before-close="handleClose"
    :close-on-click-modal="false">
    <div style="padding:20px 0 10px">
      <el-row :gutter="20">
        <el-col :span="12">
          <kade-table-wrap title="申请信息" icon="none">
            <el-divider></el-divider>
            <div class="padding-box">
              <el-form label-width="100px" size="mini">
                <el-form-item label="申请人员：">
                  <el-input readonly :model-value="state.form.userName"></el-input>
                </el-form-item>
                <el-form-item label="开始时间：">
                  <el-input readonly :model-value="state.form.beginTime"></el-input>
                </el-form-item>
                <el-form-item label="结束时间：">
                  <el-input readonly :model-value="state.form.endTime"></el-input>
                </el-form-item>
                <el-form-item label="申请原因：">
                  <el-input type="textarea" readonly :model-value="state.form.applyReason"></el-input>
                </el-form-item>
              </el-form>
            </div>
          </kade-table-wrap>
          <kade-table-wrap title="审核信息" icon="none" style="margin-bottom:0">
            <el-divider></el-divider>
            <div class="padding-box">
              <el-form label-width="100px" size="mini" :model="state.form" :rules="rules" ref="formRef">
                <el-form-item label="审核状态：" prop="auditStatus">
                  <el-radio-group :model-value="state.form.auditStatus" v-if="dialogType == 'details'">
                    <el-radio label="AUDIT_PASSED">通过</el-radio>
                    <el-radio label="AUDIT_FAIL">不通过</el-radio>
                  </el-radio-group>
                  <el-radio-group v-else v-model="state.form.auditStatus">
                    <el-radio label="AUDIT_PASSED">通过</el-radio>
                    <el-radio label="AUDIT_FAIL">不通过</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="审核备注：" prop="auditRemarks">
                  <el-input type="textarea" v-if="dialogType == 'details'" readonly
                    :model-value="state.form.auditRemarks">
                  </el-input>
                  <el-input type="textarea" v-else v-model="state.form.auditRemarks"></el-input>
                </el-form-item>
              </el-form>
              <div style="text-align:center" v-if="dialogType == 'edit'">
                <el-button size="mini" @click="handleSave" type="primary">保存</el-button>
                <el-button size="mini" @click="handleClose">取消</el-button>
              </div>
            </div>
          </kade-table-wrap>
        </el-col>
        <el-col :span="12">
          <kade-table-wrap title="申请门禁" icon="none">
            <el-divider></el-divider>
            <div style="padding:10px 10px 0">
              <el-table height="422px" :data="state.dataList" border>
                <el-table-column show-overflow-tooltip label="区域" align="center">
                  <template #default="{ row }">
                    {{ `${row.areaName ? row.areaName + '>' : ''}${row.buildName ? row.buildName +
                        '>' : ''}${row.unitNum ? row.unitNum + '单元>' : ''}${row.floorNum ? row.floorNum + '楼>'
                          : ''}${row.roomName ? '>' + row.roomName : ''}`
                    }}
                  </template>
                </el-table-column>
              </el-table>
              <div class="pagination">
                <el-pagination background :total="state.total" layout="prev, pager, next"
                  @current-change="handleCurrentChange" />
              </div>
            </div>
          </kade-table-wrap>
        </el-col>
      </el-row>
    </div>
  </el-dialog>
</template>
<script>
import { ElDialog, ElRow, ElCol, ElDivider, ElButton, ElForm, ElFormItem, ElInput, ElRadioGroup, ElRadio, ElTable, ElTableColumn, ElPagination, ElMessage } from "element-plus"
import { reactive, watch, computed, ref, nextTick } from 'vue'
import { authCheck } from '@/applications/eccard-uac/api.js'
const rules = {
  auditStatus: [{ required: true, message: '请选择审核状态', trigger: 'change' },],
  auditRemarks: [{ required: false, max: 200, message: '备注信息不能超过200' }]
}
export default {
  components: {
    ElDialog, ElRow, ElCol, ElDivider, ElButton, ElForm, ElFormItem, ElInput, ElRadioGroup, ElRadio, ElTable, ElTableColumn, ElPagination
  },
  emits: ["update:modelValue"],
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    rowData: {
      type: Object,
      default: null
    },
    dialogType: {
      type: String,
      default: ''
    }
  },
  setup(props, context) {
    const formRef = ref(null)
    const state = reactive({
      form: {},
      dataList: [],
      total: 0
    })
    const handleClose = () => {
      context.emit("update:modelValue", false)
    }
    watch(() => props.modelValue, val => {
      if (val) {
        nextTick(() => {
          formRef.value.clearValidate()
        })
        let { id, userName, beginTime, endTime, applyReason, auditStatus, auditRemarks, acsApplyAuthorizeDeviceList } = { ...props.rowData }
        state.form = { id, userName, beginTime, endTime, applyReason, auditStatus, auditRemarks }
        state.dataList = acsApplyAuthorizeDeviceList
        console.log(state.form)
      }
    })
    const title = computed(() => {
      if (props.dialogType == 'edit') {
        return '权限申请审核'
      } else {
        return '权限审核详情'
      }
    })
    const handleSave = () => {
      formRef.value.validate((valid) => {
        if (valid) {
          let param = { ...state.form }
          console.log(param)
          authCheck(param).then((res) => {
            if (res.code === 0) {
              ElMessage.success(res.message)
              context.emit('update:modelValue', true)
            }
          })
        }
      })

    }
    return {
      state,
      title,
      rules,
      formRef,
      handleSave,
      handleClose
    }
  }
}
</script>
<style lang="scss" scoped>
:deep(.kade-table-wrap) {
  margin-bottom: 20px;
  box-sizing: border-box;
}

.padding-box {
  padding: 20px 20px 0
}

.el-divider--horizontal {
  margin: 0
}

.el-table {
  border-left: 1px solid #EBEEF5;
  border-right: 1px solid #EBEEF5;
}
</style>