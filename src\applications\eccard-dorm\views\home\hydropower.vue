<template>
  <kade-route-card>
    <template #header>
      <span class="header-title">近半月水电用量曲线</span>
    </template>
    <div id="hydropowerCharts" class="chartline"></div>
  </kade-route-card>
</template>
<script>
import * as echarts from "echarts";
import { onMounted } from "vue";
import { dayStr } from "@/utils/date.js"
export default {
  setup() {
    const echartInit = () => {
      var chartDom = document.getElementById('hydropowerCharts');
      var myChart = echarts.init(chartDom);
      var option;
      const arr = []
      for (let i = 15; i > 0; i--) {
        arr.push(dayStr(new Date().getTime() - 86400000 * i))
      }
      let fn = () => {
        let arr = []
        for (let i = 15; i > 0; i--) {
          arr.push((Math.random() * 1000).toFixed(0))
        }
        return arr
      }
      option = {
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: ['用水量', '用电量',],
          left: 0
        },
        grid: {
          left: 40,
          right: 50,
          bottom: 0,
          // top:0,
          containLabel: true
        },
        toolbox: {
          feature: {
            saveAsImage: {}
          }
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: arr
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '用水量',
            type: 'line',
            stack: 'Total',
            data: fn(),
            label: {
              show: true,
              distance: 0,
              formatter: '{c}m³'
            },
          },
          {
            name: '用电量',
            type: 'line',
            stack: 'Total',
            data: fn(),
            label: {
              show: true,
              distance: 0,
              formatter: '{c}kW·h'
            },
          },
        ]
      };

      option && myChart.setOption(option);
    };
    onMounted(() => {
      echartInit();
    });
    return {
      echartInit,
    };
  }
}
</script>
<style scoped lang="scss">
.chartline {
  height: 300px;
  width: 100%;
}
</style>