import request from '@/service';


//工作站列表分页
export function getWorkStationListByPage(params) {
  return request.get('/eccard-card/sysWorkStation/getWorkStationListByPage', {params});
}


//激活工作站
export function workStationActive(params) {
  return request.put('/eccard-card/sysWorkStation/active', params);
}

//修改工作站信息
export function workStationUpdate(params) {
  return request.post('/eccard-card/sysWorkStation/updateWorkStation', params);
}



//卡操作日志-卡操作日志列表
export function getCardLogByPage(params) {
  return request.get('/eccard-card/base/user/physical/card/log/page', {params});
}

//卡操作日志-卡操作日志导出
export function cardLogExport(params) {
  return request.get('/eccard-card/base/user/physical/card/log/export', {
    params, headers: {
      'Content-Type': 'application/octet-stream',
      Accept: "*/*"
    },
    responseType: 'blob',

  });
}

//虚拟卡-虚拟卡列表(分页)
export function getPersonVirtualCardListByPage(params) {
  return request.get('/eccard-card/base/user/virtual/card/getPersonVirtualCardListByPage', {params});
}
//虚拟卡-启用(禁用)
export function updatePersonVirtualCardStatus(status,ids) {
  return request.put('/eccard-card/base/user/virtual/card/'+status+'/'+ids);
}





