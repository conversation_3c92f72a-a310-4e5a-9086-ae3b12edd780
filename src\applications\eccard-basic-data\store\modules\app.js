import { markRaw, defineAsyncComponent } from 'vue';
import { getDictionary } from '@/applications/unified_portal/api';
import {
	actions as mixinActions,
	state as mixinState,
	getters as mixinGettgers,
} from '@/utils/tab.mixin';
const dictionary = localStorage.getItem(`kade_cache_dictionary`);
const state = {
	...mixinState,
	dictionary: dictionary ? JSON.parse(dictionary) : [],
	applyTypes: [],
	componentMap: {
		PersonnelEdit: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "personal" */ '../../views/personnelInformation/edit'))),
		// personnel: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "personal" */ '../../views/personnelInformation'))),
		personnel: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "personal" */ '../../views/personInfo'))),
		baseRole: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "role" */ '../../views/role'))),
		station: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "role" */ '../../views/station'))),
		organizationa: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "organization" */ '../../views/organizational'))),
		area: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "areas" */ '../../views/areas'))),
		areaLabel: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "areas" */ '../../views/areas/areaLabel'))),
		cardTypes: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "types" */ '../../views/types'))),

		Servers: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "types" */ '../../views/ServerManagement/Servers'))),
		newsNotice: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "types" */ '../../views/newsManagement/newsNotice'))),

		todo: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "types" */ '../../views/todoManagement/todo'))),
		setTodo: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "types" */ '../../views/todoManagement/setTodo'))),

		notifyList: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "types" */ '../../views/sysNoticeManagement/notifyList'))),
		notifyPublish: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "types" */ '../../views/sysNoticeManagement/notifyPublish'))),


	},
	customMenus: [],
};
const mutations = {
	updateState(state, { key, payload }) {
		state[key] = payload;
	},
};
const actions = {
	...mixinActions,
	async loadDictionary({ commit }) {
		const { data } = await getDictionary();
		localStorage.setItem(`kade_cache_dictionary`, JSON.stringify(data));
		commit('updateState', { key: 'dictionary', payload: data });
	},
};

const getters = {
	...mixinGettgers,
};

export default {
	namespaced: true,
	state,
	mutations,
	actions,
	getters,
}
