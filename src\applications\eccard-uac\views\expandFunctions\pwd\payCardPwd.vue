<template>
  <kade-table-filter @search="handleSearch" @reset="handleReset">
    <el-form inline label-width="100px" size="mini">
      <el-form-item label="区域">
        <el-input clearable v-model="state.form.name" placeholder="请输入"></el-input>
      </el-form-item>
      <el-form-item label="读卡器机号">
        <el-input clearable v-model="state.form.ip" placeholder="请输入"></el-input>
      </el-form-item>
      <el-form-item label="所属控制器">
        <el-input clearable v-model="state.form.ip" placeholder="请输入"></el-input>
      </el-form-item>
      <el-form-item label="控制器SN号">
        <el-input clearable v-model="state.form.ip" placeholder="请输入"></el-input>
      </el-form-item>
    </el-form>
  </kade-table-filter>
  <kade-table-wrap title="刷卡+密码列表">
    <template #extra>
      <el-button class="btn-purple" size="mini" icon="el-icon-edit" @click="handleSetAuth">批量设置</el-button>
    </template>
    <el-table border height="46vh" v-loading="state.loading" :data="state.dataList">
      <el-table-column show-overflow-tooltip prop="name" label="所属区域" align="center"></el-table-column>
      <el-table-column show-overflow-tooltip prop="workstationType" label="读卡器编号" align="center"></el-table-column>
      <el-table-column show-overflow-tooltip prop="ip" label="所属控制器编号" align="center"></el-table-column>
      <el-table-column show-overflow-tooltip prop="ip" label="所属控制器SN号" align="center"></el-table-column>
      <el-table-column show-overflow-tooltip prop="ip" label="读卡器位置" align="center"></el-table-column>
      <el-table-column show-overflow-tooltip prop="ip" label="读卡器名称" align="center"></el-table-column>
      <el-table-column show-overflow-tooltip prop="ip" label="刷卡+密码" align="center"></el-table-column>
    </el-table>
    <div class="pagination">
      <el-pagination v-model:currentPage="state.form.pageNum" v-model:page-size="state.form.pageSize"
        :page-sizes="[10, 20, 30, 40]" background layout="total, sizes, prev, pager, next, jumper" :total="state.total"
        @size-change="handleSizeChange" @current-change="handleCurrentChange">
      </el-pagination>
    </div>
  </kade-table-wrap>
  <kade-batch-set-dialog v-model="state.isShow" @update:modelValue="state.isShow = false" />
</template>
<script>
import { reactive } from "vue"
import { ElForm, ElFormItem, ElInput, ElTable, ElTableColumn, ElPagination, ElButton } from "element-plus"
import batchSet from "./components/batchSet.vue"
export default {
  components: {
    ElForm, ElFormItem,  ElInput, ElTable, ElTableColumn, ElPagination, ElButton,
    "kade-batch-set-dialog": batchSet
  },
  setup() {
    const state = reactive({
      loading: false,
      isShow: false,
      form: {
        pageNum: 1,
        pageSize: 10
      },
      dataList: [],
      total: 0,

    })
    const handleSetAuth = () => {
      state.isShow = true
    }
    const handleCurrentChange = val => {
      state.form.pageNum = val
    }
    const handleSizeChange = val => {
      state.form.pageNum = 1
      state.form.pageSize = val
    }
    return {
      state,
      handleSetAuth,
      handleCurrentChange,
      handleSizeChange
    }
  }
}
</script>