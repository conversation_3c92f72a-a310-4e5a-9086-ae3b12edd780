<template>
  <el-dialog :model-value="modelValue" :title="title + '参数'" width="500px" :before-close="handleClose"
    :close-on-click-modal="false">
    <el-form label-width="120px" size="small" :rules="rules" :model="state.form" ref="formRef">
      <el-form-item label="参数名称：" prop="paramsName">
        <el-input placeholder="请输入" v-model="state.form.paramsName"></el-input>
      </el-form-item>
      <el-form-item label="参数编码：" prop="paramsCode">
        <el-input placeholder="请输入" v-model="state.form.paramsCode"></el-input>
      </el-form-item>
      <el-form-item label="参数值类型：">
        <el-select v-model="state.form.paramsType">
          <el-option v-for="(item, index) in paramsTypeList" :key="index" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="参数值格式：">
        <el-input placeholder="请输入" v-model="state.form.paramsFormat"></el-input>
      </el-form-item>
      <el-form-item label="可选值：">
        <el-input placeholder="请输入" type="textarea" v-model="state.form.paramsValue"></el-input>
      </el-form-item>
      <div style="color:#909399">备注：可选值用顿号隔开，否则不生效</div>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose" size="mini">取消</el-button>
        <el-button type="primary" @click="submit" size="mini">保存</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import {
  ElDialog,
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
} from "element-plus";
import { reactive, computed, watch, ref, nextTick } from "vue";

const rules = {
  paramsName: [
    { required: true, message: "请输入参数名称", },
    { max: 20, message: "参数名称长度不能超过20字符", },
  ],
  paramsCode: [
    { required: true, message: "请输入参数编码", },
    { pattern: /^[0-9a-zA-Z]+$/, message: "请输入字母+数字", },
    { max: 20, message: "参数编码长度不能超过20字符", },
  ],
  paramsType: [
    { required: true, message: "请选择参数值类型", trigger: "change", },
  ],
}
const paramsTypeList = [
  { value: 'INPUT', label: '文本框' },
  { value: 'SELECT', label: '下拉选择框' },
]
export default {
  props: {
    modelValue: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Object,
      default: null
    },
    type: {
      type: String,
      default: ""
    },
    deviceTypelist: {
      type: Array,
      default: () => []
    },
  },
  components: {
    ElDialog,
    ElButton,
    ElForm,
    ElFormItem,
    ElInput,
    ElSelect,
    ElOption,
  },
  setup(props, context) {
    const formRef = ref(null)
    const state = reactive({
      form: {},
    });

    watch(() => props.modelValue, val => {
      if (val) {
        nextTick(() => {
          formRef.value.clearValidate()
        })
        if (props.type == 'children') {
          state.form = {}
        } else {
          let { paramsName, paramsCode, paramsType, paramsFormat, paramsValue, id, parentId } = { ...props.data }
          state.form = { paramsName, paramsCode, paramsType, paramsFormat, paramsValue, id, parentId }
        }
      }
    })
    const title = computed(() => {
      if (props.type == 'add') {
        return '新增'
      } else if (props.type == 'edit') {
        return '编辑'
      } else if (props.type == 'children') {
        return '新增子类'
      } else {
        return ''
      }
    })
    const submit = () => {
      formRef.value.validate(async (valid) => {
        if (valid) {
          if (props.type == 'edit') {
            context.emit("update:modelValue", { type: 'edit', data: { ...state.form } });
          } else if (props.type == 'add') {
            context.emit("update:modelValue", { type: 'add', data: { ...state.form } });
          } else if (props.type == 'children') {
            context.emit("update:modelValue", { type: 'children', data: { ...state.form } });
          }
        } else {
          return false
        }
      })
    }
    const handleClose = () => {
      context.emit("update:modelValue", false);
    };
    return {
      rules,
      paramsTypeList,
      formRef,
      state,
      title,
      submit,
      handleClose,
    };
  },
};
</script>

<style lang="scss" scoped>
.el-form {
  margin-top: 10px;
}

.table-box {
  border: 1px solid #eeeeee;
  border-radius: 0 0 8px 8px;

  .pagination {
    margin: 10px;
  }
}

.footer-box {
  margin-top: 10px;
  border: 1px solid #eeeeee;
  border-radius: 4px;

  .text-box {
    margin: 15px;
  }

  .el-form {
    margin-top: 20px;
  }
}

:deep(.el-select) {
  width: 100%;
}
</style>