<template>
  <kade-route-card style="height: auto">
    <kade-table-wrap title="补助类型">
      <template #extra>
        <el-button icon="el-icon-daoru" size="small" type="success" @click="edit('add')">新增</el-button>
        
      </template>
      <el-divider style="margin:0"></el-divider>
      <el-form inline size="small" label-width="100px" style="padding-top:20px;padding-left:20px">
        <el-form-item label="补助类型名称:">
          <el-input placeholder="关键字搜索" v-model="state.form.stName" :maxlength="20"></el-input>
        </el-form-item>
        <el-button @click="search()" size="small" type="primary" icon="el-icon-search">查询</el-button>
        <el-button icon="el-icon-refresh-right" @click="reset()" size="small">重置</el-button>
      </el-form>
      <el-table style="width: 100%" :data="state.dataList" v-loading="false" highlight-current-row border stripe>
        <el-table-column width="150" label="补助类型" prop="stName" align="center"></el-table-column>
        <el-table-column width="153" prop="stSort" label="排序" align="center">
        </el-table-column>
        <el-table-column label="是否启用" property="stStatus" align="center">
          <template #default="scope">
            <el-switch v-model="scope.row.stStatus" active-value="ENABLE_TRUE" inactive-value="ENABLE_FALSE" @change="handleChange(scope.row)"></el-switch>
          </template>
        </el-table-column>
        <el-table-column label="创建人" prop="createUserName" align="center"></el-table-column>
        <el-table-column label="创建时间"
                         prop="createTime"
                         align="center"
        ></el-table-column>
        <el-table-column label="操作" align="center">
          <template #default="scope">
            <el-button type="text" property="stId" style="color:rgb(26, 188, 156)" @click="details(scope.row.stId)"
                       size="mini">
              查看详情
            </el-button>
            <el-button size="mini" type="text" style="color:rgb(26, 188, 156)" @click="edit('edit',scope.row)">编辑</el-button>
            <el-button  size="mini" type="text" style="color:rgb(26, 188, 156)" @click="handleDel(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination
          background
          :current-page="state.form.currentPage"
          :page-size="state.form.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :page-sizes="[5, 10, 20, 50, 100]"
          :total="state.dataListTotal"
          @current-change="handlePageChange"
          @size-change="handleSizeChange"
        >
        </el-pagination>
      </div>
    </kade-table-wrap>
    <el-dialog v-model="state.dialogVisible" :title="state.title" width="30%" :before-close="()=>state.dialogVisible = false"  :close-on-click-modal="false">
      <el-form>
        <el-form-item label="补助类型名称:">
          <el-input placeholder="输入分类名称" v-model="state.editForm.stName" :maxlength="20"></el-input>
        </el-form-item>
        <el-form-item label="是否启用:">
          <el-switch v-model="state.editForm.stStatus" active-value="ENABLE_TRUE"
                     inactive-value="ENABLE_FALSE"></el-switch>
        </el-form-item>
        <el-form-item label="排序:">
          <el-input-number v-model="state.editForm.stSort" :min="0" :max="100" size="mini"/>
        </el-form-item>
      </el-form>
      <template #footer>
      <span class="dialog-footer">
        <el-button size="mini" @click="state.dialogVisible = false">取消</el-button>
        <el-button size="mini" type="primary" @click="AddOrUpdate(state.title)"
        >提交</el-button
        >
      </span>
      </template>
    </el-dialog>
    <el-dialog v-model="state.dialogVisibleInfo" :title="state.title" width="30%" :before-close="()=>state.dialogVisibleInfo=false" :close-on-click-modal="false">
      <el-form>
        <el-form-item label="补助类型名称:">
          <el-input v-model="state.editForm.stName"  disabled></el-input>
        </el-form-item>
        <el-form-item label="是否启用:">
          <el-switch v-model="state.editForm.stStatus" active-value="ENABLE_TRUE"
                     inactive-value="ENABLE_FALSE" disabled></el-switch>
        </el-form-item>
        <el-form-item label="排序:">
          <el-input-number v-model="state.editForm.stSort" :min="0" :max="100" size="mini" disabled/>
        </el-form-item>
      </el-form>
      <template #footer>
      <span class="dialog-footer">
        <el-button size="mini" @click="edit('edit')">编辑</el-button>
        <el-button size="mini" type="primary" @click="state.dialogVisibleInfo=false">关闭</el-button
        >
      </span>
      </template>
    </el-dialog>
  </kade-route-card>
  
</template>

<script>
import {onMounted, reactive} from "vue";
import {
  ElDivider,
  ElInputNumber,
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElTable,
  ElSwitch,
  ElTableColumn,
  ElPagination, ElMessageBox, ElMessage, ElDialog,
} from "element-plus";
import {
  getSubsidyTypeListByPage,
  deleteSubsidyTypeById,
  addSubsidyType,
  updateSubsidyTypeById,
  getSubsidyTypeById,
} from "@/applications/eccard-finance/api";

export default {
  components: {
    ElDivider,
    "el-dialog": ElDialog,
    "el-button": ElButton,
    "el-input": ElInput,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    "el-table": ElTable,
    "el-table-column": ElTableColumn,
    "el-pagination": ElPagination,
    "el-switch": ElSwitch,
    "el-input-number": ElInputNumber,
  },
  setup() {
    const state = reactive({
      dialogVisible: false,
      dialogVisibleInfo: false,
      title: "",
      typeInfo: {},
      isShow: false,
      dataList: [],
      dataListTotal: 0,
      stSort: 0,
      stStatus: "",
      stName: "",
      selectRow: "",
      form: {
        currentPage: 1,
        pageSize: 10,
        stName: "",
        stId: 0,
      },
      editForm: {
        stId: "",
        stName: "",
        stStatus: "ENABLE_FALSE",
        stSort: 0,
      },
      deleteForm: {
        stId: "",
      }
    });
    const getList = () => {
      getSubsidyTypeListByPage(state.form).then((res) => {
        state.dataList = res.data.list;
        state.dataListTotal = res.data.total;
      });
    };
    const details = (val) => {
      console.log(val)
      state.deleteForm.stId = val
      getSubsidyTypeById(state.deleteForm).then((res) => {
        state.editForm = res.data
      });
      state.title = "补助类型详情"
      state.dialogVisibleInfo = true
    }
    const edit = (val,row) => {
      if (val == 'edit') {
        if(row){
          state.editForm={...row}
        }
        state.title = "编辑补助类型"
        state.dialogVisible = true
        state.dialogVisibleInfo=false
      } else if (val == 'add') {
        state.editForm = {
          stName: "",
          stStatus: "ENABLE_FALSE",
          stSort: 1,
        }
        state.title = "新增补助类型"
        state.dialogVisible = true
      }
    }
    const AddOrUpdate = (val) => {
      if (val === "编辑补助类型") {
        updateSubsidyTypeById(state.editForm).then((res) => {
          ElMessage.success(res.message)
          getList()
          state.dialogVisible = false
        });
      } else if (val == "新增补助类型") {
        addSubsidyType(state.editForm).then((res) => {
          ElMessage.success(res.message)
          getList()
          state.dialogVisible = false
        });
        console.log(val);
      }
    }

    const handleChange=(row)=>{
      if(row.stId){
        let params={...row}
        updateSubsidyTypeById(params).then((res) => {
          ElMessage.success(res.message)
          getList()
        });
      }
      
    }

    const search = () => {
      getList();
    };
    const reset = () => {
      state.form = {
        currentPage: 1,
        pageSize: 10,
      };
    };
    const off = () => {
      state.isShow = false;
    };
    const handlePageChange = (val) => {
      state.form.currentPage = val;
      getList();
    };
    const handleSizeChange = (val) => {
      state.form.currentPage = 1;
      state.form.pageSize = val;
      getList();
    };
    const handleDel = (row) => {
      ElMessageBox.confirm(`确认删除补助类型${state.editForm.stName}?`, {
        type: 'warning',
        closeOnPressEscape: false,
        closeOnClickModal: false,
      }).then(async () => {
        try {
          const {message} = await deleteSubsidyTypeById({stId : row.stId});
          ElMessage.success(message);
          getList();
        } catch (e) {
          throw new Error(e.message);
        }
      });
    }
    onMounted(() => {
      getList();
    });
    return {
      state,
      details,
      edit,
      handleChange,
      search,
      reset,
      off,
      AddOrUpdate,
      handleDel,
      handlePageChange,
      handleSizeChange,
    };
  },
};
</script>
<style lang="scss" scoped>
.income-detail {
  height: 680px;
}

.padding-form-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
