<template>
  <div class="Device">
    <kade-route-card style="height: auto">
      <kade-table-filter @search="getList()" @reset="reset()">
        <el-form inline size="small" label-width="100px">
          <el-form-item label="所属区域">
            <kade-area-select-tree style="width: 100%" :value="state.form.areaId" valueKey="id" :multiple="false" @valueChange="(val) => (state.form.areaId = val.id)" />
          </el-form-item>
          <el-form-item label="网关状态">
            <el-select clearable v-model="state.form.gatewayStatus" placeholder="全部">
              <el-option v-for="(item, index) in gatewayStatusList" :key="index" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="网关机号">
            <el-input placeholder="请输入" v-model="state.form.gatewayNo"></el-input>
          </el-form-item>
          <el-form-item label="网关名称">
            <el-input placeholder="请输入" v-model="state.form.gatewayName"></el-input>
          </el-form-item>
          <el-form-item label="网关ip">
            <el-input placeholder="请输入" v-model="state.form.gatewayIp"></el-input>
          </el-form-item>
        </el-form>
      </kade-table-filter>
      <kade-table-wrap :title="TabModule().title+'列表'">
        <template #extra>
          <el-button @click="state.rowData={};state.isEdit = true" icon="el-icon-daoru" size="small" class="btn-yellow">新增</el-button>
          <el-button @click="handleBind()" icon="el-icon-daoru" size="small" class="btn-green">绑定设备</el-button>
          <el-button @click="exportClick()" :disabled="!state.dataList.length" icon="el-icon-daoru" size="small" class="btn-blue">导出</el-button>
        </template>
        <el-table style="width: 100%" :data="state.dataList" @row-click="rowClick" ref="multipleTable" v-loading="state.loading" height="55vh" highlight-current-row border stripe>
          <el-table-column show-overflow-tooltip label="所属区域" prop="areaName" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip label="所属设备类型" prop="deviceTypeName" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip label="网关状态" prop="gatewayStatus" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip label="网关机号" prop="gatewayNo" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip label="网关名称" prop="gatewayName" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip label="网关IP" prop="gatewayIp" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip label="网关端口" prop="gatewayPort" align="center"></el-table-column>
          <el-table-column label="操作" align="center" width="220">
            <template #default="scope">
              <el-button size="mini" type="text" class="green" @click="handleDetails(scope.row)">详情</el-button>
              <el-button size="mini" type="text" class="green" @click="handleEdit(scope.row)">编辑</el-button>
              <el-button size="mini" type="text" class="green" @click="del(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination">
          <el-pagination background :current-page="state.form.pageNum" :page-size="state.form.pageSize" layout="total, sizes, prev, pager, next, jumper" :page-sizes="[6, 10, 20, 50, 100]" :total="state.total" @current-change="handlePageChange" @size-change="handleSizeChange">
          </el-pagination>
        </div>
      </kade-table-wrap>
    </kade-route-card>
    <kade-gateway-edit v-model:modelValue="state.isEdit" :rowData="state.rowData" :TabModule="TabModule" @update:modelValue="editClose" />
    <kade-gateway-details v-model:modelValue="state.isDetails" :rowData="state.rowData" :TabModule="TabModule" @update:modelValue="state.isDetails=$event" />
    <kade-gateway-bind-device v-model:modelValue="state.isBind" :rowData="state.rowData" :TabModule="TabModule" @update:modelValue="state.isBind=$event" />
  </div>
</template>
<script>
import {
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElTable,
  ElTableColumn,
  ElPagination,
  ElMessage,
  ElMessageBox,
} from "element-plus";
import { reactive } from "@vue/reactivity";
import { onMounted } from "@vue/runtime-core";
import { useStore } from "vuex";
import { downloadXlsx } from "@/utils"
import { useDict } from "@/hooks/useDict.js";
import { financeGatewayList, financeGatewayAdd, financeGatewayExport, identityGatewayList, identityGatewayAdd, identityGatewayExport, hydropowerGatewayList, hydropowerGatewayAdd, hydropowerGatewayExport, gatewayDel } from "@/applications/eccard-iot/api";
import areaSelectTree from "@/components/tree/areaSelectTree.vue";
import edit from "./edit";
import details from "./details";
import bindDevice from "./bindDevice"

export default {
  components: {
    "el-button": ElButton,
    "el-input": ElInput,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    "el-select": ElSelect,
    "el-option": ElOption,
    "el-table": ElTable,
    "el-table-column": ElTableColumn,
    "el-pagination": ElPagination,
    "kade-area-select-tree": areaSelectTree,
    "kade-gateway-edit": edit,
    "kade-gateway-details": details,
    "kade-gateway-bind-device": bindDevice
  },

  setup() {
    const gatewayStatusList = useDict("GATEWAY_STATUS")
    const store = useStore();
    const state = reactive({
      loading: false,
      isEdit: false,
      isDetails: false,
      isBind: false,
      form: {
        pageSize: 10,
        pageNum: 1,
      },
      dataList: [],
      total: 0,
      rowData: {},
    });
    /**
     * return {
     *  ListFnc    //列表数据请求
     *  addFnc    //新增数据
     * exportFnc    //导出
     * }
     */
    const TabModule = () => {
      let type = store.state.app.activeTab;
      if (type === "financePayGateway") {
        return {
          title: "金融支付设备网关",
          gatewayType: "TERMINAL",
          productType:"CONSUME_TERMINAL",
          ListFnc: financeGatewayList,
          addFnc: financeGatewayAdd,
          exportFnc: financeGatewayExport,
        };
      } else if (type === "identifyGateway") {
        return {
          title: "身份识别设备网关",
          gatewayType: "IDENTIFICATION",
          productType:"HARDWARE_IDENTIFICATION",
          ListFnc: identityGatewayList,
          addFnc: identityGatewayAdd,
          exportFnc: identityGatewayExport,
        };
      } else if (type === "energyWaterPowerGateway") {
        return {
          title: "节能水电设备网关",
          gatewayType: "WATERELECTRICITY",
          productType:"HARDWARE_WATER_ELECTRIC",
          ListFnc: hydropowerGatewayList,
          addFnc: hydropowerGatewayAdd,
          exportFnc: hydropowerGatewayExport,
        };
      } else {
        return {
          title: "网关",
        };
      }
    };
    const getList = async () => {
      state.rowData = {};
      for (let key in state.form) {
        if (!state.form[key]) {
          delete state.form[key];
        }
      }
      state.loading = true;
      try {
        let { data: { list, total } } = await TabModule().ListFnc(state.form)
        state.dataList = list
        state.total = total
        state.loading = false
      }
      catch {
        state.loading = false;

      }
    };
    const rowClick = (row) => {
      state.rowData = row;
    };
    const handleDetails = (row) => {
      state.rowData = row;
      state.isDetails = true
    }
    const handleEdit = (row) => {
      state.rowData = row;
      state.isEdit = true
    }
    const handleBind = () => {
      if (!state.rowData.id) {
        return ElMessage.error('请选择参数！')
      }
      state.isBind = true
    }
    const del = async (row) => {
      ElMessageBox.confirm(`确认删除?`, {
        type: "warning",
        closeOnPressEscape: false,
        closeOnClickModal: false,
      }).then(async () => {
        try {
          const { message } = await gatewayDel(row.id);
          ElMessage.success(message);
          getList();
        } catch (e) {
          throw new Error(e.message);
        }
      });
    };
    const exportClick = async () => {
      for (let key in state.form) {
        if (!state.form[key]) {
          delete state.form[key];
        }
      }
      let res = await TabModule().exportFnc(state.form)
      downloadXlsx(res, TabModule().title + '列表.xlsx')
    }
    const handlePageChange = (val) => {
      state.form.pageNum = val;
      getList();
    };
    const handleSizeChange = (val) => {
      state.form.pageNum = 1;
      state.form.pageSize = val;
      getList();
    };
    //重置
    const reset = () => {
      state.form = { pageSize: 10, pageNum: 1 };
    };
    const editClose = (val) => {
      if (val) {
        getList()
      }
      state.isEdit = false
    }
    onMounted(() => {
      getList();
    });
    return {
      TabModule,
      gatewayStatusList,
      state,
      getList,
      rowClick,
      handleDetails,
      handleEdit,
      handleBind,
      del,
      handlePageChange,
      handleSizeChange,
      reset,
      editClose,
      exportClick
    };
  },
};
</script>
<style lang="scss" scoped>
.details {
  color: #1abc9c;
  margin: 0 10px;
}
.el-divider--horizontal {
  margin: 0 0 10px;
}
.search-box {
  padding: 0 20px;
  margin-top: 20px;
}
.el-divider--horizontal {
  margin: 0 0 10px;
}
:deep(.el-dialog__header) {
  border-bottom: 1px solid #efefef;
}

:deep(.el-overlay) {
  position: absolute;
  background-color: rgba(0, 0, 0, 0.3);
}

:deep(.el-dialog__footer) {
  text-align: center;
}
:deep(.el-form-item__content) {
  .el-select {
    width: 200px;
  }

  .el-textarea {
    width: 100%;

    .el-textarea__inner {
      width: 100%;
    }
  }
  .el-input__inner {
    width: 200px;
  }
}
</style>
