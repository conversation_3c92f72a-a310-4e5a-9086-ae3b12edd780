<template>
  <el-dialog :model-value="modelValue" :title="rowData.id?'编辑权限申请':'新增权限申请'" width="1700px" :before-close="handleClose"
    :close-on-click-modal="false">
    <div style="padding:20px 0 10px">
      <el-form label-width="100px" ref="formRef" size="mini" :model="state.form" :rules="rules">
        <el-row>
          <el-col :span="12">
            <el-row>
              <el-col :span="24">
                <el-form-item label="申请人员：" prop="userIds">
                  <el-input placeholder="请选择" v-model="state.userName" readonly @click="handleSelectPerson">
                  </el-input>
                  <select-person-dialog :isShow="state.isPerson" :isMultiple="true" @close="personClose" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="开始时间：" prop="beginTime">
                  <el-date-picker v-model="state.form.beginTime" type="datetime" placeholder="请选择时间" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="结束时间：" prop="endTime">
                  <el-date-picker v-model="state.form.endTime" type="datetime" placeholder="请选择时间" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-col>
          <el-col :span="12">
            <el-form-item label="备注">
              <el-input type="textarea" :rows="4" v-model="state.form.reason" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <kade-table-wrap title="申请门禁" icon="none">
        <el-divider style="margin-bottom:20px"></el-divider>
        <!--         <el-form inline size="mini" label-width="100px" style="margin-top:20px">
          <el-form-item label="区域">
            <kade-area-select-tree style="width: 100%" :value="state.deviceForm.areaPath" valueKey="areaPath"
              :multiple="false" @valueChange="(val) => (state.deviceForm.areaPath = val.areaPath)" />
          </el-form-item>
          <el-button type="primary" size="mini" @click="deviceSearch">搜索</el-button>
        </el-form>
        <el-table :data="state.deviceDataList" @selection-change="handleDeviceSelectChange" border height="35vh">
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column show-overflow-tooltip prop="userName" label="区域" align="center">
            <template #default="{ row }">
              {{ `${row.areaName}>${row.buildName ? row.buildName +
                  '>' : ''}${row.unitNumName ? row.unitNumName + '>' : ''}${row.floorNumName ? row.floorNumName + '>' :
                    ''}${row.deviceName}${row.doorName ? '>' + row.doorName : ''}`
              }}
            </template>
          </el-table-column>
        </el-table> -->
        <kade-select-table :isShow="modelValue" :value='[]' :reqFnc="authApplyAreaList"
          :selectCondition="selectCondition" :column="column" :params="params" @change="personChange" />
      </kade-table-wrap>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose" size="mini">取消</el-button>
        <el-button type="primary" @click="submit" size="mini">保存</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script>
import { ElDialog, ElRow, ElCol, ElDivider, ElButton, ElForm, ElFormItem, ElInput, ElDatePicker, ElMessage } from "element-plus"
import { reactive, ref } from '@vue/reactivity'
import { timeStr } from "@/utils/date.js"
import { acsDeviceList, authApplyAdd, authApplyAreaList, authApplyEdit } from "@/applications/eccard-uac/api";
import selectPersonDialog from "@/components/table/selectPersonDialog.vue";
import { nextTick, watch } from '@vue/runtime-core';
import selectTable from "@/components/table/selectTable.vue";
const column = [
  { label: "区域", prop: "areaString", isDict: false, width: "" },
];
const rules = {
  userIds: [
    {
      required: true,
      message: "请选择人员",
      trigger: "blur",
    },
  ],
  beginTime: [
    {
      required: true,
      message: "请选择开始时间",
      trigger: "change",
    },
  ],
  endTime: [
    {
      required: true,
      message: "请选择结束时间",
      trigger: "change",
    },
  ],
}
export default {
  components: {
    ElDialog, ElRow, ElCol, ElDivider, ElButton, ElForm, ElFormItem, ElInput, ElDatePicker,
    "select-person-dialog": selectPersonDialog,
    "kade-select-table": selectTable,
  },
  emits: ["update:modelValue"],
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    rowData: {
      type: Object,
      default: null
    }
  },
  setup(props, context) {
    const formRef = ref(null)
    const state = reactive({
      deviceForm: {},
      deviceDataList: [],
      DeviceListData: {
        list: []
      },
      form: {},
      userName: "",
      isPerson: false
    })
    const selectCondition = [
      { label: "区域", valueKey: "areaPath", dataKey: "areaPath", placeholder: "请选择", isSelect: false, isTree: "area" },
    ]
    const params = {
      currentPageKey: "currentPage",
      pageSizeKey: "pageSize",
      resListKey: "dataList",
      resTotalKey: "totalCount",
      value: {

      },
      tagNameKey: "areaString",
      valueKey: "id",
    }
    watch(() => props.modelValue, val => {
      if (val) {
        if (props.rowData.id) {
          state.form = { ...props.rowData }
          state.form.userIds=[{ ...props.rowData }.userId]
          state.userName = props.rowData.userName
        } else {
          state.form = {}
          state.userName = ""
        }
        nextTick(() => {
          formRef.value.clearValidate()
        })
        getDeviceList()
      }
    })
    const getDeviceList = async () => {
      let { data } = await acsDeviceList(state.deviceForm)
      state.deviceDataList = data
    }
    const handleSelectPerson = () => {
      if (!props.rowData.id) {
        state.isPerson = true
      }
    }
    const deviceSearch = () => {
      getDeviceList()
    }
    const personChange = (val) => {
      state.DeviceListData = val
    }
    const personClose = (val) => {
      if (val) {
        state.form.userIds = val.list.map(item => item.id)
        state.userName = val.list.map(item => item.userName).join("、")
      }
      state.isPerson = false
    }
    const submit = () => {
      if (!state.DeviceListData.list.length) {
        return ElMessage.error("请选择设备！")
      }
      formRef.value.validate(async (valid) => {
        if (valid) {
          let params = {
            areaNodeInfos: state.DeviceListData.list,
            ...state.form,
            beginTime: timeStr({ ...state.form }.beginTime),
            endTime: timeStr({ ...state.form }.endTime),
          }
          let fn = props.rowData.id ? authApplyEdit : authApplyAdd
          state.loading = true
          try {
            let { message, code } = await fn(params)
            if (code === 0) {
              ElMessage.success(message)
              context.emit("update:modelValue", true)
            }
            state.loading = false
          }
          catch {
            state.loading = false
          }

        }
      })
    }
    const handleClose = () => {
      context.emit("update:modelValue", false)
    }
    return {
      column,
      selectCondition,
      authApplyAreaList,
      params,
      rules,
      formRef,
      state,
      handleSelectPerson,
      deviceSearch,
      personChange,
      submit,
      personClose,
      handleClose,
    }
  }
}
</script>
<style lang="scss" scoped>
.selected {
  border: 1px solid #eeeeee;
  border-radius: 5px;
  margin: 20px 20px 50px 20px;

  .title {
    margin: 10px;
  }

  .dividing {
    margin-bottom: 15px;
  }
}

:deep(.kade-table-wrap) {
  margin-bottom: 20px;
}

.padding-box {
  padding: 10px 10px 0
}

.el-divider--horizontal {
  margin: 0
}

:deep(.box) {
  padding: 10px 10px 0;

  .el-form {
    .el-select {
      width: 150px;
    }

    .el-input {
      width: 150px
    }
  }
}

.el-table {
  border-left: 1px solid #EBEEF5;
  border-right: 1px solid #EBEEF5;
}
</style>