<template>
  <el-dialog v-loading="state.loading" :model-value="modelValue" :title="rowData.id?'编辑数据分发项目':'新增数据分发项目'" width="400px" :before-close="beforeClose">
    <div class="padding-form-box">
      <div class="width-box">
        <el-form size="mini" ref="formRef" :model="state.form" label-width="120px" :rules="rules">
          <el-form-item label="数据分发名称：" prop="distributeName">
            <el-input placeholder="请输入" v-model="state.form.distributeName" maxlength="20"></el-input>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="submit()" type="primary" size="mini">保&nbsp;&nbsp;存</el-button>
        <el-button @click="beforeClose" size="mini">返&nbsp;&nbsp;回</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script>
import { reactive, ref, watch, nextTick } from "vue";
import { dataDistributeAdd,dataDistributeEdit } from "@/applications/eccard-iot/api";
import {
  ElDialog,
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElMessage,
} from "element-plus";
export default {
  components: {
    "el-dialog": ElDialog,
    "el-button": ElButton,
    "el-input": ElInput,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
  },
  props: {
    modelValue: {
      type: Boolean,
      default: false,
    },
    rowData: {
      type: Object,
      default: null,
    },
    editType: {
      type: String,
      default: ""
    }
  },
  setup(props, context) {
    const formRef = ref(null)
    const state = reactive({
      loading: false,
      form: {},
      deviceTypeList: []

    });
    watch(
      () => props.modelValue,
      (val) => {
        if (val) {
          if (props.editType == 'edit') {
            state.form = { ...props.rowData }
          } else {
            state.form = {}
          }
          nextTick(() => {
            formRef.value.clearValidate()
          })
        }

      }
    );
    const rules = {
      distributeName: [
        {
          required: true,
          message: "请输入数据分发名称",
          trigger: "change",
        },
      ],
    }
    const beforeClose = () => {
      context.emit("update:modelValue", false);
    };
    const submit = () => {
      formRef.value.validate(async (valid) => {
        if (valid) {
          let fn = state.form.id ? dataDistributeEdit : dataDistributeAdd
          state.loading = true
          try {
            let { code, message } = await fn(state.form)
            if (code === 0) {
              ElMessage.success(message)
              context.emit("update:modelValue", true);
              state.loading = false
            }
          }
          catch {
            state.loading = false
          }
        } else {
          return false;
        }
      });
    };
    return {
      state,
      formRef,
      rules,
      submit,
      beforeClose,
    };
  },
};
</script>
<style lang="scss" scoped>
:deep(.el-form-item__content) {
  .el-select {
    width: 178px;
  }

  .el-textarea {
    width: 100%;

    .el-textarea__inner {
      width: 100%;
    }
  }
}
</style>