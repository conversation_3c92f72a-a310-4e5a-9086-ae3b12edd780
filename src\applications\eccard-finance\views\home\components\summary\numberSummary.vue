<template>
    <div id="echartdiv" class="chartDiv"></div>
    <div class="number-box">
        <div class="yoy-box">
            <div class="title">周同比</div>
            <div class="icon"></div>
            <div>12%</div>
        </div>
        <div class="day-box">
            <div class="title">日环比</div>
            <div class="icon"></div>
            <div>11%</div>
        </div>
    </div>
</template>

<script>
import * as echarts from "echarts";
import { onMounted } from "vue";

export default {
    setup() {
        const echartInit = () => {
            var chartDom = document.getElementById("echartdiv");
            var myChart = echarts.init(chartDom);
            var option = {
                tooltip: {
                    trigger: "axis",
                    axisPointer: {
                        type: "shadow",
                    },
                },
                xAxis: [
                    {
                        type: "category",
                        axisTick: {
                            alignWithLabel: true,
                        },
                        show:false,
                    },
                ],
                yAxis: [
                    {
                        type: "value",
                        show:false,

                    },
                ],
                grid:{
                    top:1,
                    right:1,
                    bottom:1,
                    left:1
                },
                series: [
                    {
                        color: "#3ba1ff",
                        type: "bar",
                        barWidth: "10",
                        data: [6,1,3,4,1,2,5,6,4,3,5,2,1,6],
                    },
                ] /*  */,
            };
            // 使用刚指定的配置项和数据显示图表。
            option && myChart.setOption(option);
        };
        //挂载
        onMounted(() => {
            echartInit();
        });

        return {
            echartInit,
        };
    },
};
</script>


<style lang="scss" scoped>
.number-box {
    display: flex;
    width: 100%;
    color: #0000006d;
    align-items: center;
    border-top: 1px solid #eeeeee;
    min-width: 328px;
    padding: 5px 0 0 5px;
    .yoy-box {
        display: flex;
        font-size: 10px;
        align-items: center;
        margin-right: 80px;
        .title {
        }
        .icon {
            margin: 5px;
            width: 0;
            height: 0;
            border-right: 7px solid #01a855;
            border-bottom: 7px solid #fff;
            transform: rotate(-45deg);
        }
    }
    .day-box {
        display: flex;
        font-size: 10px;
        align-items: center;
        .icon {
            margin: 5px;
            width: 0;
            height: 0;
            border-left: 7px solid #f00;
            border-top: 7px solid #fff;
            transform: rotate(-45deg);
        }
    }
}
.chartDiv {
    box-sizing: border-box;
    width: 100%;
    height: 25px;
    margin-bottom: 15px;
}
</style>