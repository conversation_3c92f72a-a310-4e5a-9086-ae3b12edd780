<template>
  <el-row :gutter="20" class="dept-row" style="padding: 20px; box-sizing: border-box">
    <el-col :md="24" :lg="11" :xl="8" class="dept-col">
      <el-card>
        <template #header>
          <div class="card-head">
            <div class="left">
              <i class="el-icon-location-information"></i>
              <span class="dept-htit">组织机构</span>
            </div>
            <div class="right">
              <el-upload style="margin:0 10px" ref="uploadRef"
                :headers="{ Authorization: `bearer ${state.uploadHeader}` }" class="upload-demo" :show-file-list="false"
                :action="state.uploadUrl" :on-success="uploadSuccess" :on-error="uploadError">
                <el-button class="btn-import" icon="el-icon-daoru" size="mini">导入</el-button>
              </el-upload>
              <el-button :disabled="!state.data.length" @click="handleExport" icon="el-icon-daochu" size="mini"
                class="btn-blue">导出</el-button>
            </div>
          </div>
        </template>
        <div class="filter">
          <el-input size="small" style="width: 250px; padding-bottom: 20px" placeholder="请输入组织名称"
            v-model="state.keyword">
            <template #append>
              <el-button icon="el-icon-search" @click="handleSearch" size="small" type="primary">查询</el-button>
            </template>
          </el-input>
        </div>
        <div class="tree">
          <el-tree :props="treeProps" :data="state.treeList" :filter-node-method="filterNode"
            :default-expanded-keys="state.expandKeys" :expand-on-click-node="false" :auto-expand-parent="false"
            node-key="deptId" @node-click="(node) => handleNodeClick(node, true)" @node-collapse="handleCollapse"
            ref="treeRef">
            <template #default="{ node }">
              <div class="custom-tree-node hasbtn">
                <span class="left">
                  <kade-icon name="iconquyuguanli" />
                  <span style="margin-left: 5px; color: #333">{{ node.label }}</span>
                </span>
                <span class="right" v-if="node.data.checked">
                  <span class="a-btn" @click.stop="handleAdd(node, 1)" v-show="node.data.deptParentId !== 0">
                    <i class="el-icon-plus"></i>
                    <span class="text">本级</span>
                  </span>
                  <span class="a-btn" @click.stop="handleAdd(node, 2)">
                    <i class="el-icon-plus"></i>
                    <span class="text">下级</span>
                  </span>
                  <span class="a-btn" @click.stop="handleDel(node)">
                    <i class="el-icon-close"></i>
                    <span class="text">删除</span>
                  </span>
                </span>
              </div>
            </template>
          </el-tree>
        </div>
      </el-card>
    </el-col>
    <el-col :md="24" :lg="13" :xl="16" class="dept-col">
      <el-card>
        <template #header>
          <i class="el-icon-xinxi"></i>
          <span class="dept-htit">{{ cardTitle }}</span>
        </template>
        <el-form ref="formRef" :model="state.model" :rules="rules" :label-width="labelWidth" size="small"
          v-loading="loading">
          <el-row>
            <el-col :md="24" :lg="12">
              <el-form-item label="组织名称:" :prop="state.isInfo ? '' : 'deptName'">
                <span v-if="state.isInfo">{{ state.model.deptName }}</span>
                <el-input v-else placeholder="请输入" v-model="state.model.deptName"></el-input>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="组织编码:" :prop="state.isInfo ? '' : 'deptCode'">
                <span v-if="state.isInfo">{{ state.model.deptCode }}</span>
                <el-input v-else placeholder="请输入" v-model="state.model.deptCode"></el-input>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="上级组织:" :prop="state.isInfo ? '' : 'deptParentId'">
                <span v-if="state.isInfo">{{ state.model.deptParentName }}</span>
                <kade-dept-select-tree v-else style="width: 100%" :value="state.model.deptParentId" valueKey="id"
                  :multiple="false" @valueChange="(val) => (state.model.deptParentId = val.id)" />
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="负责人:" :prop="state.isInfo ? '' : 'deptDirector'">
                <span v-if="state.isInfo">{{ state.model.deptDirector.map(item => item.userName).join(',') }}</span>
                <el-input v-else placeholder="请选择"
                  :model-value="state.model.deptDirector.map(item => item.userName).join(',')" readonly
                  @click="state.isPersoner = true"></el-input>
                <select-person-dialog :isShow="state.isPersoner" @close="closePersonSelect" />
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="组织联系电话:" :prop="state.isInfo ? '' : 'deptTel'">
                <span v-if="state.isInfo">{{ state.model.deptTel }}</span>
                <el-input v-else placeholder="请输入" v-model="state.model.deptTel"></el-input>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="所属区域:" :prop="state.isInfo ? '' : 'deptArea'">
                <span v-if="state.isInfo">{{ state.model.deptAreaName }}</span>
                <kade-area-select-tree v-else style="width: 100%" :value="state.model.deptArea" valueKey="id"
                  :multiple="false" @valueChange="(val) => (state.model.deptArea = val.id)" />
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="组织类别:" :prop="state.isInfo ? '' : 'deptType'">
                <span v-if="state.isInfo">{{ dictionaryFilter(state.model.deptType) }}</span>
                <el-select clearable v-else style="width: 100%" placeholder="请选择" v-model="state.model.deptType">
                  <el-option v-for="item in deptTypes" :key="item.value" :value="item.value" :label="item.label">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="组织来源:" :prop="state.isInfo ? '' : 'deptSource'">
                <span v-if="state.isInfo">{{ dictionaryFilter(state.model.deptSource) }}</span>
                <el-select clearable disabled v-else style="width: 100%" placeholder="请选择"
                  v-model="state.model.deptSource">
                  <el-option v-for="item in sources" :key="item.value" :value="item.value" :label="item.label">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="备注:" :prop="state.isInfo ? '' : 'deptRemark'">
                <span v-if="state.isInfo">{{ state.model.deptRemark }}</span>
                <el-input show-word-limit maxlength="100" v-else type="textarea" :rows="5" placeholder="请输入"
                  v-model="state.model.deptRemark"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24" align="center">
              <el-button size="small" icon="el-icon-circle-close" @click="handleCancel">取消</el-button>
              <el-button size="small" icon="el-icon-edit" type="primary" :disabled="state.model.deptParentId === '0'||!state.model.checked"
                v-if="state.isInfo" @click="handleEdit()">编辑</el-button>
              <el-button v-else size="small" icon="el-icon-circle-check" :loading="btnLoading" type="primary"
                @click="handleSubmit">保存</el-button>
            </el-col>
          </el-row>
        </el-form>
      </el-card>
    </el-col>
  </el-row>
</template>
<script>
import {
  ElInput,
  ElButton,
  ElSelect,
  ElOption,
  ElUpload,
  ElTree,
  ElForm,
  ElFormItem,
  ElRow,
  ElCol,
  ElMessageBox,
  ElMessage,
  ElCard,
} from "element-plus";
import { reactive, ref, computed, onMounted, nextTick } from "vue";
import {
  getDepartTree,
  getDepartInfo,
  delPart,
  editPart,
  addPart,
  getUserInfoListByPage,
  getAreaMenuList,
  DeptInfoExport,
  getUserOrgPurviewTree
} from "@/applications/eccard-basic-data/api";
import { useStore } from "vuex";
import { getToken, downloadXlsx } from "@/utils";
import deptSelectTree from "@/components/tree/deptSelectTree.vue";
import areaSelectTree from "@/components/tree/areaSelectTree.vue";
import selectPersonDialog from "@/components/table/selectPersonDialog.vue";
import { useDict } from "@/hooks/useDict";
import { validateMobileAndFixTel } from "@/utils/validate";

const getDefaultModel = () => ({
  deptParentId: "",
  deptParentName: "",
  deptArea: "",
  deptName: "",
  deptCode: "",
  deptDirector: [],
  deptRemark: "",
  deptSource: "FROM_SYS",
  deptTel: "",
  deptType: "",
  deptSort: 1,
});
export default {
  emits: ["closePersonSelect"],
  components: {
    "el-card": ElCard,
    "el-col": ElCol,
    "el-row": ElRow,
    "el-button": ElButton,
    "el-tree": ElTree,
    "el-input": ElInput,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    "el-select": ElSelect,
    "el-option": ElOption,
    ElUpload,
    "kade-dept-select-tree": deptSelectTree,
    "kade-area-select-tree": areaSelectTree,
    "select-person-dialog": selectPersonDialog,
  },
  setup() {
    const state = reactive({
      isPersoner: false,
      data: [],
      treeList: [],
      loading: false,
      btnLoading: false,
      uploadHeader: getToken(),
      uploadUrl: `${CONFIG.BASE_API_PATH}eccard-basic-data/depart/DepartInfoImport`,
      keyword: "",
      model: getDefaultModel(),
      selectUsers: "",
      isInfo: false,
      hasDept: false,
      expandKeys: [],
    });

    const formRef = ref(null);
    const treeRef = ref(null);
    const loading = ref(false);
    const btnLoading = ref(false);
    const store = useStore();
    const deptTypes = useDict("BASE_DEPT_TYPE");
    const sources = useDict("SYS_DATA_SOURCE");
    const rules = {
      deptName: [
        {
          required: true,
          message: "请输入组织名称",
        },
        {
          max: 20,
          message: "组织名称长度不得超过20字符",
        },
      ],
      deptParentId: [
        {
          required: true,
          message: "请选择上级组织",
          trigger: "change",
        },
      ],
      deptDirector: [{
        required: true,
        type: 'array',
        trigger: 'change',
        message: '请选择组织负责人'
      }],
      deptCode: [
        {
          required: true,
          message: "请输入组织编码",
        },
        {
          pattern: /^[a-zA-Z0-9]{1,20}$/,
          message: "组织编码格式错误,只能输入1~20位字母数字",
        },
      ],
      deptSource: [
        {
          required: true,
          message: "请选择组织来源",
          trigger: "change",
        },
      ],
      deptType: [
        {
          required: true,
          message: "请选择组织类别",
          trigger: "change",
        },
      ],
      deptTel: [
        {
          required: true,
          message: "请输入组织联系电话",
        },
        {
          validator: validateMobileAndFixTel,
        },
      ],
      deptArea: [
        {
          required: true,
          message: "请选择区域",
          trigger: "change",
        },
      ],
    };

    const closePersonSelect = (val) => {
      console.log(val);
      if (val.list && val.list.length) {
        console.log(val);
        state.model.deptDirector = val.list
        state.isPersoner = false;
      } else if (!val) {
        state.isPersoner = false;
      } else {
        state.model.deptDirector = []
      }

    };

    const filterNode = (value, data) => {
      if (!value) {
        return true;
      }
      return data.deptName?.indexOf(value) !== -1;
    };

    const loadNode = async () => {
      const {
        data
      } = await getUserOrgPurviewTree();
      state.hasDept = data.length === 0;
      state.expandKeys.push(data[0].id);
      state.treeList = data
    };

    const handleLoad = (node, resolve) => {
      console.log(node);
      let arr = state.data.filter(item => item[state.data] == node.data.id)
      console.log(arr);
      resolve(arr)
    }

    const handleAdd = (node, type) => {
      state.isInfo = false;
      if (!node) {
        state.model = getDefaultModel();
      } else if (type === 1) {
        state.model = Object.assign(getDefaultModel(), {
          deptParentId: node.data.deptParentId,
          deptParentName: node.data.deptParentName,
        });
      } else {
        state.model = Object.assign(getDefaultModel(), {
          deptParentId: node.data.id,
          deptParentName: node.data.deptName,
        });
      }
      nextTick(() => {
        formRef.value?.clearValidate?.();
      });
    };

    const handleSearch = () => {
      treeRef.value.filter(state.keyword);
    };

    const handleDel = ({ data }) => {
      ElMessageBox.confirm(`确认删除?`, {
        type: "warning",
        closeOnPressEscape: false,
        closeOnClickModal: false,
      }).then(async () => {
        try {
          const { message, code } = await delPart({
            deptId: data.deptId,
          });
          if (code === 0) {
            ElMessage.success(message);
            const node = treeRef.value.getNode(state.model.deptId);
            treeRef.value.remove(node);
            handleAdd();
          } else {
            ElMessage.error(message);
          }
        } catch (e) {
          throw new Error(e.message);
        }
      });
    };

    const handleSubmit = () => {
      formRef.value.validate(async (valid) => {
        if (valid) {
          try {
            btnLoading.value = true;
            const fn = state.model.deptId ? editPart : addPart;
            // eslint-disable-next-line no-unused-vars
            const { deptParentName, deptDirector, deptAreaName, ...fields } =
              state.model;
            const { message } = await fn({
              ...fields,
              deptDirector: deptDirector.map((it) => ({ userId: it.id })),
              operId: store.state.user.userInfo.id,
            });
            ElMessage.success(message);
            if (!state.model.deptId) {
              handleAdd();
            }
            loadNode();
          } catch (e) {
            throw new Error(e.message);
          } finally {
            btnLoading.value = false;
          }
        }
      });
    };

    const handleNodeClick = async (target, isInfo) => {
      console.log(target);
      try {
        loading.value = true;
        formRef.value.clearValidate();
        const { data } = await getDepartInfo({ deptId: target.id });
        data.deptDirector = Array.isArray(data.deptDirector)
          ? data.deptDirector.map(item => {
            return {
              userName: item.userName,
              id: item.userId
            }
          })
          : [];
        state.model = Object.assign(getDefaultModel(), { ...data,checked:target.checked });
        state.isInfo = !!isInfo;
      } catch (e) {
        throw new Error(e.message);
      } finally {
        loading.value = false;
      }
    };

    const dirDefaultValue = computed(() => {
      return state.model.deptDirector.map((it) => ({
        id: it.userId,
        userName: it.userName,
      }));
    });

    const handleAreaChange = (data) => {
      state.model.deptArea = data.areaId;
      state.model.deptAreaName = data.areaName;
    };

    const areaAction = async (params) => {
      const {
        data: { areaMenuList },
      } = await getAreaMenuList(params);
      return areaMenuList;
    };

    const handleEdit = (node) => {
      if (node) {
        handleNodeClick(node.data);
      } else {
        state.isInfo = false;
      }
    };

    const handleCancel = () => {
      if (state.isInfo) {
        handleAdd();
      } else if (state.model.deptId) {
        state.isInfo = true;
        handleNodeClick(state.model, true);
      } else {
        handleAdd();
      }
    };

    const handleExpand = (data) => {
      state.expandKeys.push(data.deptId);
    };
    const handleCollapse = (data) => {
      state.expandKeys = state.expandKeys.filter((it) => it !== data.deptId);
    };

    const cardTitle = computed(() => {
      if (state.isInfo) {
        return "组织机构详情";
      } else if (state.model.deptId) {
        return "编辑组织机构";
      } else {
        return "新增组织机构";
      }
    });

    const getParentOrgs = (list, deptId) => {
      const arr = [];
      list.forEach((it) => {
        if (it.deptId !== deptId) {
          const target = { ...it };
          if (it.children && it.children.length) {
            target.children = getParentOrgs(it.children, deptId);
          }
          arr.push(target);
        }
      });
      return arr;
    };

    const parentOrgs = computed(() => {
      if (state.model.deptId && !state.isInfo) {
        return getParentOrgs(state.data, state.model.deptId);
      }
      return state.data;
    });

    const handleExport = async () => {
      let res = await DeptInfoExport({ deptName: state.data[0].deptName });
      downloadXlsx(res, "组织机构表.xlsx");
    };

    const uploadSuccess = ({ code, message }) => {
      if (code === 0) {
        ElMessage.success(message);
        loadNode();
      } else {
        ElMessage.error(message);
      }
    };
    const uploadError = (err) => {
      console.log(err);
    };

    onMounted(() => {
      loadNode();
    });

    return {
      loading,
      btnLoading,
      labelWidth: THEMEVARS.formLabelWidth,
      state,
      rules,
      formRef,
      treeRef,
      treeProps: {
        children: "children",
        label: "deptName",
        isLeaf: "leaf",
      },
      filterNode,
      closePersonSelect,
      handleEdit,
      handleSearch,
      handleAdd,
      handleDel,
      handleSubmit,
      handleExpand,
      handleCollapse,
      handleNodeClick,
      getUserInfoListByPage,
      dirDefaultValue,
      handleAreaChange,
      handleCancel,
      areaAction,
      deptTypes,
      sources,
      cardTitle,
      parentOrgs,
      treeDepartOpts: {
        props: {
          children: "children",
          label: "deptName",
          isLeaf: (data) => {
            if (data["isLeaf"] === undefined) {
              return true;
            }
            return !!data.isLeaf;
          },
        },
      },
      areaTreeOpts: {
        props: {
          children: "children",
          label: "areaName",
          isLeaf: (data) => {
            if (data["isLeaf"] === undefined) {
              return true;
            }
            return !!data.isLeaf;
          },
        },
      },
      selectDeptAction: async (params) => {
        const {
          data: { deptMenuList },
        } = await getDepartTree(params);
        return deptMenuList;
      },
      selectDeptChange: (data) => {
        Object.assign(state.model, {
          deptParentName: data?.deptName || "",
          deptParentId: data?.deptId || "",
        });
      },
      handleExport,
      uploadSuccess,
      uploadError,
      handleLoad
    };
  },
};
</script>
<style lang="scss" scoped>
.dept-row {
  height: 100%;
  overflow: hidden;

  .dept-col {
    height: 100%;
    overflow: hidden;
  }

  .dept-htit {
    margin-left: 5px;
  }

  .el-card {
    height: calc(100% - 2px);

  }

  :deep(.el-card__body) {
    height: calc(100% - 70px);
    box-sizing: border-box;
    overflow-y: auto;
    overflow-x: auto;

  }

  .card-head {
    display: flex;
    justify-content: space-between;

    .right {
      display: flex;
      justify-content: flex-end;
    }
  }

  .tree {

    .el-tree {
      width: auto;
      display: inline-block;
      min-width: 100%;

      .right {
        padding-left: 10px;
      }
    }
  }
}

@media screen and(max-width: 1200px) {
  .dept-row {
    .dept-col+.dept-col {
      margin-top: 20px;
    }
  }
}
</style>
