<template>
  <!-- 充值汇总报表 -->
  <div class="cashRecharge" v-loading="state.loading">
    <kade-route-card style="height: auto">
      <kade-table-filter @search="getList()" @reset="reset()">
        <el-form inline size="mini" label-width="100px">
          <el-form-item label="组织机构:">
            <kade-dept-select-tree style="width: 100%" :value="state.form.deptPath" valueKey="deptPath"
              :multiple="false" @valueChange="(val) => (state.form.deptPath = val.deptPath)" />
          </el-form-item>
          <el-form-item label="汇总方式:">
            <el-select v-model="state.form.settlementMethod" placeholder="请选择" size="small" clearable>
              <el-option v-for="(item, index) in sumMethodList" :key="index" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </kade-table-filter>
      <kade-table-wrap title="消费分类汇总报表">
        <template #extra>
          <el-button @click="exportClick()" icon="el-icon-daoru" size="mini" class="btn-blue">导出查询明细</el-button>
        </template>
        <el-table style="width: 100%" :data="state.detailList" ref="multipleTable" height="55vh" highlight-current-row
          border stripe>
          <el-table-column v-for="(item, index) in column" :key="index" :width="item.width" :label="item.label"
            :prop="item.prop" align="center" show-overflow-tooltip>
            <template v-if="item.render" #default="scope">
              {{ item.render(scope.row[item.prop]) }}
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination">
          <el-pagination background :current-page="state.form.currentPage" :page-size="state.form.pageSize"
            layout="total, sizes, prev, pager, next, jumper" :page-sizes="[6, 10, 20, 50, 100]" :total="state.total"
            @current-change="handlePageChange" @size-change="handleSizeChange">
          </el-pagination>
        </div>
      </kade-table-wrap>
    </kade-route-card>
  </div>
</template>
<script>
import {
  ElButton,
  ElForm,
  ElFormItem,
  ElSelect,
  ElOption,
  ElTable,
  ElTableColumn,
  ElPagination,
} from "element-plus";
import { timeStr } from "@/utils/date.js";
import { reactive } from "@vue/reactivity";
import { useDict } from "@/hooks/useDict.js";
import {
  oralConsumeSum,
  exportConsumeSumList,
} from "@/applications/eccard-finance/api";
import { onMounted, watch } from "@vue/runtime-core";
import deptSelectTree from "@/components/tree/deptSelectTree.vue";
const column = [
  { label: "汇总部门", prop: "deptName", width: "" },
  { label: "汇总时间", prop: "tradeDate", width: "" },
  { label: "现金消费笔数", prop: "consumeCount", width: "" },
  { label: "现金消费金额", prop: "consumeAmount", width: "" },
  { label: "补助消费笔数", prop: "subsidyCount", width: "" },
  { label: "补助消费金额", prop: "subsidyAmount", width: "",},
  { label: "消费总笔数", prop: "totalCount", width: "" },
  { label: "消费总金额", prop: "totalAmount", width: "" },
]

export default {
  components: {
    "el-button": ElButton,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    "el-select": ElSelect,
    "el-option": ElOption,
    "el-table": ElTable,
    "el-table-column": ElTableColumn,
    "el-pagination": ElPagination,
    "kade-dept-select-tree": deptSelectTree,
  },
  setup() {
    const sumMethodList = useDict("CONSUMPTION_SUMMARY_TYPE");
    const state = reactive({
      loading: false,
      form: {
        pageNum: 1,
        pageSize: 10,
        settlementMethod:"YEAR"
      },
      detailList: [],
      total: 0,
      systemUserList: [],
    });
    watch(
      () => state.requestDate,
      (val) => {
        if (!val) {
          delete state.form.startDate;
          delete state.form.endDate;
        }
      }
    );
    const getList = async () => {
      state.loading = true;
      try {
        let { code, data:{list,total} } = await oralConsumeSum(state.form);
        if (code === 0) {

          state.detailList = list;
          state.total = total;
        }
        state.loading = false;
      }
      catch {
        state.loading = false;
      }
    };

    const exportClick = async () => {
      state.loading = true
      try {
        let res = await exportConsumeSumList(state.form);
        let url = window.URL.createObjectURL(
          new Blob([res], { type: "application/vnd.ms-excel" })
        );
        let link = document.createElement("a");
        link.style.display = "none";
        link.href = url;
        link.setAttribute("download", "消费分类汇总报表.xlsx");
        document.body.appendChild(link);
        link.click();
        state.loading = false
      }
      catch {
        state.loading = false
      }
    };

    const handlePageChange = (val) => {
      state.form.pageNum = val;
      getList();
    };
    const handleSizeChange = (val) => {
      state.form.pageNum = 1;
      state.form.pageSize = val;
      getList();
    };

    const reset = () => {
      state.form = {
        pageNum: 1,
        pageSize: 10,
        settlementMethod:"YEAR"
      };
    };
    onMounted(() => {
      getList();
    });
    return {
      column,
      sumMethodList,
      state,
      timeStr,
      exportClick,
      getList,
      handlePageChange,
      handleSizeChange,
      reset,
    };
  },
};
</script>
<style lang="scss" scoped>
:deep(.el-divider--horizontal) {
  margin: 0 0 10px 0;
}

.cashRecharge {
  :deep(.search-box) {
    .el-input__inner {
      width: 193px;
    }

    .el-date-editor {
      width: 400px;
    }
  }
}
</style>