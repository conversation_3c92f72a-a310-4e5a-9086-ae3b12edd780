<template>
  <el-dropdown trigger="click" @command="handleClick">
    <div class="usercontrol">
      <kade-avatar :color="primaryColor" :dark="true" :src="avatar" />
      <span class="username">{{ label }}</span>
      <kade-icon name="icondown" color="#666" />
    </div>
    <template #dropdown>
      <el-dropdown-menu>
        <el-dropdown-item v-for="item in menus" :key="item.action" :command="item.action">{{ item.label }}</el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
</template>
<script>
// 头部用户名头像
import { ElDropdown, ElDropdownMenu, ElDropdownItem } from 'element-plus';
import Icon from './icon';
import Avatar from './avatar';

export default {
  components: {
    'el-dropdown': ElDropdown,
    'el-dropdown-menu': ElDropdownMenu, 
    'el-dropdown-item': ElDropdownItem,
    'kade-icon': Icon,
    'kade-avatar': Avatar,
  },
  emits: ['menuClick'],
  props: {
    label: {
      type: [String, Number],
      default: ''
    },
    avatar: String,
    menus: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      primaryColor: THEMEVARS.primaryColor,
    }
  },
  methods: {
    handleClick(action) {
      this.$emit('menuClick', action);
    }
  },
}
</script>
<style lang="scss">
.usercontrol{
  display: flex;
  justify-content: flex-end;
  align-items: center;
  &:hover{
    .username,i{
      color: $primary-color;
    }
  }
  .username{
    color: #666;
  }
  i{
    color: #666;
    margin-left: 5px;
  }
  .el-image i{
    font-size: 30px!important;
  }
}
</style>