<template>
  <kade-route-card>
    <kade-table-filter @search="handleSearch" @reset="handleReset">
      <el-form inline label-width="90px" size="mini">
        <el-form-item label="关键字">
          <el-input v-model="state.form.keyWord" placeholder="输入人员编号或人员姓名搜索"></el-input>
        </el-form-item>
        <el-form-item label="组织机构">
          <kade-dept-select-tree style="width: 100%" :value="state.form.deptPath" valueKey="deptPath" :multiple="false" @valueChange="(val) => (state.form.deptPath = val.deptPath)" />
        </el-form-item>
        <kade-linkage-select :data="linkageData" :value="state.form" @change="linkageChange" />
        <el-form-item label="请假原因">
          <el-select v-model="state.form.reason" placeholder="全部" size="small">
            <el-option v-for="(item,index) in reasonList" :key="index" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="请假时间">
          <el-date-picker v-model="state.requestDate" type="daterange" range-separator="~" start-placeholder="请选择开始日期" end-placeholder="请选择结算日期" />
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="学生请假记录列表">
      <template #extra>
        <el-button class="btn-purple" icon="el-icon-daochu" size="mini" @click="handleExport">导出</el-button>
      </template>
      <el-table :data="state.data" border v-loading="state.loading">
        <el-table-column prop="userCode" label="人员编号" align="center"></el-table-column>
        <el-table-column prop="userName" label="人员姓名" align="center"></el-table-column>
        <el-table-column prop="deptName" label="组织机构" align="center"></el-table-column>
        <el-table-column prop="leavePeriod" label="请假时间" align="center" show-overflow-tooltip>
          <template #default="scope">
            {{(scope.row.leavePeriod)}}
          </template>
        </el-table-column>
        <el-table-column prop="reason" label="请假原因" align="center">
          <template #default="scope">
            {{dictionaryFilter(scope.row.reason)}}
          </template>
        </el-table-column>
        <el-table-column prop="areaName" label="所属区域" align="center"></el-table-column>
        <el-table-column prop="roomString" label="房间" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column label="操作" align="center" width="80px">
          <template #default="scope">
            <el-button class="green" type="text" @click="details(scope.row)" size="mini">详情</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination v-model:currentPage="state.form.currentPage" v-model:page-size="state.form.pageSize" :page-sizes="[5,10, 20, 30, 40]" :small="small" :disabled="disabled" background layout="total, sizes, prev, pager, next, jumper" :total="state.total" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      </div>
    </kade-table-wrap>
    <kade-leave-details :dialogVisible="state.dialogVisible" :rowData="state.rowData" @close="()=>state.dialogVisible=false" />
  </kade-route-card>

</template>

<script>
import { reactive } from '@vue/reactivity';
import { ElForm, ElFormItem, ElInput, ElSelect, ElOption, ElButton, ElTable, ElTableColumn, ElPagination, ElDatePicker, } from "element-plus"
import { onMounted } from '@vue/runtime-core';
import linkageSelect from '@/applications/eccard-dorm/components/linkageSelect'
import { dateStr } from "@/utils/date.js";
import deptSelectTree from '@/components/tree/deptSelectTree'
import { getLeaveInfo,exportLeaveInfo } from "@/applications/eccard-dorm/api.js"
import { useDict } from "@/hooks/useDict";
import details from "./components/details"
// import { downloadXlsx } from '@/utils/index.js';
const linkageData = {
  area: { label: '区域', valueKey: 'areaPath', key: 'areaPath' },
  building: { label: '楼栋', valueKey: 'buildId' },
  unit: { label: '单元', valueKey: 'unitNum' },
  floor: { label: '楼层', valueKey: 'floorNum' }
}
export default {
  components: {
    ElForm,
    ElFormItem,
    ElInput,
    ElSelect,
    ElOption,
    ElButton,
    ElTable,
    ElTableColumn,
    ElPagination,
    ElDatePicker,
    "kade-leave-details": details,
    "kade-linkage-select": linkageSelect,
    "kade-dept-select-tree":deptSelectTree
  },
  setup() {
    const reasonList = useDict('DORM_LEAVE_REASON')//请假原因
    const state = reactive({
      form: {
        pageSize: 10,
        currentPage: 1,
      },
      data: [],
      dialogVisible: false,
      rowData: {},
      total: 0,
      requestDate: [],
      loading:false,
    });
    const linkageChange=(val)=>{
      state.form={...state.form,...val}
    }
    const getList = async() => {
      console.log(state.form)
      if (state.requestDate && state.requestDate.length) {
        state.form.startDate = dateStr(state.requestDate[0])
        state.form.endDate = dateStr(state.requestDate[1])
      } else {
        delete state.form.startDate
        delete state.form.endDate
      }
      state.loading=true
      try{
        let { data:{list,total}} = await getLeaveInfo(state.form)
        state.data = list
        state.total = total
        state.loading=false
      }
      catch{
          state.loading=false
      }
    };

    const details = (row) => {
      console.log(row)
      state.dialogVisible = true
      state.rowData = row
    };
    const handleSearch = () => {
      getList()
    };
    const handleReset = () => {
      state.form = {
        pageSize: 10,
        currentPage: 1,
      },
      state.requestDate = []
      getList()
    };
    const handleSizeChange = (val) => {
      state.form.currentPage = 1
      state.form.pageSize = val
      getList()
    };
    const handleCurrentChange = (val) => {
      state.form.currentPage = val
      getList()
    }
    const handleExport = async () => {
      let res = await exportLeaveInfo(state.form)
      let url = window.URL.createObjectURL(
        new Blob([res],{type:'application/vnd.ms-excel'})
      )
      let link = document.createElement('a')
      link.href=url
      link.style.display='none'
      link.setAttribute('download','学生请假记录表.xlsx')
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link) 
    }
    onMounted(() => {
      getList();
    });
    return {
      state,
      getList,
      handleSearch,
      linkageChange,
      details,
      reasonList,
      handleReset,
      handleExport,
      linkageData,
      handleSizeChange,
      handleCurrentChange,
    }
  }
}
</script>

<style lang="scss" scoped>
.green :hover {
  text-decoration: underline;
}
:deep([data-v-055d1f34] .el-select .el-input__inner) {
  width: 210px;
}
:deep(.el-select .el-input__inner) {
  height: 28px;
}
:deep(.el-pagination__editor.el-input .el-input__inner) {
  width: 40px;
}
:deep(.el-pagination .el-select .el-input .el-input__inner) {
  width: 100px;
}
:deep(.el-input--mini .el-input__inner) {
  width: 193px;
}
:deep([data-v-055d1f34] .el-select .el-input__inner) {
  width: 193px;
}
:deep(.el-dialog) {
  border-radius: 6px;
  padding-bottom: 50px;
}
:deep(.el-dialog__header) {
  border-bottom: 1px solid #eeeeee;
  margin-bottom: 15px;
}
:deep(.el-input__inner) {
  height: 28px;
}
</style>