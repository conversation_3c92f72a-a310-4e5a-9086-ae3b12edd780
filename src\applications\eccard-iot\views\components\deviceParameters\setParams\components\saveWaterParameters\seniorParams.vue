<template>
  <div class="padding-form-box setBasicParam" v-loading="state.loading">
    <el-form inline size="small" label-width="200px">

      <el-form-item label="一阶段比例(%)：">
        <el-input-number :min="0" v-model="state.form.paramContent.oneStageRatio"></el-input-number>
      </el-form-item>
      <el-form-item label="二阶段比例(%)：">
        <el-input-number :min="0" v-model="state.form.paramContent.twoStageRatio"></el-input-number>
      </el-form-item>
      <el-form-item label="三阶段比例(%)：">
        <el-input-number :min="0" v-model="state.form.paramContent.thirdStageRatio"></el-input-number>
      </el-form-item>
      <el-form-item label="一阶段消费总用水量(升)：">
        <el-input-number :min="0" v-model="state.form.paramContent.oneStageTotalConsumerWater"></el-input-number>
      </el-form-item>
      <el-form-item label="二阶段消费总用水量(升)：">
        <el-input-number :min="0" v-model="state.form.paramContent.twoStageTotalConsumerWater"></el-input-number>
      </el-form-item>
      <el-form-item label="防盗水时间间隔(秒)：">
        <el-input-number :min="0" v-model="state.form.paramContent.waterTheftTimeInterval"></el-input-number>
      </el-form-item>
      <el-form-item label="计量脉冲数：">
        <el-input-number :min="0" v-model="state.form.paramContent.meteringPulseNumber"></el-input-number>
      </el-form-item>
      <el-form-item label="当天最大消费次数：">
        <el-input-number :min="0" v-model="state.form.paramContent.maxConsumerCount"></el-input-number>
      </el-form-item>
      <el-form-item label="当天消费最大使用时间(秒)：">
        <el-input-number :min="0" v-model="state.form.paramContent.maxConsumerTime"></el-input-number>
      </el-form-item>
      <el-form-item label="两次消费时间间隔(分)：">
        <el-input-number :min="0" v-model="state.form.paramContent.consumerInterval"></el-input-number>
      </el-form-item>
      <el-form-item label="卡片最大余额(元)：">
        <el-input-number :min="0" v-model="state.form.paramContent.cardMaxBalance"></el-input-number>
      </el-form-item>
      <el-form-item label="防盗水脉冲数：">
        <el-input-number :min="0" v-model="state.form.paramContent.antiTheftWaterPulseNumber"></el-input-number>
      </el-form-item>
      <el-form-item label="地址锁卡模式：">
        <el-select v-model="state.form.paramContent.addressLockCardMode">
          <el-option v-for="(item,index) in dictListFnc().addressLockCardMode" :key="index" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="启用红外控制功能：">
        <el-select v-model="state.form.paramContent.infraredControlFunction">
          <el-option v-for="(item,index) in dictListFnc().infraredControlFunction" :key="index" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="是否允许使用补助：">
        <el-select v-model="state.form.paramContent.whetherSubsidiesAllowed">
          <el-option v-for="(item,index) in dictListFnc().whetherSubsidiesAllowed" :key="index" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
    </el-form>
  </div>
  <div style="text-align: center">
    <el-button size="mini" @click="beforeClose()">取消</el-button>
    <el-button size="mini" type="primary" @click="saveClick()">保存</el-button>
  </div>
</template>


<script>
import {
  ElButton,
  ElForm,
  ElFormItem,
  ElInputNumber,
  ElMessage,
  ElOption,
  ElSelect,
} from "element-plus";
import { onMounted, reactive } from "vue";
import { useStore } from "vuex";
const defaultParamsFnc = () => {
  return {
    oneStageRatio: 1,	              //一阶段比列（%）	integer(int32)	
    twoStageRatio: 1,	              //二阶段比列（%）	integer(int32)	
    thirdStageRatio: 1,	            //三阶段比列（%）	integer(int32)	
    oneStageTotalConsumerWater: 1,	//一阶段消费总用水量（升）	integer(int32)	
    twoStageTotalConsumerWater: 1,	//二阶段消费总用水量（升）	integer(int32)	
    waterTheftTimeInterval: 1,	    //防盗水时间间隔（秒）	integer(int32)	
    meteringPulseNumber: 1,	        //计量脉冲数	integer(int32)	
    maxConsumerCount: 1,	          //当天最大消费次数	integer(int32)	
    maxConsumerTime: 1,	            //当天消费最大使用时间(秒)	integer(int32)	
    cardMaxBalance: 1,	            //卡片最大余额(元)	integer(int32)	
    consumerInterval: 1,	          //两次消费时间间隔(分)	integer(int32)	
    antiTheftWaterPulseNumber: 1,	  //防盗水脉冲数	integer(int32)	
    addressLockCardMode: "OFF",	      //地址锁卡模式	string	
    infraredControlFunction: "OFF",	  //启用红外控制功能	string	
    whetherSubsidiesAllowed: "OFF",	  //是否允许使用补助	string
  };
};

export default {
  components: {
    ElInputNumber,
    ElForm,
    ElFormItem,
    ElSelect,
    ElOption,
    ElButton,
  },
  setup() {
    const store = useStore();
    const state = reactive({
      loading: false,
      form: {
        paramContent: defaultParamsFnc(),
        id: "",
        paramId: "",
        paramType: "SENIOR",
      },
    });
    const dictListFnc = () => {
      return store.state.deviceParameters[store.state.app.activeTab].dict
    }
    const saveClick = async () => {
      state.loading = true;
      let storeData = store.state.deviceParameters[store.state.app.activeTab]
      state.form.paramId = storeData.selectRow.id;
      let fn = state.form.id ? storeData.TabModule.detailsEditFnc : storeData.TabModule.detailsAddFnc

      let params = { ...state.form }
      console.log(params.paramContent);
      params.paramContent = JSON.stringify(params.paramContent)
      let { message, code } = await fn(params);
      if (code === 0) {
        setTimeout(() => {
          ElMessage.success(message);
          store.dispatch("deviceParameters/getParams", store.state.deviceParameters[store.state.app.activeTab].selectRow.id);
          state.loading = false;
        }, 1500);
      }
    };

    const beforeClose = () => {
      store.commit("deviceParameters/updateState", {
        key: store.state.app.activeTab,
        childKey: "isSetParams",
        payload: false,
      });
    };
    onMounted(() => {
      let val = store.state.deviceParameters[store.state.app.activeTab].detailsParams
      let arr = val.filter(item => item.paramType == 'SENIOR')
      if (arr && arr.length) {
        state.form = { ...arr[0] }
        state.form.paramContent = JSON.parse(state.form.paramContent)
      } else {
        //设置默认选项
        state.form.id = "";
        state.form.paramContent = defaultParamsFnc();
      }
    });
    return {
      state,
      dictListFnc,
      saveClick,
      beforeClose,
    };
  },
};
</script>

<style lang="scss" scoped>
.setBasicParam {
  border: 1px solid #dcdfe6;
  border-radius: 5px;
  margin-bottom: 10px;
}
:deep(.el-form-item__content) {
  width: 200px;
  .el-input-number {
    width: 100%;
  }
  .el-select {
    width: 100% !important;
  }
  .el-date-editor.el-input,
  .el-date-editor.el-input__inner {
    width: 200px;
  }
}
</style>
