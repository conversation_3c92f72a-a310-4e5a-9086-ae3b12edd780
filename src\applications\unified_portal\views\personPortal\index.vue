<template>
    <el-row :gutter="20">
        <el-col class="card-col" v-bind="personalPortalCardLayout">
            <el-card :body-style="{ padding: 0 }">
                <template #header>
                    <div class="card-header">
                        <span>应用中心</span>
                    </div>
                </template>
                <div class="card-content pon-card-con">
                  <app-list-auth />
                </div>             
            </el-card>                    
        </el-col>
        <el-col class="card-col" v-bind="personalPortalCardLayout">
            <el-card :body-style="{ padding: 0 }">
                <template #header>
                    <div class="card-header">
                        <span>门户管理</span>
                    </div>
                </template>
                <div class="card-content pon-card-con">
                  <app-list-portal />
                </div>             
            </el-card>                    
        </el-col>        
    </el-row>
</template>
<script>
import { ElCard, ElRow, ElCol } from 'element-plus';
import { personalPortalCardLayout } from '@/service/dictionary';
import AuthAppList from './components/authAppList';
import PortalAppList from './components/portalAppList';
export default {
    components: {
        'el-card': ElCard,
        'el-row': ElRow,
        'el-col': ElCol,
        'app-list-auth': AuthAppList,
        'app-list-portal': PortalAppList,
    },
    setup() {
        return {
            personalPortalCardLayout,
        }
    }
}
</script>
<style lang="scss" scoped>
  .pon-card-con{
    height: 790px;
    height: calc(100vh - 145px);
  }
</style>