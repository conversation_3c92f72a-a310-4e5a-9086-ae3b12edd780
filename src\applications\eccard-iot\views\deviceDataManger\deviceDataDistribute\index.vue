<template>
  <kade-route-card style="height: 100%">
    <div class="deviceRealTimeMonitor">
      <kade-table-wrap title="数据分发列表" style="width: 300px; height: 100%; margin-right: 20px;min-width:300px"
        v-loading="state.loading">
        <el-divider></el-divider>
        <div class="search-box">
          <el-input v-model="state.form.distributeName" placeholder="请输入关键字搜索" size="small" clearable>
            <template #append>
              <el-button icon="el-icon-sousuo" @click="search()">查询</el-button>
            </template>
          </el-input>
          <div class="list-box" v-infinite-scroll="load" :infinite-scroll-immediate="false">
            <div class="list-item" :class="index == state.dataIndex && 'item-active'"
              @click="dataNameClick(item, index)" v-for="(item, index) in state.deviceList" :key="index">
              <i class="el-icon-liebiaolist46"></i>
              <div class="item-name">{{ item.distributeName }}</div>
            </div>
          </div>
          <el-button @click="handleAdd()" size="mini" icon="el-icon-plus" type="success">添加</el-button>
          <el-button @click="handleEdit()" size="mini" icon="el-icon-edit" type="primary">编辑</el-button>
          <el-button @click="handleDel()" size="mini" icon="el-icon-close" type="danger">删除</el-button>
        </div>
      </kade-table-wrap>
      <kade-tab-wrap :tabs="tabs" v-model="state.tab" style="flex: 1; height: 100%">
        <template #yysq>
          <kade-app-auth :rowData="state.rowData" :appList="state.appList" />
        </template>
        <template #bdsb>
          <kade-bind-device-list :rowData="state.rowData" />
        </template>
      </kade-tab-wrap>
      <kade-data-distribute-edit v-model="state.isEdit" :rowData="state.rowData" :editType="state.editType"
        @update:modelValue="close" />
    </div>
  </kade-route-card>
</template>
<script>
import { onMounted, reactive } from "vue";
import {
  ElDivider,
  ElInput,
  ElButton,
  ElMessage,
  ElMessageBox
} from "element-plus";
import { dataDistributeList, dataDistributeDel, appList } from "@/applications/eccard-iot/api";
import edit from "./components/edit.vue"
import appAuth from "./components/appAuth.vue"
import bindDeviceList from "./components/bindDeviceList.vue"
const tabs = {
  yysq: {
    name: "yysq",
    label: "应用授权",
  },
  bdsb: {
    name: "bdsb",
    label: "绑定设备",
  }

};
export default {
  components: {
    ElDivider,
    ElInput,
    ElButton,
    "kade-data-distribute-edit": edit,
    "kade-app-auth": appAuth,
    "kade-bind-device-list": bindDeviceList,
  },
  setup() {
    const state = reactive({
      tab: "yysq",
      areaKeyword: "",
      loading: false,
      isEdit: false,
      dataIndex: 0,
      editType: "",
      form: {
        pageNum: 1,
        pageSize: 20
      },
      deviceList: [],
      rowData: {},
      appList: []
    });
    const getList = async () => {

      for (let key in state.form) {
        if (!state.form[key]) {
          delete state.form[key]
        }
      }
      state.loading = true;
      try {
        let { data: { list }, code } = await dataDistributeList(state.form);
        if (code === 0) {
          state.deviceList = state.deviceList.concat(list);
          if (state.deviceList.length) {
            state.rowData = state.deviceList[state.dataIndex] ? state.deviceList[state.dataIndex] : state.deviceList[0]
          } else {
            state.rowData = {}
          }
        }
        state.loading = false;
      }
      catch {
        state.loading = false;
      }
    };
    const getAppList = async () => {
      let { data } = await appList()
      state.appList = data
    }
    const load = () => {
      state.form.pageNum++
      getList()
    }
    const search = () => {
      state.form.pageNum = 1
      state.deviceList = []
      getList()
    }
    const handleAdd = () => {
      state.editType = 'add'
      state.isEdit = true
    }
    const handleEdit = () => {
      if (!state.rowData.id) {
        return ElMessage.error("请选择数据分发！")
      }
      state.editType = 'edit'
      state.isEdit = true
    }
    const handleDel = () => {
      if (!state.rowData.id) {
        return ElMessage.error("请选择数据分发！")
      }
      ElMessageBox.confirm("确认删除?", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        let { code, message } = await dataDistributeDel(state.rowData.id);
        if (code === 0) {
          ElMessage.success(message);
          state.form.pageNum = 1
          state.deviceList = []
          getList();
        }
      });
    }
    const dataNameClick = (item, index) => {
      console.log(item, index);
      state.dataIndex = index
      state.rowData = item
    }
    const close = (val) => {
      if (val) {
        state.form.pageNum = 1
        state.deviceList = []
        getList()
      }
      state.isEdit = false
    }
    onMounted(() => {
      getList();
      getAppList()
    });
    return {
      tabs,
      state,
      load,
      search,
      dataNameClick,
      handleAdd,
      handleEdit,
      handleDel,
      close
    };
  },
};
</script>
<style lang="scss" scoped>
.deviceRealTimeMonitor {
  height: 79vh;
  display: flex;
  justify-content: space-between;

  .search-box {
    padding: 10px;
    height: 70vh;
  }

  .list-box {
    margin: 10px 0;
    height: 90%;
    overflow-y: scroll;

    .list-item {
      display: flex;
      align-items: center;
      padding: 10px;

      .item-name {
        margin-left: 10px;
      }
    }

    .item-active {
      background: #409eff;
      color: #fff;
    }
  }
}

:deep(.el-tabs__item) {
  height: 52px;
  line-height: 52px;
}

:deep(.el-divider--horizontal) {
  margin: 0;
}

:deep(.el-dialog__footer) {
  text-align: center;
}
</style>