<template>
  <div class="bind-device-box" v-loading="state.loading">
    <kade-table-filter @search="handleSearch" @reset="handleReset">
      <el-form inline label-width="90px" size="mini">
        <el-form-item label="组织机构">
          <kade-dept-select-tree style="width: 100%" :value="state.form.deptPath" valueKey="deptPath" :multiple="false"
            @valueChange="(val) => (state.form.deptPath = val.deptPath)" />
        </el-form-item>
        <el-form-item label="身份类别">
          <el-select v-model="state.form.userRole" clearable>
            <el-option v-for="(item, index) in state.roleList" :key="index" :label="item.roleName" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="性别">
          <el-select v-model="state.form.userSex" clearable>
            <el-option v-for="(item, index) in sexList" :key="index" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="编号/姓名">
          <el-input placeholder="请输入" v-model="state.form.keyWord" clearable></el-input>
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="组内人员列表" v-loading="state.loading">
      <template #extra>
        <el-button @click="state.isBind = true" class="btn-green" icon="el-icon-plus" size="mini" :disabled="!rowData.id">添加人员</el-button>
        <el-button @click="handleBatchDel()" class="btn-purple" icon="el-icon-close" size="mini">批量删除</el-button>
      </template>
      <el-table :data="state.dataList" @selection-change="handleSelectChange" border>
        <el-table-column prop="" show-overflow-tooltip type="selection" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="userCode" label="人员编号" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="userName" label="人员姓名" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="deptName" label="组织机构" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="userSex" label="性别" align="center">
          <template #default="scope">
            {{ dictionaryFilter(scope.row.userSex) }}
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip prop="roleName" label="身份类别" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="createTime" label="绑定时间" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip label="操作" align="center">
          <template #default="scope">
            <el-button @click="handleDel(scope.row)" type="text" size="mini">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination v-model:currentPage="state.form.currentPage" v-model:page-size="state.form.pageSize"
          :page-sizes="[6,10, 20, 30, 40]" :small="small" :disabled="disabled" background
          layout="total, sizes, prev, pager, next, jumper" :total="state.total" @size-change="handleSizeChange"
          @current-change="handleCurrentChange" />
      </div>
    </kade-table-wrap>
    <kade-bind-device v-model="state.isBind" :rowData="rowData" @update:modelValue="close" />
  </div>
</template>
<script>
import { ElButton, ElForm, ElFormItem, ElInput, ElSelect, ElOption, ElTable, ElTableColumn, ElPagination, ElMessageBox, ElMessage } from "element-plus"
import { reactive, onMounted } from 'vue'
import { acsUserGroupList, acsUserGroupDel, acsUserGroupBatchDel } from "@/applications/eccard-uac/api";
import {
  getRolelist
} from "@/applications/eccard-basic-data/api";
import bind from "./bind.vue"
import { watch } from '@vue/runtime-core';
import { useDict } from "@/hooks/useDict.js";
import deptSelectTree from "@/components/tree/deptSelectTree.vue";
export default {
  components: {
    ElButton, ElTable, ElTableColumn, ElPagination, ElForm, ElFormItem, ElInput, ElSelect, ElOption,
    "kade-bind-device": bind,
    "kade-dept-select-tree": deptSelectTree,
  },
  props: {
    rowData: {
      type: Object,
      default: null
    }
  },
  setup(props) {
    const sexList = useDict("SYS_SEX")
    const state = reactive({
      loading: false,
      isBind: false,
      roleList: [],
      selectList: [],
      dataList: [],
      form: {
        currentPage: 1,
        pageSize: 6,
      },
      total: 0
    })

    watch(() => props.rowData, val => {
      if (val.id) {
        state.isBind = false
        getList()
      }
    })
    const queryRolelist = async () => {
      let { data } = await getRolelist();
      state.roleList = data;
    };
    const getList = async () => {
      for (let key in state.form) {
        if (!state.form[key]) {
          delete state.form[key]
        }
      }
      state.form.groupId = props.rowData.id
      state.loading = true
      try {
        let { data: { list, total } } = await acsUserGroupList(state.form)
        state.dataList = list
        state.total = total
        state.loading = false
      }
      catch {
        state.loading = false
      }
    }
    const handleSelectChange = (val) => {
      state.selectList = val
    }
    const handleDel = row => {
      ElMessageBox.confirm("确认删除?", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        let { code, message } = await acsUserGroupDel(row.id);
        if (code === 0) {
          ElMessage.success(message);
          getList();
        }
      });
    }
    const handleBatchDel = () => {
      if (!state.selectList.length) {
        return ElMessage.error("请选择需要删除的人员！")
      }
      ElMessageBox.confirm("确认删除已选择人员?", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        let { code, message } = await acsUserGroupBatchDel(state.selectList.map(item => item.id));
        if (code === 0) {
          ElMessage.success(message);
          getList();
        }
      });
    }
    const handleSearch = () => {
      getList();
    }
    const handleReset = () => {
      state.form = {
        currentPage: 1,
        pageSize: 6,
      }
    }
    const handleCurrentChange = (val) => {
      state.form.currentPage = val
      getList()
    };
    const handleSizeChange = (val) => {
      state.form.currentPage = 1
      state.form.pageSize = val
      getList()
    };
    const close = val => {
      if (val) {
        getList()
      }
      state.isBind = false
    }
    onMounted(() => {
      if (props.rowData.id) {
        getList()
      }
      queryRolelist()
    })
    return {
      sexList,
      state,
      handleSelectChange,
      handleDel,
      handleBatchDel,
      handleSearch,
      handleReset,
      handleCurrentChange,
      handleSizeChange,
      close
    }
  }
}
</script>
<style lang="scss" scoped>
.bind-device-box {
  width: 100%;
  height: 100%;
}
</style>