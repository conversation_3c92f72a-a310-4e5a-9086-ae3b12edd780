<template>
  <kade-route-card>
    <kade-table-filter @search="handleSearch" @reset="handleReset">
      <el-form inline label-width="90px" size="mini">
        <kade-linkage-select :value="state.form" :data="linkageData" @change="linkageChange" />
        <el-form-item label="房间">
          <el-input clearable v-model="state.form.roomName" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="设备类型">
          <el-select clearable v-model="state.form.deviceType" placeholder="请选择">
            <el-option v-for="(item,index) in deviceTypeList" :key="index" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="设备机号">
          <el-input clearable v-model="state.form.deviceNo" placeholder="请输入"></el-input>
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="房间设备列表" v-loading="state.loading">
      <template #extra>
        <el-button type="primary" icon="el-icon-top" size="mini" @click="state.isImport=true">导入</el-button>
        <el-button type="success" icon="el-icon-plus" size="mini" @click="add()">新增</el-button>
      </template>
      <el-table :data="state.dataList" border height="55vh">
        <el-table-column show-overflow-tooltip prop="areaName" label="区域" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="" label="房间" align="center" width="300px">
          <template #default="scope">
            {{`${scope.row.buildName}>${scope.row.unitNum}单元>${scope.row.floorNum}楼>${scope.row.roomName}`}}
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip prop="deviceTypeName" label="设备类型" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="deviceNo" label="设备机号" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="deviceName" label="设备名称" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="createTime" label="绑定时间" align="center"></el-table-column>
        <el-table-column label="操作" align="center" width="100px">
          <template #default="scope">
            <el-button class="green" type="text" size="mini" @click="del(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination v-model:currentPage="state.form.currentPage" v-model:page-size="state.form.pageSize" :page-sizes="[10, 20, 30, 40]" background layout="total, sizes, prev, pager, next, jumper" :total="state.total" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      </div>
    </kade-table-wrap>
    <kade-room-device-add v-model:modelValue="state.isEdit" @update:modelValue="close"></kade-room-device-add>
    <kade-room-device-import v-model:modelValue="state.isImport" @update:modelValue="close"></kade-room-device-import>
  </kade-route-card>
</template>

<script>
import { reactive } from "@vue/reactivity";
import {
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElTable,
  ElButton,
  ElTableColumn,
  ElPagination,
  ElMessageBox,
  ElMessage
} from "element-plus";
import { useDict } from "@/hooks/useDict.js";
import { roomDeviceList, roomDeviceDelete } from "@/applications/eccard-dorm/api";
import linkageSelect from "@/applications/eccard-dorm/components/linkageSelect.vue"
import add from "./components/add";
import roomDeviceImport from "./components/roomDeviceImport";

import { onMounted } from '@vue/runtime-core';
const linkageData = {
  area: { label: '区域', valueKey: "areaId", key: "id" },
  building: { label: '楼栋', valueKey: "buildId" },
  unit: { label: '单元', valueKey: "unitNum" },
  floor: { label: '楼层', valueKey: "floorNum" },
}
const column = [
  { label: "区域", prop: "areaName" },
  { label: "房间", prop: "", width: "300px" },
  { label: "设备类型", prop: "" },
  { label: "设备机号", prop: "" },
  { label: "设备名称", prop: "" },
  { label: "绑定时间", prop: "" }
];
export default {
  components: {
    ElForm,
    ElFormItem,
    ElInput,
    ElSelect,
    ElOption,
    ElTable,
    ElButton,
    ElTableColumn,
    ElPagination,
    "kade-linkage-select": linkageSelect,
    "kade-room-device-add": add,
    "kade-room-device-import":roomDeviceImport
  },
  setup() {
    const deviceTypeList = useDict("DORM_ROOM_DEVICE_TYPE");
    const state = reactive({
      loading: false,
      isEdit: false,
      isImport:false,
      form: {
        currentPage: 1,
        pageSize: 10,
      },
      total: 0,
      dataList: [],

    });
    const getList = async () => {
      state.loading = true
      try {
        let { data: { list, total } } = await roomDeviceList(state.form)
        state.dataList = list
        state.total = total
        state.loading = false
      }
      catch {
        state.loading = false
      }
    };
    const linkageChange = (val) => {
      state.form = { ...state.form, ...val }
    }
    const add = () => {
      state.isEdit = true;
    };
    const del = (row) => {
      ElMessageBox.confirm("确认删除?", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        let { code, message } = await roomDeviceDelete(row.id);
        if (code === 0) {
          ElMessage.success(message);
          getList();
        }
      });
    };
    const handleSearch = () => {
      state.form.currentPage = 1
      state.form.pageSize = 10
      getList()
    };
    const handleReset = () => {
      state.form = {
        pageSize: 10,
        currentPage: 1
      }
    };
    const handleCurrentChange = (val) => {
      state.form.currentPage = val
      getList()
    };
    const handleSizeChange = (val) => {
      state.form.currentPage = 1
      state.form.pageSize = val
    };
    const close = val => {
      if (val) {
        getList()
      }
      state.isEdit = false
      state.isImport = false
    }
    onMounted(() => {
      getList()
    })
    return {
      linkageData,
      deviceTypeList,
      state,
      column,
      linkageChange,
      add,
      del,
      handleSearch,
      handleReset,
      handleCurrentChange,
      handleSizeChange,
      close
    };
  },
};
</script>

<style lang="scss" scoped>
:deep(.el-input--mini .el-input__inner) {
  width: 198px;
}
:deep(.el-pagination .el-select .el-input .el-input__inner) {
  width: 100px;
}
:deep(.el-pagination__editor.el-input .el-input__inner) {
  width: 46px;
}
:deep(.el-divider--horizontal) {
  margin: 10px 0;
}
:deep(.el-dialog__footer) {
  text-align: center;
}
</style>