<template>
  <div class="violation-msg">
    <div class="violation-title">违规违纪</div>
    <div class="table-head">
      <div class="table-head-item">房间号</div>
      <div class="table-head-item">违规人员</div>
      <div class="table-head-item">违规时间</div>
      <div class="table-head-item">违规类型</div>
    </div>
    <el-carousel height="100%" direction="vertical" :autoplay="true" :interval="3000" indicator-position="none">
      <el-carousel-item v-for="item in 4" :key="item" style="color:#fff">
        <div class="table-body" v-for="item in 5" :key="item">
          <div class="table-body-item">101</div>
          <div class="table-body-item">张三1233333331312</div>
          <div class="table-body-item">2022-01-01</div>
          <div class="table-body-item">违规用电</div>
        </div>
      </el-carousel-item>
    </el-carousel>
  </div>
</template>
<script>
import { ElCarousel, ElCarouselItem } from "element-plus"
export default {
  components: {
    ElCarousel, ElCarouselItem
  },
  setup() {

  }
}
</script>\
<style lang="scss" scoped>
.violation-msg {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .violation-title {
    font-weight: 700;
    font-size: 18px;
    color: #0EE4F9;
    margin: 5px 0 10px;
  }

  .table-head {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    background: #1e408a;

    .table-head-item {
      text-align: center;
      flex: 1;
      font-weight: 700;
      font-style: normal;
      font-size: 16px;
      color: rgba(255, 255, 255, 0.8);
    }
  }

  .table-body {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;

    .table-body-item {
      text-align: center;
      flex: 1;
      font-weight: 700;
      font-style: normal;
      font-size: 16px;
      color: rgba(255, 255, 255, 0.8);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

.el-carousel {
  width: 100%;
  height: 100%;
}
</style>