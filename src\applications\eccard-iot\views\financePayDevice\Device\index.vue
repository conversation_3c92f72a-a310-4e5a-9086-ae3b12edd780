<template>
  <div class="Device">
    <kade-route-card style="height: auto">
      <kade-table-filter @search="getList()" @reset="reset()">
        <el-form inline size="small" label-width="100px">
          <el-form-item label="终端型号">
            <el-select clearable v-model="state.form.deviceModel" placeholder="全部">
              <el-option v-for="(item, index) in state.listData.modelList" :key="index" :label="item.productMode"
                :value="item.productMode">
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="所属区域">
            <kade-area-select-tree style="width: 100%" :value="state.form.areaId" valueKey="id" :multiple="false"
              @valueChange="(val) => (state.form.areaId = val.id)" />
          </el-form-item>

          <el-form-item label="机号">
            <el-input placeholder="请输入" v-model="state.form.deviceNo"></el-input>
          </el-form-item>

          <el-form-item label="所属商户">
            <el-select clearable v-model="state.form.merchantId" placeholder="全部">
              <el-option v-for="(item, index) in state.listData.merchantList" :key="index" :label="item.merchantName"
                :value="item.merchantId">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="设备状态">
            <el-select clearable v-model="state.form.deviceStatus" placeholder="全部">
              <el-option v-for="(item, index) in state.listData.deviceStaticList" :key="index" :label="item.dictValue"
                :value="item.dictCode">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="设备IP">
            <el-input placeholder="请输入" v-model="state.form.deviceIp"></el-input>
          </el-form-item>
          <el-form-item label="设备名称">
            <el-input placeholder="请输入" v-model="state.form.deviceName"></el-input>
          </el-form-item>
          <el-form-item label="所属工作站">
            <el-select clearable v-model="state.form.workstationId" placeholder="全部">
              <el-option v-for="(item, index) in state.listData.workStationList" :key="index" :label="item.name"
                :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="连接类型">
            <el-select clearable v-model="state.form.deviceConnectType" placeholder="全部">
              <el-option v-for="(item, index) in state.listData.deviceConnectTypeList" :key="index"
                :label="item.dictValue" :value="item.dictCode">
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </kade-table-filter>
      <kade-table-wrap title="智能消费终端列表">
        <template #extra>
          <el-button @click="edit({})" icon="el-icon-daoru" size="small" class="btn-yellow">新增
          </el-button>
          <el-button @click="state.isShowAddBatch = true" icon="el-icon-daoru" size="small" class="btn-purple">批量新增
          </el-button>
          <el-button @click="replaceDevice()" icon="el-icon-daoru" size="small" class="btn-green">更换设备
          </el-button>
          <el-button @click="exportClick()" icon="el-icon-daoru" size="small" class="btn-blue">导出
          </el-button>
        </template>
        <el-table style="width: 100%" :data="state.deviceList" @row-click="selectCurrentRow" ref="multipleTable"
          v-loading="state.loading" height="55vh" highlight-current-row border stripe>
          <!--           <el-table-column
            type="selection"
            width="55"
            align="center"
          ></el-table-column> -->
          <el-table-column show-overflow-tooltip width="150" label="终端型号" prop="deviceModel" align="center">
          </el-table-column>
          <el-table-column show-overflow-tooltip width="153" prop="areaName" label="所属区域" align="center">
          </el-table-column>

          <el-table-column show-overflow-tooltip label="所属商户" prop="merchantName" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip label="设备机号" prop="deviceNo" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip label="设备名称" prop="deviceName" align="center">
          </el-table-column>
          <el-table-column show-overflow-tooltip label="参数名称" prop="paramName" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip label="设备状态" prop="deviceStatus" align="center">
            <template #default="scope">
              {{
              filterDictionary(
              scope.row.deviceStatus,
              state.listData.deviceStaticList
              )
              }}
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip label="连接类型" prop="deviceConnectType" align="center"><template
              #default="scope">
              {{
              filterDictionary(
              scope.row.deviceConnectType,
              state.listData.deviceConnectTypeList
              )
              }}
            </template></el-table-column>
          <el-table-column show-overflow-tooltip label="设备IP" prop="deviceIp" align="center"></el-table-column>
          <el-table-column label="操作" align="center" width="220" fixed="right">
            <template #default="scope">
              <span class="details" @click="listDetails(scope.row)">
                详情
              </span>
              <span class="details" @click="edit(scope.row)"> 编辑 </span>
              <span class="details" @click="del(scope.row)"> 删除 </span>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination">
          <el-pagination background :current-page="state.form.pageNum" :page-size="state.form.pageSize"
            layout="total, sizes, prev, pager, next, jumper" :page-sizes="[6, 10, 20, 50, 100]" :total="state.total"
            @current-change="handlePageChange" @size-change="handleSizeChange">
          </el-pagination>
        </div>
      </kade-table-wrap>
    </kade-route-card>
    <!-- 
    <kade-add-terminal title="新增设备" :isShow="state.isShowAdd" :listData="state.listData" @off="state.isShowAdd = false"
      @success="
  getList();
state.isShowAdd = false;
      " /> -->
    <kade-add-batch-terminal title="批量新增设备" :isShow="state.isShowAddBatch" :listData="state.listData"
      @off="state.isShowAddBatch = false" @success="
  getList();
state.isShowAddBatch = false;
      " />
    <kade-add-replace-record title="新增设备更换记录" :isShow="state.isShowAddReplace" @off="state.isShowAddReplace = false" />
    <kade-device-detail title="智能消费终端详情" />

    <kade-edit-terminal :isShow="state.isEdit" :listData="state.listData" @close="close"/>
  </div>
</template>

<script>
import {
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElTable,
  ElTableColumn,
  ElPagination,
  ElMessage,
  ElMessageBox,
} from "element-plus";
import { reactive } from "@vue/reactivity";
import {
  devicePage,
  getModel,
  getTgetAreaCheckListype,
  // getType,
  exportList,
  delDavice,
  getDeviceParamList,
  getMerchantList,
  getWorkStationList,
} from "@/applications/eccard-iot/api";
import { onMounted } from "@vue/runtime-core";
import {
  tradeMode,
} from "@/applications/eccard-finance/api";
import areaSelectTree from "@/components/tree/areaSelectTree.vue";

/* import AddTerminal from "./components/AddTerminal.vue"; */
import AddBatchTerminal from "./components/AddBatchTerminal.vue";
import AddreplaceRecord from "./components/AddreplaceRecord.vue";
import deviceDetail from "./components/deviceDetail.vue";
import editTerminal from "./components/editTerminal.vue";
import { useStore } from "vuex";

export default {
  components: {
    "el-button": ElButton,
    "el-input": ElInput,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    "el-select": ElSelect,
    "el-option": ElOption,
    "el-table": ElTable,
    "el-table-column": ElTableColumn,
    "el-pagination": ElPagination,
    "kade-area-select-tree": areaSelectTree,
    /*     "kade-add-terminal": AddTerminal, */
    "kade-add-batch-terminal": AddBatchTerminal,
    "kade-add-replace-record": AddreplaceRecord,
    "kade-device-detail": deviceDetail,
    "kade-edit-terminal": editTerminal,
  },

  setup() {
    const store = useStore();
    const state = reactive({
      loading: false,
      isShowAdd: false,
      isShowAddBatch: false,
      isShowAddReplace: false,
      isShowDeviceDetail: false,
      isShowEdit: false,
      form: {
        pageSize: 10,
        pageNum: 1,
        deviceType: "智能消费终端",
      },
      deviceList: [],
      total: 0,
      deviceDatail: "",
      listData: {
        modelList: [], //终端型号
        tgetAreaCheckList: [], //所属区域
        typelList: [], //终端类型
        deviceParamList: [], //参数列表
        merchantList: [], //商户列表
        workStationList: [], //工作站列表
        deviceStaticList: JSON.parse(
          localStorage.getItem("kade_cache_dictionary")
        ).filter((item) => item.dictType == "SYS_DEVICE_STATICE"), //设备使用状态
        deviceConnectTypeList: JSON.parse(
          localStorage.getItem("kade_cache_dictionary")
        ).filter((item) => item.dictType == "SYS_DEVICE_CONNECT_TYPE"), //连接类型
      },
    });

    const filterDictionary = (val, dictionaryList) => {
      for (let i of dictionaryList) {
        if (val == i.dictCode) {
          return i.dictValue;
        }
      }
    };
    //获取终端型号
    const queryModel = async () => {
      let { data } = await getModel({ productClass: "SMART_CONSUMPTION_TERMINAL" });
      state.listData.modelList = data;
    };
    //获取所属区域
    const queryTgetAreaCheckList = async () => {
      let { data } = await getTgetAreaCheckListype();
      state.listData.tgetAreaCheckList = data;
    };
    //获取支付方式
    const getTradeModeList = () => {
      tradeMode({ costType: 201 }).then((res) => {
        state.listData.tradeModeList = res.data;
      });
    };
    //获取参数列表
    const queryDeviceParamList = async () => {
      let { data } = await getDeviceParamList({ paramType: "智能消费终端" });
      state.listData.deviceParamList = data;
    };

    //获取商户列表
    const queryMerchantList = async () => {
      let { data } = await getMerchantList();
      state.listData.merchantList = data;
    };

    //获取工作站
    const queryWorkStationList = async () => {
      let { data: { list } } = await getWorkStationList();
      state.listData.workStationList = list;
    };
    const getList = async () => {
      state.loading = true;
      for (let key in state.form) {
        if (!state.form[key]) {
          delete state.form[key];
        }
      }
      let { data } = await devicePage(state.form);
      console.log(data);
      state.loading = false;
      state.deviceList = data.list;
      state.total = data.total;
    };

    const selectCurrentRow = (row) => {
      let data = { ...row };
      store.commit("deviceData/updateState", {
        key: "deviceDetail",
        payload: data,
      });
    };
    const replaceDevice = () => {
      if (!store.state.deviceData.deviceDetail) {
        ElMessage.error("请选择更换设备！");
      } else {
        store.commit("deviceData/updateState", {
          key: "isreplace",
          payload: {
            isShow: true,
            data: {}
          }
        });
      }
    };
    const listDetails = (row) => {
      console.log(row);
      store.commit("deviceData/updateState", {
        key: "isShowDeviceDetail",
        payload: true,
      });
    };

    const edit = (row) => {
      let data = { ...row };
      store.commit("deviceData/updateState", {
        key: "deviceDatail",
        payload: data,
      });
      state.isEdit = true;
    };

    const del = async (row) => {
      console.log(row);
      ElMessageBox.confirm(`确认删除?`, {
        type: "warning",
        closeOnPressEscape: false,
        closeOnClickModal: false,
      }).then(async () => {
        try {
          const { message } = await delDavice(row.id);
          ElMessage.success(message);
          getList();
        } catch (e) {
          throw new Error(e.message);
        }
      });
    };

    const exportClick = async () => {
      let data = state.form;
      delete data.pageSize;
      delete data.pageNum;
      let res = await exportList(data);
      let url = window.URL.createObjectURL(
        new Blob([res], { type: "application/vnd.ms-excel" })
      );
      let link = document.createElement("a");
      link.style.display = "none";
      link.href = url;
      link.setAttribute("download", "消费机设备信息表.xlsx");
      document.body.appendChild(link);
      link.click();
    };
    const handlePageChange = (val) => {
      state.form.pageNum = val;
      getList();
    };
    const handleSizeChange = (val) => {
      state.form.pageNum = 1;
      state.form.pageSize = val;
      getList();
    };
    //重置
    const reset = () => {
      state.form = { pageSize: 10, pageNum: 1, deviceType: "智能消费终端" };
    };
    const close=(val)=>{
      if(val){
        getList();
      }
      state.isEdit=false
    }

    onMounted(() => {
      getList();
      queryModel();
      queryTgetAreaCheckList();
      // queryType();
      getTradeModeList()
      queryDeviceParamList();
      queryMerchantList();
      queryWorkStationList();
    });
    return {
      state,
      getList,
      selectCurrentRow,
      replaceDevice,
      edit,
      del,
      exportClick,
      listDetails,
      filterDictionary,
      handlePageChange,
      handleSizeChange,
      reset,
      close
    };
  },
};
</script>
<style lang="scss" scoped>
.details {
  color: #1abc9c;
  margin: 0 10px;
}

.el-divider--horizontal {
  margin: 0 0 10px;
}

.search-box {
  padding: 0 20px;
  margin-top: 20px;
}

:deep(.el-form-item__content) {
  .el-select {
    width: 200px;
  }

  .el-textarea {
    width: 100%;

    .el-textarea__inner {
      width: 100%;
    }
  }

  .el-input__inner {
    width: 200px;
  }
}
</style>
