<template>
  <div class="padding-box">
    <el-form label-width="120px" inline size="mini">
      <el-form-item label="参数名称：">
        <el-input readonly :model-value="details().name"></el-input>
      </el-form-item>
      <el-form-item label="参数编号：">
        <el-input readonly :model-value="details().code"></el-input>
      </el-form-item>
      <el-form-item label="是否启用：">
        <el-switch :model-value="details().useStatus" active-value="TRUE" inactive-value="FALSE" />
      </el-form-item>
      <el-form-item label="添加时间：">
        <el-input readonly :model-value="details().createTime"></el-input>
      </el-form-item>
      <el-form-item label="备注：">
        <el-input style="width:900px" type="textarea" readonly :model-value="details().remarks"></el-input>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import { ElForm, ElFormItem, ElInput,ElSwitch } from "element-plus";
import { reactive } from '@vue/reactivity';
import {useStore} from "vuex"
// import { computed } from '@vue/runtime-core';
export default {
  components: {
    ElForm,
    ElFormItem,
    ElInput,
    ElSwitch
  },
  setup() {
    const store=useStore()
    const state=reactive({
      form:{}
    })
    const details=()=>{
      return store.state.deviceParameters[store.state.app.activeTab]?store.state.deviceParameters[store.state.app.activeTab].selectRow:{}
    }
    return {
      state,
      details
    }
  },
};
</script>
<style lang="scss" scoped>

</style>