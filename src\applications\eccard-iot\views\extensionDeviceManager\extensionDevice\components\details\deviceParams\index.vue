
<template>
  <el-tabs v-if="state.tabs.length" v-model="state.tab" class="demo-tabs" @tab-click="handleClick">
    <el-tab-pane v-for="(item, index) in state.tabs" :key="index" :label="item.paramsName" :name="item.id">
      <el-form label-width="120px" size="mini" inline>
        <el-form-item :label="v.paramsName" v-for="(v, i) in item.children" :key="i">
          <el-input readonly v-model="state.form[v.paramsCode]"></el-input>
        </el-form-item>
      </el-form>
    </el-tab-pane>
  </el-tabs>
  <el-empty v-else description="当前设备暂未绑定自定义参数"></el-empty>


</template>


<script>
import { reactive, onMounted } from 'vue'
import { useStore } from "vuex";
import { makeTree } from "@/utils"
import { ElEmpty, ElTabs, ElTabPane, ElForm, ElFormItem, ElInput } from "element-plus";
/* import basicParams from "./basicParams"
import chargingParams from "./chargingParams"
import cardParams from "./cardParams"
import seniorParams from "./seniorParams"
import temperatureParams from "./temperatureParams" */
const tabs1 = [
  {
    name: "jbcs",
    label: "基本参数",
  },
  {
    name: "zdycs",
    label: "自定义参数",
  },
];
const tabs2 = [
  {
    name: "jbcs",
    label: "基本参数",
  },
  {
    name: "jfcs",
    label: "计费参数",
  },
  {
    name: "klcs",
    label: "卡类参数",
  },
  {
    name: "gjcs",
    label: "高级参数",
  },
  {
    name: "wdcs",
    label: "温度参数",
  },
]
export default {
  components: {
    ElEmpty, ElTabs, ElTabPane, ElForm, ElFormItem, ElInput,
/*     "kade-basic-params": basicParams,
    "kade-charging-params": chargingParams,
    "kade-card-params": cardParams,
    "kade-senior-params": seniorParams,
    "kade-temperature-params": temperatureParams, */
  },
  props: {
    data: {
      type: String,
      default: ""
    }
  },
  setup(props) {
    const store = useStore();
    const state = reactive({
      tab2: "jbcs",
      tab1: "jbcs",
      tabs: [],
      tab: '',
    })
    const handleClick = (val) => {
      state.form = {}
      let a = state.tabs.find(item => item.id == val.props.name)
      state.tabData = a
      let list = JSON.parse(sessionStorage.getItem("customParamsList"))
      let listCutItem = list.find(item => item.deviceType == '自定义')
      console.log(listCutItem, a);
      if (listCutItem && listCutItem.paramsData) {
        state.form = { ...listCutItem.paramsData[a.paramsCode] }
      }
    }
    onMounted(async () => {
      await store.dispatch("hydropowerDevice/waterController/getParams", props.data.paramId);
      state.tab1 = "jbcs"
      let data = JSON.parse(sessionStorage.getItem('templateList')).find(item => item.deviceType == '自定义')
      console.log(data)
      if (data && data.paramsList) {
        state.tabs = makeTree(data.paramsList, "id", "parentId", "children");
      } else {
        state.tabs = []
      }
      if (state.tabs.length) {
        state.tab = state.tabs[0].id
      }
      handleClick({
        props: {
          name: state.tab
        }
      })

    });
    return {
      tabs1,
      tabs2,
      state,
      handleClick
    }
  }
}
</script>