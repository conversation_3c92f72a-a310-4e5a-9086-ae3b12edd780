<template>
  <el-dialog :model-value="modelValue" title="更改胁迫密码" width="400px" :before-close="handleClose"
    :close-on-click-modal="false">
    <div style="padding:20px 0 10px">
      <el-form label-width="120px" size="mini">
        <el-form-item label="新密码：">
          <el-input placeholder="请输入6位数字"></el-input>
        </el-form-item>
        <el-form-item label="确认新密码：">
          <el-input placeholder="请输入6位数字"></el-input>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button type="primary" @click="submit" size="mini">保存</el-button>
        <el-button @click="handleClose" size="mini">返回</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script>
import { ElDialog, ElForm, ElFormItem, ElInput,ElButton } from "element-plus"
import { reactive } from '@vue/reactivity'
export default {
  components: {
    ElDialog, ElForm, ElFormItem, ElInput,ElButton
  },
  emits: ["update:modelValue"],
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    rowList: {
      type: Array,
      default: null
    }
  },
  setup(props, context) {
    const state = reactive({
      form: {},
      dataList: [],
      total: 0
    })
    const handleClose = () => {
      context.emit("update:modelValue", false)
    }
    return {
      state,
      handleClose
    }
  }
}
</script>
<style lang="scss" scoped>
:deep(.kade-table-wrap) {
  margin-bottom: 20px;
  box-sizing: border-box;
}

.padding-box {
  padding: 20px 20px 0
}

.el-divider--horizontal {
  margin: 0
}

.el-table {
  border-left: 1px solid #EBEEF5;
  border-right: 1px solid #EBEEF5;
}
</style>