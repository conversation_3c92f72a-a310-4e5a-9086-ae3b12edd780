<template>
  <div>
    <el-form inline size="small" label-width="100px">
      <el-form-item label="精准查询:">
        <el-input style="width: 215px" placeholder="输入交易单号" v-model="state.form.tradeNo"></el-input>
      </el-form-item>
      <el-form-item label="&nbsp;">
        <el-button @click="search()" size="small" type="primary" icon="el-icon-search">搜索</el-button>
        <el-button icon="el-icon-refresh-right" @click="reset()" size="small">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table style="width: 100%" :data="RechargeRecord" v-loading="false" border stripe>
      <el-table-column show-overflow-tooltip width="150" label="交易单号" prop="tradeNo" align="center"></el-table-column>
      <el-table-column show-overflow-tooltip width="153" prop="tradeDate" label="交易时间" align="center">
        <template #default="scope">
          {{ timeStrDate(scope.row.tradeDate) }}
        </template>
      </el-table-column>
      <el-table-column show-overflow-tooltip label="交易钱包" prop="walletName" align="center"></el-table-column>
      <el-table-column show-overflow-tooltip label="收支类型" prop="inoutType" align="center">
        <template #default="scope">
          {{ filterDictionary(scope.row.inoutType, state.accountStatusList) }}
        </template>
      </el-table-column>
      <el-table-column show-overflow-tooltip label="交易类型" prop="costTypeName" align="center"></el-table-column>
      <el-table-column show-overflow-tooltip label="交易前次数(次)" prop="tradeBeforeBalance" align="center"></el-table-column>
      <el-table-column show-overflow-tooltip label="交易次数(次)" prop="tradeAmount" align="center"></el-table-column>
      <el-table-column show-overflow-tooltip label="次数余额(次)" prop="tradeAfterBalance" align="center"></el-table-column>
      <el-table-column show-overflow-tooltip label="交易设备" prop="deviceName" align="center"></el-table-column>
      <el-table-column show-overflow-tooltip label="操作员" prop="operatorName" align="center"></el-table-column>
      <el-table-column label="操作" align="center">
        <template #default="scope">
          <div @click="selectRowClick(scope)" style="color: #409eff">
            {{ scope.row.isSelect ? "取消选择" : "选择" }}
          </div>
        </template>
      </el-table-column>
    </el-table>
    <div class="pagination-box">
      <div>
        <div v-show="state.selectRow">已选中交易明细，所属现金钱包，此笔交易额为<span style="color: #3399ff">{{ state.selectRow.tradeAmount }}</span>次</div>
      </div>
      <el-pagination
        background
        :current-page="state.form.currentPage"
        :page-size="state.form.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="[6, 10, 20, 50, 100]"
        :total="RechargeRecordTotal"
        @current-change="handlePageChange"
        @size-change="handleSizeChange"
      >
      </el-pagination>
    </div>
    <div class="righting-money-box" v-show="state.selectRow">
      <div class="righting-money-text">实际充值次数</div>
      <div class="righting-money-input">
        &nbsp;&nbsp;<el-input v-model="state.rightingForm.actualRechargeAmount" type="number" style="width: 100px" size="mini" :min="0"></el-input>次
      </div>
      <div class="righting-money-success">
        冲正后余额为
        <span style="color: #3399ff">{{
          (parseFloat(state.selectRow.tradeBeforeBalance) +
          Number(state.rightingForm.actualRechargeAmount)).toFixed(2)
        }}</span>
        次
      </div>
    </div>
  </div>
</template>
<script>
import {
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  // ElDivider,
  // ElSelect,
  // ElOption,
  ElTable,
  ElTableColumn,
  ElPagination,
} from "element-plus";
import {} from "@/applications/eccard-finance/api";
import { reactive } from "@vue/reactivity";
import { timeStr } from "@/utils/date.js";
import { useStore } from "vuex";
import { computed, onMounted, watch } from "@vue/runtime-core";
export default {
  components: {
    // "el-divider": ElDivider,
    "el-button": ElButton,
    "el-input": ElInput,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    // "el-select": ElSelect,
    // "el-option": ElOption,
    "el-table": ElTable,
    "el-table-column": ElTableColumn,
    "el-pagination": ElPagination,
  },
  props: {
    personDetail: {
      type: Object,
      default: null
    }
  },
  setup(props, context) {
    const store = useStore();
    const state = reactive({
      form: {
        currentPage: 1,
        pageSize: 6,
        amountType: 2,
        isRighting: 1,
        userId: null,
        tradeNo: null,
      },
      rightingForm: {
        actualRechargeAmount: null,
        relationTradeId: null,
        relationTradeNo: null,
        userId: null,
        afterAmount:null
      },
      selectRow: "",
      afterAmount: 0,
      walletActiveList: [],
      accountStatusList: JSON.parse(
        localStorage.getItem("kade_cache_dictionary")
      ).filter((item) => item.dictType == "WALLET_INOUT_TYPE"), //收支类型
    });

    const RechargeRecord = computed(() => {
      let list = store.state.rightingData.RechargeRecord;
      if (list.length) {
        list.forEach((item) => {
          item.isSelect = false;
        });
        return list;
      } else {
        return [];
      }
    });
    const timeStrDate = computed(() => {
      return timeStr;
    });
    const RechargeRecordTotal = computed(() => {
      return store.state.rightingData.RechargeRecordTotal;
    });
    const filterDictionary = (val, dictionaryList) => {
      for (let i of dictionaryList) {
        if (val == i.dictCode) {
          return i.dictValue;
        }
      }
    };
    watch(
      () => state.rightingForm.actualRechargeAmount,
      () => {
        if (state.rightingForm.actualRechargeAmount < 0) {
          state.rightingForm.actualRechargeAmount = 0;
        } else if (state.rightingForm.actualRechargeAmount > 100000) {
          state.rightingForm.actualRechargeAmount = 100000;
        }
        context.emit("formData", state.rightingForm);
      }
    );
    watch(
      () => store.state.rightingData.selectPerson,
      () => {
        console.log(1);
        getPersonTradeList();
      }
    );

    const selectRowClick = (scope) => {
      if (scope.row.isSelect) {
        scope.row.isSelect = false;
        state.selectRow = "";
        state.rightingForm = {
          actualRechargeAmount: null,
          relationTradeId: null,
          relationTradeNo: null,
          userId: null,
          afterAmount:null
        };
        context.emit("formData", state.rightingForm);
      } else {
        scope.row.isSelect = true;
        state.selectRow = scope.row;
        state.afterAmount = parseFloat(props.personDetail.acctWallets.filter(item => item.walletCode == state.selectRow.walletCode)[0].walletBalance) - Number(state.selectRow.tradeAmount)
        state.rightingForm.relationTradeId = scope.row.tradeId;
        state.rightingForm.relationTradeNo = scope.row.tradeNo;
        state.rightingForm.afterAmount = state.afterAmount;
        context.emit("formData", state.rightingForm);
        store.state.rightingData.RechargeRecord.forEach((item, index) => {
          if (
            !(
              store.state.rightingData.RechargeRecord[index] ==
              store.state.rightingData.RechargeRecord[scope.$index]
            )
          ) {
            item.isSelect = false;
          }
        });
      }
    };
    const search = () => {
      getPersonTradeList();
    };

    const getPersonTradeList = () => {
      state.form.userId = store.state.rightingData.selectPerson.userId;
      for (let key in state.form) {
        if (!state.form[key]) {
          delete state.form[key];
        }
      }
      store.dispatch("rightingData/queryRechargeRecord", state.form);
      state.selectRow = "";
    };
    const handlePageChange = (val) => {
      state.form.currentPage = val;
      getPersonTradeList();
    };
    const handleSizeChange = (val) => {
      state.form.currentPage = 1;
      state.form.pageSize = val;
      getPersonTradeList();
    };
    const reset = () => {
      state.form = {
        currentPage: 1,
        pageSize: 6,
        userId: store.state.data.selectPerson.userId,
        walletCode: null,
        amountType: 2,
        isRighting: 1,
      };
    };
    onMounted(() => {
      getPersonTradeList();
      // queryWalletActiveList();
    });
    return {
      state,
      RechargeRecord,
      RechargeRecordTotal,
      timeStrDate,
      selectRowClick,
      filterDictionary,
      handlePageChange,
      handleSizeChange,
      search,
      reset,
    };
  },
};
</script>
<style lang="scss" scoped>
.date {
  margin: 0 10px;
  color: rgba(0, 0, 0, 0.847058823529412);
}
.date:hover {
  color: #06f;
}
.pagination-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.righting-money-box {
  margin-top: 20px;
  display: flex;
  align-items: center;
  .righting-money-text {
    font-family: "PingFangSC-Regular", "PingFang SC", sans-serif;
    font-weight: 400;
    font-style: normal;
    font-size: 16px;
    color: #000000;
  }
  .righting-money-input {
    margin: 0 20px;
    font-family: "PingFangSC-Semibold", "PingFang SC Semibold", "PingFang SC",
      sans-serif;
    font-weight: 650;
    color: #3399ff;
    .el-input {
      width: 100px !important;
      .el-input__inner {
        width: 100px !important;
      }
    }
  }
  .righting-money-success {
    color: #999999;
  }
}
.el-table{
  border-left: 1px solid #EBEEF5 !important;
  border-right: 1px solid #EBEEF5 !important;
}
</style>