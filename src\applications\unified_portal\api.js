import request from '@/service';
/**
 * 登录
 * @param { username, password }
 */
export function login(data) {
    return request('/test/login', {
        method: 'post',
        data
    });
}

/**
 * 获取应用列表
 * @param {}
 */
export function getAllApplyList(params) {
    return request.get('/eccard-partal-manage/applicationManage/getApplyForPage', { params });
}

/**
 * 获取用户应用列表
 */
export function getUserApplyList(params) {
    return request.get('/eccard-sys/sysPermission/getUserAppPermission', { params })
}

export function getAuthUserApplyList(params) {
    return request.get('/eccard-partal-manage/applicationManage/getAuthApplyListByUserId', { params })
}

export function setUserAppAuth(params) {
    return request.post('/eccard-partal-manage/applicationManage/setUserAppAuth', params);
}

/**
 * 获取全部应用列表
 * @param {}
 */
 export function getAllApply(params) {
    return request.get('/eccard-partal-manage/applicationManage/getAllApplyList', { params });
}


/**
 * 添加应用
 */
export function addApply(data) {
    return request.post('/eccard-partal-manage/applicationManage/addApply', data);
}

/**
 * 编辑应用
 */
export function editApply(data) {
    return request.post('/eccard-partal-manage/applicationManage/editApply', data);
}

/**
 * 应用详情
 */
export function getApplyById(params) {
    return request.get('/eccard-partal-manage/applicationManage/getApplyDetailByID', { params });
}

/**
 * 上传应用logo
 */
export function uploadApplyLogo(data) {
    return request.post('/eccard-partal-manage/applicationManage/uploadApplyLogo', data);
}
/**
 * 上传人脸照片
 */
 export function uploadFaceImage(data) {
    return request.post('/eccard-basic-data/face/uploadImages', data);
}


/**
 * 添加应用类别
 */
export function addOrEditApplyType(data) {
    return request.post('/eccard-partal-manage/applicationManage/editApplyType', data);
}

/**
 * 获取应用类别
 */
export function getAllApplyType() {
    return request.get('/eccard-partal-manage/applicationManage/getAllApplyType');
}


/**
 * 数据字典
 */
/* export function getDictionary() {
    return request.get('/eccard-partal-manage/dict/getDictionaryList');
} */
export function getDictionary() {
    return request.get('/eccard-ops/dict/getDictionaryList');
}
/**
 * 首页待办
 * @param { beginPage, rowNum, endDay, isNowDay }
 */

export function getTaskList(params) {
    return request.post('/eccard-partal/dayTask/getDayTaskListNowDayForPage', params);
}

/**
 * 首页帮助中心
 */

export function getHomeHelpList(params) {
    return request.post('/eccard-partal/helpInfo/getHelpInfoByPage', params)
}

/**
 * 我的消息
 */
export function getMyMessages(params) {
    return request.post('/eccard-partal/personal/getPersonMessageByPage', params);
}

/**
 * 个人信息
 */
export function getUserInfo(params) {
    return request.post('/eccard-partal/personal/getPersonalDetailsById', params)
}

/**
 * 用户模块
 */
export function getUserModuleList(params) {
    return request.post('/eccard-partal/personal/getUserModuleList', params)
}

/**
 * 修改密码
 */
export function updatePassword(data) {
    return request.post('/eccard-partal/personal/updatePasswordByUserId', data);
}

/**
 * 个人信息修改
 */
export function updatePersonalInfo(data) {
    return request.post('/eccard-partal/personal/updatePersonalDetailsById', data);
}

/**
 * 上传头像
 */
export function uploadPersonPhoto(data) {
    return request.post('/eccard-partal/personal/upPersonPhoto', data);
}

/**
 * 详细信息查询
 */
export function getMessageDetailsByMsgId(params) {
    return request.post('/eccard-partal/personal/getMessageDetailsByMsgId', params);
}

/**
 * 更新用户模块状态
 */
export function updateUserModuleState(data) {
    return request.post('/eccard-partal/personal/editUserModuleState', data);
}

/**
 * 添加待办
 */
export function createTodo(data) {
    return request.post('/eccard-partal/dayTask/addDayTaskByUser', data);
}

/**
 * 修改待办状态
 */
export function updateTodoState(data) {
    return request.post('/eccard-partal/dayTask/updateDayTaskStatusByTaskID', data);
}

/**
 * 首页消息
 */
export function getMessageList(params) {
    return request.post('/eccard-partal/personal/getPersonMessageByPage', params);
}

/**
 * 获取授权应用分页列表
 */

export function getAuthAppInfoByIdForPage(params) {
    return request.get('/eccard-partal-manage/applicationManage/getAuthAppInfoByIdForPage', { params });
}

/**
 * 设置授权应用打开状态
 */

export function setAuthAppInfoById(params) {
    return request.post('/eccard-partal-manage/applicationManage/setAuthAppInfoById', params);
}

/**
 * 获取帮助信息详情
 */
export function getHelpInfoDetailsById(params) {
    return request.post('/eccard-partal/helpInfo/getHelpInfoDetailsById', params);
}
