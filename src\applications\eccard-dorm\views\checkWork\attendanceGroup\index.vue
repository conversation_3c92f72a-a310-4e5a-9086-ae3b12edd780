<template>
  <kade-route-card>
    <kade-table-filter @search="handleSearch" @reset="handleReset">
      <el-form inline label-width="90px" size="mini">
        <el-form-item label="分组名称">
          <el-input v-model="state.form.name" placeholder="请输入" maxlength="20"></el-input>
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="考勤分组列表">
      <template #extra>
        <el-button icon="el-icon-plus" type="success" size="mini" @click="edit('add')">新增</el-button>
      </template>
      <el-table border :data="state.data" v-loading="state.loading">
        <el-table-column show-overflow-tooltip v-for="(item, index) in column" :key="index" :label="item.label"
          :prop="item.prop" :width="item.width" align="center">
          <template #default="scope" v-if="item.isDict">
            {{ dictionaryFilter(scope.row[item.prop]) }}
          </template>
        </el-table-column>
        <el-table-column prop="" label="操作" align="center" class="green" width="100px">
          <template #default="scope">
            <el-button type="text" class="green" size="mini" @click="edit('edit', scope.row)">编辑</el-button>
            <el-button type="text" class="green" size="mini" @click="handleDel(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination v-model:currentPage="state.form.pageNum" v-model:page-size="state.form.pageSize"
          :page-sizes="[10, 20, 30, 40, 50]" background layout="total, sizes, prev, pager, next, jumper"
          :total="state.total" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      </div>
    </kade-table-wrap>
    <kade-attendance-group-edit :dialogVisible="state.dialogVisible" :dialogType="state.dialogType"
      :rowData="state.rowData" @close="close" />
  </kade-route-card>
</template>

<script>
import { reactive } from "@vue/reactivity";
import {
  ElForm,
  ElFormItem,
  ElInput,
  ElTable,
  ElTableColumn,
  ElPagination,
  ElButton,
  ElMessageBox,
  ElMessage,
} from "element-plus";
import { onMounted } from "@vue/runtime-core";
import edit from "./components/edit";
import {
  getAttendanceGroup,
  delAttendanceGroup
} from '@/applications/eccard-dorm/api.js'
const column = [
  { label: "分组名称", prop: "name", width: "200px" },
  { label: "考勤开始日期", prop: "beginDate" },
  { label: "考勤结束日期", prop: "endDate" },
  { label: "启用状态", prop: "status", isDict: true },
  { label: "考勤班级", prop: "deptName", width: "200px" },
  { label: "公休模式", prop: "holidayMode" },
  { label: "创建时间", prop: "createTime" },
  { label: "备注", prop: "remarks", width: "200px" },
];
export default {
  components: {
    ElForm,
    ElFormItem,
    ElInput,
    ElTable,
    ElTableColumn,
    ElPagination,
    ElButton,
    "kade-attendance-group-edit": edit,
  },
  setup() {
    const state = reactive({
      form: {
        pageSize: 10,
        currentPage: 1,
      },
      data: [],
      dialogVisible: false,
      rowData: {},
      dialogType: "",
      total: 0,
      loading: false,
    });

    const getList = async () => {
      state.loading = true
      try {
        let { data: { list, total } } = await getAttendanceGroup(state.form);
        state.data = list;
        state.total = total;
        state.loading = false;
      } catch {
        state.loading = false;
      }
    };
    const handleSearch = () => {
      getList();
    };
    const handleReset = () => {
      state.form = {
        pageSize: 10,
        currentPage: 1,
      };
      getList();
    };
    const edit = (type, row) => {
      console.log(type, row);
      state.rowData = row;
      state.dialogType = type;
      state.dialogVisible = true;
    };
    const handleDel = (row) => {
      console.log(row)
      ElMessageBox.confirm("确定删除?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        let { code, message } = await delAttendanceGroup(row.id)
        if (code === 0) {
          ElMessage.success(message)
          getList()
        } else {
          return false
        }
      });
    };
    const handleSizeChange = (val) => {
      state.form.pageNum = 1;
      state.form.pageSize = val;
      getList();
    };
    const handleCurrentChange = (val) => {
      state.form.pageNum = val;
      getList();
    };
    const close = (val) => {
      if (val) {
        getList()
      }
      state.dialogVisible = false;
    };
    onMounted(async () => {
      getList();
    });
    return {
      state,
      handleReset,
      handleSearch,
      edit,
      close,
      column,
      handleDel,
      handleSizeChange,
      handleCurrentChange,
    };
  },
};
</script>

<style scoped>
.green :hover {
  text-decoration: underline;
}

:deep(.el-dialog) {
  border-radius: 6px;
  padding-bottom: 20px;
}

:deep(.el-dialog__header) {
  border-bottom: 1px solid #eeeeee;
  margin-bottom: 20px;
}

:deep(.el-dialog__footer) {
  text-align: center;
  border-top: none;
  padding-top: 0;
  padding-bottom: 0;
}

:deep(.el-dialog__body) {
  line-height: 30px;
}

:deep(.el-input--mini .el-input__inner) {
  width: 193px;
  height: 29px;
}

:deep(.el-input-number--mini) {
  width: 193px;
}

:deep(.el-input-number .el-input-number--mini) {
  display: flex;
  align-items: center;
}

:deep(.el-input-number__increase) {
  height: 26px;
  top: 2px;
}

:deep(.el-input-number__decrease) {
  height: 26px;
  top: 2px;
  left: 2px;
}

:deep(.el-date-editor.el-input, .el-date-editor.el-input__inner) {
  width: 193px;
}

:deep(.el-pagination__editor.el-input .el-input__inner) {
  width: 46px;
}

:deep(.el-input--small .el-input__inner) {
  height: 28px;
  width: 193px;
}

:deep(.el-pagination .el-select .el-input .el-input__inner) {
  width: 100px;
}

:deep(.el-dialog__footer) {
  padding-top: 15px !important;
  padding-bottom: 0 !important;
}
</style>