<template>
  <div class="box" ref="ScaleBox">
    <kade-header-box title="男生宿舍一栋晚归寝考勤监控大屏" />
    <div class="main">
      <el-row :gutter="15" style="height: 100%;">
        <el-col :span="10" class="person-msg">
          <kade-border-box style="flex:1;margin-bottom: 20px;">
            <kade-person-msg />
          </kade-border-box>
          <kade-border-box style="height: 50%;">
            <kade-not-returned />
          </kade-border-box>
        </el-col>
        <el-col :span="14">
          <kade-border-box style="height: 100%;">
            <kade-room-msg />
          </kade-border-box>
        </el-col>
      </el-row>
    </div>
  </div>
</template>
<script>
import { reactive } from "vue"
import { ElCol, ElRow, } from "element-plus"
import headerBox from "../components/headerBox.vue"
import borderBox from "../components/borderBox.vue"
import personMsg from "./components/personMsg.vue"
import notReturned from "./components/notReturned.vue"
import roomMsg from "./components/roomMsg.vue"

export default {
  components: {
    ElCol, ElRow,
    "kade-header-box": headerBox,
    "kade-border-box": borderBox,
    "kade-person-msg": personMsg,
    "kade-not-returned": notReturned,
    "kade-room-msg": roomMsg,

  },
  setup() {
    const state = reactive({

    })
    return {
      state,
    }
  }
}
</script>
<style scoped lang="scss">
.box {
  background: #001034;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow-y: auto;

  .main {
    flex: 1;
    padding: 0 20px 20px;

    .person-msg {
      display: flex;
      flex-direction: column;
    }
  }

}
</style>