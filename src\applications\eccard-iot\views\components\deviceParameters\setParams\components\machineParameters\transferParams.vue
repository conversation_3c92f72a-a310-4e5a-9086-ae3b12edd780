<template>
  <div class="padding-form-box setBasicParam" v-loading="state.loading">
    <el-form inline size="mini" label-width="230px">
      <el-form-item
        :label="item.label"
        v-for="(item, index) in formList"
        :key="index"
      >
        <el-input-number
          v-model="state.form.transerEntity[item.valueKey]"
          :max="item.max"
          :min="item.min"
        ></el-input-number>
      </el-form-item>
    </el-form>
  </div>
  <div style="text-align: center">
    <el-button size="mini" @click="beforeClose()">取消</el-button>
    <el-button size="mini" type="primary" @click="saveClick()">保存</el-button>
  </div>
</template>

<script>
import {
  ElButton,
  ElForm,
  ElFormItem,
  ElInputNumber,
  ElMessage,
} from "element-plus";
import { onMounted, reactive } from "vue";
import { useStore } from "vuex";
import { saveTransfer } from "@/applications/eccard-iot/api";

const option = [
  {
    value: 1,
    label: "是",
  },
  {
    value: 0,
    label: "否",
  },
];
const formList = [
  { label: "充值机待机时长(秒)", valueKey: "dev_czdj", max: 3600, min: 60 },
  {
    label: "同卡充值转账时间间隔(秒)",
    valueKey: "dev_cardjg",
    max: 100,
    min: 0,
  },
  { label: "主钱包最大余额(元)", valueKey: "dev_Mmax", max: 160000, min: 1 },
  {
    label: "主钱包单次充值/转账最大金额(元)",
    valueKey: "dev_Mcmax",
    max: 160000,
    min: 1,
  },
  { label: "主钱包定值充值金额(元)", valueKey: "dev_Mcdin", max: 9999, min: 1 },
  { label: "水控钱包最大余额(元)", valueKey: "dev_Smax", max: 10000, min: 1 },
  {
    label: "水控包单次充值/转账最大金额(元)",
    valueKey: "dev_Scmax",
    max: 10000,
    min: 1,
  },
];
const defaultParamsFnc = () => {
  return {
    dev_czdj: 60,
    dev_cardjg: 3,
    dev_Mmax: 20000,
    dev_Mcmax: 100,
    dev_Mcdin: 50,
    dev_Smax: 500,
    dev_Scmax: 50,
  };
};
export default {
  components: {
    ElForm,
    ElFormItem,
    ElButton,
    ElInputNumber,
  },
  setup() {
    const store = useStore();
    const state = reactive({
      loading: false,
      form: {
        transerEntity: defaultParamsFnc(),
        id: "",
        paramId: "",
        paramType: "transe",
      },
    });
    const saveClick = async () => {
      state.loading = true;
      state.form.paramId = store.state.deviceParameters[store.state.app.activeTab].selectRow.id;
      let { message, code } = await saveTransfer(state.form);
      if (code === 0) {
        setTimeout(() => {
          ElMessage.success(message);
          store.dispatch("deviceParameters/getParams",store.state.deviceParameters[store.state.app.activeTab].selectRow.id);
          state.loading = false;
        }, 1500);
      }
    };

    const beforeClose = () => {
      store.commit("deviceParameters/updateState", {
        key: store.state.app.activeTab,
        childKey: "isSetParams",
        payload: false,
      });
    };
    onMounted(() => {
      let val = store.state.deviceParameters[store.state.app.activeTab].detailsParams
      if (val && val.length) {
          let transeList = val.filter((item) => item.paramType == "transe");

          if (transeList.length) {
            state.form.id = transeList[0].id;
            for (let key in state.form.transerEntity) {
              state.form.transerEntity[key] = JSON.parse(
                transeList[0].paramContent
              )[key];
            }
          } else {
            state.form.id = "";
            state.form.transerEntity = defaultParamsFnc();
          }
        } else {
          //设置默认选项
          state.form.id = "";
          state.form.baseDetailEntity = defaultParamsFnc();
        }
    });
    return {
      option,
      formList,
      state,
      saveClick,
      beforeClose,
    };
  },
};
</script>

<style lang="scss" scoped>
.setBasicParam {
  border: 1px solid #dcdfe6;
  border-radius: 5px;
  margin-bottom: 10px;
}
:deep(.el-form-item__content) {
  width: 200px;
  .el-input-number {
    width: 100%;
  }
}
</style>
