<template>
  <kade-route-card>
    <template #header>
      <span class="header-title">今日考勤结果</span>
    </template>
    <div id="checkWorkCharts" class="chartline"></div>
  </kade-route-card>
</template>
<script>
import * as echarts from "echarts";
import { onMounted,nextTick } from "vue";
export default {
  setup() {
    const echartInit = () => {
      var chartDom = document.getElementById('checkWorkCharts');
      var myChart = echarts.init(chartDom);
      var option;
      option = {
        tooltip: {
          trigger: 'item'
        },
        legend: {
          orient: 'vertical',
          right: '0%',
          top:'30%'
        },
        
        series: [
          {
            name: '考勤结果',
            type: 'pie',
            radius: '80%',
            right:"40%",
            label: {
              position: "inside",
              formatter: '{b}: {c}'
            },
            data: [
              { value: 1048, name: '正常' },
              { value: 735, name: '请假' },
              { value: 580, name: '晚归' },
              { value: 484, name: '未归' },
            ],
            emphasis: {
              scale: false,
              
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      };
      nextTick(() => {
        myChart.resize();
        myChart.setOption(option, true);
      });
    };
    onMounted(() => {
      echartInit();
    });
    return {
      echartInit,
    };
  }
}
</script>
<style scoped lang="scss">
.chartline {
  height: 260px;
  width: 100%;
}
</style>