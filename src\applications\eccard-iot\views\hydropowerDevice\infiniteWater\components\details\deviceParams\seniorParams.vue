<template>
  <div class="padding-box">
    <el-form inline size="small" v-if="details" label-width="200px">
      <el-form-item :label="item.label + ':'" v-for="(item, index) in list" :key="index">
        <el-input v-if="item.isDict"
          :model-value="dictListFnc()[item.valueKey].filter(val => val.value == details[item.valueKey])[0]?.label"
          readonly></el-input>
        <el-input v-else :model-value="details[item.valueKey]" readonly></el-input>
      </el-form-item>
    </el-form>
    <el-empty v-else description="当前设备参数未设定"></el-empty>
  </div>
</template>
<script>
import { computed } from "@vue/runtime-core";
import { useStore } from "vuex";
import { ElForm, ElFormItem, ElInput, ElEmpty } from "element-plus";
const list = [
  { label: "超时断开(n*6秒)", valueKey: "timeoutdisconnection" },
];
export default {
  components: {
    ElForm,
    ElFormItem,
    ElInput,
    ElEmpty
  },
  setup() {
    const store = useStore();
    const details = computed(() => {
      let data = "";
      for (let item of store.state['hydropowerDevice/infiniteWater'].params) {
        if (item.paramType == "SENIOR") {
          data = JSON.parse(item.paramContent);
          break;
        }
      }
      if (!data) {
        data = ""
      }
      return data;
    });
    const dictListFnc = () => {
      return store.state['hydropowerDevice/infiniteWater'].dict
    }
    return {
      list,
      details,
      dictListFnc
    };
  },
};
</script>
<style lang="scss" scoped>
.basic-info-box {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  border-left: 1px solid #ccc;
  border-top: 1px solid #ccc;
  min-width: 900px;

  .box-item {
    box-sizing: border-box;
    width: 33.333%;
    min-width: 300px;
    line-height: 50px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #ccc;

    .item-label {
      width: 50%;
      text-align: center;
      background: #f6f6f6;
      border-right: 1px solid #ccc;
    }

    .item-value {
      text-align: center;
      width: 50%;
      background: #fff;
      border-right: 1px solid #ccc;
    }
  }
}</style>