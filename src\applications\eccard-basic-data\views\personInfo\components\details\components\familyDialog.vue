<template>
  <el-dialog :model-value="isShow" title="新增家庭成员" width="500px" :before-close="beforeClose" :append-to-body="true">
    <div style="padding-top: 20px">
      <el-form size="mini" label-width="120px" ref="formDom" :rules="rules" :model="state.form">
        <el-form-item label="称呼" prop="fuserRelationship">
          <el-select clearable placeholder="请选择" style="width: 100%" v-model="state.form.fuserRelationship">
            <el-option value="父亲">父亲</el-option>
            <el-option value="母亲">母亲</el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="姓名" prop="fname">
          <el-input placeholder="请输入" v-model="state.form.fname"></el-input>
        </el-form-item>
        <el-form-item label="联系电话" prop="ftel">
          <el-input placeholder="请输入" v-model="state.form.ftel"></el-input>
        </el-form-item>
        <el-form-item label="来源" prop="fsource">
          <el-select clearable placeholder="请选择" style="width: 100%" v-model="state.form.fsource">
            <el-option v-for="item in sources" :key="item.value" :value="item.value" :label="item.label"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="家庭编码" prop="fcode">
          <el-input placeholder="请输入" v-model="state.form.fcode"></el-input>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="beforeClose" size="mini">取消</el-button>
        <el-button type="primary" @click="submit()" size="mini">提交</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script>
import {
  ElDialog,
  ElForm,
  ElFormItem,
  ElInput,
  ElButton,
  ElSelect,
  ElOption,
  ElMessage,
} from "element-plus";
import { reactive, ref,nextTick,watch } from "vue";
import { useStore } from "vuex";
import { validateMobileAndFixTel } from "@/utils/validate";
import { useDict } from "@/hooks/useDict";
import { addFamilyInfo } from "@/applications/eccard-basic-data/api";
export default {
  components: {
    ElDialog,
    ElForm,
    ElFormItem,
    ElInput,
    ElButton,
    ElSelect,
    ElOption,
  },
  props: {
    isShow: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Object,
      default: null,
    },
  },
  setup(props, context) {
    const store = useStore();
    const sources = useDict("SYS_DATA_SOURCE");
    const formDom = ref(null);
    const state = reactive({
      title: "",
      form: {
        fstate: "ENABLE_TRUE"
      },
    });
    watch(
      () => props.isShow,
      (val) => {
        if (val) {
          state.form = {
            fstate: "ENABLE_TRUE"
          };
        }
      }
    );

    const rules = {
      fsex: [{ required: true, message: "请选择性别", trigger: "change" }],
      fname: [
        { required: true, message: "请输入姓名" },
        { max: 20, message: "姓名长度不得超过20字符" },
      ],
      ftel: [
        { required: true, message: "请输入电话" },
        { validator: validateMobileAndFixTel },
      ],
      fstate: [{ required: true, message: "请选择卡状态", trigger: "change" }],
      fuserRelationship: [
        { required: true, message: "请选择称呼", trigger: "change" },
      ],
      fcode: [{ required: true, message: "请输入家庭编码", trigger: "blur" }],
    };
    const submit = () => {
      formDom.value.validate(async (valid) => {
        if (valid) {
          let params={...state.form}
          params.fuserId=store.state.userInfo.rowData.id
          let { code, message } = await addFamilyInfo(params);
          if (code === 0) {
            ElMessage.success(message);
            context.emit("close", true);
          }
        } else {
          return false;
        }
      });
    };

    const beforeClose = () => {
      context.emit("close", false);
      nextTick(() => {
        formDom.value.clearValidate();
      });
    };
    return {
      sources,
      formDom,
      state,
      rules,
      submit,
      beforeClose,
    };
  },
};
</script>
<style lang="scss" scoped>
:deep(.el-form-item__content) {
  .el-select {
    width: 100% !important;
  }
  .el-input__inner {
    width: 100% !important;
  }
  .el-date-editor.el-input {
    width: 100%;
  }
}
</style>