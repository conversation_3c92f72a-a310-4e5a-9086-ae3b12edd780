<template>
  <el-dialog :model-value="modelValue" title="绑定设备" width="90%" :before-close="beforeClose">
    <div style="margin-top:20px">
      <kade-select-table :isShow="modelValue" :value="[]" :reqFnc="getUnGroupDeviceListByPage"
        :selectCondition="state.selectCondition" :column="column" :isCurrentSelect="true" :params="state.params" @change="deviceChange" />
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="beforeClose" size="mini">取消</el-button>
        <el-button type="primary" @click="submit()" size="mini">提交</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script>
import { ElDialog, ElButton, ElMessage } from "element-plus";
import { onMounted, reactive, watch } from "vue";
// import { objToArray } from "@/utils"
import {
  iotCfg,
} from "@/applications/eccard-iot/api";
import { getUnGroupDeviceListByPage, acsDeviceGroupBatchAdd } from "@/applications/eccard-uac/api";
import selectTable from "@/components/table/selectTable.vue";
const column = [
  { label: "区域", prop: "areaName", isDict: false, width: "" },
  { label: "设备类型", prop: "deviceTypeName", isDict: false, width: "" },
  { label: "设备机号", prop: "deviceNo", isDict: false, width: "" },
  { label: "设备名称", prop: "deviceName", isDict: false, width: "" },
  { label: "控制门编号", prop: "doorNo", isDict: false, width: "" },
];

export default {
  emits: ["update:modelValue"],
  components: {
    ElDialog,
    ElButton,
    "kade-select-table": selectTable,
  },
  props: {
    modelValue: {
      type: Boolean,
      default: false,
    },
    rowData: {
      type: Object,
      default: null,
    },
    TabModule: {
      type: Function,
      default: null,
    }
  },
  setup(props, context) {
    const state = reactive({
      loading: false,
      selectCondition: [
        { label: "所属区域", valueKey: "areaPath", dataKey: "areaPath", placeholder: "请选择", isSelect: false, isTree: "area" },
        {
          label: "设备类型", valueKey: "deviceType", placeholder: "请选择", isSelect: true,
          select: {
            list: [],
            option: { label: "cfgValue", value: "cfgKey", },
          },
        },
        { label: "设备机号", valueKey: "deviceNo", placeholder: "请输入", isSelect: false, },
        { label: "设备名称", valueKey: "deviceName", placeholder: "请输入", isSelect: false, },
      ],
      params: {
        currentPageKey: "currentPage",
        pageSizeKey: "pageSize",
        resListKey: "list",
        resTotalKey: "total",
        value: {
          groupId: props.rowData.id
        },
        tagNameKey: "deviceName",
        valueKey: "id",
      },
      deviceList: [],
    });
    watch(() => props.modelValue, val => {
      if (val) {
        state.params.value.groupId = props.rowData.id
      }
    })
    const getDeviceTypeList = async () => {
      let { data } = await iotCfg({ cfgType: 'ACCESS_DEVICE_TYPE' });
      state.selectCondition[1].select.list = data
    }
    const deviceChange = (val) => {
      console.log(val);
      state.deviceList = val.list
    };
    const submit = async () => {
      state.loading = true
      try {
        let params = {
          groupId: props.rowData.id,
          deviceList: state.deviceList
        }
        let { code, message } = await acsDeviceGroupBatchAdd(params)
        if (code === 0) {
          ElMessage.success(message)
          context.emit("update:modelValue", true);
        }
        state.loading = false
      }
      catch {
        state.loading = false
      }
    }
    const beforeClose = () => {
      context.emit("update:modelValue", false);
    };
    onMounted(async () => {
      await getDeviceTypeList()
    });

    return {
      getUnGroupDeviceListByPage,
      column,
      state,
      deviceChange,
      submit,
      beforeClose,
    };
  },
};
</script>

<style lang="scss" scoped>
</style>
