<template>
  <kade-route-card>
    <kade-table-filter @search="handleSearch" @reset="handleReset">
      <el-form label-width="100px" size="mini" inline>
        <el-form-item label="区域">
          <el-select>
            <el-option :label="item" :value="item" v-for="(item,index) in 10" :key="index"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="楼栋">
          <el-select>
            <el-option :label="item" :value="item" v-for="(item,index) in 10" :key="index"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="单元">
          <el-select>
            <el-option :label="item" :value="item" v-for="(item,index) in 10" :key="index"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="楼层">
          <el-select>
            <el-option :label="item" :value="item" v-for="(item,index) in 10" :key="index"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="房间">
          <el-input placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="绑定电表">
          <el-select>
            <el-option :label="item" :value="item" v-for="(item,index) in 10" :key="index"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="电表号">
          <el-input placeholder="请输入"></el-input>
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="电表列表">
      <el-table height="55vh" :data="[1]" border>
        <el-table-column v-for="(item,index) in column" :key="index" :width="item.width" :prop="item.prop" :label="item.label" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column label="操作" align="center">
          <template #default="scope">
            <el-button class="green" size="mini" type="text" @click="del(scope.row)" >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination
          background
          :current-page="state.form.currentPage"
          layout="total, sizes, prev, pager, next, jumper"
          :page-sizes="[10, 20, 50, 100, 200]"
          :total="state.total"
          :page-size="state.form.pageSize"
          @current-change="handlePageChange"
          @size-change="handleSizeChange"
        >
        </el-pagination>
      </div>
    </kade-table-wrap>
  </kade-route-card>
</template>
<script>
import {
  ElForm,
  ElFormItem,
  ElSelect,
  ElOption,
  ElInput,
  ElTable,
  ElTableColumn,
  ElPagination,
} from "element-plus";
import { reactive } from '@vue/reactivity';
const column =[
  {label:"区域",prop:""},
  {label:"房间",prop:""},
  {label:"绑定电表",prop:""},
  {label:"电表号",prop:""},
  {label:"表名称",prop:""},
  {label:"绑定时间",prop:""},
]
export default {
  components: {
    ElForm,
    ElFormItem,
    ElSelect,
    ElOption,
    ElInput,
    ElTable,
    ElTableColumn,
    ElPagination,
  },
  setup(){
    const state=reactive({
      form:{
        currentPage:1,
        pageSize:10,
      },
      total:0
    })
    const handlePageChange=(val)=>{
      console.log(val);
    }
    const handleSizeChange=(val)=>{
      console.log(val);
    }
    return {
      column,
      state,
      handlePageChange,
      handleSizeChange
    }
  }
};
</script>
<style lang="scss" scoped>
  :deep(.el-form-item__content){
    .el-input__inner{
      width: 200px;
    }
  }
</style>