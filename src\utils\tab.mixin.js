import { flatArrayToTree } from '@/utils';

// 标签管理混入

export const state = {
	tabs: [],
	activeTab: '',
  menus: [],  
}

export const actions = {
	addTab({ commit, state, getters }, { id, payload }) {
		const index = state.tabs.findIndex(it => it.id === id);
		if (index !== -1) {
			const arr = [ ...state.tabs ];
			const options = arr[index].options;
			arr.splice(index, 1, {
				id,
				options,
			}); 
			commit('updateState', {
				key: 'tabs',
				payload: arr,
			});
		} else {
			let temp = payload;
			if (!temp) {
				temp = getters.menus.find(it => it.menuEnName === id);
			}
			commit('updateState', {
				key: 'tabs',
				payload: [ ...state.tabs, {
					id,
					options: temp,
				}]
			});			
		}
		commit('updateState', {
			key: 'activeTab',
			payload: id,
		});		
	},
	removeTab({ commit, state }, id) {
		if (state.tabs.length === 1) {
			return;
		}
		const arr = [ ...state.tabs ];
		const index = arr.findIndex(it => it.id === id);
		if(index !== -1) {
			arr.splice(index, 1);
			commit('updateState', {
				key: 'tabs',
				payload: arr,
			});
			if (index - 1 > -1) {
				const target = arr[index - 1];
				commit('updateState', {
					key: 'activeTab',
					payload: target.id,
				});				
			} else {
				const target = arr[index];
				commit('updateState', {
					key: 'activeTab',
					payload: target.id,
				});				
			}
		}
	},
	closeCurrent({ state, dispatch }) {
		dispatch('removeTab', state.activeTab);
	},
	initTab({ getters, dispatch }) {
		const menus = getters.menus;
		const arr = menus.filter(it => it.component !== 'Layout');
		if(arr.length) {
			dispatch('addTab', {
				id: arr[0].menuEnName,
				payload: arr[0],
			});
		}
	}  
}

export const getters = {
	menus: (state) => {
		return [...state.customMenus, ...state.menus];
	},
	menuTree: (state,  getters) => {
		console.log(",  getters",state,  getters);
		return flatArrayToTree(state.menus, {
			pidKey: 'parentId',
			idKey: 'id',
			rootValue: 0,			
		});
	},
	currentTab: (state) => {
		return state.tabs.find(it => it.id === state.activeTab);
	},
	query: (state, getters) => {
		return getters.currentTab.options?.query || {};
	}  
}