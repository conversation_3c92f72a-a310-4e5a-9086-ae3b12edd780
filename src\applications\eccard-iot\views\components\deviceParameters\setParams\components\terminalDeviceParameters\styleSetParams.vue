<template>
  <div class="padding-form-box setBasicParam" v-loading="state.loading">
    <el-form inline size="mini" label-width="100px">
      <el-form-item label="标题名称：">
        <el-input v-model="state.form.styleDetailEntity.Title"></el-input>
      </el-form-item>
      <el-form-item label="轮播间隔：">
        <el-input v-model="state.form.styleDetailEntity.Interval"></el-input>
      </el-form-item>
    </el-form>
    <el-button type="primary" size="mini" style="margin-bottom: 10px" @click="state.dialogVisible = true">添加图片</el-button>
    <el-table border :data="state.swiperList">
      <el-table-column label="排序" align="center" prop="num">
      </el-table-column>
      <el-table-column align="center" label="轮播图">
        <template #default="scope">
          <img style="width: 100px; height: 100px" :src="scope.row.img" alt="" />
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作">
        <template #default="scope">
          <span class="green" @click="del(scope)">删除</span>
        </template>
      </el-table-column>
    </el-table>
  </div>
  <div style="text-align: center">
    <el-button size="mini" @click="beforeClose()">取消</el-button>
    <el-button size="mini" type="primary" @click="saveClick()">保存</el-button>
  </div>

  <el-dialog v-model="state.dialogVisible" title="添加轮播图" width="30%" :before-close="close" append-to-body>
    <el-form inline size="mini" label-width="80px">
      <el-form-item label="排序：">
        <el-input v-model="state.model.num"></el-input>
      </el-form-item>
      <el-form-item label="轮播图：">
        <kade-single-image-upload :action="uploadApplyLogo" v-model="state.model.img" />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="close()" size="mini">取消</el-button>
        <el-button type="primary" size="mini" :disabled="!state.model.num || !state.model.img" @click="add()">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import {
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElTable,
  ElTableColumn,
  ElDialog,
  ElMessage,
} from "element-plus";
import { onMounted, reactive } from "vue";
import { useStore } from "vuex";
import SingleImageUpload from "@/components/singleImageUpload";
import { uploadApplyLogo } from "@/applications/unified_portal/api";
import { saveStyle } from "@/applications/eccard-iot/api";

const option = [
  {
    value: 1,
    label: "是",
  },
  {
    value: 0,
    label: "否",
  },
];

const defaultParamsFnc = () => {
  return {
    Title: "智能消费终端轮播图",
    Interval: 6,
    BannerList: [],
  };
};

export default {
  components: {
    ElForm,
    ElFormItem,
    ElInput,
    ElButton,
    ElTable,
    ElTableColumn,
    ElDialog,
    "kade-single-image-upload": SingleImageUpload,
  },
  setup() {
    const store = useStore();
    const state = reactive({
      dialogVisible: false,
      loading: false,
      model: {},
      swiperList: [],
      form: {
        styleDetailEntity: {
          Title: "智能消费终端轮播图",
          Interval: 6,
          BannerList: [],
        },
        id: "",
        paramId: "",
        paramType: "style",
      },
    });
    const add = () => {
      let arr = [...state.swiperList];
      arr.push(state.model);
      console.log(arr);
      arr.sort(function (a, b) {
        return a.num - b.num;
      });
      state.swiperList = arr;
      close();
      state.model = {};
    };

    const del = (scope) => {
      state.swiperList.splice(scope.$index, 1);
    };

    const close = () => {
      state.dialogVisible = false;
    };

    const saveClick = async () => {
      state.loading = true
      let params = { ...state.form };
      params.styleDetailEntity.BannerList = state.swiperList.map(
        (item) => item.img
      );
      params.paramId = store.state.deviceParameters[store.state.app.activeTab].selectRow.id;
      let { message, code } = await saveStyle(params);
      if (code === 0) {
        setTimeout(() => {
          ElMessage.success(message);
          store.dispatch("deviceParameters/getParams", store.state.deviceParameters[store.state.app.activeTab].selectRow.id);
          state.loading = false;
        }, 1500);
      }
    };

    const beforeClose = () => {
      store.commit("deviceParameters/updateState", {
        key: store.state.app.activeTab,
        childKey: "isSetParams",
        payload: false,
      });
    };
    onMounted(() => {
      // store.dispatch("deviceParameters/getParams",store.state.deviceParameters[store.state.app.activeTab].selectRow.id);
      let val = store.state.deviceParameters[store.state.app.activeTab].detailsParams

      if (val && val.length) {
        let styleList = val.filter((item) => item.paramType == "style");
        if (styleList.length) {
          console.log(JSON.parse(styleList[0].paramContent));
          state.form.id = styleList[0].id;
          state.form.styleDetailEntity.Title = JSON.parse(
            styleList[0].paramContent
          ).title;
          state.form.styleDetailEntity.Interval = JSON.parse(
            styleList[0].paramContent
          ).interval;
          state.swiperList = JSON.parse(
            styleList[0].paramContent
          ).bannerList.map((item, index) => {
            return {
              num: index + 1,
              img: item,
            };
          });
        } else {
          state.form.id = "";
          state.form.printDetailEntity = defaultParamsFnc();
        }
      } else {
        //设置默认选项
        state.form.id = "";
        state.form.printDetailEntity = defaultParamsFnc();
      }
    });
    return {
      uploadApplyLogo,
      option,
      state,
      add,
      del,
      close,
      saveClick,
      beforeClose,
    };
  },
};
</script>

<style lang="scss" scoped>
.setBasicParam {
  border: 1px solid #dcdfe6;
  border-radius: 5px;
  margin-bottom: 10px;
}
:deep(.el-form-item__content) {
  width: 215px;
  .el-select {
    width: 100%;
  }
}
.operation {
  color: #1abc9c;
}

:deep(.el-divider--horizontal) {
  margin: 0;
}
:deep(.el-dialog__header) {
  border-bottom: 1px solid #efefef;
}

:deep(.el-dialog__footer) {
  text-align: center;
}
</style>
