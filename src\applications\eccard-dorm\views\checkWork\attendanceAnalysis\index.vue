<template>
  <kade-route-card>
    <kade-table-filter @search="handleSearch" @reset="handleReset">
      <el-form inline label-width="90px" size="mini">
        <kade-linkage-select :data="linkageData" :value="state.form" @change="linkageChange" />
        <el-form-item label="考勤日期">
          <el-date-picker v-model="state.requestDate" type="daterange" range-separator="~" start-placeholder="请选择"
            end-placeholder="请选择 ">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="考勤时段">
          <el-select clearable v-model="state.form.attendancePeriodId" placeholder="全部">
            <el-option v-for="(item, index) in state.periodIdList" :key="index" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="分析记录列表">
      <template #extra>
        <div class="text">
          备注：系统会自动在考勤时段结束后进行分析，得到考勤结果和考勤汇总数据。
        </div>
      </template>
      <el-table border :data="state.data" v-loading="state.loading">
        <el-table-column show-overflow-tooltip v-for="(item, index) in column" :key="index" :label="item.label"
          :prop="item.prop" :width="item.width" align="center">
          <template #default="scope">
            {{ dictionaryFilter(scope.row[item.prop]) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center">
          <template #default="scope">
            <el-button type="text" class="green" @click="again(scope.row)" size="mini">重新分析</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination v-model:currentPage="state.form.currentPage" v-model:page-size="state.form.pageSize"
          :page-sizes="[10, 20, 30, 40]" background layout="total, sizes, prev, pager, next, jumper"
          :total="state.total" @size-change="handleSizeChange" @current-change="handleCurrentChange">
        </el-pagination>
      </div>
    </kade-table-wrap>
  </kade-route-card>
</template>

<script>
import { ElButton, ElForm, ElFormItem, ElSelect, ElOption, ElTable, ElTableColumn, ElPagination, ElDatePicker, ElMessage } from "element-plus"
import { reactive } from '@vue/reactivity';
import { dateStr } from "@/utils/date.js"
import { onMounted } from '@vue/runtime-core';
import linkageSelect from '@/applications/eccard-dorm/components/linkageSelect'
import { attendancePeriodList, attendanceAnalysisList, queryAttendanceAnalysis } from '@/applications/eccard-dorm/api.js'
const linkageData = {
  area: { label: '区域', valueKey: 'areaPath', key: 'areaPath' },
  building: { label: '楼栋', valueKey: 'buildId' },
  unit: { label: '单元', valueKey: 'unitNum' }
}
const column = [
  { label: '区域', prop: 'areaName' },
  { label: '楼栋', prop: 'buildName' },
  { label: '单元', prop: 'unitName' },
  { label: '考勤日期', prop: 'attendanceDate' },
  { label: '考勤时段', prop: 'attendancePeriodName' },
  { label: '分析状态', prop: 'status', isDict: 'true' },
  { label: '应到人数', prop: 'dueNumber' },
  { label: '实到人数', prop: 'actuaNumber' },
  { label: '请假人数', prop: 'leaveNumber' },
  { label: '晚归人数', prop: 'lateNumber' },
  { label: '未归人数', prop: 'notReturnNumber' },
  { label: '分析时间', prop: 'analysisTime' }
]
export default {
  components: {
    ElButton,
    ElForm,
    ElFormItem,
    ElSelect,
    ElOption,
    ElTable,
    ElTableColumn,
    ElPagination,
    ElDatePicker,
    "kade-linkage-select": linkageSelect,
  },
  setup() {
    const state = reactive({
      form: {
        pageSize: 10,
        currentPage: 1
      },
      data: [],
      total: 0,
      requestDate: '',
      loading: false,
      periodIdList: ''
    });
    const linkageChange = (val) => {
      state.form = { ...state, ...val }
    }
    const getPeriod = async () => {
      let { data } = await attendancePeriodList(state.form)
      state.periodIdList = data
    }
    const getList = async () => {
      console.log(state.form)
      if (state.requestDate && state.requestDate.length) {
        state.form.beginDate = dateStr(state.requestDate[0])
        state.form.endDate = dateStr(state.requestDate[1])
      } else {
        delete state.form.beginDate
        delete state.form.endDate
      }
      state.loading = true
      try {
        let { data: { list, total } } = await attendanceAnalysisList(state.form)
        state.data = list
        state.total = total
        state.loading = false
      } catch {
        state.loading = false
      }
    };
    const handleSearch = () => {
      getList()
    };
    const handleReset = () => {
      state.form = {
        pageSize: 10,
        currentPage: 1
      }
      state.requestDate = ''
      getList()
    }
    const handleCurrentChange = (val) => {
      state.form.currentPage = val
      getList()
    }
    const handleSizeChange = (val) => {
      state.form.currentPage = 1
      state.form.pageSize = val
      getList()
    }
    const again = async (row) => {
      let { analyseDate,areaId,areaPath,buildId,unitNum } = {...row}
      console.log(row)
      let param = { analyseDate,areaId,areaPath,buildId,unitNum }
      console.log(param)
      let { code, message } = await queryAttendanceAnalysis(param)
      if (code === 0) {
        ElMessage.success(message)
        getList()
      }
    };
    onMounted(() => {
      getList();
      getPeriod()
    })
    return {
      state,
      again,
      column,
      handleSearch,
      handleReset,
      linkageChange,
      linkageData,
      handleCurrentChange,
      handleSizeChange
    }
  }
}
</script>

<style lang="scss" scoped>
.green :hover {
  text-decoration: underline;
}

.text {
  color: rgb(199, 8, 8);
  margin-left: 50px;
  font-size: 12px;
}

:deep(.el-input--suffix .el-input__inner) {
  width: 193px;
}

:deep(.el-pagination .el-select .el-input .el-input__inner) {
  width: 100px;
}

:deep(.kade-table-wrap .table-wrap-head) {
  justify-content: start;
  align-items: center;
}

:deep(.el-date-editor.el-input, .el-date-editor.el-input__inner) {
  width: 193px;
}
</style>