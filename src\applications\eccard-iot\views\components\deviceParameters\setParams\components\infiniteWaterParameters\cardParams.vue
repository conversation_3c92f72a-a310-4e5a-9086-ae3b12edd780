<template>
  <div class="padding-form-box timeParam" v-loading="state.loading">
    <el-form inline size="small" label-width="120px">
      <el-row>
        <el-col :span="24">
          <el-form-item label="扣费1:">
            <el-input-number :min="0" v-model="state.form.paramContent.deduction1"></el-input-number>
          </el-form-item>
          <el-form-item label="费率1:">
            <el-input-number :min="0" v-model="state.form.paramContent.rate1" :precision="2"></el-input-number>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="扣费2:">
            <el-input-number :min="0" v-model="state.form.paramContent.deduction2"></el-input-number>
          </el-form-item>
          <el-form-item label="费率2:">
            <el-input-number :min="0" v-model="state.form.paramContent.rate2" :precision="2"></el-input-number>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="扣费3:">
            <el-input-number :min="0" v-model="state.form.paramContent.deduction3"></el-input-number>
          </el-form-item>
          <el-form-item label="费率3:">
            <el-input-number :min="0" v-model="state.form.paramContent.rate3" :precision="2"></el-input-number>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="扣费4:">
            <el-input-number :min="0" v-model="state.form.paramContent.deduction4"></el-input-number>
          </el-form-item>
          <el-form-item label="费率4:">
            <el-input-number :min="0" v-model="state.form.paramContent.rate4" :precision="2"></el-input-number>
          </el-form-item>
        </el-col>
      </el-row>
      <div>单位：元/计费单位</div>
    </el-form>
    <div style="text-align: center">
      <el-button size="mini" @click="beforeClose()">取消</el-button>
      <el-button size="mini" type="primary" :loading="state.loading" @click="saveClick()">保存</el-button>
    </div>
  </div>
</template>

<script>
import {
  ElRow,
  ElCol,
  ElForm,
  ElFormItem,
  ElInputNumber,
  ElButton,
  ElMessage,
} from "element-plus";
import { onMounted, reactive } from "vue";
import { useStore } from "vuex";
const defaultParamsFnc = () => {
  return {
    deduction1: 1,
    rate1: 1,
    deduction2: 1,
    rate2: 1,
    deduction3: 1,
    rate3: 1,
    deduction4: 1,
    rate4: 1,
  };
};

export default {
  components: {
    ElRow,
    ElCol,
    ElForm,
    ElFormItem,
    ElButton,
    ElInputNumber,
  },
  setup() {
    const store = useStore();
    const state = reactive({
      loading: false,
      form: {
        paramContent: defaultParamsFnc(),
        id: "",
        paramId: "",
        paramType: "CARD",
      },
    });
    const dictListFnc = () => {
      return store.state.deviceParameters[store.state.app.activeTab].dict
    }
    const saveClick = async () => {
      state.loading = true;
      let storeData = store.state.deviceParameters[store.state.app.activeTab]
      state.form.paramId = storeData.selectRow.id;
      let fn = state.form.id ? storeData.TabModule.detailsEditFnc : storeData.TabModule.detailsAddFnc

      let params = { ...state.form }
      console.log(params.paramContent);
      params.paramContent = JSON.stringify(params.paramContent)
      let { message, code } = await fn(params);
      if (code === 0) {
        setTimeout(async () => {
          ElMessage.success(message);
          await store.dispatch("deviceParameters/getParams", store.state.deviceParameters[store.state.app.activeTab].selectRow.id);
          init()
          state.loading = false;
        }, 1500);
      } else {
        state.loading = false;
      }
    };

    const beforeClose = () => {
      store.commit("deviceParameters/updateState", {
        key: store.state.app.activeTab,
        childKey: "isSetParams",
        payload: false,
      });
    };
    const init = () => {
      let val = store.state.deviceParameters[store.state.app.activeTab].detailsParams
      let arr = val.filter(item => item.paramType == 'CARD')
      if (arr && arr.length) {
        state.form = { ...arr[0] }
        state.form.paramContent = JSON.parse(state.form.paramContent)
      } else {
        //设置默认选项
        state.form.id = "";
        state.form.paramContent = defaultParamsFnc();
      }
    }
    onMounted(() => {
      init()
    });

    onMounted(() => {

    });
    return {
      state,
      dictListFnc,
      saveClick,
      beforeClose,
    };
  },
  name: "basicParam",
};
</script>

<style lang="scss" scoped>
:deep(.el-form-item__content) {
  width: 200px;

  .el-input-number {
    width: 100%;
  }

  .el-select {
    width: 100% !important;
  }
}
</style>
