<template>
  <div class="padding-box">
    <el-table border :data="details" style="width: 100%">
      <el-table-column
        align="center"
        v-for="(item, index) in column"
        :key="index"
        :prop="item.prop"
        :label="item.label"
      />
    </el-table>
  </div>
</template>
<script>
import { computed } from "vue";
import { useStore } from "vuex";

import { ElTable, ElTableColumn } from "element-plus";
const column = [
  { label: "餐时段名称", prop: "name" },
  { label: "餐时段开始时间", prop: "start" },
  { label: "餐时段结束时间", prop: "stop" },
];
export default {
  components: {
    ElTable,
    ElTableColumn,
  },
  setup() {
    const store = useStore();
    const details = computed(() => {
      let data = "";
      for (let item of store.state.deviceParameters[store.state.app.activeTab].detailsParams) {
        if (item.paramType == "time") {
          data = JSON.parse(item.paramContent).ListTime;
          break;
        }
      }
      if (data) {
        data = data.map((item, index) => {
          return { ...item, name: "餐时段" + (index + 1) };
        });
      }else{
        data=[]
      }

      return data;
    });
    return {
      column,
      details,
    };
  },
};
</script>
<style lang="scss" scoped>
</style>