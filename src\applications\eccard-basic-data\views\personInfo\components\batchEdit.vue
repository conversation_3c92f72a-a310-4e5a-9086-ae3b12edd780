<template>
  <el-dialog :model-value="isBatchEdit.isShow" :title="'批量修改人员' + (isBatchEdit.type == 'dept' ? '部门' : '状态')"
    width="90%" :before-close="beforeClose" :close-on-click-modal="false">
    <kade-table-wrap title="选择人员" style="margin-top:20px">
      <el-divider></el-divider>
      <kade-select-table :isShow="isBatchEdit.isShow" :value="[]" :reqFnc="getUserInfoListByPage"
        :selectCondition="state.selectCondition" :column="column" :params="params" @change="personChange" />
    </kade-table-wrap>
    <kade-table-wrap :title="isBatchEdit.type == 'dept' ? '部门' : '状态'" style="margin-top:20px;margin-bottom:20px">
      <el-divider></el-divider>
      <el-form inline size="mini" label-width="120px">
        <el-form-item label="部门修改为" v-if="isBatchEdit.type == 'dept'">
          <kade-dept-select-tree :value="state.form.userDept" valueKey="id" :multiple="false"
            @valueChange="(val) => (state.form.userDept = val.id)" />
        </el-form-item>
        <el-form-item label="状态修改为" v-else-if="isBatchEdit.type == 'status'">
          <el-select v-model="state.form.userState">
            <el-option v-for="(item, index) in statusList" :key="index" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </kade-table-wrap>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="beforeClose()" size="mini">返&nbsp;&nbsp;回</el-button>
        <el-button @click="submit()" type="primary" :loading="state.loading" size="mini">保&nbsp;&nbsp;存</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script>
import {
  ElDialog,
  ElDivider,
  ElForm,
  ElFormItem,
  ElSelect,
  ElOption,
  ElButton,
  ElMessage,
} from "element-plus";
import { reactive } from "@vue/reactivity";
import { useStore } from "vuex";
import { useDict } from "@/hooks/useDict.js";
import { computed, watch } from "@vue/runtime-core";
import {
  getUserInfoListByPage,
  batchEditDept,
  batchEditStatus,
} from "@/applications/eccard-basic-data/api";
import selectTable from "@/components/table/selectTable.vue";
import deptSelectTree from "@/components/tree/deptSelectTree.vue";
const column = [
  { label: "用户编号", prop: "userCode", isDict: false, width: "" },
  { label: "姓名", prop: "userName", isDict: false, width: "" },
  { label: "组织机构", prop: "deptName", isDict: false, width: "" },
  { label: "身份类别", prop: "roleName", isDict: false, width: "" },
];
const params = {
  currentPageKey: "beginPage",
  pageSizeKey: "rowNum",
  resListKey: "dataList",
  resTotalKey: "totalCount",
  value: {},
  tagNameKey: "userName",
  valueKey: "id",
};
export default {
  components: {
    ElDialog,
    ElDivider,
    ElForm,
    ElFormItem,
    ElButton,
    ElSelect,
    ElOption,
    "kade-select-table": selectTable,
    "kade-dept-select-tree": deptSelectTree,
  },
  props: {
    roleList: {
      type: Array,
      default: null,
    },
  },
  setup(props) {
    console.log(props);
    const store = useStore();
    const statusList = useDict("BASE_USER_STATE");
    const state = reactive({
      loading: false,
      selectCondition: [
        {
          label: "关键字",
          valueKey: "userName",
          placeholder: "姓名或编号关键字搜索",
          isSelect: false,
        },
        {
          label: "组织机构",
          valueKey: "userDept",
          dataKey: "deptPath",
          placeholder: "请选择",
          isSelect: false,
          isTree: "dept",
        },
        {
          label: "身份类别",
          valueKey: "userRole",
          placeholder: "请选择",
          isSelect: true,
          select: {
            list: [],
            option: {
              label: "roleName",
              value: "id",
            },
          },
        },
      ],
      form: {
        userIds: [],
        isAll: false,
      },
    });
    watch(
      () => props.roleList,
      (val) => {
        state.selectCondition[2].select.list = val;
      }
    );
    const isBatchEdit = computed(() => {
      return store.state.userInfo.isBatchEdit;
    });
    const personChange = (val) => {
      state.form.userIds = val.list.map((item) => item.id);
      state.form.isAll = val.isSelectAll;
      console.log(state.form);
    };

    const submit = async () => {
      if (!state.form.isAll) {
        if (!state.form.userIds.length) {
          return ElMessage.error("请选择人员！");
        }
      }
      let fn;
      if (store.state.userInfo.isBatchEdit.type == "dept") {
        if (!state.form.userDept) {
          return ElMessage.error("请选择部门");
        } else {
          fn = batchEditDept;
        }
      } else {
        if (!state.form.userState) {
          return ElMessage.error("请选择状态");
        } else {
          fn = batchEditStatus;
        }
      }
      state.loading = true
      let { code, message } = await fn(state.form);
      if (code === 0) {
        ElMessage.success(message);
        beforeClose();
        store.commit("userInfo/updateState", {
          key: "isUpdateList",
          payload: true,
        });
      }
      state.loading = false
    };

    const beforeClose = () => {
      store.commit("userInfo/updateState", {
        key: "isBatchEdit",
        payload: {
          type: "",
          isShow: false,
        },
      });
      state.form.userIds = [];
    };
    return {
      getUserInfoListByPage,
      column,
      params,
      statusList,
      state,
      isBatchEdit,
      personChange,
      submit,
      beforeClose,
    };
  },
};
</script>
<style lang="scss" scoped>
.el-divider--horizontal {
  margin: 0px 0px 10px;
}

.kade-table-wrap {
  padding-bottom: 0;
}
</style>