<template>
  <kade-modal v-bind="attrs" :modelValue="modelValue" @update:modelValue="update">
    <el-form
      :model="model" 
      :rules="rules" 
      ref="formRef" 
      label-width="100px"
      size="small"
      @keyup.enter="submit"
      v-loading="loading"
    >
      <template v-if="isEdit">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="头像" prop="userHeadUrl">
              <kade-single-image-upload :action="uploadApplyLogo" icon="iconuser" v-model="model.userHeadUrl" />
            </el-form-item>            
          </el-col>
          <el-col :span="12">
            <el-form-item label="用户账号" prop="userAccount">
              <el-input disabled placeholder="请输入" v-model="model.userAccount" />
            </el-form-item>            
          </el-col>
          <el-col :span="12">
            <el-form-item label="用户名称" prop="userName">
              <el-input placeholder="请输入" v-model="model.userName" />
            </el-form-item>            
          </el-col>
          <el-col :span="12">
            <el-form-item label="性别" prop="userSex">
              <el-radio-group v-model="model.userSex" disabled>
                <el-radio v-for="item in sexList" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
              </el-radio-group>
            </el-form-item>            
          </el-col>
          <el-col :span="12">
            <el-form-item label="邮箱" prop="userEmail">
              <el-input placeholder="请输入" v-model="model.userEmail" />
            </el-form-item>            
          </el-col>
          <el-col :span="12">
            <el-form-item label="电话" prop="userTel">
              <el-input placeholder="请输入" v-model="model.userTel" />
            </el-form-item>            
          </el-col>
        </el-row>
      </template>
      <template v-else>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="头像:">
              <el-avatar shape="square" :size="100" fit="cover" :src="model.userHeadUrl" @error="() => true">
                <i style="font-size: 50px;line-height: 100px" class="el-icon-user-solid"></i>
              </el-avatar>
            </el-form-item>            
          </el-col>
          <el-col :span="12">
            <el-form-item label="用户账号:">
              {{ model.userAccount }}
            </el-form-item>            
          </el-col>
          <el-col :span="12">
            <el-form-item label="用户名称:">
              {{ model.userName }}
            </el-form-item>            
          </el-col>
          <el-col :span="12">
            <el-form-item label="性别:">
              {{ dictionaryFilter(model.userSex) }}
            </el-form-item>            
          </el-col>
          <el-col :span="12">
            <el-form-item label="邮箱:">
              {{ model.userEmail }}
            </el-form-item>            
          </el-col>
          <el-col :span="12">
            <el-form-item label="电话:">
              {{ model.userTel }}
            </el-form-item>            
          </el-col>                                                          
        </el-row>
      </template>         
    </el-form>  
    <template #footer>
      <el-button size="small" icon="el-icon-circle-close" @click="cancel">取消</el-button>
      <el-button size="small" :icon="isEdit ? 'el-icon-circle-check' : 'el-icon-edit-outline'" :loading="btnLoading" type="primary" @click="submit">{{ isEdit ? '保存' : '编辑'}}</el-button>
    </template>
  </kade-modal>
</template>
<script>
import { 
  ElForm,
  ElFormItem,
  ElInput,
  ElRadio,
  ElRadioGroup,
  ElButton,
  ElMessage,
  ElAvatar,
  ElRow,
  ElCol,
} from 'element-plus';
import { FormLayout } from '@/service/dictionary';
import SingleImageUpload from '@/components/singleImageUpload';
import Modal from '@/components/modal';
import { reactive, ref, computed, watch } from 'vue';
import {  uploadApplyLogo } from '@/applications/unified_portal/api';

import { updateMe } from "@/applications/eccard-sys/api";
import { getUserInfo } from '@/service';
import { setCacheUser } from '@/utils';
import { useStore } from 'vuex';
import { useDict } from '@/hooks/useDict';
export default {
  components: {
    'el-form': ElForm,
    'el-form-item': ElFormItem,
    'el-input': ElInput,
    'el-radio': ElRadio,
    'el-radio-group': ElRadioGroup,
    'el-button': ElButton,
    'el-avatar': ElAvatar,
    'kade-single-image-upload': SingleImageUpload,
    'el-row': ElRow,
    'el-col': ElCol,  
    'kade-modal': Modal,
  },
  props: {
    title: {
      type: String,
      default: '',
    },
    modelValue: {
      type: Boolean,
      default: false,
    },    
  },  
  setup(props, context) {
    const store = useStore();
    const sexList = useDict('SYS_SEX');
    const isEdit = ref(false);
    const model = reactive({
      userHeadUrl: '',
      userName: '',
      userSex: '',
      userTel: '',
      userEmail: '',
      userId: '',
      userAccount: ''      
    });
    const loading = ref(false);
    const formRef = ref(null);
    const btnLoading = ref(false);
    const submit = () => {
      if(!isEdit.value) {
        isEdit.value = true;
        return;
      }
      formRef.value.validate(async (valid) => {
        if(valid) {
          console.log(model);
          let {id,userEmail,userHeadUrl,userName,userTel}={...model}
          let params={id,userEmail,userHeadUrl,userName,userTel}
          try {
            btnLoading.value = true;
            const { message } = await updateMe(params);
            ElMessage.success(message);
            const { data } = await getUserInfo();
            setCacheUser(data);
            store.commit('user/updateState', {
                key: 'userInfo',
                payload: data,
            });
          } catch(e) {
            throw new Error(e.message);
          } finally {
            btnLoading.value = false;
          }
        }
      });      
    }

    const loadInfo = async () => {

      console.log(store.state.user.userInfo);
       Object.assign(model, store.state.user.userInfo);
      /* try{
        loading.value = true;
        const { data } = await getUserInfo({ id: userId.value });
        Object.assign(model, data);
      }catch(e) {
        throw new Error(e.message);
      }finally{
        loading.value = false;
      } */
    }        
    const attrs = computed(() => {
      // eslint-disable-next-line no-unused-vars
      const { modelValue, ...attrs } = props;
      return attrs;
    });
    const update = (v) => {
      context.emit('update:modelValue', v);
    };   
    const cancel = () => {
      context.emit('update:modelValue', false);
    }
    watch(() => props.modelValue, () => {
      loadInfo();
    });    
    return {
      attrs,
      update,
      cancel,
      layout: FormLayout,
      model,
      loading,
      btnLoading,
      sexList,
      submit,
      formRef,
      isEdit,
      rules: {
        userName: [{ required: true, message: '请输入用户名称', trigger: 'blur' }], 
        userEmail: [{ required: true, message: '请输入邮箱', trigger: 'blur' }], 
        userTel: [{ required: true, message: '请输入手机号', trigger: 'blur' }],     
      },
      labelWidth: '50px',
      uploadApplyLogo,       
    }
  }
}
</script>