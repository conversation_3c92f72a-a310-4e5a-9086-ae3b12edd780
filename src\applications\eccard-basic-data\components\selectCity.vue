<template>
  <el-dropdown trigger="click" ref="dropdown" placement="bottom-start">
    <el-input style="cursor: pointer" :model-value="modelValue" readonly type="text" placeholder="请选择" suffix-icon="el-icon-arrow-down" />
    <template #dropdown>
      <el-cascader-panel ref="cascaderRef" :props="options" @change="handleChange" />
    </template>
  </el-dropdown>
</template>
<script>
import { ElCascaderPanel, ElInput, ElDropdown } from 'element-plus';
import { ref } from 'vue';
const cn = require('china-region')
export default {
  components: {
    'el-cascader-panel': ElCascaderPanel,
    'el-input': ElInput,
    'el-dropdown': ElDropdown,
  },
  emits: ['change'],
  props: {
    modelValue: {
      type: String,
      default: '',
    },
  },
  setup(props, context) {
    const cascaderRef = ref(null);
    const dropdown = ref(null);
    const options = {
      lazy: true,
      lazyLoad: (node, resolve) => {
        if(node.root) {
          resolve(cn.getProvinces().map(it => ({
            ...it,
            label: it.name,
            value: it.name,
            leaf: false,
          })));
        } else if(node.level === 1 || node.level === 2) {
          let data = [];
          if(node.level === 1){
            data = cn.getPrefectures(node.data.code).map(it => ({ ...it, label: it.name, value: it.name, leaf: false }));
          }
          if(!data.length) {
            data = cn.getCounties(node.data.code).map(it => ({ ...it, label: it.name, value: it.name, leaf: true }));
          }
          resolve(data);
        } else {
          resolve([]);
        }
      }
    };
    const handleChange = (value) => {
      dropdown.value.visible = false;
      context.emit('change', value.join('-'));
    }
    return {
      options,
      handleChange,
      cascaderRef,
      dropdown,
    }
  }
}
</script>