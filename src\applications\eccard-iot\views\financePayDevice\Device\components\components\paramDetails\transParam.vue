<template>
  <div class="padding-form-box transParam">
    <div class="param-box-item" v-for="(item,index) in state.detailList" :key="index">
      <div class="item-name">{{ item.label }}</div>
      <div class="item-val">{{ state.details[item.field] }}</div>
    </div>
  </div>
</template>

<script>
import {reactive} from "vue";

export default {
  setup() {
    const state = reactive({
      detailList: [
        {
          label: "充值机待机时长(秒)",
          field: "dev_czdj",
        },
        {
          label: "同卡充值转账时间间隔(秒)",
          field: "dev_cardjg",
        },
        {
          label: "主钱包最大余额(元)",
          field: "dev_Mmax",
        },
        {
          label: "主钱包单次充值/转账最大金额(元)",
          field: "dev_Mcmax",
        },
        {
          label: "主钱包定值充值金额(元)",
          field: "dev_Mcdin",
        },
        {
          label: "水控钱包最大余额(元)",
          field: "dev_Smax",
        },
        {
          label: "水控包单次充值/转账最大金额(元)",
          field: "dev_Scmax",
        },
      ],
      details: {
        dev_Mcdin: 0,
        dev_Mcmax: 0,
        dev_Mmax: 0,
        dev_Scdin: 0,
        dev_Scmax: 0,
        dev_Smax: 0,
        dev_cardjg: 0,
        dev_czdj: 0,
      },
    })
    return {
      state
    }
  },
  name: "transParam"
}
</script>

<style lang="scss" scoped>
.transParam {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  align-items: center;

  .param-box-item {
    width: 33.3333%;
    line-height: 48px;
    display: flex;

    .item-name {
      width: 50%;
      text-align: center;
      background: #f6f6f6;
      border: 1px solid #858585;
    }

    .item-val {
      width: 50%;
      text-align: center;
      border: 1px solid #858585;
    }
  }
}
</style>
