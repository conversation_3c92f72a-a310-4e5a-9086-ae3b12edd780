<template>
  <div>
    <el-dialog :model-value="dialogVisible" title="现金发放详情" width="100%" :before-close="beforeClose">
      <div>
        <el-card header="商家基本信息"> </el-card>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="off()" size="mini">关&nbsp;&nbsp;闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script>
import { reactive, toRefs } from "vue";
import {
  ElDialog,
  ElButton,

} from "element-plus";
export default {
  components: {
    "el-dialog": ElDialog,
    "el-button": ElButton,

  },
  props: {
    dialogVisible: {
      types: Boolean,
      default: false,
    },
  },
  setup(props, context) {
    const state = reactive({

    })
    const off = () => {
      context.emit("offDatails", false);
    };
    const beforeClose = () => {
      context.emit("offDatails", false);
    };
    return {
      ...toRefs(state),
      off,
      beforeClose,
    };
  },
};
</script>
<style lang="scss" scoped>
:deep(.el-dialog__header) {
  border-bottom: 1px solid #efefef;
}

:deep(.el-overlay) {
  position: absolute;
  background-color: rgba(0, 0, 0, 0);
}

:deep(.el-dialog__footer) {
  text-align: center;
}
</style>