<template>
  <kade-route-card>
    <kade-table-filter @search="handleSearch" @reset="handleReset">
      <el-form inline label-width="100px" size="small">
        <el-form-item label="终端型号">
          <el-select clearable v-model="state.form.deviceModel" placeholder="全部">
            <el-option v-for="(item, index) in state.listData.modelList" :key="index" :label="item.productMode" :value="item.productMode">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="所属区域">
          <kade-area-select-tree style="width: 100%" :value="state.form.areaId" valueKey="id" :multiple="false" @valueChange="(val) => (state.form.areaId = val.id)" />
        </el-form-item>
        <el-form-item label="设备机号">
          <el-input v-model="state.form.deviceNo" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="设备状态">
          <el-select clearable v-model="state.form.deviceStatus" placeholder="全部">
            <el-option v-for="(item, index) in deviceStaticList" :key="index" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="设备名称">
          <el-input v-model="state.form.deviceName" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="设备IP">
          <el-input v-model="state.form.deviceIp" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="所属工作站">
          <el-select v-model="state.form.workstationId">
            <el-option :label="item.name" :value="item.id" v-for="(item,index) in state.listData.workStationList" :key="index"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="连接类型">
          <el-select v-model="state.form.deviceConnectType">
            <el-option :label="item.label" :value="item.value" v-for="(item,index) in deviceConnectTypeList" :key="index"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="通道控制器列表">
      <template #extra>
        <el-button icon="el-icon-plus" @click="edit('','add')" size="small" class="btn-green">新增</el-button>
        <el-button icon="el-icon-daorutupian" @click="bindRead()" size="small" class="btn-purple">绑定读头</el-button>
        <el-button icon="el-icon-daorutupian" @click="addRecord()" size="small" class="btn-yellow">更换设备</el-button>
        <el-button icon="el-icon-daochu" size="small" class="btn-deep-blue" @click='handleExport'>导出</el-button>
      </template>
      <el-table style="width: 100%" height="55vh" v-loading="state.loading" :data="state.dataList" @rowClick="rowClick" highlight-current-row border stripe>
        <el-table-column v-for="(item,index) in column" :key="index" :width="item.width" :prop="item.prop" :label="item.label" align="center" show-overflow-tooltip>
          <template #default="scope" v-if="item.isDict">
            {{dictionaryFilter(scope.row[item.prop])}}
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="180px">
          <template #default="scope">
            <el-button class="green" size="mini" @click="detail(scope.row)" type="text">详情</el-button>
            <el-button class="green" size="mini" @click="edit(scope.row,'edit')" type="text">编辑</el-button>
            <el-button class="green" size="mini" @click="del(scope.row)" type="text">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination background :current-page="state.form.pageNum" layout="total, sizes, prev, pager, next, jumper" :page-sizes="[10, 20, 50, 100, 200]" :total="state.total" :page-size="state.form.pageSize" @current-change="handlePageChange" @size-change="handleSizeChange">
        </el-pagination>
      </div>
    </kade-table-wrap>
    <kade-access-controller-channel-edit :isShow="state.isEdit" :listData="state.listData" :type="state.editType" :data="state.selectRow" @close="editClose" />
    <kade-access-controller-channel-details :isShow="state.isDetails" :data="state.selectRow" @close="state.isDetails=false" />
    <kade-replace-record :data="state.selectRow" />
    <kade-read-bind :isShow="state.isBind" :listData="state.listData" :data="state.selectRow" @close="state.isBind=false" />
  </kade-route-card>
</template>
<script>
import { ElButton, ElForm, ElFormItem, ElInput, ElSelect, ElOption, ElTable, ElTableColumn, ElPagination, ElMessage, ElMessageBox } from "element-plus";
import { reactive, onMounted } from 'vue';
import { useStore } from 'vuex';
import { useDict } from "@/hooks/useDict.js";
import { getModel, getWorkStationList, getAccessControlDevice, delAccessControl, identityGatewayListNoPage,exportaccessControl } from "@/applications/eccard-iot/api";
import areaSelectTree from "@/components/tree/areaSelectTree.vue";
import edit from "./components/edit.vue"
import details from "./components/details"
import replaceRecord from "./components/replaceRecord"
import bind from "./components/bind"
const column = [
  { prop: "deviceModel", label: "终端型号", width: "" },
  { prop: "areaName", label: "所属区域", width: "" },
  { prop: "deviceNo", label: "设备机号", width: "" },
  { prop: "deviceName", label: "设备名称", width: "" },
  { prop: "deviceStatus", label: "设备状态", width: "", isDict: true,},
  { prop: "deviceConnectType", label: "连接类型", width: "", isDict: true },
  { prop: "deviceIp", label: "设备IP", width: "" },
]

export default {
  components: {
    ElButton,
    ElForm,
    ElFormItem,
    ElInput,
    ElSelect,
    ElOption,
    ElTable,
    ElTableColumn,
    ElPagination,
    "kade-area-select-tree": areaSelectTree,
    'kade-access-controller-channel-edit': edit,
    'kade-access-controller-channel-details': details,
    "kade-replace-record": replaceRecord,
    "kade-read-bind": bind

  },
  setup() {
    const store = useStore()
    const deviceStaticList = useDict("SYS_DEVICE_STATICE")
    const deviceConnectTypeList = useDict("SYS_DEVICE_CONNECT_TYPE")
    const state = reactive({
      isEdit: false,
      isDetails: false,
      isBind: false,
      form: {
        pageNum: 1,
        pageSize: 10
      },
      dataList: [],
      total: 0,
      editType: "",
      selectRow: {},
      listData: {
        modelList: [],
        workStationList: [],
        gatewayList: []
      },
    })
    //获取终端型号
    const queryModel = async () => {
      let { data } = await getModel();
      state.listData.modelList = data;
    };
    //获取工作站
    const queryWorkStationList = async () => {
      let { data: { list } } = await getWorkStationList();
      state.listData.workStationList = list;
    };
    //获取网关
    const queryGatewayList = async () => {
      let { data } = await identityGatewayListNoPage()
      state.listData.gatewayList = data;
    };
    const getList = async () => {
      state.selectRow = {}
      let { data: { total, list } } = await getAccessControlDevice(state.form)
      state.dataList = list
      state.total = total
    }
    const rowClick = row => {
      state.selectRow = row
    }
    const edit = (row, type) => {
      if (row.id) {
        state.selectRow = row
      }
      state.editType = type
      state.isEdit = true
    }
    const detail = (row) => {
      state.selectRow = row
      state.isDetails = true
    }
    const addRecord = () => {
      if (!state.selectRow.id) {
        return ElMessage.error("请选择设备！")
      }
      store.commit("identityDevice/accessControllerChannel/updateState", {
        key: "isRecord",
        payload: {
          type: "add",
          data: {},
          isShow: true
        },
      });
    }
    const bindRead = () => {
      if (!state.selectRow.id) {
        return ElMessage.error("请选择设备！")
      }
      state.isBind = true
    }
    const del = (row) => {
      ElMessageBox.confirm("确认删除?", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        let { code, message } = await delAccessControl(row.id);
        if (code === 0) {
          ElMessage.success(message);
          getList();
        }
      });
    }
    const handlePageChange = (val) => {
      state.form.pageNum = val
      getList()
    }
    const handleSizeChange = (val) => {
      state.form.pageNum = 1
      state.form.pageSize = val
      getList()
    }
    const handleSearch = () => {
      getList()
    }
    const handleReset = () => {
      state.form = {
        pageNum: 1,
        pageSize: 10
      }
    }
    const handleExport=async()=>{
      let res = await exportaccessControl(state.form)
      let url = window.URL.createObjectURL(
        new Blob([res],{type:'application/vnd.ms-excel'})
      )
      let link = document.createElement('a')
      link.href=url
      link.style.display='none'
      link.download='通道控制器信息表.xlsx'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }
    const editClose = (val) => {
      if (val) {
        getList()
      }
      state.isEdit = false
    }
    onMounted(() => {
      getList()
      queryModel()
      queryWorkStationList()
      queryGatewayList()
    })
    return {
      column,
      deviceStaticList,
      deviceConnectTypeList,
      state,
      rowClick,
      edit,
      detail,
      addRecord,
      bindRead,
      del,
      handlePageChange,
      handleSizeChange,
      handleSearch,
      handleReset,
      editClose,
      handleExport,
    }
  }
};
</script>
<style lang="scss" scoped>
:deep(.el-form-item__content) {
  .el-select {
    width: 200px;
  }
  .el-input__inner {
    width: 200px;
  }
}
:deep(.el-dialog__header) {
  border-bottom: 1px solid #efefef;
}

:deep(.el-overlay) {
  position: absolute;
  background-color: rgba(0, 0, 0, 0.3);
}

:deep(.el-dialog__footer) {
  text-align: center;
}
</style>