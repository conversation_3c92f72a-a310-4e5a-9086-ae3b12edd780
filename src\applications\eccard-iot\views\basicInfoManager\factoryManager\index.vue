<template>
  <div class="attendance-device padding-box">
    <kade-table-filter @search="handleSearch" @reset="handleReset">
      <el-form inline label-width="80px" size="mini">
        <el-form-item label="厂家名称">
          <el-input v-model="state.form.factoryName" placeholder="请输入"></el-input>
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="设备厂家列表">
      <template #extra>
        <el-button icon="el-icon-plus" type="success" size="mini" @click="handleEdit({},'add')">新增</el-button>
        <el-button icon="el-icon-daochu" class="btn-purple" size="mini" @click="handleExport()">导出</el-button>
      </template>
      <el-table border height="55vh" :data="state.dataList" v-loading="state.loading">
        <el-table-column show-overflow-tooltip prop="factoryName" label="厂家名称" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="contacts" label="联系人" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="phone" label="联系电话" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="address" label="厂家地址" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="remarks" label="备注" align="center"></el-table-column>
        <el-table-column label="操作" align="center">
          <template #default="scope">
            <el-button type="text" @click="handleEdit(scope.row,'details')" size="mini">详情</el-button>
            <el-button type="text" @click="handleEdit(scope.row,'edit')" size="mini">编辑</el-button>
            <el-button type="text" @click="handleDel(scope.row)" size="mini">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination v-model:currentPage="state.form.pageNum" v-model:page-size="state.form.pageSize" :page-sizes="[10, 20, 30, 40]" background layout="total, sizes, prev, pager, next, jumper" :total="state.total" @size-change="handleSizeChange" @current-change="handleCurrentChange">
        </el-pagination>
      </div>
    </kade-table-wrap>
    <kade-factory-edit :dialogVisible="state.dialogVisible" :data="state.rowData" :type="state.type" @edit="state.type='edit'" @close="close"></kade-factory-edit>
  </div>
</template>

<script>
import {
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElTable,
  ElTableColumn,
  ElPagination,
  ElMessage,
  ElMessageBox,
} from "element-plus";
import { reactive,onMounted } from "vue";
import {downloadXlsx} from "@/utils"
import { deviceFactoryList, deviceFactoryDel,deviceFactoryExport } from "@/applications/eccard-iot/api";
import edit from "./components/edit.vue";

export default {
  components: {
    ElButton,
    ElForm,
    ElFormItem,
    ElInput,
    ElTable,
    ElTableColumn,
    ElPagination,
    "kade-factory-edit": edit,
  },
  setup() {
    const state = reactive({
      loading:false,
      dialogVisible: false,
      form: {
        pageNum: 1,
        pageSize: 10
      },
      dataList: [],
      total: 0,
      rowData: {},
    });
    const getList = async () => {
      state.loading = true
      try {
        let { data: { list, total } } = await deviceFactoryList(state.form)
        state.dataList = list
        state.total = total
        state.loading = false
      }
      catch {
        state.loading = false
      }
    }
    const handleEdit = (row, type) => {
      state.type = type
      state.rowData = row
      state.dialogVisible = true;
    };
    const handleDel = ({ id }) => {
      ElMessageBox.confirm(`确认删除?`, {
        type: "warning",
        closeOnPressEscape: false,
        closeOnClickModal: false,
      }).then(async () => {
        try {
          const { message, code } = await deviceFactoryDel(id);
          if (code === 0) {
            ElMessage.success(message);
            getList()
          }
        } catch (e) {
          throw new Error(e.message);
        }
      });
    }
    const handleSearch = () => {
      getList()
    }
    const handleReset = () => {
      state.form = {
        pageNum: 1,
        pageSize: 10
      }
    }
    const handleExport=async ()=>{
      let params={...state.form}
      delete params.pageNum
      delete params.pageSize
      let res=await deviceFactoryExport(params)
      console.log(res);
      downloadXlsx(res,'设备厂家列表.xlsx')
    }
    const handleSizeChange = (val) => {
      state.form.pageNum = 1
      state.form.pageSize = val
      getList()
    }
    const handleCurrentChange = (val) => {
      state.form.pageNum = val
      getList()
    }
    const close = (val) => {
      if (val) {
        getList()
      }
      state.dialogVisible = false;
    };
    onMounted(()=>{
      getList()
    })
    return {

      state,
      handleEdit,
      handleDel,
      handleSearch,
      handleReset,
      handleExport,
      handleSizeChange,
      handleCurrentChange,
      close,
    };
  },
};
</script>

<style lang="scss" scoped>
:deep(.el-divider--horizontal) {
  margin: 0;
}
:deep(.el-dialog__footer) {
  text-align: center;
  border: 0;
}
:deep(.el-dialog__header) {
  border-bottom: 1px solid #eeeeee;
}
:deep(.el-dialog) {
  border-radius: 8px;
}
</style>