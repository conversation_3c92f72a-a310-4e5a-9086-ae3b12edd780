<template>
  <div class="cashierdetails" v-loading="state.loading">
    <kade-route-card style="height: auto">
      <kade-table-filter @search="search()" @reset="reset()">
        <el-form inline label-width="100px" size="mini">
          <el-form-item label="操作员">
            <el-select v-model="state.form.operatorList" filterable multiple collapse-tags placeholder="请选择" clearable>
              <el-option v-for="(item,index) in state.systemUserList" :key="index" :label="item.userName" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="用户编号">
            <el-input v-model="state.form.userCode" placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="用户姓名">
            <el-input v-model="state.form.userName" placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="组织机构:">
            <kade-dept-select-tree style="width: 100%" :value="state.form.deptId" valueKey="deptPath" :multiple="false" @valueChange="(val) => (state.form.deptPath = val.deptPath)" />
          </el-form-item>

          <el-form-item label="交易钱包">
            <el-select v-model="state.form.walletCodeList" multiple collapse-tags placeholder="请选择" size="small" clearable>
              <el-option v-for="(item,index) in state.walletActiveList" :key="index" :label="item.walletName" :value="item.walletCode" />
            </el-select>
          </el-form-item>
          <el-form-item label="交易类型">
            <el-select v-model="state.form.costTypeList" multiple collapse-tags placeholder="请选择" size="small" clearable>
              <el-option v-for="(item, index) in state.costTypeList" :key="index" :label="item.costTypeName" :value="item.costType">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="交易时间">
            <el-date-picker unlink-panels v-model="state.requestDate" type="datetimerange" :default-time="defaultTime" range-separator="~" start-placeholder="请选择日期" end-placeholder="请选择日期" />
          </el-form-item>
        </el-form>
      </kade-table-filter>
      <kade-table-wrap title="出纳明细报表" >
        <template #extra>
          <el-button class="btn-blue" icon="el-icon-daochu" size="mini" @click="exportClick()">导出</el-button>
        </template>
        <el-table :data="state.dataList" border height="55vh">
          <el-table-column show-overflow-tooltip prop="operatorName" label="操作员" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip prop="userCode" label="用户编号" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip prop="userName" label="用户姓名" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip prop="deptName" label="组织机构" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip prop="tradeDate" label="交易时间" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip prop="walletName" label="交易钱包" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip prop="costTypeName" label="交易类型" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip prop="tradeAmount" label="交易金额" align="center">
            <template #default="scope">
              {{Number(scope.row.tradeAmount).toFixed(2)}}
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip prop="tradeSourceName" label="交易来源" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip prop="tradeModeName" label="交易方式" align="center"></el-table-column>
        </el-table>
        <div class="pagination">
          <el-pagination :currentPage="state.form.pageNum" :page-size="state.form.pageSize" :page-sizes="[10, 20, 30, 40]" :small="small" background layout="total, sizes, prev, pager, next, jumper" :total="state.total" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
        </div>
      </kade-table-wrap>
    </kade-route-card>
  </div>
</template>

<script>
import { reactive } from "@vue/reactivity";
import {
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElDatePicker,
  ElButton,
  ElTable,
  ElTableColumn,
  ElPagination,
} from "element-plus";
import deptSelectTree from "@/components/tree/deptSelectTree";
import {
  // getWalletActiveList,
  cashierDetailCostTypeList,
  cashierDetailWalletList,
  getSystemUser,
  cashierDetail,
  cashierDetailExport,
} from "@/applications/eccard-finance/api";
import { downloadXlsx } from "@/utils";
import { onMounted } from "@vue/runtime-core";
import { timeStr } from "@/utils/date.js";
import { requestDate } from "@/utils/reqDefaultDate";
const defaultTime = [
  new Date(new Date(new Date().toLocaleDateString()).getTime()),
  new Date(
    new Date(new Date().toLocaleDateString()).getTime() +
    24 * 60 * 60 * 1000 -
    1
  ),
];
export default {
  components: {
    ElForm,
    ElFormItem,
    ElSelect,
    ElOption,
    ElInput,
    ElDatePicker,
    ElButton,
    ElTable,
    ElTableColumn,
    ElPagination,
    "kade-dept-select-tree": deptSelectTree,
  },
  setup() {
    const state = reactive({
      loading: false,
      requestDate: [requestDate()[0], requestDate()[1]],
      walletActiveList: [],
      form: {
        pageNum: 1,
        pageSize: 10,
        operatorList: [],
        costTypeList: [],
        walletCodeList: []
      },
      dataList: [],
      total: 0,
      costTypeList: [],
      systemUserList: [],
    });

    const getList = () => {
      if (state.requestDate && state.requestDate.length) {
        state.form.startDateTime = timeStr(state.requestDate[0]);
        state.form.endDateTime = timeStr(state.requestDate[1]);
      } else {
        delete state.form.startDateTime;
        delete state.form.endDateTime;
      }
      let params = { ...state.form };
      if (params.operatorList && params.operatorList.length) {
        params.operatorList = params.operatorList.join(",");
      } else {
        delete params.operatorList;
      }
      if (params.costTypeList && params.costTypeList.length) {
        params.costTypeList = params.costTypeList.join(",");
      } else {
        delete params.costTypeList;
      }
      if (params.walletCodeList && params.walletCodeList.length) {
        params.walletCodeList = params.walletCodeList.join(",");
      } else {
        delete params.walletCodeList;
      }
      state.loading = true
      cashierDetail(params).then((res) => {
        let {
          data: {
            generate,
            pageInfo: { list, total },
          },
        } = res;
        state.dataList = list;
        state.total = total;
        if (list.length > 0) {
          state.dataList.push({ ...generate });
        }
        state.loading = false
        // state.form = res.data;
      }).catch(() => {
        state.loading = false
      });
    };

    const search = () => {
      getList();
    };
    const reset = () => {
      state.form = {
        pageNum: 1,
        pageSize: 10,
      };
      state.requestDate = [requestDate()[0], requestDate()[1]];
    };
    const handleSizeChange = (val) => {
      state.form.pageNum = 1;
      state.form.pageSize = val;
      getList();
    };
    const handleCurrentChange = (val) => {
      state.form.pageNum = val;
      getList();
    };
    const queryWalletActiveList = () => {
      cashierDetailWalletList().then((res) => {
        state.walletActiveList = res.data;
      });
    };
    const querycostTypeList = () => {
      cashierDetailCostTypeList().then((res) => {
        console.log(res);
        state.costTypeList = res.data;
      });
    };
    const querySystemUser = () => {
      getSystemUser().then((res) => {
        state.systemUserList = res.data;
      });
    };
    const exportClick = async () => {
      if (state.requestDate && state.requestDate.length) {
        state.form.startDateTime = timeStr(state.requestDate[0]);
        state.form.endDateTime = timeStr(state.requestDate[1]);
      } else {
        delete state.form.startDateTime;
        delete state.form.endDateTime;
      }
      let params = { ...state.form };
      if (params.operatorList && params.operatorList.length) {
        params.operatorList = params.operatorList.join(",");
      } else {
        delete params.operatorList;
      }
      if (params.costTypeList && params.costTypeList.length) {
        params.costTypeList = params.costTypeList.join(",");
      } else {
        delete params.costTypeList;
      }
      if (params.walletCodeList && params.walletCodeList.length) {
        params.walletCodeList = params.walletCodeList.join(",");
      } else {
        delete params.walletCodeList;
      }
      state.loading = true
      try {
        let res = await cashierDetailExport(params);
        downloadXlsx(res, "出纳明细报表.xlsx");
        state.loading = false
      }
      catch {
        state.loading = false
      }
    };
    onMounted(() => {
      getList();
      queryWalletActiveList();
      querycostTypeList();
      querySystemUser();
    });
    return {
      defaultTime,
      state,
      search,
      exportClick,
      reset,
      handleSizeChange,
      handleCurrentChange,
    };
  },
};
</script>

<style lang="scss" scoped>
// :deep(.el-range-editor--mini.el-input__inner){
//     width: 205px;
// }
:deep(.el-select .el-input__inner) {
  height: 28px;
  width: 205px;
}
:deep(.el-input--mini .el-input__inner) {
  width: 205px;
}
:deep(.el-pagination .el-select .el-input .el-input__inner) {
  width: 100px;
}
:deep(.el-pagination__editor.el-input .el-input__inner) {
  width: 46px;
}
</style>