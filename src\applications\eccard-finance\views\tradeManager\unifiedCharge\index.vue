<template>
  <kade-route-card>
    <kade-table-wrap title="统一收费列表">
      <template #extra>
        <el-button icon="el-icon-plus" size="small" class="btn-green" @click="add()">创建收费</el-button>
        <el-button icon="el-icon-daoru" size="small" class="btn-blue" @click="exportList()">导出</el-button>
        <el-button icon="el-icon-daochu" size="small" class="btn-purple">打印</el-button>
      </template>
      <kade-tab-wrap :tabs="tabs" v-model="state.tab" @active="active">
        <template #qb>
          <kade-all-charge/>
        </template>
        <template #dsh>
          <kade-examine-charge/>
        </template>
        <template #ytg>
          <kade-to-pass-charge/>
        </template>
        <template #wtg>
          <kade-out-pass-charge/>
        </template>
      </kade-tab-wrap>
      <kade-edit-charge/>
      <kade-person-charge/>
      <kade-details-charge/>
    </kade-table-wrap>
  </kade-route-card>
  
</template>
<script>
import { ElButton } from "element-plus";
import { onMounted, reactive } from "vue";
import { useStore } from "vuex";
import {getUnifiedChargeType,getRolelist,getCardTypeList} from "@/applications/eccard-finance/api";

import allCharge from "./allCharge.vue"
import examineCharge from "./examineCharge.vue"
import outPassCharge from "./outPassCharge.vue"
import toPassCharge from "./toPassCharge.vue"

import editCharge from "./components/editCharge.vue"
import personList from "./components/personList.vue"
import detailsCharge from "./components/detailsCharge.vue"

const tabs = [
  {name: "qb",label: "全部",},
  {name: "dsh",label: "待审核",},
  {name: "ytg",label: "已通过",},
  {name: "wtg",label: "未通过",},
];
export default {
  components: {
    ElButton,
    "kade-all-charge":allCharge,
    "kade-examine-charge":examineCharge,
    "kade-to-pass-charge":toPassCharge,
    "kade-out-pass-charge":outPassCharge,
    "kade-edit-charge":editCharge,
    "kade-person-charge":personList,
    "kade-details-charge":detailsCharge,
  },
  setup() {
    const store = useStore();
    const state = reactive({
      tab: "qb",
      dialogVisibleAdd: false,
      isImportReversal: false,
    });

    const getChargeTypeList=async ()=>{
      let {data,code}=await getUnifiedChargeType({uctStatus:"ENABLE_TRUE"})
      if(code===0){
          store.commit("chargeData/updateState", {
          key: "ChargeTypeList",
          payload: data
        });
      }
    }
    //获取身份类别
    const queryRolelist = () => {
      getRolelist().then((res) => {
        store.commit("chargeData/updateState", {
          key: "roleList",
          payload: res.data,
        });
      });
    };

    //获取卡片类型
    const queryCardTypeList = () => {
      getCardTypeList().then((res) => {
        store.commit("chargeData/updateState", {
          key: "cardTypeList",
          payload: res.data,
        });
      });
    };
    const active=()=>{
       store.commit("chargeData/updateState", {
          key: "selectRow",
          payload: "",
        });
    }
    const add=()=>{
      store.commit("chargeData/updateState", {
        key: "isEditData",
        payload: {
          isShow:true,
          isEdit:false
        },
      });
    }

    onMounted(() => {
      getChargeTypeList()
      queryRolelist()
      queryCardTypeList()
    });
    return {
      state,
      tabs,
      add,
      active
    };
  },
};
</script>
<style lang="scss" scoped>
.grant-search-box {
  :deep(.el-form-item__content){
    .el-input__inner {
      width: 120px;
    }
    .el-date-editor {
      width: 360px;
    }
  }
}
</style>