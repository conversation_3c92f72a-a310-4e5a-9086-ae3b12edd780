<template>
  <kade-route-card style="height: 100%">
    <div class="deviceRealTimeMonitor">
      <kade-table-wrap title="区域" style="width: 300px; height: 100%; margin-right: 20px">
        <el-divider></el-divider>
        <div class="search-box">
          <el-input v-model="state.areaKeyword" placeholder="请输入关键字搜索" size="small" clearable>
            <template #append>
              <el-button icon="el-icon-sousuo" @click="areaSearch()">查询</el-button>
            </template>
          </el-input>
          <el-tree class="area-tree" :data="state.areaCheckTreeList" :props="defaultProps" default-expand-all
            @node-click="handleNodeClick">
            <template #default="{ node }">
              <div class="custom-tree-node hasbtn">
                <span class="left">
                  <i class="el-icon-location-information"></i>
                  <span style="margin-left: 5px; color: #333">{{
                      node.label
                  }}</span>
                </span>
              </div>
            </template>
          </el-tree>
        </div>
      </kade-table-wrap>
      <kade-table-wrap title="设备监控列表" style="flex: 1; height: 100%">
        <el-divider></el-divider>
        <div class="padding-box" style="height: 100%">
          <el-form size="small" inline label-width="100px" style="min-width: 800px">
            <el-form-item label="设备类型：">
              <el-select v-model="state.form.deviceType" clearable>
                <el-option v-for="(item, index) in state.deviceTypeList" :key="index" :label="item.productName"
                  :value="item.productName"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="设备状态：">
              <el-select v-model="state.form.deviceOnline" clearable>
                <el-option v-for="(item, index) in deviceStatic" :key="index" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
            <el-button @click="getList()" icon="el-icon-sousuo" size="small" class="shop-upload" type="primary">搜索
            </el-button>
          </el-form>
          <div class="device-box" v-loading="state.loading">
            <div class="device-item" v-for="(item, index) in state.deviceList" :key="index" :style="{
              'background-color': colorFnc(item.deviceOnline).background,
            }">
              <div class="status-box">
                <div class="icon">
                  <div v-for="(v, i) in 4" :key="i" class="icon-item"></div>
                </div>
                <div class="status" :style="{ color: colorFnc(item.deviceOnline).color }">
                  <span class="status-icon" :style="{
                    'background-color': colorFnc(item.deviceOnline).color,
                  }"></span>{{ filterDictionary(item.deviceOnline, deviceStatic) }}
                </div>
              </div>
              <div class="device-num">机号：{{ item.deviceNo }}</div>
              <div class="device-ip">IP：{{ item.deviceIp }}</div>
            </div>
          </div>
        </div>
      </kade-table-wrap>
    </div>
  </kade-route-card>
</template>
<script>
import { onMounted, reactive, onBeforeUnmount } from "vue";
import {
  ElDivider,
  ElInput,
  ElTree,
  ElForm,
  ElFormItem,
  ElSelect,
  ElOption,
  ElButton,
} from "element-plus";
import { getUserAreaPurview } from "@/applications/eccard-basic-data/api";
import { getDeviceStatus, getDeviceType } from "@/applications/eccard-iot/api";
import { useDict } from "@/hooks/useDict";

import { makeTree, filterDictionary } from "@/utils/index.js";
const defaultProps = {
  children: "children",
  label: "areaName",
};

const colorFnc = (val) => {
  if (val === "FALSE") {
    return {
      color: "#999999",
      background: "#eeeeee",
    };
  } else if (val === "TRUE") {
    return {
      color: "#02D200",
      background: "#02d2001a",
    };
  }
};
export default {
  components: {
    ElDivider,
    ElInput,
    ElTree,
    ElForm,
    ElFormItem,
    ElSelect,
    ElOption,
    ElButton,
  },
  setup() {
    const deviceStatic = useDict("SYS_BOOL_STRING");
    const state = reactive({
      areaKeyword: "",
      loading: false,
      areaCheckTreeList: [],
      areaCheckTreeList_copy: [],
      deviceTypeList: [],
      form: {},
      deviceList: [],
      timer: Function,
    });
    const queryAreaCheckList = () => {
      getUserAreaPurview().then((res) => {
        let arr = makeTree(res.data, "id", "areaParentId", "children");
        state.areaCheckTreeList = [...arr];
        state.areaCheckTreeList_copy = [...arr];
      });
    };

    const queryDeviceType = async () => {
      let { data } = await getDeviceType();
      state.deviceTypeList = data;
    };

    const getList = async () => {
      state.loading = true;
      for (let key in state.form) {
        if (!state.form[key]) {
          delete state.form[key]
        }
      }
      let { data, code } = await getDeviceStatus(state.form);
      if (code === 0) {
        state.deviceList = data;
      } else {
        state.deviceList = [];
      }
      state.loading = false;
    };

    const mapTree = (value, arr) => {
      let newarr = [];
      arr.forEach((element) => {
        if (element.areaName.indexOf(value) > -1) {
          // 判断条件
          newarr.push(element);
        } else {
          if (element.children && element.children.length > 0) {
            let redata = mapTree(value, element.children);
            if (redata && redata.length > 0) {
              let obj = {
                ...element,
                children: redata,
              };
              newarr.push(obj);
            }
          }
        }
      });
      return newarr;
    };

    const areaSearch = () => {
      state.areaCheckTreeList = mapTree(state.areaKeyword, state.areaCheckTreeList_copy)
    }

    const handleNodeClick = (row) => {
      state.form.areaPath = [row.areaPath];
      getList();
    };
    onMounted(() => {
      queryDeviceType();
      queryAreaCheckList();
      getList();
      state.timer = setInterval(() => {
        getList();
      }, 1000 * 30)
    });
    onBeforeUnmount(() => {
      clearInterval(state.timer)
    })
    return {
      filterDictionary,
      defaultProps,
      colorFnc,
      deviceStatic,
      state,
      areaSearch,
      getList,
      handleNodeClick,
    };
  },
};
</script>
<style lang="scss" scoped>
.deviceRealTimeMonitor {
  height: 79vh;
  display: flex;
  justify-content: space-between;

  .search-box {
    padding: 10px;
    height: 70vh;
  }

  .area-tree {
    margin-top: 20px;
    height: 63vh;
    overflow-y: scroll;
  }

  .device-box {
    width: 100%;
    height: 65vh;
    overflow-y: scroll;

    .device-item {
      display: inline-block;
      box-sizing: border-box;
      min-width: 150px;
      width: 15%;
      height: 100px;
      padding: 10px 10px;
      background: rgba(2, 210, 0, 0.101960784313725);
      border-radius: 5px;
      margin: 10px;

      .status-box {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .icon {
          display: flex;
          justify-content: space-between;
          align-items: center;
          flex-wrap: wrap;
          width: 27px;

          .icon-item {
            width: 12px;
            height: 12px;
            border-radius: 2px;
            background: #3399ff;
            margin-bottom: 3px;
          }
        }

        .status-icon {
          display: inline-block;
          width: 10px;
          height: 10px;
          border-radius: 50%;
          margin-right: 3px;
        }
      }

      .device-num {
        margin: 5px 0 3px;
      }
    }
  }
}

:deep(.el-divider--horizontal) {
  margin: 0;
}
</style>