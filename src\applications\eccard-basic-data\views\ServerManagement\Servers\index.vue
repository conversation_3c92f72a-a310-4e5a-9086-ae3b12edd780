<template>
  <div class="server-management">
    <div class="search-box">
      <div class="top-box">
        <div class="top-box-left">
          <i class="el-icon-search"></i>
          <span class="text">条件筛选</span>
        </div>
        <div class="top-box-right">
          <el-button icon="el-icon-refresh-right" size="mini">重置</el-button>
          <el-button icon="el-icon-search" type="primary" size="mini"
            >搜索</el-button
          >
        </div>
      </div>
      <el-divider></el-divider>
      <div class="bottom-box">
        <el-form inline label-width="80px" size="mini">
          <el-form-item label="操作系统">
            <el-input placeholder="全部" size="mini"></el-input>
          </el-form-item>
          <el-form-item label="服务器IP">
            <el-input placeholder="请输入"></el-input>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="table-box">
      <div class="title-box">
        <div class="title-box-left">
          <i class="el-icon-s-fold"></i>
          <span class="text">服务器列表</span>
        </div>
        <div class="title-box-right">
          <el-button type="success" icon="el-icon-plus" size="mini" @click="edit('add')">新增</el-button>
          <el-button type="danger"  icon="el-icon-delete-solid" size="mini">删除</el-button>
        </div>
      </div>
      <el-table :data="state.dataList" border style="width: 100%">
        <el-table-column type="selection" align="center"></el-table-column>
        <el-table-column prop="ip" label="服务器IP" align="center">
        </el-table-column>
        <el-table-column prop="system" label="操作系统" align="center">
        </el-table-column>
        <el-table-column prop="CPU" label="CPU核心数" align="center">
        </el-table-column>
        <el-table-column prop="CPUhz" label="CPU核频率(GHz)" align="center">
        </el-table-column>
        <el-table-column prop="Memory" label="内存大小(GB)" align="center">
        </el-table-column>
        <el-table-column prop="disk" label="磁盘大小(GB)" align="center">
        </el-table-column>
        <el-table-column prop="server" label="部署服务" align="center">
        </el-table-column>
        <el-table-column label="操作" align="center">
          <template #default="scope">
            <el-button type="text" size="mini" @click="edit('detail',scope.row)">查看详情</el-button>
            <el-button type="text" size="mini" @click="edit('edit',scope.row)">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-size="10"
          layout="total, prev, pager, next, jumper"
          :total="100"
        >
        </el-pagination>
      </div>
    </div>
    <kade-server-edit :dialogTitle="state.dialogTitle" :dialogData="state.dialogData" :dialogVisible="state.dialogVisible"  @close="close"></kade-server-edit>
  </div>
</template>

<script>
import {
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElDivider,
  ElTable,
  ElTableColumn,
  ElPagination,
} from "element-plus";
import { reactive } from "@vue/reactivity";
import edit from "./components/edit.vue"

export default {
  components: {
    ElButton,
    ElForm,
    ElFormItem,
    ElInput,
    ElDivider,
    ElTable,
    ElTableColumn,
    ElPagination,
    "kade-server-edit":edit,
  },
  setup() {
    const state = reactive({
      currentPage: 1,
      dialogVisible:false,
      dialogTitle:"",
      dialogData:{},
      form: {
        currentPage: 1,
        pageSize: 10
      },
      dataList: [
        {
          ip: "***********",
          system: "win7",
          CPU: "I5",
          CPUhz: "50hz",
          Memory: "8g",
          disk: "500g",
          server: "a,b,v,4,5,6",
        },
        {
          ip: "***********",
          system: "win7",
          CPU: "I5",
          CPUhz: "50hz",
          Memory: "8g",
          disk: "500g",
          server: "1,2,3,4,5,6",
        },
        {
          ip: "***********",
          system: "win7",
          CPU: "I5",
          CPUhz: "50hz",
          Memory: "8g",
          disk: "500g",
          server: "1,2,3,4,5,6"
        },
      ],
    });

    const edit=(type,row)=>{
      console.log(type,row)
      if(type === 'add'){
        state.dialogTitle="新增服务器"
      }else if(type === 'edit') {
        state.dialogTitle="编辑服务器"
        state.dialogData=row
      }else if(type === 'detail') {
        state.dialogTitle="服务器详情"
        state.dialogData=row
      }
      state.dialogVisible=true
    };
    const close = ()=>{
      state.dialogVisible=false
      state.dialogData={}
    };
    const handleSizeChange = val => {
      console.log(`每页 ${val} 条`);
    };
    const handleCurrentChange = val => {
      console.log(`当前页: ${val}`);
    };
    return {
      state,
      close,
      edit,
      handleSizeChange,
      handleCurrentChange,
    };
  }
};
</script>

<style lang="scss" scoped>
.server-management {
  padding: 20px;
  width: 100%;
  box-sizing: border-box;
  .search-box {
    width: 100%;
    border: 1px solid #eeeeee;
    border-radius: 5px;
    .top-box {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px;
      .top-box-left {
        .text {
          margin-left: 10px;
        }
      }
    }
    .bottom-box {
      margin-top: 10px;
      padding-left: 5px;
    }
  }
  .table-box {
    border: 1px solid #eeeeee;
    border-radius: 5px;
    margin-top: 20px;

    .title-box {
      padding: 10px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .text {
        margin-left: 10px;
      }
    }
    .pagination {
      margin: 10px;
    }
  }
  
}
:deep(.el-divider--horizontal) {
  margin: 0;
}
:deep(.el-dialog__footer){
  border: 0;
  text-align: center;
}
:deep(.el-dialog__header){
  border-bottom: 1px solid  #eeeeee;
}
</style>