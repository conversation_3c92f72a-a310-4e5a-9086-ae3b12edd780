<template>
  <div class="personnelInformation">
    <kade-route-card>
      <kade-table-filter @search="search" @reset="reset">
        <el-form inline label-width="120px" size="small">
          <el-form-item label="编号/姓名">
            <el-input size="small" v-model="state.queryListForm.keyWord" placeholder="请输入用户编号或姓名" />
          </el-form-item>
          <el-form-item label="组织机构">
            <kade-dept-select-tree style="width: 100%" :value="state.queryListForm.departPath" valueKey="deptPath" :multiple="false" @valueChange="(val) => (state.queryListForm.departPath = val.deptPath)" />
          </el-form-item>
          <el-form-item label="身份类别">
            <el-select clearable v-model="state.queryListForm.userRoleId" placeholder="请选择">
              <el-option v-for="(item, index) in state.roleList" :key="index" :label="item.roleName" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="虚拟卡号">
            <el-input size="small" v-model="state.queryListForm.cardNo" placeholder="请输入物理卡号" />
          </el-form-item>
          <el-form-item label="虚拟卡状态">
            <el-select clearable v-model="state.queryListForm.cardStatus" placeholder="请选择">
              <el-option v-for="(item, index) in state.PERSON_VIRTUAL_CARD_STATUS_LIST" :key="index" :label="item.dictValue" :value="item.dictCode">
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </kade-table-filter>
      <kade-table-wrap title="人员列表">
        <template #extra>
          <el-button icon="el-icon-plus" size="small" type="success" @click="enable()">启用</el-button>
          <el-button icon="el-icon-close" size="small" type="danger" @click="disable()">禁用</el-button>
        </template>
        <el-table style="width: 100%" :data="state.dataList" v-loading="false" highlight-current-row border stripe @row-click="rowClick" @selection-change="selectionChange">
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column label="人员编号" prop="userCode" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column label="人员姓名" prop="userName" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column label="组织机构" prop="departName" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column label="卡类" prop="cardType" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column label="卡片类别" prop="cardCategoryName" align="center" show-overflow-tooltip>
            <template #default="scope">
              {{dictionaryFilter(scope.row.cardCategory)}}
            </template>
          </el-table-column>
          <el-table-column label="使用人员" prop="userNameOfUse" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column label="虚拟卡号" prop="cardNo" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column label="虚拟卡状态" prop="virtualCardStatus" align="center" show-overflow-tooltip>
            <template #default="scope">
              {{dictionaryFilter(scope.row.virtualCardStatus)}}
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination">
          <el-pagination background v-model:current-page="state.queryListForm.currentPage" layout="total, sizes, prev, pager, next, jumper" :page-sizes="[10, 20, 50, 100, 200]" :total="state.dataListTotal" v-model:page-size="state.queryListForm.pageSize" @current-change="handlePageChange" @size-change="handleSizeChange">
          </el-pagination>
        </div>
      </kade-table-wrap>
    </kade-route-card>
  </div>
</template>
<script>
import {
  ElTable,
  ElTableColumn,
  ElForm,
  ElFormItem,
  ElInput,
  ElPagination,
  ElSelect,
  ElMessage,
  ElOption,
  ElButton,
  ElMessageBox,
} from "element-plus";
import { onMounted, reactive } from "vue";
import {
  getPersonVirtualCardListByPage,
  updatePersonVirtualCardStatus,
} from "@/applications/eccard-card/api";
import {
  getDepartCheckList,
  getRolelist,
} from "@/applications/eccard-finance/api";
import { makeTree } from "@/utils";
import deptSelectTree from "@/components/tree/deptSelectTree.vue";
export default {
  components: {
    "el-table": ElTable,
    "el-table-column": ElTableColumn,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    "el-input": ElInput,
    "el-pagination": ElPagination,
    "el-select": ElSelect,
    "el-option": ElOption,
    "el-button": ElButton,
    "kade-dept-select-tree": deptSelectTree,
  },
  setup() {
    const state = reactive({
      dialogVisible: false,
      dataList: [],
      dataListTotal: 0,

      selectRow: [],

      departCheckList: [], //组织机构列表
      roleList: [], //身份类别列表

      queryListForm: {
        currentPage: 1,
        pageSize: 10,
      },

      PERSON_VIRTUAL_CARD_STATUS_LIST: JSON.parse(
        localStorage.getItem("kade_cache_dictionary")
      ).filter((item) => item.dictType == "PERSON_VIRTUAL_CARD_STATUS"), //卡片操作类型
    });
    const getList = () => {
      getPersonVirtualCardListByPage(state.queryListForm).then((res) => {
        state.dataList = res.data.list;
        state.dataListTotal = res.data.total;
        state.refundTotal = res.data.refundTotal;
        state.sendCardTotal = res.data.total;
      });
    };
    const queryDepartCheckList = () => {
      getDepartCheckList().then((res) => {
        let arr = makeTree(res.data, "deptId", "deptParentId", "children");
        state.departCheckList = arr;
      });
    };
    const queryRolelist = () => {
      getRolelist().then((res) => {
        state.roleList = res.data;
      });
    };
    const selectionChange=val=>{
       state.selectRow =val;
    }

    const search = () => {
      getList();
      state.selectRow =[];
    };
    const reset = () => {
      state.queryListForm = {
        currentPage: 1,
        pageSize: 10,
      };
    };
    const off = () => {
      state.isShow = false;
    };
    const enable = () => {
      if (state.selectRow.length) {
        ElMessageBox.confirm(`确定要启用选择的虚拟卡吗?`, {
          type: "warning",
          closeOnPressEscape: false,
          closeOnClickModal: false,
        }).then(async () => {
          try {
            const { message } = await updatePersonVirtualCardStatus(
              "VIRTUAL_NORMAL",
              state.selectRow.map(item=>item.id)
            );
            ElMessage.success(message);
            getList();
          } catch (e) {
            throw new Error(e.message);
          }
        });
      } else {
        ElMessage.error("请选择人员！");
      }
    };
    const disable = () => {
      if (state.selectRow) {
        ElMessageBox.confirm(`确定要禁用选择的虚拟卡吗?`, {
          type: "warning",
          closeOnPressEscape: false,
          closeOnClickModal: false,
        }).then(async () => {
          try {
            const { message } = await updatePersonVirtualCardStatus(
              "VIRTUAL_FROZEN",
              state.selectRow.map(item=>item.id)
            );
            ElMessage.success(message);
            getList();
          } catch (e) {
            throw new Error(e.message);
          }
        });
      } else {
        ElMessage.error("请选择人员！");
      }
    };
    const handlePageChange = (val) => {
      state.queryListForm.currentPage = val;
      getList();
    };
    const handleSizeChange = (val) => {
      state.queryListForm.currentPage = 1;
      state.queryListForm.pageSize = val;
      getList();
    };
    onMounted(() => {
      getList();
      queryDepartCheckList();
      queryRolelist();
    });
    return {
      enable,
      disable,
      state,
      selectionChange,
      search,
      reset,
      off,
      handlePageChange,
      handleSizeChange,
    };
  },
};
</script>
<style lang="scss" scoped>
.personnelInformation {
  width: 100%;
  height: 100%;
}

.card-log-out {
  background: rgb(90, 92, 124);
  color: #fff;
}
</style>
