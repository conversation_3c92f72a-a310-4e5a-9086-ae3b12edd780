### 使用框架及库
vue3、element-plus、scss、dart
### 使用命令
启动
```bash
npm run serve 或npm run serve
```
启动指定应用
```bash
npm run serve --name=${applicationFolderName}
```
打包
``` bash
npm run build 或 npm run build --name=${applicationFolderName}
```
### 目录结构

applications 应用目录
components 公共组件
hooks 复用钩子函数
plugins vue插件加载入口
services axios封装
utils 工具函数、复用逻辑封装

#### 应用目录详解

assets  样式图片字体图标
components 应用级公共组件目录
router 应用路由
store 应用数据商店
views 应用视图组件，以前对应路由现在对应菜单或自定义菜单的Tab标签映射组件
api.js 应用api归纳
config.js 应用配置
index.js 应用入口

### 备注：

1.Tab项数据格式
```javascript
{
  id: '',//唯一
  options: {
    menuName: 'tab标题',
    menuEnName: 'tab加载的组件名称'// 组件映射定义在store/app里的componentMap，
    query: {},// tab传参
  }
}
```
2.scss变量
/src/assets/styles/export.scss scss js变量导出
/src/assets/styles/variable.scss 自动加载的scss变量