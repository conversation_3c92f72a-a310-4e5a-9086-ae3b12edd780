<template>
  <kade-route-card>
    <kade-table-filter @search="handleSearch" @reset="handleReset">
      <el-form inline label-width="90px" size="mini">
        <el-form-item label="通行情况">
          <el-select v-model="state.form.type">
            <el-option v-for="(item, index) in passThroughList" :key="index" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
       <el-form-item label="组织结构">
          <kade-dept-select-tree style="width: 100%" :value="state.form.deptPath" valueKey="deptPath" :multiple="false"
            @valueChange="(val) => (state.form.userDept = val.deptPath)" />
        </el-form-item>
        <el-form-item label="身份类别">
          <el-select clearable v-model="state.form.userRole">
            <el-option v-for="(item, index) in state.roleList" :key="index" :label="item.roleName" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="关键字">
          <el-input placeholder="姓名、编号或车牌号关键字搜索" v-model="state.form.keyWord"></el-input>
        </el-form-item>
        <el-form-item label="通行区域">
          <kade-area-select-tree style="width: 100%" :value="state.form.areaId" valueKey="id" :multiple="false"
            @valueChange="(val) => (state.form.areaId = val.id)" />
        </el-form-item>
        <el-form-item label="进出方向">
          <el-select v-model="state.form.passThrough">
            <el-option v-for="(item, index) in directionList" :key="index" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="起始日期">
          <el-date-picker v-model="state.form.startDate" type="datetime" placeholder="请选择起始日期"
            :default-time="new Date(2001, 1, 1, 0, 0, 0).getTime()" />
        </el-form-item>
        <el-form-item label="截止日期">
          <el-date-picker v-model="state.form.endDate" type="datetime" placeholder="请选择截止日期"
            :default-time="new Date(2001, 1, 1, 23, 59, 59).getTime()" />
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="车辆通行记录列表" v-loading="state.loading">
      <template #extra>
        <el-button @click="handleExport()" class="btn-green" icon="el-icon-daochu" size="mini">导出</el-button>
      </template>
      <el-table :data="state.dataList"  @selection-change="handleSelectChange" v-loading="state.loading" border height="55vh">
        <el-table-column  show-overflow-tooltip label="序号" width="50px" align="center">
          <template #default="scope">
            {{(scope.$index+1)+((state.form.currentPage-1)*state.form.pageSize)}}
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip prop="userCode" label="用户编号" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="userName" label="用户名称" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="carNumber" label="车牌号" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="deptName" label="组织机构" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="userRole" label="身份类别" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="areaName" label="进出区域" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="inOutDirection" label="进出方向" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="inOutDatetime" label="进出时间" align="center">
          <template #default="scope">
            {{ timeFilter(scope.row.inOutDatetime)}}
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip prop="passFlag" label="是否通过" align="center">
          <template #default="scope">
            <div :style="{color:scope.row.passFlag=='TRUE'?'#02D200':'#ff3f3f'}">{{ dictionaryFilter(scope.row.passFlag)}}</div>
          </template>
        </el-table-column>
        <el-table-column v-if="state.form.passThrough == 'FALSE'" show-overflow-tooltip prop="userName" label="描述"
          align="center">
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination v-model:currentPage="state.form.currentPage" v-model:page-size="state.form.pageSize" :page-sizes="[10, 20, 30, 40]"
         backgroundlayout="total, sizes, prev, pager, next, jumper" :total="state.total" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      </div>
    </kade-table-wrap>
  </kade-route-card>
</template>

<script>
import { reactive } from '@vue/reactivity';
import { ElForm, ElFormItem, ElButton, ElTable, ElTableColumn, ElInput, ElSelect, ElOption, ElPagination, ElDatePicker, } from "element-plus"
import { onMounted } from '@vue/runtime-core';
import deptSelectTree from "@/components/tree/deptSelectTree.vue";
import areaSelectTree from "@/components/tree/areaSelectTree.vue";
import { useDict } from '@/hooks/useDict.js'
import { timeStr } from '@/utils/date.js'
import { getRolelist } from "@/applications/eccard-basic-data/api";
import { getAcsVehicleRecord,exportAcsVehicleRecord } from '@/applications/eccard-uac/api.js'
const passThroughList = [
  { label: "通行正常", value: "normal" },
  { label: "通行异常", value: "except" },
]
export default {
  components: {
    ElForm,
    ElFormItem,
    ElButton,
    ElInput,
    ElSelect,
    ElOption,
    ElTable,
    ElTableColumn,
    ElPagination,
    ElDatePicker,
    "kade-dept-select-tree": deptSelectTree,
    "kade-area-select-tree": areaSelectTree,
  },
  setup() {
    const directionList = useDict("SYS_DEVICE_CAMAMER_DIRECTION")
    const state = reactive({
      loading: false,
      form: {
        pageSize: 10,
        currentPage: 1,
        type: 'normal'
      },
      dataList: [],
      total: 0,
      roleList:[]
    });
    const getList = async() => {
      state.loading=true
      let params = { ...state.form }
      if(params.startDate){
        params.startDate = timeStr(params.startDate)
      }
      if(params.startDate){
        params.endDate = timeStr(params.endDate)
      }
      try{
        let { data:{list,total}} = await getAcsVehicleRecord(state.form)
      state.dataList = list
      state.total = total
      state.loading = false
      }
      catch{
        state.loading = false
      }
    }
    const queryRolelist = async()=>{
      let { data } = await getRolelist()
      state.roleList = data
    }
    const handleSearch=()=>{
      getList()
    }
    const handleReset=()=>{
      state.form={
        pageSize:10,
        currentPage:1,
        type:'normal'
      }
      getList()
    }
    const handleSizeChange = (val) => {
      state.form.currentPage = 1
      state.form.pageSize = val
      getList()
    };
    const handleCurrentChange = (val) => {
      state.form.currentPage = val
      getList()
    }
    const handleExport = async ()=>{
      let res = await exportAcsVehicleRecord(state.form)
      let link = document.createElement("a");
      link.href = window.URL.createObjectURL(
        new Blob([res], { type: "application/vnd.ms-excel" })
      )
      link.style.display='none'
      link.setAttribute('download','车辆通行记录表.xlsx')
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }
    onMounted(() => {
      getList();
      queryRolelist()
    });
    return {
      passThroughList,
      state,
      directionList,
      handleSearch,
      handleExport,
      handleReset,
      handleSizeChange,
      handleCurrentChange,
    }
  }
}
</script>

<style lang="scss" scoped>
.green :hover {
  text-decoration: underline;
}

:deep(.el-form) {
  .el-input {
    width: 198px
  }
}
</style>