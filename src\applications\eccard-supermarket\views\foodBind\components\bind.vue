<template>
  <el-dialog :model-value="modelValue" title="绑定菜品" width="970px" :before-close="handleClose">
    <el-card header="设备信息">
      <el-table :data="[rowData]" border stripe>
        <el-table-column show-overflow-tooltip label="终端型号" prop="deviceModel" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip label="所属区域" prop="areaName" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip label="设备机号" prop="deviceNo" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip label="设备名称" prop="deviceName" align="center"></el-table-column>
      </el-table>
    </el-card>
    <el-card header="绑定菜品选择">
      <kade-select-table :isShow="state.isSelectFood" :value='state.form.dishVoList' :reqFnc="dishListAPI"
        :selectCondition="selectCondition" :column="column" :params="params" :isCurrentSelect="true"
        @change="foodChange" />
    </el-card>
    <el-card header="绑定菜品设置编号" v-if="state.form.dishVoList.length">
      <el-form ref="formRef" class="food-code-form" inline label-width="100px" size="mini" :model="state.form"
        :rules="rules">
        <el-form-item v-for="(item,index) in state.form.dishVoList" :key="index" :label="item.dishName"
          :prop="`dishVoList[${index}].dishCode`" :rules="rules.dishCode">
          <el-input v-model="item.dishCode" placeholder="菜品编号"></el-input>
        </el-form-item>
      </el-form>
    </el-card>
    <template #footer>
      <span class="dialog-footer">
        <el-button type="primary" @click="handleSave" :loading="state.loading" size="mini">保存</el-button>
        <el-button @click="handleClose" size="mini">返回</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { ElDialog, ElButton, ElCard, ElForm, ElFormItem, ElInput, ElTable, ElTableColumn, ElMessage, } from "element-plus"
import { reactive, ref, onMounted, watch } from 'vue'
import { useDict } from "@/hooks/useDict.js";
import { dishList, labelList, bindDishList, deviceBindDish } from "@/applications/eccard-supermarket/api.js";
import selectTable from "@/components/table/selectTable.vue";

const column = [
  { label: "菜品名称", prop: "dishName", isDict: false, width: "" },
  { label: "类型", prop: "dishType", isDict: true, width: "" },
  { label: "单位", prop: "dishUnit", isDict: false, width: "" },
  { label: "价格", prop: "dishPrice", isDict: false, width: "" },
];
const params = {
  currentPageKey: "pageNum",
  pageSizeKey: "pageSize",
  resListKey: "list",
  resTotalKey: "total",
  value: {},
  tagNameKey: "dishName",
  valueKey: "id",
}

const rules = {
  dishCode: [
    { required: true, message: "请输入菜品编号", trigger: 'blur' }
  ]
}
export default {
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    rowData: {
      type: Object,
      default: null
    },
  },
  components: {
    ElDialog, ElCard,
    ElButton, ElForm, ElFormItem, ElInput, ElTable, ElTableColumn,
    "kade-select-table": selectTable,
  },
  setup(props, context) {
    const formRef = ref(null)
    const state = reactive({
      isSelectFood: false,
      form: {
        dishVoList: [],
      }
    });
    const selectCondition = [
      {
        label: "菜品标签", valueKey: "labelEntityList", placeholder: "请选择", isSelect: true,
        select: {
          isMultiple: true,
          isValueObj: true,
          list: props.labelList,
          option: {
            label: "labelName",
            value: "id",
          },
        },
      },
      {
        label: "菜品类型", valueKey: "dishType", placeholder: "请选择", isSelect: true,
        select: {
          list: useDict("SYS_DISH_TYPE"),
          option: {
            label: "label",
            value: "value",
          },
        },
      },
    ]
    watch(() => props.modelValue, async val => {
      if (val) {
        state.form = {
          dishVoList: [],
        }
        let { data } = await bindDishList({ deviceId: props.rowData.id })
        state.form.dishVoList = data
        state.isSelectFood = true
      }
    })
    const getLabelList = async () => {
      let { data: { list } } = await labelList({})
      selectCondition[0].select.list = list
    }
    const foodChange = (val) => {
      let dishVoList = [...state.form.dishVoList]
      console.log(dishVoList);
      console.log("adasd", val, state.form.dishVoList);
      let arr = dishVoList.map(item => item.id)
      console.log("arr", arr);
      dishVoList = val.list.map(item => {
        if (arr.includes(item.id)) {
          return dishVoList.find(v => v.id == item.id)
        } else {
          return {
            ...item
          }
        }
      })
      dishVoList = dishVoList.sort(function (x, y) {
        return x.dishCode > y.dishCode ? 1 : -1;
      })
      let hasCodeList=dishVoList.filter(item=>item.dishCode)
      let noHasCodeList=dishVoList.filter(item=>!item.dishCode)
      state.form.dishVoList=hasCodeList.concat(noHasCodeList)
      console.log(state.form.dishVoList);
    }
    const handleSave = () => {
      if (!state.form.dishVoList.length) {
        return ElMessage.error("请选择菜品！")
      }
      formRef.value.validate(async valid => {
        if (valid) {
          let params = {
            deviceVo: {
              deviceName: props.rowData.deviceName,
              deviceNo: props.rowData.deviceNo,
              id: props.rowData.id,
            },
            dishVoList: [...state.form.dishVoList]
          }
          state.loading = true
          let { code, message } = await deviceBindDish(params)
          if (code === 0) {
            ElMessage.success(message)
            handleClose()
          }
          state.loading = false

        } else {
          return false
        }
      })
    }
    const handleClose = () => {
      context.emit("update:modelValue", false)
      state.isSelectFood = false
    };
    onMounted(() => {
      getLabelList()
    })
    return {
      dishListAPI: dishList,
      column,
      params,
      rules,
      selectCondition,
      formRef,
      state,
      foodChange,
      handleSave,
      handleClose,
    }
  }
}
</script>

<style lang="scss" scoped>
.el-card {
  margin-bottom: 20px;
}

:deep(.food-code-form) {
  .el-input {
    width: 100px
  }
}
</style>