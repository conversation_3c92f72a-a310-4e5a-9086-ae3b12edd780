<template>
  <kade-route-card>
    <kade-table-filter @search="search" @reset="reset">
      <el-form inline size="mini" label-width="90px">
        <el-form-item label="登录结果">
          <el-select
            v-model="state.form.operateResult"
            clearable
            placeholder="全部"
          >
            <el-option
              :label="item.label"
              :value="item.value"
              v-for="(item, index) in resultList"
              :key="index"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="登录账号">
          <el-input
            v-model="state.form.operateAccount"
            placeholder="请输入页面名称"
          ></el-input>
        </el-form-item>
        <el-form-item label="登录时间">
          <el-date-picker
            style="width: 200px"
            v-model="state.loginTime"
            type="daterange"
            range-separator="~"
            @change="timeChange"
            unlink-panels
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="响应时间">
          <el-input
            v-model="state.form.spendTime"
            placeholder="输入数值搜索"
          ></el-input>
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="登录日志列表">
      <template #extra>
        <el-button class="btn-blue" icon="el-icon-daoru" size="mini"
          >导出</el-button
        >
      </template>
      <el-table border :data="state.data">
        <el-table-column
          prop="id"
          label="日志ID"
          align="center"
        ></el-table-column>
        <el-table-column prop="operateResult" label="登录结果" align="center">
          <template #default="scope">
            <div
              :style="{ color: scope.row.operateResult == 1 ? 'red' : 'green' }"
            >
              {{ scope.row.operateResult == 1 ? "失败" : "成功" }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="operateAccount"
          label="登录账号"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="operateName"
          label="账号名称"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="ip"
          label="登录地址"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="operateTime"
          label="登录时间"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="spendTime"
          label="响应时间（毫秒）"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="os"
          label="客户端系统"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="browser"
          label="浏览器名"
          align="center"
        ></el-table-column>
        <el-table-column label="操作" align="center">
          <template #default="scope">
            <el-button type="text" @click="deatils(scope.row)"
              >查看详情</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination
          background
          v-model:currentPage="state.form.currentPage"
          v-model:page-size="state.form.pageSize"
          :page-sizes="[5, 10, 20, 30]"
          layout="total,sizes, prev, pager, next, jumper"
          :total="state.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </div>
    </kade-table-wrap>
    <kade-login-details
      :data="state.selectRow"
      :dialogVisible="state.dialogVisible"
      @close="close"
    ></kade-login-details>
  </kade-route-card>
</template>

<script>
import {
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElTable,
  ElTableColumn,
  ElPagination,
  ElDatePicker,
  ElSelect,
  ElOption,
} from "element-plus";
import { getLoginLogList } from "@/applications/eccard-basic-data/api.js";
import { dateStr } from "@/utils/date.js";
import { reactive, onMounted } from "vue";
import deatils from "./components/details.vue";
const resultList = [
  { label: "成功", value: 0 },
  { label: "失败", value: 1 },
];

export default {
  components: {
    ElButton,
    ElForm,
    ElFormItem,
    ElInput,
    ElTable,
    ElTableColumn,
    ElPagination,
    ElDatePicker,
    ElSelect,
    ElOption,
    "kade-login-details": deatils,
  },
  setup() {
    const state = reactive({
      dialogVisible: false,
      loginTime: [],
      data: [],
      total: 0,
      form: {
        currentPage: 1,
        pageSize: 10,
      },
      selectRow: "",
    });
    const getList = () => {
      getLoginLogList(state.form).then((res) => {
        console.log(res);
        state.data = res.data.list;
        state.total = res.data.total;
      });
    };

    const search = () => {
      getList();
    };
    const reset = () => {
      state.form = {
        currentPage: 1,
        pageSize: 10,
      };
      state.loginTime = [];
    };
    const timeChange = (val) => {
      if (val) {
        (state.form.operateStartTime = dateStr(val[0])),
          (state.form.operateEndtTime = dateStr(val[1]));
      } else {
        delete state.form.operateStartTime, delete state.form.operateEndtTime;
      }
    };
    const deatils = (row) => {
      console.log(row);
      state.selectRow = row;
      state.dialogVisible = true;
    };
    const close = () => {
      state.dialogVisible = false;
    };
    const handleSizeChange = (val) => {
      console.log(val);
      state.form.pageSize = val;
      getList();
    };
    const handleCurrentChange = (val) => {
      console.log(val);
      state.form.currentPage = val;
      getList();
    };
    onMounted(() => {
      getList();
    });
    return {
      resultList,
      state,
      getList,
      deatils,
      reset,
      search,
      close,
      handleSizeChange,
      handleCurrentChange,
      timeChange,
    };
  },
};
</script>

<style lang="scss" scoped>
:deep(.el-input__inner){
  width: 180px;
}

:deep(.el-pagination__editor.el-input .el-input__inner){
  width: 46px;
}
:deep(.el-pagination__sizes .el-input .el-input__inner){
  width: 100px;
}
</style>