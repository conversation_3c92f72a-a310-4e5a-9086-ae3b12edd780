<template>
  <div class="AddreplaceRecord">
    <el-dialog :model-value="isreplace.isShow" :title="isreplace.data.id ? '新增更换记录' : '修改更换记录'" width="40%"
      :before-close="beforeClose">
      <div class="padding-form-box">
        <div class="width-box">
          <el-form size="mini" ref="ruleForm" :model="state.form" label-width="120px" :rules="state.rules">
            <el-form-item label="更换原因：" prop="replaceReason">
              <el-input placeholder="请输入" v-model="state.form.replaceReason" maxlength="20"></el-input>
            </el-form-item>
            <el-form-item label="更换时间：" prop="replaceDate">
              <el-date-picker v-model="state.form.replaceDate" type="datetime" placeholder="请选择"></el-date-picker>
            </el-form-item>
            <el-form-item label="更换人员：" maxlength="20">
              <el-input placeholder="请输入" v-model="state.form.operatorName"></el-input>
            </el-form-item>
            <el-form-item label="备注：">
              <el-input type="textarea" maxlength="200" placeholder="请输入备注" v-model="state.form.replaceRemarks"></el-input>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="submit()" type="primary" size="mini">保&nbsp;&nbsp;存</el-button>
          <el-button @click="beforeClose()" size="mini">返&nbsp;&nbsp;回</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script>
import { computed, reactive, ref, watch } from "vue";
import { useStore } from "vuex";
import { timeStr } from "@/utils/date.js";
import { insertRecords, updateRecords } from "@/applications/eccard-iot/api";
import {
  ElDialog,
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElMessage,
  ElDatePicker,
} from "element-plus";
export default {
  components: {
    "el-dialog": ElDialog,
    "el-button": ElButton,
    "el-input": ElInput,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    ElDatePicker,
  },
  props: {
    listData: {
      types: Object,
      default: {},
    },
  },
  setup() {
    const store = useStore();
    let ruleForm = ref(null);
    const state = reactive({
      form: {},
      rules: {
        replaceReason: [
          {
            required: true,
            message: "请输入更换原因",
            trigger: "blur",
          },
        ],
        replaceDate: [
          {
            required: true,
            message: "请选择更换时间",
            trigger: "change",
          },
        ],
      },
    });
    /*    watch(
      () => props.isShow,
      (val) => {
        if (val) {
          
        }
      }
    ); */
    const isreplace = computed(() => {
      return store.state.deviceData.isreplace
    });
    watch(() => store.state.deviceData.isreplace.isShow, (val) => {
      if (val) {
        state.form = store.state.deviceData.isreplace.data
      }
    })
    const beforeClose = () => {
      store.commit("deviceData/updateState", {
        key: "isreplace",
        payload: {
          isShow: false,
          data: {}
        }
      });
    };
    const submit = () => {
      ruleForm.value.validate(async (valid) => {
        let data = Object.assign(state.form);
        data.replaceDate = timeStr(data.replaceDate);
        data.deviceId = store.state.deviceData.deviceDetail.id
        let fn = store.state.deviceData.isreplace.data.id ? updateRecords : insertRecords;
        if (valid) {
          let { code, message } = await fn(data);
          if (code === 0) {
            ElMessage.success(message);
            store.commit("deviceData/updateState", {
              key: "isreplace",
              payload: {
                isShow: false,
                data: {}
              }
            });
          } else {
            ElMessage.error(message);
          }
        } else {
          return false;
        }
      });
    };
    return {
      state,
      isreplace,
      ruleForm,
      submit,
      beforeClose,
    };
  },
};
</script>
<style lang="scss" scoped>
:deep(.el-input__inner) {
  width: 100% !important;
}

:deep(.el-date-editor.el-input) {
  width: 100%;
}
</style>