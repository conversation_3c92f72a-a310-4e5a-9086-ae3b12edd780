<template>
  <div style="text-align:right;margin-bottom:10px">
    <el-button @click="state.isAdd=true" type="primary" icon="el-icon-plus" size="mini">添加照片</el-button>
  </div>
  <div class="photo-list" v-loading="state.loading">
    <div class="photo-item" v-for="(item,index) in state.dataList" :key="index">
      <el-image class="img" fit="scale-down" :preview-src-list="state.dataList.map(item=>item.faceImg)" :initial-index="index" :src="item.faceImg" alt="" />
      <div class="del">
        <i @click="handleDel(item)" class="el-icon-delete-solid"></i>
      </div>
    </div>
    <el-empty v-if="!state.dataList.length" style="width:100%" description="暂无数据"></el-empty>
  </div>
  <kade-photo-dialog :isShow="state.isAdd" @close="close" />
</template>
<script>
import { ElButton, ElImage,ElEmpty, ElMessageBox,ElMessage } from "element-plus";
import photoDialog from "./components/photoDialog.vue";
import { reactive } from "@vue/reactivity";
import { onMounted } from "@vue/runtime-core";
import { useStore } from "vuex";
import { faceList, delFace } from "@/applications/eccard-basic-data/api";
export default {
  components: {
    ElButton,
    ElImage,
    ElEmpty,
    "kade-photo-dialog": photoDialog,
  },
  setup() {
    const store = useStore();
    const state = reactive({
      loading:false,
      isAdd: false,
      dataList: [],
    });

    const getList = async () => {
      state.loading=true
      let { data } = await faceList({userId:store.state.userInfo.rowData.id});
      state.dataList = data;
      state.loading=false
    };

    const handleDel = (row) => {
      ElMessageBox.confirm(`确认删除?`, {
        type: "warning",
        closeOnPressEscape: false,
        closeOnClickModal: false,
      }).then(async () => {
        try {
          const { message } = await delFace(row.id);
          ElMessage.success(message);
          getList();
        } catch (e) {
          throw new Error(e.message);
        }
      });
    };
    const close = (val) => {
      if (val) {
        getList();
      }
      state.isAdd = false;
    };
    onMounted(() => {
      getList();
    });
    return {
      state,
      handleDel,
      close,
    };
  },
};
</script>
<style lang="scss" scoped>
.photo-list {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  .photo-item {
    margin: 10px;
    position: relative;
    width: 240px;
    height: 240px;
    border: 1px dashed #d9d9d9;
    background: #eeeeee;
    border-radius: 6px;
    .img {
      height: 100%;
      width: 100%;
    }
    .del {
      position: absolute;
      bottom: 5px;
      right: 5px;
      display: none;
      align-items: center;
      justify-content: center;
      color: #999999;
      font-size: 30px;
    }
    &:hover .del {
      display: flex;
      cursor: pointer;
    }
  }
}
</style>