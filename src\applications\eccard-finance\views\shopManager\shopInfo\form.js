import { validateMobileAndFixTel, validateEmail } from '@/utils/validate';
export const getDefaultFields = (fields = {}) => ({
  "merchantBanksEntity": {
    "accountBank": "",
    "accountBankOutlets": "",
    "accountName": "",
    "bankNo": "",
    "bankOutletsAddress": "",
  },
  "merchantInfoEntity": {
    "areaId": 0,
    "areaName": "",
    "beginDate": "",
    "endDate": "",
    "merchantContacts": "",
    "merchantEmail": "",
    "merchantId": 0,
    "merchantName": "",
    "merchantNo": "",
    "merchantTel": "",
    "merchantType": "",
  },
  "merchantLicenseEntity": {
    "businessBeginDate": "",
    "businessEndDate": "",
    "businessLicenseImg": "",
    "businessLicenseName": "",
    "businessScope": "",
    "businessType": "",
    "creditCode": "",
    "establishDate": "",
    "legalPerson": "",
  },
  ...fields,  
});

export const rules = {
  'info.merchantName': [{
    required: true,
    message: '请输入商户名称',
  },{
    max: 50,
    min: 2,
    message: '长度在2~50个字符'    
  }],
  'info.merchantTel': [{
    required: true,
    message: '请输入联系电话'
  },{
    validator: validateMobileAndFixTel,
  }],
  'info.merchantEmail': [{
    required: true,
    message: '请输入邮箱地址'
  },{
    validator: validateEmail,
  }],
  'info.areaId': [{
    required: true,
    message: '请选择区域',
    trigger: 'change',
  }],
  'info.merchantContacts': [{
    required: true,
    message: '请输入商户联系人'
  }],
  'info.merchantType': [{
    required: true,
    message: '请选择商户类型',
    trigger: 'change'
  }],
  'license.businessBeginDate': [{
    required: true,
    triger: 'change',
    message: '请选择营业期限',
    type: 'date'
  }],
  'license.businessLicenseImg': [{
    required: true,
    message: '请上传营业执照',
    trigger: 'change',
  }],
  'license.businessLicenseName': [{
    required: true,
    message: '请输入营业执照名称',
  }],
  'license.businessScope': [{
    required: true,
    message: '请输入营业范围',
  },{
    max: 200,
    message: '长度不能超过200字符'
  }],
  'license.businessType': [{
    required: true,
    message: '请选择营业类型',
    trigger: 'change',
  }],
  'license.creditCode': [{
    required: true,
    message: '请输入信用代码'
  }],
  'license.establishDate': [{
    required: true,
    message: '请选择成立日期',
    trigger: 'change',
    type: 'date',
  }],
  'license.legalPerson': [{
    required: true,
    message: '请输入法定代表人'
  }],
  'bank.accountBank': [{
    required: true,
    message: '请输入开户网点',
  }],
  'bank.accountBankOutlets': [{
    required: true,
    message: '请选择开户银行',
    trigger: 'change',
  }],
  'bank.accountName': [{
    required: true,
    message: '请输入开户姓名',
  }],
  'bank.bankNo': [{
    required: true,
    message: '请输入银行卡号'
  }],
  'bank.bankOutletsAddress': [{
    required: true,
    message: '请输入网点地址'
  },{
    max: 200,
    message: '不能超过200字符'
  }]
};