// 屏幕适配 mixin 函数
export default {
  data() {
    return {
      scale: 1,
      width:1920,
      height:1080
    }
  },
  mounted() {
    this.setScale();
    window.addEventListener("resize", this.setScale);
  },
  methods: {
    getScale() {
      const { width, height } = this;
      console.log(window, width, height,this);
      let ww = window.innerWidth / width;
      let wh = window.innerHeight / height;
      return ww < wh ? ww : wh;
    },
    setScale() {
      this.scale = this.getScale();
      this.$refs.ScaleBox.style.setProperty("--scale", this.scale);
    },
  }
}