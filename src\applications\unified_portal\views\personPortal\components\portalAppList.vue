<template>
    <kade-list @change="handleToggle" v-if="dataList.length" :data="dataList" />
    <el-empty v-else description="您还没有门户" />
</template>
<script>
import List from '@/components/list';
import { ElEmpty, ElMessageBox, ElMessage } from 'element-plus';
import { ref, onMounted } from 'vue';
import { useStore } from 'vuex';
import {
    getUserModuleList,
    updateUserModuleState,    
} from '@/applications/unified_portal/api';
export default {
  components: {
    'el-empty': ElEmpty,
    'kade-list': List,
  },
  setup() {
    const store = useStore();
    const loading = ref(false);
    const dataList = ref([]);
    const load = async () => {
        try {
            loading.value = true;
            const { data } = await getUserModuleList({ userId: store.state.user.userInfo.id });
            dataList.value = data.map(it => {
                return {
                    ...it,
                    label: it.moduName,
                    image: null,
                    value: it.state === 1,
                }
            });
        } catch(e){
            throw new Error(e.message);
        } finally {
            loading.value = false;
        }
    }
    const handleToggle = (id, { moduName, state }) => {
        ElMessageBox.confirm(`是否${state === 1 ? '停止' : '启用'}门户'${moduName}'?`, {
            type: 'warning',
            closeOnPressEscape: false,
            closeOnClickModal: false,            
        }).then(async () => {
            try{
                const { message } = await updateUserModuleState({ id, status: state === 1 ? 0 : 1 });
                load();
                ElMessage.success(message);
            }catch(e) {
                throw new Error(e.message);
            }
        });
    }
    onMounted(() => {
        load();
    });    
    return {
      loading,
      dataList,
      handleToggle,
    }    
  }
}
</script>