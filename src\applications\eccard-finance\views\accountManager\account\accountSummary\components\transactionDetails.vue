<template>
  <div class="box">
    <kade-route-card style="height: auto">
      <div class="header-flexbox">
        <kade-tab-wrap
          :tabs="tabs"
          style="margin-top: 20px"
          v-model="state.tab"
          @active="active"
        >
          <template #jelx>
            <kade-transaction-balance />
          </template>
          <template #cslx>
            <kade-transaction-number />
          </template>
        </kade-tab-wrap>
        <div class="header-btn">
          <el-button
            icon="el-icon-daoru"
            size="mini"
            class="btn-blue"
            :disabled="isDisabled"
            @click="exportClick()"
            >下载查询明细</el-button
          >
          <el-button
            icon="el-icon-printer"
            size="mini"
            class="btn-purple"
            :disabled="isDisabled"
            >打印</el-button
          >
        </div>
      </div>
    </kade-route-card>
  </div>
</template>
<script>
import { ElButton } from "element-plus";
import { watch, reactive, computed } from "vue";
import transactionBalance from "./components/transactionBalance";
import transactionNumber from "./components/transactionNumber";
import { getPersonTradeListByExport } from "@/applications/eccard-finance/api";
import { downloadXlsx } from "@/utils";
import { useStore } from "vuex";
import {requestDate} from "@/utils/reqDefaultDate.js"
const tabs = [
  {
    name: "jelx",
    label: "金额类型",
  },
  {
    name: "cslx",
    label: "次数类型",
  },
];
export default {
  components: {
    "el-button": ElButton,
    "kade-transaction-balance": transactionBalance,
    "kade-transaction-number": transactionNumber,
  },
  setup() {
    const store = useStore();
    const state = reactive({
      tab: "jelx",
      departCheckList: [],
      cardStatuslist: [],
      form: {},
    });
    const isDisabled = computed(() => {
      return store.state.data.selectPerson ? false : true;
    });

/*     watch(
      () => store.state.data.selectPerson,
      () => {
        state.tab = "jelx";
        store.commit("data/updateState", {
          key: "transactionTabIndex",
          payload: "0",
        });
      }
    ); */

    watch(
      () => store.state.data.watchExportData,
      (val) => {
        state.form = val;
      }
    );

    const active = (val) => {
      store.commit("data/updateState", {
        key: "transactionTabIndex",
        payload: val.index,
      });
      store.commit("data/updateState", {
        key: "watchExportData",
        payload: {
          beginDate: requestDate()[0],
          endDate:  requestDate()[1],
          currentPage: 1,
          pageSize: 6,
        },
      });
    };
    const exportClick = () => {
      state.form.userId = store.state.data.selectPerson.userId;
      getPersonTradeListByExport(state.form).then((res) => {
        console.log(res)
        downloadXlsx(res,store.state.data.selectPerson.userName+'交易明细.xlsx')
      });
    };
    return {
      state,
      tabs,
      isDisabled,
      active,
      exportClick,
    };
  },
};
</script>
<style lang="scss" scoped>
.box {
  min-height: 650px;
  // overflow-y: scroll;
}
.kade-tab-wrap {
  margin-top: 0 !important;
}
.header-flexbox {
  position: relative;
}
.header-btn {
  position: absolute;
  top: 8px;
  right: 20px;
}
</style>