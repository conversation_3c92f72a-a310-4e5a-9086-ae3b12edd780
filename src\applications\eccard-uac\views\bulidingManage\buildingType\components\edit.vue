<template>
  <el-dialog :model-value="modelValue" :title="title(editType)" width="500px" :before-close="handleClose">
    <el-form label-width="100px" size="mini" ref="formRef" :model="state.form" :rules="rules">

      <el-form-item label="类型名称：" prop="buildTypeName">
        <el-input v-model="state.form.buildTypeName" placeholder="请输入类型名称" maxlength="20"></el-input>
      </el-form-item>
      <el-form-item label="备注：">
        <el-input v-model="state.form.remarks" type="textarea" maxlength="200" placeholder="请输入备注"></el-input>
      </el-form-item>
    </el-form>
    <div style="height:1px"></div>
    <template #footer v-if="editType !== 'details'">
      <span class="dialog-footer">
        <el-button type="primary" @click="handleSave" size="mini">保存</el-button>
        <el-button @click="handleClose" size="mini">返回</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { ElDialog, ElButton, ElForm, ElFormItem, ElInput, ElMessage } from "element-plus"
import { reactive, ref, watch, nextTick } from 'vue'
import { baseBuildingTypeAdd, baseBuildingTypeEdit } from "@/applications/eccard-uac/api";
export default {
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    rowData: {
      type: Object,
      default: null
    },
    editType: {
      type: String,
      default: ''
    },
  },
  components: {
    ElDialog,
    ElButton,
    ElForm,
    ElFormItem,
    ElInput,
  },
  setup(props, context) {
    const formRef = ref(null)
    const state = reactive({
      form: {
        receiveMessage: 'TRUE'
      }
    });
    watch(() => props.modelValue, val => {
      if (val) {
        if (props.editType == 'add') {
          state.form = {
            receiveMessage: 'TRUE'
          }
        } else {
          state.form = { ...props.rowData }
        }
        nextTick(() => {
          formRef.value.clearValidate()
        })
      }
    })
    const title = (val) => {
      if (val == 'add') {
        return '新建类型'
      } else if (val == 'edit') {
        return '编辑类型'
      }
    }
    const rules = {
      buildTypeName: [
        {
          required: true,
          message: "请输入楼栋类型",
          trigger: "blur",
        },
        
      ],
    }
    const handleSave = () => {
      formRef.value.validate(async valid => {
        if (valid) {
          let fn = props.editType == 'add' ? baseBuildingTypeAdd : baseBuildingTypeEdit
          state.loading = true
          try {
            let { code, message } = await fn(state.form)
            if (code === 0) {
              ElMessage.success(message)
              context.emit("update:modelValue", true)
            }
            state.loading = false
          }
          catch {
            state.loading = false
          }
        } else {
          return false
        }
      })
    }
    const handleClose = () => {
      context.emit("update:modelValue", false)
    };
    return {
      formRef,
      state,
      title,
      rules,
      handleSave,
      handleClose,
    }
  }
}
</script>

<style lang="scss" scoped>
.el-form {
  margin-top: 20px;
}
</style>