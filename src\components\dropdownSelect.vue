<template>
  <el-select clearable v-bind="attrs" @change="handleChange">
    <el-option
      v-for="item in state.dataList"
      :key="item.value"
      :label="item.label"
      :value="item.value">
    </el-option>
  </el-select>
</template>
<script>
import {
  ElSelect,
  ElOption,
} from 'element-plus';
import { computed, reactive, ref } from 'vue';
export default {
  components: {
    'el-select': ElSelect,
    'el-option': ElOption,
  },
  emits: ['update:modelValue', 'change'],
  props: {
    options: {
      type: Object,
      default: () => ({
        placeholder: '请选择',
        multiple: false,
        filterable: true,
        remote: true,
        reserveKeyword: true
      }),
    },
    modelValue: {
      type: [String, Number, Array],
      default: ''
    },
    action: {
      type: Function,
      required: true,
    }
  },
  setup(props, context) {
    const loading = ref(false);
    const state = reactive({
      dataList: [],
    });
    const remoteMethod = async (query) => {
      try {
        loading.value = true;
        const data = await props.action(query);
        state.dataList = data;
      } catch(e) {
        throw new Error(e.message);
      } finally {
        loading.value = false;
      }
    }
    const attrs = computed(() => ({
      ...props.options,
      loading,
      remoteMethod,
      modelValue: props.modelValue,
    }));

    const handleChange = (v) => {
      context.emit('change', v);
    }

    return {
      attrs,
      state,
      handleChange,
    }
  }
}
</script>