<template>
  <div class="helplist" v-loading="state.loading">
    <template v-if="state.dataList.length">
      <div class="helplist-scroll" v-infinite-scroll="load">
        <div @click="handleInfo(item.id)" class="helpitem" v-for="item in state.dataList" :key="item.id">{{ item.helpTitle }}</div>
      </div>
    </template>
    <el-empty description="暂无文档" v-else />
    <help-info title="消息详情" v-model="showInfoModal" :id="id" />
  </div>  
</template>
<script>
import { getHomeHelpList } from '@/applications/unified_portal/api';
import { ElEmpty } from 'element-plus';
import { usePagination } from '@/hooks/usePagination';
import HelpInfo from './helpInfo';
import { ref } from 'vue';
export default {
  components: { 
    'el-empty': ElEmpty,
    'help-info': HelpInfo,
  },
  setup() {
    const { options, loadData } = usePagination(getHomeHelpList, {}, { pageSize: 6 });
    const id = ref(null);
    const showInfoModal = ref(false);
    const load = () => {
      const { dataList, total } = options;
      if(dataList.length === total) {
        return false;
      }
      options.currentPage += 1;
      loadData(true);
    }
    const handleInfo = (msgId) => {
      id.value = msgId;
      showInfoModal.value = true;
    }
    return {
      state: options,
      load,
      handleInfo,
      id,
      showInfoModal,
    }    
  },  
}
</script>
<style lang="scss" scoped>
.helplist{
  position: relative;
  width: 100%;
  height: 338px;
  height: calc(50vh - 130px);
  padding: 0 20px;
  box-sizing: border-box;
  .helpitem{
    padding: 8px 20px 8px 20px;
    box-sizing: border-box;
    position: relative;
    cursor: pointer;
    &::before{
      content: '';
      display: block;
      width: 5px;
      height: 5px;
      background-color: #777;
      border-radius: 50%;
      position: absolute;
      left: 0;
      margin: auto;
      top: 0;
      bottom: 0;
    }
  }
  .helplist-scroll{
    height: 298px;
    height: calc(50vh - 170px);
    overflow-y: auto;    
  }
}
</style>