<template>
  <el-dialog
    :model-value="isShow"
    title="门禁控制器设备详情"
    width="90%"
    :before-close="beforeClose"
    :close-on-click-modal="false"
  >
    <kade-tab-wrap :tabs="tabs" style="margin-top: 20px" v-model="state.tab">
      <template #jbxx><kade-basic-params :data="data" /> </template>
      <template #mcs><kade-door-params  :data="data"/> </template>
      <template #dtbdlb><kade-read-bind-list :data="data" /> </template>
      <template #ghjl> <kade-record :data="data" /></template>
    </kade-tab-wrap>
  </el-dialog>
</template>
<script>
import { ElDialog } from "element-plus"
import basicParams from "./basicParams"
import doorParams from "./doorParams"
import readBindList from "./readBindList"
import record from "./record"
import { reactive } from '@vue/reactivity'
const tabs = [
  {
    name: "jbxx",
    label: "基本信息",
  },
  {
    name: "mcs",
    label: "门参数",
  },
  {
    name: "dtbdlb",
    label: "读头绑定列表",
  },
  {
    name: "ghjl",
    label: "更换记录",
  },
];
export default {
  components:{
    ElDialog,
    "kade-basic-params":basicParams,
    "kade-door-params":doorParams,
    "kade-read-bind-list":readBindList,
    "kade-record":record,
  },
  props:{
    isShow:{
      type:Boolean,
      default:false
    },
    data:{
      type:Object,
      default:null
    }
  },
  setup(props,context){
    const state=reactive({
      tab:"jbxx"
    })
    const beforeClose=()=>{
      context.emit("close",false)
      state.tab="jbxx"
    }
    return {
      tabs,
      state,
      beforeClose
    }
  }
}
</script>
<style lang="scss" scoped>
</style>