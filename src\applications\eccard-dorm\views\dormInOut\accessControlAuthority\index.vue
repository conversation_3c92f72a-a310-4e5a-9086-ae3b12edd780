<template>
  <kade-route-card>
    <kade-table-filter @search="handleSearch" @reset="handleReset">
      <el-form inline label-width="80px" size="mini">
        <kade-linkage-select :data="linkageData" :value="state.form" @change="linkageChange" />
        <el-form-item label="同步状态">
          <el-select v-model="state.form" placeholder="请选择">
            <el-option v-for="(item,index) in 5" :key="index" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="门禁SN">
          <el-input v-model="state.form.a" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="姓名/编号">
          <el-input v-model="state.form.name" placeholder="请输入"></el-input>
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="权限列表">
      <template #extra>
        <el-button type="success" icon="el-icon-plus" size="mini" @click="add">新增授权</el-button>
        <el-button type="primary" icon="el-icon-delete-solid" size="mini" @click="handleBatch()">批量删除</el-button>
      </template>
      <el-table :data="state.data" border @selection-change="selectChange" v-loading="state.loading">
        <el-table-column type="selection" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip v-for="(item,index) in column" :key="index" :label="item.label" :prop="item.prop" align="center"></el-table-column>
        <el-table-column label="操作" align="center">
          <template #default="scope">
            <el-button type="text" class="green" @click="handleDel(scope.row)">删除授权</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination v-model:currentPage="state.currentPage" v-model:page-size="state.pageSize" :page-sizes="[10, 20, 30, 40]" background layout="total, sizes, prev, pager, next, jumper" :total="state.total" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      </div>
    </kade-table-wrap>
    <kade-access-add v-model:modelValue="state.isShow" @update:modelValue="close" />
  </kade-route-card>
</template>

<script>
import { reactive, onMounted } from 'vue'
import add from "./components/add.vue"
import linkageSelect from '@/applications/eccard-dorm/components/linkageSelect'
import { ElForm, ElFormItem, ElSelect, ElOption, ElInput, ElButton, ElTable, ElTableColumn, ElPagination,ElMessageBox,ElMessage } from "element-plus"
const linkageData = {
  area: { label: '区域', valueKey: 'areaId', key: 'id' },
  building: { label: '楼栋', valueKey: 'buildId' },
  unit: { label: '单元', valueKey: 'unitNum' }
}
const column = [
  { label: '人员编号', prop: '' },
  { label: '人员姓名', prop: '' },
  { label: '性别', prop: '' },
  { label: '组织机构', prop: '' },
  { label: '区域', prop: '' },
  { label: '楼栋', prop: '' },
  { label: '单元', prop: '' },
  { label: '门禁SN', prop: '' },
  { label: '门禁名称', prop: '' },
  { label: '同步状态', prop: '' },
  { label: '同步时间', prop: '' },
  { label: '添加时间', prop: '' }
]
export default {
  components: {
    ElForm,
    ElFormItem,
    ElSelect,
    ElOption,
    ElInput,
    ElButton,
    ElTable,
    ElTableColumn,
    ElPagination,
    "kade-linkage-select": linkageSelect,
    "kade-access-add": add
  },
  setup() {
    const state = reactive({
      form: {
        currentPage: 1,
        pageSize: 10
      },
      total: 0,
      isShow: false,
      selectData: '',
      loading: false
    })
    const handleSearch = () => {
      getList()
    }
    const handleReset = () => {
      state.form = {
        currentPage: 1,
        pageSize: 10
      }
      getList()
    }
    const linkageChange = (val) => {
      state.form = { ...state.form, ...val }
    }
    const getList = async () => {
      state.loading = true
      try {
        let { data: { list, total } } = await ElForm(state.fomr)
        state.data = list
        state.total = total
        state.loading = false
      }
      catch{
        state.loading = false
      }
    }
    const add = () => {
      state.isShow = true
    }
    const close = () => {
      state.isShow = false
    }

    const handleDel = (row) => {
      ElMessageBox.confirm(`确认删除？`,`提示`,{
        type:'warning',
        confirmButtonText:'确认',
        cancelButtonText:'取消'
      }).then(async()=>{
        let {code,message} = await ElForm(row)
        if(code===0){
          ElMessage.success(message)
          getList()
      }
      })
    }
    const selectChange = (val) => {
      state.selectData = val
    }
    const handleBatch = () => {
       if(!state.selectData.length){
         return ElMessage.error( '请先选择需要删除的信息!')
       }
       ElMessageBox.confirm(`确认删除已选择信息？`,`提示`,{
         type:'warning',
         confirmButtonText:'确定',
         cancelButtonText:'取消'
       }).then(async()=>{
        let param = state.selectChange.map(item=>item.id).join(',')
       let { code,message } = await ElForm(param)
       if(code===0){
         ElMessage.success(message)
         getList()
       }
       })
     }
    const handleSizeChange = (val) => {
      state.currentPage = 1
      state.pageSize = val
      getList()
    }
    const handleCurrentPage = (val) => {
      state.currentPage = val
      getList()
    }
    onMounted(() => {
      getList()
    })
    return {
      state,
      add,
      handleDel,
      handleBatch,
      column,
      close,
      handleSearch,
      handleReset,
      linkageData,
      selectChange,
      linkageChange,
      handleSizeChange,
      handleCurrentPage
    }
  }
}
</script>

<style lang="scss" scoped>
.green {
  text-decoration: underline;
}
:deep(.el-input--mini .el-input__inner) {
  width: 198px;
}
:deep(.el-pagination .el-select .el-input .el-input__inner) {
  width: 100px;
}
:deep(.el-pagination__editor.el-input .el-input__inner) {
  width: 46px;
}
:deep(.el-dialog) {
  border-radius: 6px;
  padding-bottom: 15px;
}
:deep(.el-dialog__header) {
  border-bottom: 1px solid #eeeeee;
}
:deep(.el-dialog__footer) {
  border-top: none;
  text-align: center;
}
:deep(.el-divider--horizontal) {
  margin: 0 0 20px 0;
}
</style>