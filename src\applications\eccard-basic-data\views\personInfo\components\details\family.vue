<template>
  <div style="text-align:right;margin-bottom:10px">
    <el-button @click="state.isAdd=true" type="primary" icon="el-icon-plus" size="mini">添加成员</el-button>
  </div>
  <el-table v-loading="state.loading" :data="state.dataList" border>
    <el-table-column align="center" label="人员编号" prop="fcode"></el-table-column>
    <el-table-column align="center" label="姓名" prop="fname"></el-table-column>
    <el-table-column align="center" label="关系" prop="fuserRelationship"></el-table-column>
    <el-table-column align="center" label="联系方式" prop="ftel"></el-table-column>
    <el-table-column align="center" label="操作" prop="">
      <template #default="scope">
        <el-button @click="handleDel(scope.row)" size="mini" type="text">删除</el-button>
      </template>
    </el-table-column>
  </el-table>
  <kade-family-dialog :isShow="state.isAdd" @close="close" />
</template>
<script>
import {
  ElButton,
  ElTable,
  ElTableColumn,
  ElMessageBox,
  ElMessage,
} from "element-plus";
import { reactive } from "@vue/reactivity";
import { useStore } from "vuex";
import {
  delFamilyInfo,
  getFamilyList,
} from "@/applications/eccard-basic-data/api";
import familyDialog from "./components/familyDialog.vue";
import { onMounted } from "@vue/runtime-core";
export default {
  components: {
    ElButton,
    ElTable,
    ElTableColumn,
    "kade-family-dialog": familyDialog,
  },
  setup() {
    const store = useStore();
    const state = reactive({
      isAdd: false,
      loading: false,
      dataList: [],
    });

    const getList = async () => {
      state.loading = true;
      const { code, data } = await getFamilyList({
        userId: store.state.userInfo.rowData.id,
      });
      if (code === 0) {
        state.dataList = data;
      }
      state.loading = false;
    };
    const handleDel = (row) => {
      ElMessageBox.confirm(`确认删除?`, {
        type: "warning",
        closeOnPressEscape: false,
        closeOnClickModal: false,
      }).then(async () => {
        try {
          const { message } = await delFamilyInfo({ id: row.id });
          ElMessage.success(message);
          getList()
        } catch (e) {
          throw new Error(e.message);
        }
      });
    };
    const close = (val) => {
      if (val) {
        getList();
      }
      state.isAdd = false;
    };
    onMounted(() => {
      getList();
    });
    return {
      state,
      handleDel,
      close,
    };
  },
};
</script>
<style lang="scss" scoped>
.el-table--border {
  border-right: 1px solid #ebeef5;
  border-bottom: 1px solid #ebeef5;
}
</style>