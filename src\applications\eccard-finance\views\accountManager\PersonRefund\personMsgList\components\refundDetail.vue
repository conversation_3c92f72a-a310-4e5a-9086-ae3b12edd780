<template>
  <div class="box-dialog">
    <kade-table-wrap title="退款人员信息">

      <el-table style="width: 100%" :data="[selectPerson]" border stripe>
        <el-table-column width="150" label="用户编号" prop="userCode" align="center"></el-table-column>
        <el-table-column prop="userName" label="姓名" align="center">
        </el-table-column>

        <el-table-column label="组织机构" prop="deptName" align="center"></el-table-column>
        <el-table-column label="身份类别" prop="userRoleName" align="center"></el-table-column>
        <el-table-column label="账户状态" prop="acctStatus" align="center">
          <template #default="scope">
            {{
                filterDictionary(scope.row.acctStatus, state.accountStatusList)
              }}
          </template>
        </el-table-column>
        <el-table-column label="卡片类别" prop="ctName" align="center"></el-table-column>
        <el-table-column label="卡片状态" align="center"><template #default="scope">
            {{ filterDictionary(scope.row.cardStatus, state.cardStatusList) }}
          </template></el-table-column>
        <el-table-column width="153" label="联系方式" prop="userTel" align="center"></el-table-column>
      </el-table>
    </kade-table-wrap>
    <kade-table-wrap title="退款清单" style="margin-top: 10px">
      <el-table style="width: 100%" :data="refundDetail.refundList" v-loading="false" border stripe>
        <el-table-column label="退款项目" prop="walletName" align="center"></el-table-column>
        <el-table-column label="项目余额" align="center"><template #default="scope">
            <div>
              {{ scope.row.walletBalance&&scope.row.walletBalance + "元" }}
            </div>
          </template></el-table-column>
        <el-table-column label="退款比例" align="center">
          <template #default="scope">
            <div>
              {{ scope.row.refundRatio&&scope.row.refundRatio + "%" }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="实退金额" align="center"><template #default="scope">
            <div>
              {{ scope.row.actualRefundAmount&&scope.row.actualRefundAmount + "元" }}
            </div>
          </template></el-table-column>
      </el-table>
      <el-form label-width="100px" inline size="small">
        <el-form-item label="退款方式：">
          <el-input :model-value="filterDictionary(refundDetail.refundMode,state.refundModeList)" readonly></el-input>
        </el-form-item>
        <el-form-item label="退款时间：">
          <el-input :model-value="timeStrDate(refundDetail.refundDate)" readonly></el-input>
        </el-form-item>
        <el-form-item label="操作员：">
          <el-input v-model="refundDetail.createUserName" readonly></el-input>
        </el-form-item>
      </el-form>
      <el-form label-width="100px">
        <el-form-item label="退款说明：">
          <el-input v-model="refundDetail.refundRemark" type="textarea" readonly></el-input>
        </el-form-item>
      </el-form>
    </kade-table-wrap>
    <div class="submit-btn">
      <el-button @click="off()" size="mini">关&nbsp;&nbsp;闭</el-button>
    </div>
  </div>
</template>
<script>
import {
  ElTable,
  ElTableColumn,
  ElForm,
  ElFormItem,
  ElButton,
  ElInput,
} from "element-plus";
import { reactive } from "@vue/reactivity";
import { dateStr } from "@/utils/date.js";
import {} from "@/applications/eccard-finance/api";
import { computed, watch } from "@vue/runtime-core";
import { useStore } from "vuex";
export default {
  components: {
    ElTable,
    ElTableColumn,
    ElForm,
    ElFormItem,
    ElButton,
    ElInput,
  },
  props: {
    accountStrategyList: {
      types: Array,
      default: [],
    },
    cardTypeList: {
      types: Array,
      default: [],
    },
    walletList: {
      types: Array,
      default: [],
    },
  },
  setup(props) {
    const store = useStore();
    const state = reactive({
      form: {
        refundMode: null,
        userId: null,
        refundRemark: null,
        refundTotalAmount: null,
      },
      accountStrategy: "",
      accountStatusList: JSON.parse(
        localStorage.getItem("kade_cache_dictionary")
      ).filter((item) => item.dictType == "PERSON_ACCOUNT_STATUS"), //账户状态
      walletTypeList: JSON.parse(
        localStorage.getItem("kade_cache_dictionary")
      ).filter((item) => item.dictType == "PERSON_WALLET_TYPE"), //钱包类型
      walletStatusList: JSON.parse(
        localStorage.getItem("kade_cache_dictionary")
      ).filter((item) => item.dictType == "PERSON_WALLET_STATUS"), //钱包状态
      cardStatusList: JSON.parse(
        localStorage.getItem("kade_cache_dictionary")
      ).filter((item) => item.dictType == "PERSON_CARD_STATUS"), //卡片状态
      refundModeList: JSON.parse(
        localStorage.getItem("kade_cache_dictionary")
      ).filter((item) => item.dictType == "PERSON_REFUND_MODE"), //退款类型
    });
    const timeStrDate = computed(() => {
      return dateStr;
    });

    const selectPerson = computed(() => {
      return store.state.refundData.selectPerson;
    });

    const refundDetail = computed(() => {
      return store.state.refundData.refundDetail;
    });

    watch(
      () => props.selectPerson,
      (val) => {
        console.log(val);
        state.form = {
          refundMode: null,
          userId: null,
          refundRemark: null,
          refundTotalAmount: null,
        };
        state.form.userId = val.userId;
      }
    );

    const filterDictionary = (val, dictionaryList) => {
      for (let i of dictionaryList) {
        if (val == i.dictCode) {
          return i.dictValue;
        }
      }
    };

    const off = () => {
      store.commit("refundData/updateState", {
        key: "isRefundDetail",
        payload: false,
      });
    };
    return {
      state,
      timeStrDate,
      selectPerson,
      refundDetail,
      filterDictionary,
      off,
    };
  },
};
</script>
<style lang="scss" scoped >
.box-dialog {
  padding: 10px 0;
  .el-table__row {
    height: 30px !important;
  }
  .account-tips {
    text-align: center;
    margin-bottom: 10px;
    color: #f00;
  }
}
.table-box {
  padding: 10px;
}
.submit-btn {
  text-align: center;
  margin: 10px auto;
}
.el-form-item {
  margin-bottom: 10px;
  margin-top: 10px;
}
.el-input .el-input__inner {
  width: 180px !important;
}
</style>