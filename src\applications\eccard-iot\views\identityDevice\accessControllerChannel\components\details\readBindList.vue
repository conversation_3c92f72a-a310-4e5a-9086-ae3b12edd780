<template>
  <el-form label-width="80px">
    <el-form-item label="门号：">
      <el-select v-model="state.form.doorNo" size="mini" @change="handleDoorNoChange">
        <el-option v-for="(item, index) in state.doorList" :key="index" :label="item.doorName" :value="item.doorNo">
        </el-option>
      </el-select>
    </el-form-item>
  </el-form>
  <el-table style="width: 100%" v-loading="state.loading" :data="state.dataList" border stripe>
    <el-table-column v-for="(item, index) in column" :key="index" :width="item.width" :prop="item.prop"
      :label="item.label" align="center" show-overflow-tooltip>
      <template #default="scope" v-if="item.isDict">
        {{ dictionaryFilter(scope.row[item.prop]) }}
      </template>
      <template v-else-if="item.render" #default="scope">
        {{item.render(scope.row[item.prop])}}
      </template>
    </el-table-column>
    <el-table-column align="center" label="操作">
      <template #default="scope">
        <!-- <el-button size="mini" class="green" type="text" @click="details(scope.row)">详情</el-button> -->
        <el-button size="mini" class="green" type="text" @click="handleOnBind(scope.row)">取消绑定</el-button>
      </template>
    </el-table-column>
  </el-table>
  <div class="pagination">
    <el-pagination background :current-page="state.form.pageNum" layout="total, sizes, prev, pager, next, jumper"
      :page-sizes="[10, 20, 50, 100, 200]" :total="state.total" :page-size="state.form.pageSize"
      @current-change="handlePageChange" @size-change="handleSizeChange">
    </el-pagination>
  </div>

</template>
<script>
import { ElForm, ElFormItem, ElSelect, ElOption, ElTable, ElTableColumn, ElButton, ElPagination, ElMessageBox, ElMessage } from "element-plus"
import { reactive, onMounted } from 'vue'
import { accessControlDoorList, accessControlReadRelationList, accessControlDeviceCanelBind } from "@/applications/eccard-iot/api";
const column = [
  { label: "读头类型", prop: "relationTypeName", isDict: false, width: "", },
  { label: "所属区域", prop: "areaName", isDict: false, width: "" },
  { label: "设备状态", prop: "deviceStatus", isDict: true, width: "" },
  { label: "设备机号", prop: "deviceNo", isDict: false, width: "" },
  { label: "设备名称", prop: "deviceName", isDict: false, width: "" },
  { label: "终端型号", prop: "deviceModel", isDict: false, width: "" },
  { label: "连接类型", prop: "deviceConnectType", isDict: true, width: "" },
  { label: "设备IP", prop: "deviceIp", isDict: false, width: "" },
]
export default {
  components: {
    ElForm,
    ElFormItem,
    ElSelect,
    ElOption,
    ElTable,
    ElTableColumn,
    ElButton,
    ElPagination
  },
  props: {
    data: {
      type: Object,
      default: null,
    }
  },
  setup(props) {
    const state = reactive({
      loading: false,
      door: "",
      doorList: [],

      form: {
        pageNum: 1,
        pageSize: 10
      },
      dataList: [],
      total: 0
    })
    const getDoorList = async () => {
      let params = {
        accessControlDeviceId: props.data.id,
        pageNum: 1,
        pageSize: 1000
      }
      let { data: { list } } = await accessControlDoorList(params)
      state.doorList = list
    }
    const getList = async () => {
      state.form.accessControlDeviceId = props.data.id
      state.form.bindFlag = 1
      state.loading = true
      try {
        let { data: { list, total } } = await accessControlReadRelationList(state.form)
        state.dataList = list
        state.total = total
        state.loading = false
      }
      catch {
        state.loading = false
      }

    }
    const handleOnBind = (row) => {
      ElMessageBox.confirm("确认删除?", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        let params = {
          doorNo: row.doorNo,
          accessControlDeviceId: props.data.id,
          relationDeviceId: row.id,
          relationType: row.relationType
        }
        state.loading = true
        try {
          let { code, message } = await accessControlDeviceCanelBind(params);
          if (code === 0) {
            ElMessage.success(message);
            getList();
          }
          state.loading = false
        }
        catch {
          state.loading = false
        }
      });
    }
    const handleDoorNoChange = () => {
      getList()
    }
    const handlePageChange = (val) => {
      state.form.pageNum = val
      getList()
    }
    const handleSizeChange = (val) => {
      state.form.pageNum = 1
      state.form.pageSize = val
      getList()

    }
    onMounted(() => {
      getDoorList()
      getList()
    })
    return {
      column,
      state,
      handleOnBind,
      handleDoorNoChange,
      handlePageChange,
      handleSizeChange
    }
  }
}
</script>