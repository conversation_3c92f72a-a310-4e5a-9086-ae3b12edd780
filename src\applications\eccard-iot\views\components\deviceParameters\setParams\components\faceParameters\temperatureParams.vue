<template>
  <div class="padding-form-box setBasicParam" v-loading="state.loading">
    <el-form inline size="small" label-width="200px">
      <el-form-item label="测温模式开关:">
        <el-select v-model="state.form.paramContent.temperatureSwith">
          <el-option v-for="item in dictListFnc().temperatureSwith" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="异常温度语音播报开关:">
        <el-select v-model="state.form.paramContent.nomalTemperatureSwith">
          <el-option v-for="item in dictListFnc().nomalTemperatureSwith" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="语音自定义:">
        <el-input v-model="state.form.paramContent.voiceCustomization" placeholder="请输入"></el-input>
      </el-form-item>
      <el-form-item label="异常温度判断值(℃):">
        <el-input-number :min="0" :max="100" v-model="state.form.paramContent.abnormalTemperatureValue"></el-input-number>
      </el-form-item>
      <el-form-item label="体温异常通行开关:">
        <el-select v-model="state.form.paramContent.abnormalTemperatureSwith">
          <el-option v-for="item in dictListFnc().abnormalTemperatureSwith" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="测温位置:">
        <el-select v-model="state.form.paramContent.temperaturePosition">
          <el-option v-for="item in dictListFnc().temperaturePosition" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="有效温度最低值(℃):">
        <el-input-number :min="0" :max="100" v-model="state.form.paramContent.temperatureLowValue"></el-input-number>
      </el-form-item>
      <el-form-item label="有效温度最高值(℃):">
        <el-input-number :min="0" :max="100" v-model="state.form.paramContent.temperatureHighValue"></el-input-number>
      </el-form-item>
      <el-form-item label="环境温度补偿开关:">
        <el-select v-model="state.form.paramContent.temperatureCompensationSwitch">
          <el-option v-for="item in dictListFnc().temperatureCompensationSwitch" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="设备模式:">
        <el-select v-model="state.form.paramContent.deviceMode">
          <el-option v-for="item in dictListFnc().deviceMode" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="测温模块类型:">
        <el-select v-model="state.form.paramContent.temperatureModuleType">
          <el-option v-for="item in dictListFnc().temperatureType" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
    </el-form>
  </div>
  <div style="text-align: center">
    <el-button size="mini" @click="beforeClose()">取消</el-button>
    <el-button size="mini" type="primary" @click="saveClick()">保存</el-button>
  </div>
</template>

<script>
import {
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElInputNumber,
  ElMessage,
  ElOption,
  ElSelect,
} from "element-plus";
import { onMounted, reactive } from "vue";
import { useStore } from "vuex";
/* const option = [
  {
    value: 1,
    label: "是",
  },
  {
    value: 0,
    label: "否",
  },
];
const option2 = [
  {
    value: 1,
    label: "允许",
  },
  {
    value: 0,
    label: "禁止",
  },
]; */
const defaultParamsFnc = () => {
  return {
    abnormalTemperatureSwith: '2',	  //体温异常同行开关	string	
    abnormalTemperatureValue: '37.3',	//异常温度判断值（℃）	number	
    deviceMode: '1',	//设备模式	string	
    nomalTemperatureSwith: '1',	//异常温度语音播报开关	string	
    temperatureHighValue: '43',	//有效温度最高值	number	
    temperatureCompensationSwitch:'2',	//环境温度补偿开关	string	
    temperatureLowValue: '34',	//有效温度最低值	number	
    temperatureModuleType: '2',	//测温模块类型	string	
    temperaturePosition: '1',	//测温位置	string	
    temperatureSwith: '1',	//测温模式开关	string	
    voiceCustomization: "",	//语音自定义	string
  };
};

export default {
  components: {
    ElInputNumber,
    ElForm,
    ElFormItem,
    ElInput,
    ElSelect,
    ElOption,
    ElButton,
  },
  setup() {
    const store = useStore();
    const state = reactive({
      loading: false,
      form: {
        paramContent: defaultParamsFnc(),
        id: "",
        paramId: "",
        paramType: "temperature",
      },
    });
    const dictListFnc = () => {
      return store.state.deviceParameters[store.state.app.activeTab].dict
    }
    const saveClick = async () => {
      state.loading = true;
      let storeData = store.state.deviceParameters[store.state.app.activeTab]
      state.form.paramId = storeData.selectRow.id;
      let fn = state.form.id ? storeData.TabModule.detailsEditFnc : storeData.TabModule.detailsAddFnc
      let params = { ...state.form }
      params.paramContent = JSON.stringify(params.paramContent)
      let { message, code } = await fn(params);
      if (code === 0) {
        setTimeout(() => {
          ElMessage.success(message);
          store.dispatch("deviceParameters/getParams", store.state.deviceParameters[store.state.app.activeTab].selectRow.id);
          state.loading = false;
        }, 1500);
      }
    };

    const beforeClose = () => {
      store.commit("deviceParameters/updateState", {
        key: store.state.app.activeTab,
        childKey: "isSetParams",
        payload: false,
      });
    };
    onMounted(() => {
      let val = store.state.deviceParameters[store.state.app.activeTab].detailsParams
      if (val) {
        if (val.temperature && val.temperature.id) {
          state.form = { ...val.temperature }
          state.form.paramContent = JSON.parse(state.form.paramContent)
        } else {
          state.form.id = "";
          state.form.paramContent = defaultParamsFnc();
        }
      } else {
        //设置默认选项
        state.form.id = "";
        state.form.paramContent = defaultParamsFnc();
      }
    });
    return {
      state,
      dictListFnc,
      saveClick,
      beforeClose,
    };
  },
  name: "basicParam",
};
</script>

<style lang="scss" scoped>
.setBasicParam {
  border: 1px solid #dcdfe6;
  border-radius: 5px;
  margin-bottom: 10px;
}
:deep(.el-form-item__content) {
  width: 200px;
  .el-input-number {
    width: 100%;
  }
  .el-select {
    width: 100% !important;
  }
  .el-date-editor.el-input,
  .el-date-editor.el-input__inner {
    width: 200px;
  }
}
</style>
