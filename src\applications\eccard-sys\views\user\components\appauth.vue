<template>
  <kade-modal v-bind="attrs" :modelValue="modelValue" @update:modelValue="update">
    <div v-loading="state.dataLoading">
      <el-checkbox-group v-model="state.selected">
        <el-row :gutter="20">
          <el-col class="app-item" :span="8" v-for="item in state.appList" :key="item.id">
            <el-checkbox :label="item.id">{{ item.applyName }}</el-checkbox>
          </el-col>
        </el-row>
      </el-checkbox-group>
    </div>
    <template #footer>
      <el-button icon="el-icon-circle-close" size="small" @click="cancel">取消</el-button>
      <el-button icon="el-icon-circle-check" size="small" :loading="state.loading" @click="submit" type="primary">保存</el-button>
    </template>
  </kade-modal>
</template>
<script>
import Modal from '@/components/modal';
import { computed, reactive, watch } from 'vue';
import { 
  ElButton,
  ElCheckbox,
  ElCheckboxGroup,
  ElMessage,
  ElRow,
  ElCol,
} from 'element-plus';
import { getAllApply, getAuthUserApplyList, setUserAppAuth } from '@/applications/unified_portal/api';
export default {
  emits: ['update:modelValue', 'change'],
  props: {
    title: {
      type: String,
      default: ''
    },
    modelValue: {
      type: Boolean,
      default: false,
    },
    id: {
      type: [String, Number],
      default: null
    }
  },
  components: {
    'kade-modal': Modal,
    'el-checkbox': ElCheckbox,
    'el-checkbox-group': ElCheckboxGroup,
    'el-row': ElRow,
    'el-col': ElCol,
    'el-button': ElButton,
  },
  setup(props, context) {
    const state = reactive({
      loading: false,
      dataLoading: false,
      appList: [],
      selected: [],
    });
    const cancel = () => {
      context.emit('update:modelValue', false);
    }
    const submit = async () => {
      try {
        state.loading = true;
        const { message } = await setUserAppAuth({
          userIds: [props.id],
          appIds: state.selected,
        });
        ElMessage.success(message);
        context.emit('change');
        context.emit('update:modelValue', false);
      } catch(e) {
        throw new Error(e.message);
      } finally {
        state.loading = false;
      }    
    }
    const attrs = computed(() => {
      // eslint-disable-next-line no-unused-vars
      const { modelValue, id, ...attrs } = props;
      return attrs;
    });
    const update = (v) => {
      context.emit('update:modelValue', v);
    }
    const loadData = async () => {
      try {
        state.dataLoading = true;
        const res1 = await getAllApply({ status: "AS_ONLINE" });
        const res2 = await getAuthUserApplyList({ userId: props.id });
        const target1 = Array.isArray(res1.data) ? res1.data : [];
        const target2 = Array.isArray(res2.data) ? res2.data : [];
        state.appList = target1;
        state.selected = target2.map(it => it.appId);
      } catch(e) {
        throw new Error(e);
      } finally {
        state.dataLoading = false;
      }
    }
    watch(() => props.modelValue, (n) => {
      if(n){
        loadData();
      }
    });
    return {
      attrs,
      update,
      cancel,
      submit,
      state,
    }
  }
}
</script>
<style lang="scss" scoped>
.app-item{
  margin-bottom: 15px;
}
</style>