import mitt from 'mitt';
import { ref, onUnmounted } from 'vue';
const emitter = mitt();

// 通信hooks
export function useEvent() {
  const quenes = ref({});
  const $on = (message, callback) => {
    quenes.value[message] = callback
    emitter.on(message, callback)
  }
  const $emit = (message, callback) => {
    emitter.emit(message, callback)
  }  
  onUnmounted(() => {
    const keys = Object.keys(quenes.value);
    keys.forEach(message => {
      emitter.off(message, quenes.value[message]);
    });
  });
  return {
    $on,
    $emit,
  }
}