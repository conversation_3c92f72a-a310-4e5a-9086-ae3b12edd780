<template>
  <kade-tab-wrap :tabs="tabs" v-model="state.tab">
    <template #jbcs>
      <kade-basic-params />
    </template>
    <template #cwcs>
      <kade-temperature-params />
    </template>
    <template #kzcs>
      <kade-mask-params />
    </template>
    <template #wlcs>
      <kade-network-params />
    </template>
    <template #hdcs>
      <kade-callback-params />
    </template>
  </kade-tab-wrap>
</template>
<script>
import { reactive, onMounted } from 'vue'
import { useStore } from "vuex";
import { faceParamDetails } from "@/applications/eccard-iot/api";
import basicParams from "./basicParams"
import temperatureParams from "./temperatureParams"
import maskParams from "./maskParams"
import networkParams from "./networkParams"
import callbackParams from "./callbackParams"
const tabs = [
  {
    name: "jbcs",
    label: "基本参数",
  },
  {
    name: "cwcs",
    label: "测温参数",
  },
  {
    name: "kzcs",
    label: "口罩参数",
  },
  {
    name: "wlcs",
    label: "网络参数",
  },
  {
    name: "hdcs",
    label: "回调参数",
  },
]
export default {
  components: {
    "kade-basic-params": basicParams,
    "kade-temperature-params": temperatureParams,
    "kade-mask-params": maskParams,
    "kade-network-params": networkParams,
    "kade-callback-params": callbackParams,
  },
  props: {
    data: {
      type: Object,
      default: null
    }
  },
  setup(props) {
    const store = useStore();
    const state = reactive({
      tab: ''
    })
    onMounted(async () => {
      state.tab = 'jbcs'
      if (props.data.paramId) {
        let { data } = await faceParamDetails(props.data.paramId)
        store.commit("identityDevice/faceDistingDevice/updateState", {
          key: "detailsParams",
          payload: data
        });
      }else{
        store.commit("identityDevice/faceDistingDevice/updateState", {
          key: "detailsParams",
          payload: {}
        });
      }

    })
    return {
      tabs,
      state
    }
  }
}
</script>