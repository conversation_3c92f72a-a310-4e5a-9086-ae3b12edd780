<template>
  <div class="repair-msg">
    <div class="repair-title">宿舍预警</div>
    <div class="table-head">
      <div class="table-head-item">房间号</div>
      <div class="table-head-item">水表余额</div>
      <div class="table-head-item">电表余额</div>
      <div class="table-head-item">门锁电量</div>
      <div class="table-head-item">预警消息</div>
    </div>
    <el-carousel height="100%" direction="vertical" :autoplay="true" :interval="3000" indicator-position="none">
      <el-carousel-item v-for="item in 4" :key="item" style="color:#fff">
        <div class="table-body" v-for="item in 13" :key="item">
          <div class="table-body-item">101</div>
          <div class="table-body-item">10.00</div>
          <div class="table-body-item">200.00</div>
          <div class="table-body-item">50%</div>
          <div class="table-body-item">水表余额不足</div>
        </div>
      </el-carousel-item>
    </el-carousel>
  </div>
</template>
<script>
import { ElCarousel, ElCarouselItem } from "element-plus"
export default {
  components: {
    ElCarousel, ElCarouselItem
  },
  setup() {

  }
}
</script>\
<style lang="scss" scoped>
.repair-msg {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .repair-title {
    font-weight: 700;
    font-size: 18px;
    color: #0EE4F9;
    margin: 5px 0 10px;
  }

  .table-head {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    background: #1e408a;

    .table-head-item {
      text-align: center;
      flex: 1;
      font-weight: 700;
      font-style: normal;
      font-size: 16px;
      color: rgba(255, 255, 255, 0.8);
    }
  }

  .table-body {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    .table-body-item {
      text-align: center;
      flex: 1;
      font-weight: 700;
      font-style: normal;
      font-size: 16px;
      color: rgba(255, 255, 255, 0.8);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

.el-carousel {
  width: 100%;
  height: 100%;
}
</style>