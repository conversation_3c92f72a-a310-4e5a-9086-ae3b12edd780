<template>
  <el-dialog :model-value="isShow" :title="title" width="1500px" :before-close="beforeClose"
    :close-on-click-modal="false">
    <div class="flex-box">
      <div class="upload-photo">
        <div style="margin-bottom:20px">照片</div>
        <kade-single-image-upload :action="uploadApplyLogo" icon="iconuser" v-model="state.form.userPhoto" />
        <el-alert style="margin-top: 10px" center show-icon type="warning" title="请上传一寸证件照片" :closable="false" />
      </div>
      <el-form ref="formRef" :model="state.form" :rules="rules" label-width="120px" style="flex:1" size="small">
        <el-row>
          <el-col :span="6">
            <el-form-item label="人员编号：" prop="userCode">
              <el-input v-model="state.form.userCode" placeholder="请输入" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="人员姓名：" prop="userName">
              <el-input v-model="state.form.userName" placeholder="请输入" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="性别：" prop="userSex">
              <el-select v-model="state.form.userSex" clearable>
                <el-option v-for="(item,index) in sexList" :key="index" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="民族：">
              <el-select v-model="state.form.userNation" clearable>
                <el-option v-for="(item,index) in nationList" :key="index" :label="item.name" :value="item.code">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="籍贯：">
              <el-input v-model="state.form.userNativePlace" placeholder="请输入" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="出生地：">
              <el-select v-model="state.form.userBirthPlace" clearable>
                <el-option v-for="(item,index) in state.provinceList" :key="index" :label="item.name"
                  :value="item.code"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="出生日期：">
              <el-date-picker v-model="state.form.userBirthday" type="date" placeholder="请选择" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="政治面貌：">
              <el-select v-model="state.form.userPoliticalFace" clearable>
                <el-option v-for="(item,index) in faceList" :key="index" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="证件类型：">
              <el-select v-model="state.form.userIdType" clearable>
                <el-option v-for="(item,index) in cardTypes" :key="index" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="证件号码：">
              <el-input v-model="state.form.userIdNo" placeholder="请输入" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="手机号码：">
              <el-input v-model="state.form.userTel" placeholder="请输入" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="户籍地址：" class="url-input">
              <el-select v-model="state.addressForm.householdRegisterProvince" @change="householdProvinceChange"
                clearable>
                <el-option v-for="(item,index) in state.provinceList" :key="index" :label="item.name"
                  :value="item.code"></el-option>
              </el-select>
              <el-select v-model="state.addressForm.householdRegisterCity" @change="householdCityChange" clearable>
                <el-option v-for="(item,index) in state.householdCityList" :key="index" :label="item.name"
                  :value="item.code"></el-option>
              </el-select>
              <el-select v-model="state.addressForm.householdRegisterArea" clearable>
                <el-option v-for="(item,index) in state.householdAreaList" :key="index" :label="item.name"
                  :value="item.code"></el-option>
              </el-select>
              <el-input v-model="state.addressForm.householdRegisterAddress" placeholder="请输入" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="组织机构：" prop="userDept">
              <kade-dept-select-tree style="width: 100%" :value="state.form.userDept" valueKey="id" :multiple="false"
                @valueChange="(val) => (state.form.userDept = val.id)" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="岗位：">
              <el-select v-model="state.form.userPostId" clearable>
                <el-option v-for="(item,index) in state.stationList" :key="index" :label="item.postName"
                  :value="item.id"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="身份类别：" prop="userRole">
              <el-select v-model="state.form.userRole" clearable>
                <el-option v-for="(item,index) in roleList" :key="index" :label="item.roleName" :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="是否住校：">
              <el-select v-model="state.form.userIsBoarders" disabled>
                <el-option v-for="(item,index) in isSchoolList" :key="index" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="人员状态：" prop="userState" clearable>
              <el-select v-model="state.form.userState">
                <el-option v-for="(item,index) in statusList" :key="index" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="入校时间：">
              <el-date-picker v-model="state.form.userSchoolTime" type="date" placeholder="请选择" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="离校时间：">
              <el-date-picker v-model="state.form.userLeaveTime" type="date" placeholder="请选择" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="交易密码：">
              <el-input :minlength="6" :maxlength="6" v-model="state.form.userTransPwd" placeholder="请输入"
                onkeyup="this.value=this.value.replace(/\D/g,'')"
                onafterpaste="this.value=this.value.replace(/\D/g,'')"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="家庭住址：" class="url-input">
              <el-select v-model="state.addressForm.familyProvince" @change="familyProvinceChange" clearable>
                <el-option v-for="(item,index) in state.provinceList" :key="index" :label="item.name"
                  :value="item.code"></el-option>
              </el-select>
              <el-select v-model="state.addressForm.familyCity" @change="familyCityChange" clearable>
                <el-option v-for="(item,index) in state.familyCityList" :key="index" :label="item.name"
                  :value="item.code"></el-option>
              </el-select>
              <el-select v-model="state.addressForm.familyArea" clearable>
                <el-option v-for="(item,index) in state.familyAreaList" :key="index" :label="item.name"
                  :value="item.code"></el-option>
              </el-select>
              <el-input v-model="state.addressForm.familyAddress" placeholder="请输入" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注：">
              <el-input v-model="state.form.userRemark" type="textarea" placeholder="请输入" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="是否启用：">
              <el-switch v-model="state.form.userAccountState" active-value="ENABLE_TRUE"
                inactive-value="ENABLE_FALSE" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="submit()" type="primary" :loading="state.loading" size="mini">保&nbsp;&nbsp;存</el-button>
        <el-button @click="beforeClose()" size="mini">返&nbsp;&nbsp;回</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script>
import {
  ElDialog,
  ElButton,
  ElRow,
  ElCol,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElDatePicker,
  ElSwitch,
  ElAlert,
  ElMessage,
} from "element-plus";
import { reactive, computed, watch, ref, nextTick, onMounted } from "vue";
import { useStore } from "vuex";
import { useDict } from "@/hooks/useDict.js";
import { timeStr, dateStr } from "@/utils/date.js";
import { uploadApplyLogo } from "@/applications/unified_portal/api";
import {
  uploadHeadImage64,
  getBaseUserInfo,
  addUser,
  updateUserInfo,
  getUserAddressInfo,
  stationList,
  getAddressList,
  addAddress,
  editAddress,
} from "@/applications/eccard-basic-data/api";
import deptSelectTree from "@/components/tree/deptSelectTree.vue";
import SingleImageUpload from "@/components/singleImageUpload";
export default {
  components: {
    ElDialog,
    ElButton,
    ElRow,
    ElCol,
    ElForm,
    ElFormItem,
    ElInput,
    ElSelect,
    ElOption,
    ElDatePicker,
    ElSwitch,
    ElAlert,
    "kade-dept-select-tree": deptSelectTree,
    "kade-single-image-upload": SingleImageUpload,
  },
  emits: ["close"],
  props: {
    roleList: {
      type: Array,
      default: null,
    },
  },
  setup() {
    const statusList = useDict("BASE_USER_STATE");
    const sexList = useDict("SYS_SEX");
    const faceList = useDict("USER_POLITICAL_OUTLOOK");
    const isSchoolList = useDict("SYS_BOOL_STRING");
    const cardTypes = useDict("BASE_ID_TYPE");
    const formRef = ref(null);
    const store = useStore();
    const state = reactive({
      loading:false,
      form: {
        userAccountState: "ENABLE_TRUE",
        userSex: "SEX_MALE",
        userPoliticalFace: "THE_MASSES",
        userIdType: "ID_CARD",
        userIsBoarders: "FALSE",
        userState: "STATE_IN",
        userTransPwd: "000000",
        userSource: "FROM_SYS",
      },
      addressForm: {
        householdRegisterProvince: "",
        householdRegisterCity: "",
        householdRegisterArea: "",
        familyProvince: "",
        familyCity: "",
        familyArea: "",
      },
      provinceList: [],
      familyCityList: [],
      familyAreaList: [],
      householdCityList: [],
      householdAreaList: [],
      stationList: [],
      nationList: [],
    });
    const title = computed(() => {
      return store.state.userInfo.rowData.id ? "编辑人员信息" : "新增人员信息";
    });
    const isShow = computed(() => {
      return store.state.userInfo.isEdit;
    });
    const nationList = computed(() => {
      return store.state.userInfo.nationList;
    });
    watch(
      () => store.state.userInfo.isEdit,
      (val) => {
        if (val) {
          if (store.state.userInfo.rowData.id) {
            getDetails();
            getUserAddress();
          } else {
            state.form = {
              userAccountState: "ENABLE_TRUE",
              userSex: "SEX_MALE",
              userPoliticalFace: "THE_MASSES",
              userIdType: "ID_CARD",
              userIsBoarders: "FALSE",
              userState: "STATE_IN",
              userTransPwd: "000000",
            };
            state.addressForm = {};
            state.familyCityList = [];
            state.familyAreaList = [];
            state.householdCityList = [];
            state.householdAreaList = [];
          }
        } else {
          state.familyCityList = [];
          state.familyAreaList = [];
          state.householdCityList = [];
          state.householdAreaList = [];
        }
        nextTick(() => {
          formRef.value.clearValidate();
        });
      }
    );
    const rules = {
      userCode: [
        {
          required: true,
          message: "请输入用户编号",
        },
        {
          pattern: /^[0-9a-zA-Z]+$/,
          message: "请输入字母+数字",
        },
        {
          max: 20,
          message: "用户编号长度不能超过20字符",
        },
      ],
      userName: [
        {
          required: true,
          message: "请输入用户姓名",
        },
        {
          max: 20,
          message: "用户姓名长度不能超过20字符",
        },
        {
          pattern: /^\S.*\S$|(^\S{0,1}\S$)/,
          message: "用户姓名首尾不能包含空格",
        },
      ],
      userSex: [
        {
          required: true,
          message: "请选择性别",
          trigger: "change",
        },
      ],
      userDept: [
        {
          required: true,
          message: "请选择组织机构",
          trigger: "change",
        },
      ],
      userRole: [
        {
          required: true,
          message: "请选择身份类别",
          trigger: "change",
        },
      ],
      userState: [
        {
          required: true,
          message: "请选择人员状态",
          trigger: "change",
        },
      ],
    };
    const getStationList = async () => {
      let { data } = await stationList();
      state.stationList = data;
    };
    const getProvinceList = async () => {
      let { data } = await getAddressList({ level: 1 });
      state.provinceList = data;
    };

    const getCityList = async (val, type) => {
      if (!val) return;
      let { data } = await getAddressList({ parentCode: val, level: 2 });
      if (type === "house") {
        state.householdCityList = data;
      } else {
        state.familyCityList = data;
      }
    };
    const getAreaList = async (val, type) => {
      if (!val) return;
      let { data } = await getAddressList({ parentCode: val, level: 3 });
      if (type === "house") {
        state.householdAreaList = data;
      } else {
        state.familyAreaList = data;
      }
    };

    const householdProvinceChange = (val) => {
      getCityList(val, "house");
      state.householdCityList = [];
      state.householdAreaList = [];
      state.addressForm.householdRegisterCity = "";
      state.addressForm.householdRegisterArea = "";
    };
    const householdCityChange = (val) => {
      getAreaList(val, "house");
      state.householdAreaList = [];
      state.addressForm.householdRegisterArea = "";
    };
    const familyProvinceChange = (val) => {
      getCityList(val, "family");
      state.familyCityList = [];
      state.familyAreaList = [];
      state.addressForm.familyCity = "";
      state.addressForm.familyArea = "";
    };
    const familyCityChange = (val) => {
      getAreaList(val, "family");
      state.familyAreaList = [];
      state.addressForm.familyArea = "";
    };

    const getDetails = async () => {
      let params = {
        userCode: store.state.userInfo.rowData.userCode,
        userId: store.state.userInfo.rowData.id,
      };
      let { data } = await getBaseUserInfo(params);
      state.form = data;
      state.form.userRole = Number(state.form.userRole);
      state.form.userBirthPlace = Number(state.form.userBirthPlace);
    };
    const getUserAddress = async () => {
      let { data } = await getUserAddressInfo(store.state.userInfo.rowData.id);
      if (data) {
        state.addressForm = data;
        data.householdRegisterProvince &&
          getCityList(data.householdRegisterProvince, "house");
        data.householdRegisterCity &&
          getAreaList(data.householdRegisterCity, "house");
        data.familyProvince && getCityList(data.familyProvince, "family");
        data.familyCity && getAreaList(data.familyCity, "family");
      } else {
        state.addressForm = {};
      }
    };
    const submit = () => {
      formRef.value.validate(async (valid) => {
        if (valid) {
          let params = { ...state.form };
          let fn = store.state.userInfo.rowData.id ? updateUserInfo : addUser;
          let fn1 = state.addressForm.id ? editAddress : addAddress;
          if (store.state.userInfo.rowData.id) {
            params.userId = store.state.userInfo.rowData.id;
          }
          params.userBirthday = params.userBirthday
            ? dateStr(params.userBirthday)
            : "";
          params.userSchoolTime = params.userSchoolTime
            ? timeStr(params.userSchoolTime)
            : "";
          params.userLeaveTime = params.userLeaveTime
            ? timeStr(params.userLeaveTime)
            : "";
          state.loading = true
          let { code, message } = await fn(params);
          if (code === 0) {
            let params1 = { ...state.addressForm };
            params1.userId = store.state.userInfo.rowData.id;
            for (let key in params1) {
              if (!params1[key]) {
                delete params1[key];
              }
            }
            let res = await fn1(params1);

            if (res.code === 0) {
              ElMessage.success(message);
            }
            beforeClose();
            store.commit("userInfo/updateState", {
              key: "isUpdateList",
              payload: true,
            });
          }
          state.loading = false
        } else {
          return false;
        }
      });
    };

    const beforeClose = () => {
      store.commit("userInfo/updateState", {
        key: "isEdit",
        payload: false,
      });
    };

    onMounted(() => {
      getProvinceList();
      getStationList();
      let a = null,
        b = 2;
      let c = (a ??= b);
      console.log(c);
    });
    return {
      uploadHeadImage64,
      uploadApplyLogo,
      formRef,
      rules,
      nationList,
      statusList,
      sexList,
      faceList,
      isSchoolList,
      cardTypes,
      state,
      title,
      isShow,
      householdProvinceChange,
      householdCityChange,
      familyProvinceChange,
      familyCityChange,
      submit,
      beforeClose,
    };
  },
};
</script>
<style lang="scss" scoped>
.flex-box {
  margin-top: 20px;
  display: flex;

  .upload-photo {
    width: 160px;
    margin-right: 20px;
  }
}

:deep(.el-date-editor.el-input) {
  width: 200px;
}

.url-input {
  :deep(.el-form-item__content) {
    display: flex;

    .el-select {
      margin-right: 5px;
    }

    .el-input {
      width: 100%;

      .el-input__inner {
        width: 100%;
      }
    }
  }
}

:deep(.single-image-uploader) {
  width: 160px;
  height: 224px;

  .el-upload {
    width: 160px;
    height: 224px;
  }

  .images {
    width: 160px;
    height: 224px;
  }

  .image-slot {
    width: 160px;
    height: 224px;
  }
}
</style>
