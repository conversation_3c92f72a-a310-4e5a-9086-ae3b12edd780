<template>
  <el-table :data="state.dataList" border stripe v-loading="state.loading">
    <el-table-column show-overflow-tooltip label="通信状态" prop="" align="center"></el-table-column>
    <el-table-column show-overflow-tooltip label="所属区域" prop="areaName" align="center"></el-table-column>
    <el-table-column show-overflow-tooltip label="设备类型" prop="deviceType" align="center"></el-table-column>
    <el-table-column show-overflow-tooltip label="机号" prop="deviceNo" align="center"></el-table-column>
    <el-table-column show-overflow-tooltip label="设备名称" prop="deviceName" align="center"></el-table-column>
    <el-table-column show-overflow-tooltip label="规格型号" prop="deviceModel" align="center"></el-table-column>
    <el-table-column show-overflow-tooltip label="添加时间" prop="createTime" align="center"></el-table-column>
    <el-table-column label="操作" align="center">
      <template #default="scope">
        <el-button class="green" size="mini" type="text" @click="cancelBind(scope.row)">取消绑定</el-button>
      </template>
    </el-table-column>
  </el-table>
  <div class="pagination">
    <el-pagination background :current-page="state.form.pageNum" :page-size="state.form.pageSize" layout="total, sizes, prev, pager, next, jumper" :page-sizes="[6, 10, 20, 50, 100]" :total="state.total" @current-change="handlePageChange" @size-change="handleSizeChange">
    </el-pagination>
  </div>
</template>
<script>
import { ElTable, ElTableColumn, ElPagination, ElButton, ElMessageBox, ElMessage } from "element-plus";
import { onMounted, reactive } from "vue";
import { bindGatewayDeviceList, noBindGateway } from "@/applications/eccard-iot/api";
export default {
  components: {
    ElTable,
    ElTableColumn,
    ElPagination,
    ElButton,
  },
  props: {
    rowData: {
      type: Object,
      defalut: null,
    },
  },
  setup(props) {
    const state = reactive({
      loading: false,
      form: {
        pageNum: 1,
        pageSize: 10,
      },
      dataList: [],
      total: 0,
    });
    const getList = async () => {
      state.form.gatewayType = props.rowData.gatewayType
      state.form.rgatewayId = props.rowData.id
      state.loading = true
      try {
        let { data: { list, total } } = await bindGatewayDeviceList(state.form)
        state.dataList = list
        state.total = total
        state.loading = false
      }
      catch {
        state.loading = false
      }
    }
    const cancelBind = (row) => {
      console.log(row);

      ElMessageBox.confirm(`确认取消绑定?`, {
        type: "warning",
        closeOnPressEscape: false,
        closeOnClickModal: false,
      }).then(async () => {
        try {
          let params = {
            rdeviceId:row.id,
            rgatewayId: props.rowData.id,
            deviceType: row.deviceType,
            gatewayType: props.rowData.gatewayType
          }
          state.loading = true
          const { code, message } = await noBindGateway(params)
          if (code === 0) {
            ElMessage.success(message);
            getList();
          }
          state.loading = false

        }
        catch {
          state.loading = false
        }
      });
    };
    const handlePageChange = (val) => {
      console.log(val);
      state.form.pageNum = val
      getList()
    };
    const handleSizeChange = (val) => {
      state.form.pageNum = 1
      state.form.pageSize = val
      console.log(val);
      getList()
    };
    onMounted(() => {
      getList()
    })
    return {
      state,
      cancelBind,
      handlePageChange,
      handleSizeChange,
    };
  },
};
</script>