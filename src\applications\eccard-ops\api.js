import request from '@/service';


/**
 * 查询所有菜单
 */

export function getMenuList(params) {
  return request.get('/eccard-sys/sysMenu/table', { params });
}
/**
 * 新增菜单 
 */
 export function addMenu(params) {
  return request.post('/eccard-sys/sysMenu', params);
}
/**
 * 编辑菜单 
 */
 export function editMenu(params) {
  return request.put('/eccard-sys/sysMenu', params);
}

/**
 * 修改菜单状态 
 */
 export function editMenuStatus(params) {
  return request.patch('/eccard-sys/sysMenu', params);
}

/**
 * 查询菜单权限
 */
 export function getMenuAuthList(params) {
  return request.get(`/eccard-sys/sysPermission/${params.menuId}/page`, { params:params.form });
}

/**
 * 获取用户顶级菜单权限列表
 */

 export function getUserAppPermission(params) {
  return request.get('/eccard-sys/sysPermission/getUserAppPermission', { params });
}
/**
 * 新增菜单权限
 */
 export function addMenuAuth(params) {
  return request.post(`/eccard-sys/sysPermission/${params.menuId}`, params.form);
}
/**
 * 修改菜单权限
 */
 export function editMenuAuth(params) {
  return request.put(`/eccard-sys/sysPermission/${params.menuId}`, params.form);
}
/**
 * 删除菜单权限
 */
 export function delMenuAuth(params) {
  return request.delete(`/eccard-sys/sysPermission/${params.menuId}/${params.permissionIds}`);
}




/**
 * 获取租户列表
 */
 export function getTenantListPage(params) {
  return request.get('/eccard-ops/tenant', { params });
}
/**
 * 获取租户列表（不分页）
 */
 export function getTenantList(params) {
  return request.get('/eccard-ops/tenant/table', { params });
}
/**
 * 新增租户
 */
 export function addTenant(params) {
  return request.post('/eccard-ops/tenant', params);
}
/**
 * 编辑租户
 */
 export function editTenant(params) {
  return request.put('/eccard-ops/tenant', params);
}
/**
 * 获取租户详情
 */
 export function getTenantDetails(params) {
  return request.get(`/eccard-ops/tenant/${params}/detail`);
}

/**
 * 冻结或者解冻租户
 */
 export function updateStatus(params) {
  return request.patch('/eccard-ops/tenant/updateStatus', params);
}
/**
 * 租户授权时获取所有顶级菜单列表
 */
 export function getTenantAuthAppList(params) {
  return request.get(`/eccard-ops/tenant/${params}/getTenantAuthAppList`);
}
/**
 * 保存租户授权顶级菜单列表
 */
 export function saveTenantSupperRoleMenu(params) {
  return request.put(`/eccard-ops/tenant/${params.tenantId}/saveTenantSupperRoleApp`,{ids:params.menuIds});
}




// 租户


/**
 * 租户应用授权
 */
export function addTenantApplyAuthorize(params) {
  return request.post('/eccard-tenant/Basic/addTenantApplyAuthorize', params);
}

/**
 * 添加租户
 */
export function addTenantDevops(params) {
  return request.post('/eccard-tenant/Basic/addTenantDevops', params);
}

/**
 * 自主添加租户
 */
export function addTenantSelf(params) {
  return request.post('/eccard-tenant/Basic/addTenantSelf', params);
}

/**
 * 查询一二级租户、平台、代理商
 */
export function getAgentTenant(params) {
  return request.get('/eccard-tenant/Basic/getAgentTenant', { params });
}

/**
 * 根据租户id查询信息
 */
export function getTenantInfoById(params) {
  return request.get('/eccard-tenant/Basic/getTenantInfoById', { params });
}

/**
 * 分页查询自主注册的租户列表
 */
export function getTenantSelfListToPage(params) {
  return request.get('/eccard-tenant/Basic/getTenantSelfListToPage', { params });
}

/**
 * 审核
 */
export function updateTenantAudit(params) {
  return request.post('/eccard-tenant/Basic/updateTenantAudit', params);
}

/**
 * 更新租户信息
 */
export function updateTenantInfo(params) {
  return request.post('/eccard-tenant/Basic/updateTenantInfo', params);
}

/**
 * 冻结|解冻
 */
export function updateTenantStatus(params) {
  return request.post('/eccard-tenant/Basic/updateTenantStatus', params);
}

/**
 * 获取授权
 */
export function getTenantApplyAuthorize(params) {
  return request.get('/eccard-tenant/Basic/getTenantApplyAuthorize', { params });
}

/**
 * 发短信
 */
export function sendSms(params) {
  return request.get('/eccard-message/Message/sendSms', { params });
}
/**
 * 测试发短信
 */
 export function testSms(params) {
  return request.get('/eccard-message/test/sendSms', { params });
}
