<template>
  <el-dialog :model-value="isShow" title="退宿" width="600px" :before-close="beforeClose" :close-on-click-modal="false"
    v-loading="state.loading">
    <el-form label-width="120px" size="mini" ref="formRef" style="margin-top:20px" :model="state.form" :rules="rules">
      <el-form-item label="退宿时间：" prop="checkOutTime">
        <el-date-picker v-model="state.form.checkOutTime" type="date" placeholder="请选择"></el-date-picker>
      </el-form-item>
      <el-form-item label="退宿备注：">
        <el-input type="textarea" v-model="state.form.checkOutRemark"  show-word-limit maxlength="200" :autosize="{ minRows: 2, maxRows: 6 }"></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="beforeClose()" size="mini">取&nbsp;&nbsp;消</el-button>
        <el-button @click="submit()" type="primary" size="mini">确&nbsp;&nbsp;认</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script>
import { ElDialog, ElButton, ElForm, ElFormItem, ElInput, ElDatePicker, ElMessage } from "element-plus";
import { reactive, ref } from '@vue/reactivity';
import { timeStr } from "@/utils/date.js"
import { checkOut } from "@/applications/eccard-dorm/api";

const rules = {
  checkOutTime: [
    {
      required: true,
      message: "请选择退宿时间",
      trigger: "change",
    },
  ],
}
export default {
  components: {
    ElDialog,
    ElButton,
    ElForm,
    ElFormItem,
    ElInput,
    ElDatePicker
  },
  props: {
    isShow: {
      type: Boolean,
      default: false,
    },
    rowData: {
      type: Object,
      default: null
    }
  },
  setup(props, context) {
    const formRef = ref(null)
    const state = reactive({
      loading: false,
      form: {},
    })
    const submit = () => {
      formRef.value.validate(async (valid) => {
        if (valid) {
          let params = { ...state.form, roomId: props.rowData.roomId, userId: props.rowData.userId }
          params.checkOutTime = timeStr(params.checkOutTime)
          state.loading = true
          try {
            let { code, message } = await checkOut(params)
            if (code === 0) {
              ElMessage.success(message)
              context.emit("close", true)
            }
            state.loading = false
          }
          catch {
            state.loading = false
          }
        }
      })
    }
    const beforeClose = () => {
      context.emit("close", false)
    }
    return {
      formRef,
      rules,
      state,
      submit,
      beforeClose
    }
  }
};
</script>
<style lang="scss" scoped>
.export-header {
  padding: 10px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.template-upload {
  text-decoration: underline;
}
</style>