<template>
  <div class="kade-table">
    <div class="table-wrap-head">
      <div class="title">
        <i :class="['el-icon-liebiaolist46', 'toggle-icon', { toggle: state.show }]" @click="handleToggle"></i>
        <span class="text">{{ title }}</span>
      </div>
      <div class="btns">
        <slot name="extra"></slot>
      </div>
    </div>
    <div class="col-setting" :style="settingStyle">
      <div class="col-setting-wrap" ref="wrapRef">
        <template v-for="item in columns" :key="item.label">
          <el-checkbox
            @change="handleCheckChange"
            v-model="item.show"
            v-if="item.type !== 'selection'"
          >{{ item.label }}</el-checkbox>
        </template>
      </div>
    </div>
    <el-table
      ref="tableRef"
      style="width: 100%"
      :data="options.dataList"
      v-loading="options.loading"
      border
      stripe
      highlight-current-row
      @current-change="(v) => $emit('on-select-change', v)"
      @selection-change="(v) => $emit('on-check-change', v)"
      v-bind="tableConfig"
    >
      <template v-for="(item,index) in columns" :key="index">
        <el-table-column v-bind="item" v-if="item.show">
          <template #default="scope" v-if="item.render">
            <component :is="item.render" v-bind="scope" :key="index"></component>
          </template>
        </el-table-column>
      </template>                                  
    </el-table>
    <div class="pagination">
      <el-pagination
          background
          v-model:current-page="options.currentPage"
          layout="total, sizes, prev, pager, next, jumper"
          :page-sizes="[10, 20, 50, 100, 200]"        
          :total="options.total"
          v-model:page-size="options.pageSize"
          @current-change="handlePageChange"
          @size-change="handlePageChange"
      >
      </el-pagination>           
    </div>    
  </div>
</template>
<script>
// 表格二次封装组件
import { ElCheckbox, ElPagination, ElTable, ElTableColumn } from 'element-plus';
import { reactive, onMounted, computed, ref, nextTick, onUnmounted } from 'vue';
export default {
  name: 'kade-table-wrap',
  components: {
    'el-checkbox': ElCheckbox,
    'el-table': ElTable,
    'el-table-column': ElTableColumn,
    'el-pagination': ElPagination,
  },
  emits: ['update:columns', 'on-select-change', 'on-page-change', 'on-check-change'],
  props: {
    title: String,
    tableOptions: {
      type: Object,
      default: () => {
        return {

        }
      }
    },
    columns: {
      type: Array,
      default: () => {
        return [];
      }
    },
    loadData: {
      type: Function,
      default: () => {}
    },
    tableConfig: {
      type: Object,
      default: () => ({})
    }
  },
  setup(props, context) {
    const wrapRef = ref(null);
    const tableRef = ref(null);
    const state = reactive({
      showKeys: [],
      show: false,
      height: 0,
    });
    const handleToggle = () => {
      state.show = !state.show;
    }
    const initHeight = () => {
      const target = window.getComputedStyle(wrapRef.value);
      state.height = target.height;
    }
    const settingStyle = computed(() => {
      return state.show ? {
        height: state.height,
      } : {
        height: 0,
      }
    });
    const handlePageChange = () => {
      context.emit('on-page-change');
      props.loadData();
    }
    onMounted(() => {
      nextTick(() => {
        initHeight();
      });
      window.addEventListener('resize', initHeight);
    });
    onUnmounted(() => {
      window.removeEventListener('resize', initHeight);
    });
    return {
      settingStyle,
      state,
      handleToggle,
      wrapRef,
      tableRef,
      options: props.tableOptions,
      handlePageChange,
      handleCheckChange: () => {
        context.emit('update:columns', [...props.columns]);
      }
    }
  }
}
</script>
<style lang="scss">
.kade-table{
  position: relative;
  padding-bottom: 10px;
  box-sizing: border-box;
  .el-table{
    border-left: none;
    tr > th:last-child,tr > td:last-child{
      border-right: none!important;
    }
    .el-table--border, .el-table--group{
      border: none;
    }
    &::after{
      background-color: transparent;
    }
  }
  .pagination{
    padding-right: 20px;
    box-sizing: border-box;
  }
  .table-wrap-head{
    width: 100%;
    height: 50px;
    display: flex;
    justify-content: space-between;
    padding: 0 20px;
    box-sizing: border-box;
    .title{
      height: 50px;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      i{
        margin-right: 5px;
        cursor: pointer;
        font-size: 16px;
        &:hover{
          color: $primary-color;
        }
      }
    }
    .btns{
      display: flex;
      justify-content: flex-end;
      align-items: center;
    }
  }
  border-radius: 5px;
  border: 1px solid $border-color;
  .col-setting{
    height: 0;
    overflow: hidden;
    transition: height .2s linear;
    .col-setting-wrap{
      padding: 20px 20px 0 20px;
      box-sizing: border-box;
      border-top: 1px solid $border-color;
      .el-checkbox{
        padding-bottom: 20px;
        .el-checkbox__input.is-checked + .el-checkbox__label{
          color: #606266;
        }
      }
    }
  }
  .toggle-icon{
    transition: transform .2s linear;
    transform: translateY(1px);
    &.toggle{
      transform: translateY(1px) rotateZ(-90deg);
    }
  }
}
</style>