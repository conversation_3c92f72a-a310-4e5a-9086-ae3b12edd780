<template>
  <kade-tab-wrap :tabs="tabs" v-model="state.tab">
    <template #jbcs>
      <kade-basic-params />
    </template>
    <template #sdcs>
      <kade-time-params />
    </template>
    <template #klqxcs>
      <kade-card-auth-params />
    </template>
    <template #pjdycs>
      <kade-bill-print-params />
    </template>
    <template #yssz>
      <kade-style-set-params />
    </template>
  </kade-tab-wrap>
</template>
<script>
import { reactive } from "@vue/reactivity";
import { useStore } from "vuex";
import { onMounted } from "@vue/runtime-core";
import { getdeviceParamDetail } from "@/applications/eccard-iot/api";

import basicParams from "./basicParams";
import timeParams from "./timeParams";
import cardAuthParams from "./cardAuthParams";
import billPrintParams from "./billPrintParams";
import styleSetParams from "./styleSetParams";
const tabs = [
  { name: "jbcs", label: "基本参数" },
  { name: "sdcs", label: "时段参数" },
  { name: "klqxcs", label: "卡类权限参数" },
  { name: "pjdycs", label: "票据打印参数" },
  { name: "yssz", label: "样式设置" },
];
export default {
  components: {
    "kade-basic-params": basicParams,
    "kade-time-params": timeParams,
    "kade-card-auth-params": cardAuthParams,
    "kade-bill-print-params": billPrintParams,
    "kade-style-set-params": styleSetParams,
  },
  setup() {
    const store = useStore();
    const state = reactive({
      tab: "jbcs",
    })

    const getDetails = async () => {
      let { data } = await getdeviceParamDetail(
        store.state.deviceParameters[store.state.app.activeTab].selectRow.id
      );
      store.commit("deviceParameters/updateState", {
        key: store.state.app.activeTab,
        childKey: "detailsParams",
        payload: data,
      });
    };
    onMounted(() => {
      getDetails();
    });
    return {
      tabs,
      state,
    };
  },
};
</script>
<style lang="scss" scoped>
</style>