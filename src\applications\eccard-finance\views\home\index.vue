<template>
    <div class="padding-box">
        <el-row>
            <el-col class="summary-box" :span="24" style="min-width:1621px">
                <div class="item-box" v-for="(item,index) in list" :key="index" style="min-width:390px">
                    <div class="label-box">
                        <div class="title">{{item.label}}</div>
                        <i class="el-icon-xinxi"></i>
                    </div>
                    <div class="value-box">{{item.value}}</div>
                    <component :is="item.component"/>
                </div>
            </el-col>
            <el-col :span="24" style="min-width:1621px">
                <kade-tab-wrap :tabs="tabs" v-model="state.tab">
                    <template #jye>
                        <kade-trade-volume />
                    </template>
                    <template #yhl>
                        <kade-users-number />
                    </template>
                    <template #extra>
                        <div class="date-box">
                            <div class="date" @click="handleDate(item)" v-for="(item,index) in defaultDateList" :key="index">{{item.label}}</div>
                            <el-date-picker v-model="state.requestDate" type="daterange" size="mini" range-separator="~" start-placeholder="开始时间" end-placeholder="结束时间" />
                        </div>
                    </template>
                </kade-tab-wrap> 
            </el-col>
            <el-col :span="12" style="min-width:770px">
                <kade-trade-trend />
            </el-col>
            <el-col :span="12" style="min-width:770px">
                <kade-trade-proportion />
            </el-col>
            <el-col :span="8">
                <kade-first-data />
            </el-col>
            <el-col :span="8">
                <kade-second-data />
            </el-col>
            <el-col :span="8">
                <kade-third-data />
            </el-col>
        </el-row>
    </div>
</template>
<script>
import { ElRow, ElCol, ElDatePicker } from "element-plus";
import { reactive } from "@vue/reactivity";
import { defaultDateList, requestDate } from "@/utils/reqDefaultDate";
import tradeVolume from "./tradeVolume.vue";
import usersNumber from "./usersNumber.vue";
import tradeTrend from "./tradeTrend.vue";
import tradeProportion from "./tradeProportion.vue";
import firstData from "./firstData.vue";
import secondData from "./secondData.vue";
import thirdData from "./thirdData.vue";
import tradeSummary from "./components/summary/tradeSummary.vue";
import personSummary from "./components/summary/personSummary.vue";
import numberSummary from "./components/summary/numberSummary.vue";
import onlineSummary from "./components/summary/onlineSummary.vue";

const tabs = [
    { name: "jye", label: "交易额" },
    { name: "yhl", label: "用户量" },
];
export default {
    components: {
        ElRow,
        ElCol,
        ElDatePicker,
        "kade-trade-volume": tradeVolume,
        "kade-users-number": usersNumber,
        "kade-trade-trend": tradeTrend,
        "kade-trade-proportion": tradeProportion,
        "kade-first-data": firstData,
        "kade-second-data": secondData,
        "kade-third-data": thirdData,
    },
    setup() {
        const state = reactive({
            tab: "jye",
            requestDate: requestDate(),
        });
        const list = [
            { label: "总交易额", value: "126,560", component: tradeSummary },
            { label: "总用户量", value: "8,846", component: personSummary },
            { label: "交易笔数", value: "6,560", component: numberSummary },
            { label: "设备在线率", value: "78%", component: onlineSummary },
        ];
        const handleDate = (item) => {
            state.requestDate = item.value();
        };
        return {
            defaultDateList,
            list,
            tabs,
            state,
            handleDate,
        };
    },
};
</script>
<style lang="scss" scoped>
.summary-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    .item-box {
        box-sizing: border-box;
        width: 24%;
        border: 1px solid #eeeeee;
        padding: 15px 20px 10px 15px;
        margin: 0 0px 20px 0px;
        .label-box {
            display: flex;
            justify-content: space-between;
            align-items: center;
            .title {
                color: #0000006d;
                font-size: 12px;
                padding-bottom: 5px;
            }
            .el-icon-xinxi {
                color: #0000006d;
            }
        }
        .value-box {
            font-size: 22px;
            padding-bottom: 10px;
        }
    }
}

.date-box {
    display: flex;
    align-items: center;
    .date {
        margin-right: 10px;
        color: #000000a5;
        font-size: 10px;
    }
}
.border-box {
    border: 0;
}
:deep(.kade-tab-wrap .kade-tab-extra) {
    top: 6px;
}
:deep(.el-range-editor--mini.el-input__inner) {
    width: 200px;
}
:deep(.el-tabs__item){
    font-size: 13px;
}
</style>
