import { getRoleMenuList, roleDeptList,getAllRoleDeptList, roleAreaList, rolePermissionList } from '@/applications/eccard-sys/api';

const state = {
  rowRoleData: "",
  menuList: [],
  moduleAuthIdList: [],
  areaList: [],
  areaAuthIdList: [],
  deptList: [],
  deptDefaultList: [],
  deptAuthIdList: [],
  rowModuleMenu: "",
  btnAuthList: [],
};
const mutations = {
  updateState(state, { key, payload }) {
    state[key] = payload;
  },
};
const actions = {
  async getMenuList({ commit }) {
    commit("updateState", { key: 'menuList', payload: [] })
    if (state.rowRoleData.id) {
      let { data } = await getRoleMenuList(state.rowRoleData.id)
      commit("updateState", { key: 'menuList', payload: data })
      commit("updateState", { key: 'rowModuleMenu', payload: "" })
      commit("updateState", { key: 'btnAuthList', payload: [] })
    }

  },
  async getAreaList({ commit }) {
    commit("updateState", { key: 'areaList', payload: [] })
    if (state.rowRoleData.id) {
      let { data } = await roleAreaList(state.rowRoleData.id)
      commit("updateState", { key: 'areaList', payload: data })
    }

  },
  async getDeptList({ commit }) {
    commit("updateState", { key: 'deptDefaultList', payload: [] })
    if (state.rowRoleData.id) {
      let { data } = await getAllRoleDeptList(state.rowRoleData.id)
      commit("updateState", { key: 'deptDefaultList', payload: data })
      commit("updateState", { key: 'deptList', payload: [] })
      if (state.rowRoleData.id) {
        let { data } = await roleDeptList(state.rowRoleData.id)
        commit("updateState", { key: 'deptList', payload: data })

      }
    }
  },

  async getAuthMenuList({ commit }) {
    commit("updateState", { key: 'btnAuthList', payload: [] })
    if (state.rowRoleData.id && state.rowModuleMenu.id) {
      let params = {
        roleId: state.rowRoleData.id,
        menuId: state.rowModuleMenu.id
      }
      let { data } = await rolePermissionList(params)
      commit("updateState", { key: 'btnAuthList', payload: data })
    }
  }
};
export default {
  namespaced: true,
  state,
  mutations,
  actions
}