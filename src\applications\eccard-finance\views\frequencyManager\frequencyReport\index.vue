<template>
  <kade-route-card style="height: auto">
    <kade-table-wrap title="充值明细">
      <kade-tab-wrap :tabs="tabs" v-model="state.tab">
        <template #kwzxcz>
          <kade-card-echarge
            :costTypelist="state.costTypelist"
            :departCheckList="state.departCheckList"
            :systemUserList="state.systemUserList"
          />
        </template>
        <template #yddcz>
          <kade-mobile-echarge
            :costTypelist="state.costTypelist"
            :departCheckList="state.departCheckList"
            :systemUserList="state.systemUserList"
          />
        </template>
        <template #zzzdcz>
          <kade-selfhelp-echarge
            :costTypelist="state.costTypelist"
            :departCheckList="state.departCheckList"
            :systemUserList="state.systemUserList"
          />
        </template>
        <template #webcz>
          <kade-web-echarge
            :costTypelist="state.costTypelist"
            :departCheckList="state.departCheckList"
            :systemUserList="state.systemUserList"
          />
        </template> </kade-tab-wrap
    ></kade-table-wrap>
  </kade-route-card>
</template>
<script>
import { onMounted, reactive } from "vue";
import cardEcharge from "./components/cardEcharge";
import mobileEcharge from "./components/mobileEcharge";
import selfhelpEcharge from "./components/selfhelpEcharge";
import webEcharge from "./components/webEcharge";
import {
  getCostTypelist,
  getSystemUser,
} from "@/applications/eccard-finance/api";

const tabs = [
  {
    name: "kwzxcz",
    label: "卡务中心充值",
  },
  {
    name: "zzzdcz",
    label: "自助终端充值",
  },
  {
    name: "yddcz",
    label: "移动端充值",
  },
  {
    name: "webcz",
    label: "web端充值",
  },
];
export default {
  components: {
    "kade-card-echarge": cardEcharge,
    "kade-mobile-echarge": mobileEcharge,
    "kade-selfhelp-echarge": selfhelpEcharge,
    "kade-web-echarge": webEcharge,
  },
  setup() {
    const state = reactive({
      tab: "kwzxcz",
      departCheckList: [],
      costTypelist: [],
      systemUserList: [],
    });
    onMounted(() => {
      getCostTypelist().then((res) => {
        state.costTypelist = res.data;
      });

      getSystemUser().then((res) => {
        state.systemUserList = res.data;
      });
    });
    return {
      state,
      tabs,
    };
  },
};
</script>