<template>
  <el-dialog
    :model-value="dialogVisible"
    title="异常日志详情"
    width="60%"
    :before-close="handleClose"
  >
    <el-form inline label-width="100px" size="mini" style="margin-top:20px">
      <el-form-item :label="item.label" v-for="(item,index) in state.formList" :key="index">
          <el-input readonly :model-value="dataRow[item.filed]"></el-input>
      </el-form-item>
      
    </el-form>
    <el-form label-width="100px" size="mini"  style="padding-bottom:20px">
      <el-form-item label="请求地址：">
          <el-input readonly :model-value="dataRow['uri']"></el-input>
        </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script>
import { ElDialog, ElForm, ElFormItem, ElInput } from "element-plus";
import { reactive } from "@vue/reactivity";
export default {
  components: {
    ElDialog,
    ElForm,
    ElFormItem,
    ElInput,
  },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    dataRow:{
      types: Object,
      default:{}
    }
  },
  setup(prop, context) {
    const state = reactive({
      formList: [
        {
          label: "日志ID：",
          filed: "id",
        },
        {
          label: "所属应用：",
          filed: "app",
        },
        {
          label: "功能模块：",
          filed: "bizModule",
        },
        {
          label: "操作类型：",
          filed: "operateType",
        },
        {
          label: "请求方式：",
          filed: "requestMethod",
        },
        {
          label: "请求参数：",
          filed: "requestParam",
        },
        {
          label: "返回参数：",
          filed: "result",
        },
        {
          label: "操作员账号：",
          filed: "operateAccount",
        },

        {
          label: "操作员姓名：",
          filed: "operateName",
        },
        {
          label: "客户端IP：",
          filed: "ip",
        },
        {
          label: "操作时间：",
          filed: "operateTime",
          isDate: true,
        },
        {
          label: "客户端系统：",
          filed: "os",
        },

        {
          label: "浏览器名称：",
          filed: "browser",
        },
        {
          label: "响应时间：",
          filed: "spendTime",
        },
      ],
    });
    const handleClose = () => {
      context.emit("close", false);
    };
    return {
      state,
      handleClose,
    };
  },
};
</script>

<style lang="scss" scoped>
</style>