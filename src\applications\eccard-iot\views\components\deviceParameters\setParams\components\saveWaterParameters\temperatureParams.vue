<template>
  <div class="padding-form-box timeParam" v-loading="state.loading">
    <el-form inline size="small" label-width="200px" v-for="(item, index) in state.form.paramContent" :key="index">
      <el-form-item :label="item.label+'：'"> </el-form-item>
      <el-form-item label="温度(°C)：">
        <el-input-number :min="0" v-model="item.temperature"></el-input-number>
      </el-form-item>
      <el-form-item label="温控费率(元/计费单位)：">
        <el-input-number :min="0" v-model="item.temperatureControlRate"></el-input-number>
      </el-form-item>
    </el-form>
  </div>
  <div style="text-align: center">
    <el-button size="mini" @click="beforeClose()">取消</el-button>
    <el-button size="mini" type="primary" @click="saveClick()">保存</el-button>
  </div>
</template>

<script>
import {
  ElForm,
  ElFormItem,
  ElInputNumber,
  ElButton,
  ElMessage,
} from "element-plus";
import { onMounted, reactive } from "vue";
import { useStore } from "vuex";
const defaultParamsFnc = () => {
  return [
    {
      label: "一阶段",
      temperature: 1,
      temperatureControlRate: 1,
    },
    {
      label: "二阶段",
      temperature: 1,
      temperatureControlRate: 1,
    },
    {
      label: "三阶段",
      temperature: 1,
      temperatureControlRate: 1,
    },
    {
      label: "四阶段",
      temperature: 1,
      temperatureControlRate: 1,
    },
  ]
}

export default {
  components: {
    ElForm,
    ElFormItem,
    ElInputNumber,
    ElButton,
  },
  setup() {
    const store = useStore();
    const state = reactive({
      loading: false,
      form: {
        paramContent: defaultParamsFnc(),
        id: "",
        paramId: "",
        paramType: "TEMPERETURE",
      },
    });
    const saveClick = async () => {
      state.loading = true;
      let storeData = store.state.deviceParameters[store.state.app.activeTab]
      state.form.paramId = storeData.selectRow.id;
      let fn = state.form.id ? storeData.TabModule.detailsEditFnc : storeData.TabModule.detailsAddFnc

      let params = { ...state.form }
      console.log(params.paramContent);
      params.paramContent = JSON.stringify(params.paramContent)
      let { message, code } = await fn(params);
      if (code === 0) {
        setTimeout(() => {
          ElMessage.success(message);
          store.dispatch("deviceParameters/getParams", store.state.deviceParameters[store.state.app.activeTab].selectRow.id);
          state.loading = false;
        }, 1500);
      }
    };

    const beforeClose = () => {
      store.commit("deviceParameters/updateState", {
        key: store.state.app.activeTab,
        childKey: "isSetParams",
        payload: false,
      });
    };
    onMounted(() => {
      let val = store.state.deviceParameters[store.state.app.activeTab].detailsParams
      let arr = val.filter(item => item.paramType == 'TEMPERETURE')
      if (arr && arr.length) {
        state.form = { ...arr[0] }
        state.form.paramContent = JSON.parse(state.form.paramContent)
      } else {
        //设置默认选项
        state.form.id = "";
        state.form.paramContent = defaultParamsFnc();
      }
    });
    return {
      state,
      saveClick,
      beforeClose,
    };
  },
};
</script>

<style lang="scss" scoped>
.timeParam {
  text-align: center;
  border: 1px solid #dcdfe6;
  border-radius: 5px;
  margin-bottom: 10px;
  width: 100%;
  .time-item {
    width: 100%;
    display: flex;
    align-items: center;
  }
}
</style>
