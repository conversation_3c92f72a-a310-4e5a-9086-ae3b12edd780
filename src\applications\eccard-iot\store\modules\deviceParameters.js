/* 参数Data */
// import { getdeviceParamDetail } from "@/applications/eccard-iot/api";
import appStore from "./app"
const state = {
  machineParameters: {//智能卡消费终端
    isSetParams: false, selectRow: "", detailsParams: [], TabModule: {},
    isCardAuthEdit: {
      title: "",
      value: "",
      isShow: false
    },
    cardAuthList: []
  },
  terminalDeviceParameters: {//智能消费终端
    isSetParams: false, selectRow: "", detailsParams: [], TabModule: {},
    isCardAuthEdit: {
      title: "",
      value: "",
      isShow: false
    },
    cardAuthList: []
  },
  waterParameters: {//远传水表
    isSetParams: false, selectRow: "", detailsParams: "", TabModule: {},
  },
  faceParameters: {//人脸识别终端设备
    isSetParams: false, selectRow: "", detailsParams: "", TabModule: {}, dict: {}
  },
  saveWaterParameters: {//节水控制器
    isSetParams: false, selectRow: "", detailsParams: [], TabModule: {}, dict: {}
  },
  infiniteWaterParams: {//无线联网水控
    isSetParams: false, selectRow: "", detailsParams: [], TabModule: {}, dict: {}
  }
};
const mutations = {
  updateState(state, { key, childKey, payload }) {
    console.log(key, childKey, payload);
    state[key][childKey] = payload;
  },
  updateState1(state, { key, payload }) {
    state[key] = payload;
  },
};
const actions = {
  async getParams({ commit }, params) {
    let { data } = await state[appStore.state.activeTab].TabModule.detailsFnc(params)
    commit("updateState", { key: appStore.state.activeTab, childKey: 'detailsParams', payload: JSON.parse(JSON.stringify(data)) })
  }
};
export default {
  namespaced: true,
  state,
  mutations,
  actions
}
