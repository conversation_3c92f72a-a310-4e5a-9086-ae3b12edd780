<template>
  <div class="padding-box"  v-if="details">
    <div class="mask-box">
      <div class="header-box">
        <div class="header-item">口罩模式开关</div>
        <div class="header-item">口罩检测异常播报开关</div>
        <div class="header-item">口罩检测异常语音自定义</div>
      </div>
      <div class="main-box">
        <div class="main-item">{{dictListFnc().facemaskSwith.filter(item => item.value == details.facemaskSwith)[0]?.label}}</div>
        <div class="main-item">{{dictListFnc().nomalFacemaskSwith.filter(item => item.value == details.nomalFacemaskSwith)[0]?.label}}</div>
        <div class="main-item">{{details.voiceCustomization}}</div>
      </div>
    </div>
  </div>
  <el-empty v-else description="当前设备参数未设定"></el-empty>
</template>
<script>
import { computed } from "@vue/runtime-core";
import { useStore } from "vuex";
import { ElEmpty } from "element-plus"

export default {
  components: {
    ElEmpty
  },
  setup() {
    const store = useStore();
    const details = computed(() => {
      let data = {}
      if (store.state['identityDevice/faceDistingDevice'].detailsParams.facemask) {
        data = JSON.parse(store.state['identityDevice/faceDistingDevice'].detailsParams.facemask.paramContent);
      } else {
        data = ''
      }
      console.log(data);
      return data;
    });
    const dictListFnc = () => {
      return store.state['identityDevice/faceDistingDevice'].faceDict
    }
    return {
      dictListFnc,
      details,
    };
  },
};
</script>
<style lang="scss" scoped>
.mask-box {
  border: 1px solid #eeeeee;
  line-height: 50px;
  min-width: 750px;
  .header-box {
    display: flex;
    align-items: center;
    justify-content: space-around;
    border-bottom: 1px solid #eeeeee;
    .header-item {
      text-align: center;
      width: 100%;
      min-width: 250px;
      background: #f6f6f6;
      border-right: 1px solid #eeeeee;
      &:last-child {
        border-right: 0;
      }
    }
  }
  .main-box {
    display: flex;
    align-items: center;
    justify-content: space-around;
    .main-item {
      text-align: center;
      width: 100%;
      min-width: 250px;
      border-right: 1px solid #eeeeee;
      &:last-child {
        border-right: 0;
      }
    }
  }
}
</style>