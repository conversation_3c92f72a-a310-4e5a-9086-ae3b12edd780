<template>
    <div class="trade-box">
        <div class="yoy-box">
            <div class="title">周同比</div>
            <div class="icon"></div>
            <div>12%</div>
        </div>
        <div class="day-box">
            <div class="title">日环比</div>
            <div class="icon"></div>
            <div>11%</div>
        </div>
    </div>
    <div class="bottom-box">
        <div class="left-box">日均交易额</div>
        <div>￥12,423</div>
    </div>
</template>


<style lang="scss" scoped>
.trade-box {
    display: flex;
    width: 100%;
    color: #0000006d;
    align-items: center;
    border-bottom: 1px solid #eeeeee;
    padding: 0 0 15px 5px;
    min-width: 328px;
    line-height: 25px;
    .yoy-box {
        display: flex;
        align-items: center;
        margin-right: 40px;
        font-size: 10px;
        .icon {
            margin: 5px;
            width: 0;
            height: 0;
            border-right: 5px solid rgb(138, 197, 147);
            border-bottom: 5px solid #fff;
            transform: rotate(-45deg);
        }
    }
    .day-box {
        font-size: 10px;
        display: flex;
        align-items: center;
        .icon {
            margin: 5px;
            width: 0;
            height: 0;
            border-left: 5px solid #f00;
            border-top: 5px solid #fff;
            transform: rotate(-45deg);
        }
    }
}
.bottom-box {
    display: flex;
    font-size: 10px;
    color: #080808a6;
    padding: 5px 0 0 5px;
    .left-box {
        padding-right: 10px;
    }
}
</style>