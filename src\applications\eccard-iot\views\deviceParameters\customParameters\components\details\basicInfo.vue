<template>
  <div class="padding-box">
    <el-form label-width="120px" inline size="mini">
      <el-form-item label="参数名称：">
        <el-input readonly :model-value="data.paramsName"></el-input>
      </el-form-item>
      <el-form-item label="参数编号：">
        <el-input readonly :model-value="data.paramsCode"></el-input>
      </el-form-item>
      <el-form-item label="是否启用：">
        <el-switch :model-value="data.status" />
      </el-form-item>
      <el-form-item label="添加时间：">
        <el-input readonly :model-value="timeStr(data.dateTime)"></el-input>
      </el-form-item>
      <el-form-item label="设备类型：">
        <el-input readonly :model-value="data.deviceType"></el-input>
      </el-form-item>
      <el-form-item label="参数模板名称：">
        <el-input readonly :model-value="data.paramsTemplate"></el-input>
      </el-form-item>
      <el-form-item label="备注：">
        <el-input style="width:900px" type="textarea" readonly :model-value="data.remarks"></el-input>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import { ElForm, ElFormItem, ElInput, ElSwitch } from "element-plus";
import { reactive } from '@vue/reactivity';
import { timeStr } from "@/utils/date"
export default {
  components: {
    ElForm,
    ElFormItem,
    ElInput,
    ElSwitch
  },
  props: {
    data: {
      type: Object,
      default: null
    },
  },
  setup() {
    const state = reactive({
      form: {}
    })
    return {
      timeStr,
      state
    }
  },
};
</script>
<style lang="scss" scoped>
</style>