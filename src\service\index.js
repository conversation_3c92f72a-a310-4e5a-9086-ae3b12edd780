import axios from 'axios';
import { ElMessage } from 'element-plus';
import {
    getToken,
    getRefreshToken,
    setRefreshToken,
    removeRefreshToken,
    setToken,
    removeToken,
    redirectLogin,
} from '@/utils';

// import Url from '../utils/baseUrl';
// window.BASE_API_PATH=Url.baseURL
const service = axios.create({
    baseURL: `${CONFIG.BASE_API_PATH}`,
    // baseURL,
    withCredentials: false,
    timeout: CONFIG['FETCH_TIMOUT'],
    headers: {
        'content-type': 'application/json'
    },
});

service.interceptors.request.use(
    config => {
        const token = getToken();
        if (token) {
            config.headers['Authorization'] = `bearer ${token}`;
        }
        return config;
    },
    error => {
        return Promise.reject(error)
    }
);
let time = 0;
const refreshToken = async () => {
    const refresh_token = getRefreshToken();
    if (refresh_token && time === 0) {
        time += 1;
        const { data } = await getAuthToken({
            grant_type: 'refresh_token',
            refresh_token: getRefreshToken(),
        });
        return data || {};
    } else {
        redirectLogin();
    }
}

const reLogin = () => {
    removeToken();
    removeRefreshToken();
    sessionStorage.removeItem(`${CONFIG['CACHE_PREFIX']}auth_menus`);
    redirectLogin();
}

service.interceptors.response.use(
    response => {
        const res = response.data;
        const { code, Status, message, Message } = res;
        if (response.request.responseType == "blob") {
            console.log(res);
            return res
        }
        if (code === 0 || Status === 1) {
            console.log(res);
            return res
        } else if (code === 230) {
            /* sessionStorage.removeItem('kade-common-rediect-times');
            redirectLogin(); */
            return refreshToken().then(({ token, refreshToken }) => {
                setToken(token);
                setRefreshToken(refreshToken);
                response.config.baseURL = '';
                return service(response.config);
            }).catch(() => {
                reLogin();
            });
        } else if (code == 5010) {
            reLogin();
        } else {
            ElMessage.closeAll()
            return ElMessage({
                message: message || Message || 'Server Error',
                type: 'error',
                duration: 5 * 1000,
            });
        }
        /* switch (code) {
          case 0:
            return res;
          case 230:
            return refreshToken().then(({ token, refreshToken }) => {
              setToken(token);
              setRefreshToken(refreshToken);
              response.config.baseURL = '';
              return service(response.config);
            }).catch(() => {
              reLogin();
            });
          case 5010:
            reLogin();
            break;
          default:
            ElMessage.closeAll();
            ElMessage({
              message: message || 'Server Error',
              type: 'error',
              duration: 5 * 1000,
            });
            throw new Error(message || 'Server Error');
        } */
    },
    error => {
        console.log(error.response.data);
        if (error.response.data.code === 230 || (error.response.data.error === "Unauthorized" && error.response.data.status === 401)) {
            console.log(1);
            return refreshToken().then(({ token, refreshToken }) => {
                setToken(token);
                setRefreshToken(refreshToken);
                error.response.config.baseURL = '';
                return service(error.response.config);
            }).catch(() => {
                reLogin();
            });
        }
        /*     if (error.response.data.status === 401) {
              return refreshToken().then(({ token, refreshToken }) => {
                setToken(token);
                setRefreshToken(refreshToken);
                error.response.config.baseURL = '';
                return service(error.response.config);
              }).catch(() => {
                reLogin();
              });
            } */
        ElMessage.closeAll();
        ElMessage({
            message: error.message,
            type: 'error',
            duration: 5 * 1000
        });
        return Promise.reject(error);
    }
);
/**
 * 获取权限菜单
 */
export function getMenuPurviewByUserId(params) {
    return service.get(`/eccard-sys/sysPermission/${params}/getUserMenuPermissionByApp`);
}

/**
 * 获取/刷新token
 */
export function getAuthToken(params) {

    const formData = new FormData();
    for (let key in params) {
        formData.append(key, params[key]);
    }
    return service({
        url: '/eccard-auth/oauth/token',
        method: 'post',
        data: formData,
        headers: {
            Authorization: "Basic ZWNjYXJkLWFkbWluOjEyMzQ1Ng==",
        }
    });
}

/**
 * 获取用户信息
 */
export function getUserInfo() {
    return service.get('/eccard-sys/sysUser/me');
}

/**
 * 退出登录
 */
export function loginOuted() {
    return service.delete('/eccard-auth/oauth/logout');
}

export default service;