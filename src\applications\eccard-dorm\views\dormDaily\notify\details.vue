<template>
  <el-dialog :model-value="isShow" width="1100px" :before-close="beforeClose" :close-on-click-modal="false">
    <template #title>
      <div class="dialog-title">
        <div class="detail-title">通知公告详情</div>
      </div>
    </template>
    <div style="padding-top: 20px">
      <el-form size="mini" label-width="120px">
        <el-row>
          <el-col :span="6">
            <el-form-item label=" 消息类型:">
              <el-input
                readonly
                :model-value="filterDictionary(details.msgType, msgType)"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="接收区域:">
              <el-input
                readonly
                :model-value="details.areaName"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="接收楼栋:">
              <el-input
                readonly
                :model-value="details.buildName"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="接收单元:">
              <el-input
                readonly
                :model-value="details.unitName"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="消息标题:">
          <el-input
            readonly
            :model-value="details.msgTitle"
            :maxlength="30"
          ></el-input>
        </el-form-item>
        <el-form-item label="消息内容:">
          <div class="msg-text" v-html="details.msgContent"></div>
        </el-form-item>
      </el-form>
    </div>
    <div style="height:1px"></div>
  </el-dialog>
</template>
<script>
import { computed, reactive } from "vue";
import { useStore } from "vuex";
import { filterDictionary } from "@/utils/index.js";
import { useDict } from "@/hooks/useDict";
import {
  ElDialog,
  ElRow,
  ElCol,
  ElForm,
  ElFormItem,
  ElInput,
} from "element-plus";

export default {
  components: {
    ElDialog,
    ElRow,
    ElCol,
    ElForm,
    ElFormItem,
    ElInput,
  },
  setup() {
    const store = useStore();
    const msgType = useDict("SYS_MESSAGE_TYPE"); //消息类型

    const state = reactive({
      isTailor: false,
      imgFile: "",
      form: {
        msgReceiver: [],
      },
    });

    const isShow = computed(() => {
      return store.state.msg.isDetails;
    });
    const details = computed(() => {
      return store.state.msg.selectRow;
    });

    const beforeClose = () => {
      store.commit("msg/updateState", {
        key: "isDetails",
        payload: false,
      });
    };

    return {
      filterDictionary,
      msgType,
      state,
      isShow,
      details,
      beforeClose,
    };
  },
};
</script>
<style lang="scss" scoped>
.msg-text {
  box-sizing: border-box;
  width: 100%;
  height: 300px;
  padding: 20px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow-y: scroll;
}
.dialog-title {
  display: flex;
  align-items: center;
  .detail-status {
    border: 1px solid #000;
    border-radius: 4px;
    margin-left: 20px;
    padding: 3px;
    font-size: 12px;

  }
}
::-webkit-scrollbar {
display: none !important;
}
</style>