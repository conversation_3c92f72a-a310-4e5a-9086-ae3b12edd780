<template>
    <div class="home" v-loading="state.loading">
        <div class="home-card">
            <el-row :gutter="20">
                <template v-for="item in state.dataList" :key="item.id">
                    <el-col class="card-col" v-bind="layout">
                        <el-card class="home-card-item" :body-style="{ padding: 0 }">
                            <template #header>
                                <div class="card-header">
                                    <span>{{ item.moduName }}</span>
                                </div>
                            </template>
                            <div class="card-content">
                                <component :is="getTargetComponent(item.moduCode)"></component>
                            </div>             
                        </el-card>                    
                    </el-col>                 
                </template>
            </el-row>                                   
        </div>
    </div>
</template>
<script>
import { ElCard, ElRow, ElCol } from 'element-plus';
import { homeCardLayout } from '@/service/dictionary';
import { reactive, onMounted } from 'vue';
import MessageList from './components/messageList';
import AppList from './components/applist';
import Calendar from './components/calendar';
import HelpList from './components/helpList';
import { getUserModuleList } from '@/applications/unified_portal/api';
import { useStore } from 'vuex';
export default {
    components: {
        'el-card': ElCard,
        'el-row': ElRow,
        'el-col': ElCol,
    },
    setup() {
        const state = reactive({
            loading: false,
            dataList: [],
        });
        const store = useStore();
        const loadData = async () => {
            try {
                state.loading = true;
                const { data } = await getUserModuleList({
                    userId: store.state.user.userInfo.id,
                });
                state.dataList = data
                .filter(it => !!it.state)
                .sort((a, b) => {
                    const an = parseInt(a.moduShort, 10);
                    const bn = parseInt(b.moduShort, 10);
                    return an > bn ? 1 : -1;
                });
            } catch(e) {
                throw new Error(e.message);
            } finally {
                state.loading = false;
            }
        }
        const getTargetComponent = (code) => {
            switch(code) {
                case 'MyMessage':
                    return MessageList;
                case 'AppCenter':
                    return AppList;
                case 'MyCalendar':
                    return Calendar;
                case 'HelpCenter':
                    return HelpList;
            }
        }
        onMounted(() => {
            loadData();
        });
        return {
            state,
            getTargetComponent,
            layout: homeCardLayout,
        }
    }
}
</script>
<style lang="scss" scoped>
.home{
    width: 100%;
    height: 100%;
    .home-card{
        width: 100%;
        height: 100%;
        box-sizing: border-box;
        overflow-x: hidden;
        overflow-y: auto;
    }
    .card-content{
        height: 358px;
        height: calc(50vh - 110px);
        overflow-y: auto;
        position: relative;
    }
}
@media screen and (max-width: 1200px) {
  .home .card-col{
    padding-bottom: 20px;
  }  
  .home .card-col:last-child{
      padding-bottom: 0;
  }
}
@media screen and (min-width: 1200px) {
  .home .card-col:nth-child(1),.home .card-col:nth-child(2){
    padding-bottom: 18px;
  }  
}
</style>