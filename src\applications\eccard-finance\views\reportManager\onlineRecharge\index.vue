<template>
  <!-- 线上充值对账明细 -->
  <div class="cashRecharge" v-loading="state.loading">
    <kade-route-card style="height: auto">
      <kade-table-filter @search="getList()" @reset="reset()">
        <el-form inline size="mini" label-width="100px">
          <el-form-item label="账单类型:">
            <el-select clearable v-model="state.form.billType" placeholder="请选择">
              <el-option v-for="(item, index) in state.tradeModeList" :key="index" :label="item.name" :value="item.code">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="对账结果:">
            <el-select clearable v-model="state.form.billResult" placeholder="请选择">
              <el-option v-for="(item, index) in rechargeReconciliationList" :key="index" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="账单日期:">
            <el-date-picker v-model="state.requestDate" type="daterange" unlink-panels :default-time="state.defaultTime" range-separator="~" start-placeholder="请选择日期" end-placeholder="请选择日期" @change="changeDate">
            </el-date-picker>
          </el-form-item>
        </el-form>
      </kade-table-filter>
      <kade-table-wrap title="线上支付对账明细报表">
        <template #extra>
          <el-button @click="exportClick()" icon="el-icon-daoru" size="mini" class="btn-blue">导出查询明细</el-button>
        </template>
        <el-table style="width: 100%" :data="state.detailList" ref="multipleTable" v-loading="state.loading" height="55vh" highlight-current-row border stripe>
          <el-table-column v-for="(item,index) in column" :key="index" :width="item.width" :label="item.label" :prop="item.prop" align="center" show-overflow-tooltip>
            <template v-if="item.render" #default="scope">
              {{item.render(scope.row[item.prop],rechargeReconciliationList)}}
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center">
            <template #default="scope">
              <el-button type="text" size="mini" @click="detailsClick(scope.row)">查看订单明细</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination">
          <el-pagination background :current-page="state.form.currentPage" :page-size="state.form.pageSize" layout="total, sizes, prev, pager, next, jumper" :page-sizes="[6, 10, 20, 50, 100]" :total="state.total" @current-change="handlePageChange" @size-change="handleSizeChange">
          </el-pagination>
        </div>
      </kade-table-wrap>
    </kade-route-card>

    <kade-online-charge-details :rowData="state.rowData" :dialogVisible="state.dialogVisible" @close="state.dialogVisible = false" />
  </div>
</template>
<script>
import {
  ElButton,
  ElForm,
  ElFormItem,
  ElSelect,
  ElOption,
  ElTable,
  ElTableColumn,
  ElPagination,
  ElDatePicker,
} from "element-plus";
import { timeStr,dateStr } from "@/utils/date.js";
import { filterDictionary } from "@/utils";
import { reactive } from "@vue/reactivity";
import {
  getPaymentReconcileList,
  paymentReconcileExport,
  tradeMode,
} from "@/applications/eccard-finance/api";
import { onMounted, watch } from "@vue/runtime-core";
import { useDict } from "@/hooks/useDict"
import details from "./components/details.vue";

const column = [
  { label: "账单日期", prop: "billDate", width: "170", render: (val) => val && dateStr(val) },
  { label: "账单类型", prop: "billTypeName", width: "" },
  { label: "账单总金额", prop: "billTotalAmount", width: "" },
  { label: "系统总金额", prop: "sysTotalAmount", width: "" },
  { label: "账单充值金额", prop: "billPaymentAmount", width: "" },
  { label: "系统充值金额", prop: "sysPaymentAmount", width: "" },
  { label: "账单退款金额", prop: "billRefundAmount", width: "" },
  { label: "系统退款金额", prop: "sysRefundAmount", width: "" },
  { label: "对账时间", prop: "createTime", width: "170", render: (val) => val && timeStr(val) },
  { label: "对账结果", prop: "billResult", render: (val, list) => filterDictionary(val, list) },
]
export default {
  components: {
    "el-button": ElButton,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    "el-select": ElSelect,
    "el-option": ElOption,
    "el-table": ElTable,
    "el-table-column": ElTableColumn,
    "el-pagination": ElPagination,
    ElDatePicker,
    "kade-online-charge-details": details,
  },
  setup() {
    const rechargeReconciliationList = useDict("RECHARGE_RECONCILIATION_STATUS").filter((item) => item.label != "未对账")
    const state = reactive({
      loading: false,
      dialogVisible: false,
      form: {
        pageNum: 1,
        pageSize: 6,
      },
      detailList: [],
      total: 0,
      rowData: {},
      requestDate: "",
      tradeModeList: [],
      defaultTime: [
        new Date(new Date(new Date().toLocaleDateString()).getTime()),
        new Date(
          new Date(new Date().toLocaleDateString()).getTime() +
          24 * 60 * 60 * 1000 -
          1
        ),
      ],

    });

    watch(
      () => state.requestDate,
      (val) => {
        if (!val) {
          delete state.form.startDate;
          delete state.form.endDate;
        }
      }
    );
    //获取交易方式
    const getTradeModeList = () => {
      tradeMode({ costType: 101 }).then((res) => {
        state.tradeModeList = res.data.filter(
          (item) =>
            item.code == 2 ||
            item.code == 3 ||
            item.code == 4 ||
            item.code == 5 ||
            item.code == 6
        );
      });
    };
    const getList = async () => {
      state.loading = true;
      for (let key in state.form) {
        if (!state.form[key]) {
          delete state.form[key];
        }
      }
      try {
        let { code, data } = await getPaymentReconcileList(state.form);
        if (code === 0) {
          let {
            pageInfo: { total, list },
          } = data;
          state.detailList = list;
          state.total = total;

        }
        state.loading = false;
      }
      catch {
        state.loading = false;
      }

    };
    const changeDate = (val) => {
      if (val && val.length) {
        state.form.startDate = timeStr(val[0]);
        state.form.endDate = timeStr(val[1]);
      } else {
        delete state.form.startDate;
        delete state.form.endDate;
      }
    };
    const detailsClick = (row) => {
      console.log(row);
      state.rowData = row;
      state.dialogVisible = true;
    };
    const exportClick = async () => {
      state.loading = true
      try {
        let res = await paymentReconcileExport(state.form);
        let url = window.URL.createObjectURL(
          new Blob([res], { type: "application/vnd.ms-excel" })
        );
        let link = document.createElement("a");
        link.style.display = "none";
        link.href = url;
        link.setAttribute("download", "线上支付对账明细报表.xlsx");
        document.body.appendChild(link);
        link.click();
        state.loading = false;
      }
      catch {
        state.loading = false;
      }

    };

    const handlePageChange = (val) => {
      state.form.pageNum = val;
      getList();
    };
    const handleSizeChange = (val) => {
      state.form.pageNum = 1;
      state.form.pageSize = val;
      getList();
    };

    const reset = () => {
      state.form = {
        pageNum: 1,
        pageSize: 6,
      };
      state.requestDate = "";
    };
    onMounted(() => {
      getList();
      getTradeModeList();
    });
    return {
      rechargeReconciliationList,
      column,
      state,
      timeStr,
      filterDictionary,
      exportClick,
      getList,
      changeDate,
      detailsClick,
      handlePageChange,
      handleSizeChange,
      reset,
    };
  },
};
</script>
<style lang="scss" scoped>
:deep(.el-divider--horizontal) {
  margin: 0 0 10px 0;
}
.cashRecharge {
  min-height: 680px;
  :deep(.search-box) {
    .el-input__inner {
      width: 193px;
    }
    .el-date-editor {
      width: 400px;
    }
  }
}
:deep(.el-dialog__header) {
  border-bottom: 1px solid #efefef;
}
:deep(.el-dialog__headerbtn) {
  font-size: 20px;
  top: 10px;
  right: 10px;
}
:deep(.el-dialog__header) {
  padding: 10px 20px;
}
:deep(.el-overlay) {
  position: absolute;
  background-color: rgba(0, 0, 0, 0.2);
}
:deep(.el-dialog__footer) {
  text-align: center;
}
:deep(.el-dialog__title) {
  font-size: 14px;
}
</style>