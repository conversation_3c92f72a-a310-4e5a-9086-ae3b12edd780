import request from "@/service";
// 获取菜单权限列表
export function opsWaterAuthList(params) {
  return request.post("/eccard-ops/opsWaterAuth/list", params);
}

// 获取菜单权限新增
export function opsWaterAuthAdd(params) {
  return request.post("/eccard-ops/opsWaterAuth", params);
}
// 获取菜单权限修改
export function opsWaterAuthEdit(params) {
  return request.put("/eccard-ops/opsWaterAuth", params);
}
// 获取菜单权限删除
export function opsWaterAuthDelete(id) {
  const tenantId = JSON.parse(sessionStorage.getItem("kade_cache_userinfo"))
    .tenantId;
  return request.delete(`/eccard-ops/opsWaterAuth/${tenantId}/${id}`);
}
