<template>
  <kade-route-card style="height: 100%">
    <div class="deviceRealTimeMonitor">
      <kade-table-wrap title="区域" style="width: 300px; height: 100%; margin-right: 20px">
        <el-divider></el-divider>
        <div class="search-box">
          <el-input v-model="state.areaKeyword" placeholder="请输入关键字搜索" size="small" clearable>
            <template #append>
              <el-button icon="el-icon-sousuo" @click="areaSearch()">查询</el-button>
            </template>
          </el-input>
          <el-tree class="area-tree" :data="state.areaCheckTreeList" :props="defaultProps" default-expand-all
            @node-click="handleNodeClick">
            <template #default="{ node }">
              <div class="custom-tree-node hasbtn">
                <span class="left">
                  <i class="el-icon-location-information"></i>
                  <span style="margin-left: 5px; color: #333">{{
                      node.label
                  }}</span>
                </span>
              </div>
            </template>
          </el-tree>
        </div>
      </kade-table-wrap>
      <kade-table-wrap title="设备监控列表" style="flex: 1; height: 100%;min-width: 800px">
        <template #extra>
          <el-button @click="handleEdit('UPLOAD_AUTH')" class="btn-green" icon="el-icon-upload" size="mini">上传权限
          </el-button>
          <el-button @click="handleEdit('CHECK_TIME')" class="btn-purple" icon="el-icon-timer" size="mini">校准时间
          </el-button>
          <el-button @click="handleEdit('OPEN_DOOR')" class="btn-yellow" icon="el-icon-gerenmenhu" size="mini">远程开门
          </el-button>
          <el-button @click="handleEdit('DELETE_AUTH')" type="danger" icon="el-icon-delete-solid" size="mini">删除权限
          </el-button>
        </template>
        <el-divider></el-divider>
        <div class="padding-box">
          <el-form size="small" inline label-width="100px">
            <el-form-item label="设备类型：">
              <el-select v-model="state.form.deviceType" clearable @change="deviceTypeChange">
                <el-option v-for="(item, index) in state.deviceTypeList" :key="index" :label="item.cfgValue"
                  :value="item.cfgKey"></el-option>
              </el-select>
            </el-form-item>
          </el-form>

          <div class="census-box">
            <div :class="state.activeIndex === index && 'census-item-active'" class="census-item"
              @click="handleCensus(item, index)" v-for="(item, index) in state.censusList" :key="index">
              <div>{{ item.label }}</div>
              <div :style="{ color: state.activeIndex === index ? '#fff' : item.color, fontSize: '24px' }">{{
                  state.totalData[item.filed]
              }}
              </div>
            </div>
          </div>

          <div class="device-box" v-loading="state.loading">
            <div class="device-item" :class="item.checked && 'device-item-active'" :style="{
              'background-color': colorFnc(item.deviceOnline).background,
            }" v-for="(item, index) in state.dataList" :key="index" @click="handleDevice(item)">
              <div class="status-box">
                <div class="icon">
                  <img :src="iconFnc(item.deviceType)" alt="">
                </div>
                <el-checkbox v-model="item.checked" size="large" @change="checkedChange(item)" />
              </div>
              <div class="device-msg">
                <div class="device-num">{{ item.deviceName }}</div>
                <div class="device-status" :style="{ color: colorFnc(item.deviceOnline).color }">
                  <span class="status-icon" :style="{
                    'background-color': colorFnc(item.deviceOnline).color,
                  }"></span>{{ item.deviceOnline == 'TRUE' ? '在线' : '离线' }}
                </div>
              </div>
            </div>
          </div>
          <div class="pagination-box">
            <el-checkbox v-model="state.checkedAll" label="全选" size="large" @change="checkedAllChange" />
            <el-pagination :currentPage="state.form.currentPage" :pageSize="state.form.pageSize"
              :page-sizes="[5, 10, 20, 30, 40]" background layout="total, sizes, prev, pager, next, jumper"
              :total="state.total" @size-change="handleSizeChange" @current-change="handleCurrentChange">
            </el-pagination>
          </div>
        </div>

      </kade-table-wrap>
    </div>
  </kade-route-card>
</template>
<script>
import { onMounted, reactive } from "vue";
import {
  ElDivider,
  ElInput,
  ElTree,
  ElForm,
  ElFormItem,
  ElSelect,
  ElOption,
  ElCheckbox,
  ElButton,
  ElPagination,
  ElMessage
} from "element-plus";
import { getUserAreaPurview } from "@/applications/eccard-basic-data/api";
import { acsDeviceConsoleList, getOnlineCount, deleteAuth, deviceCheckTime, openDoor, uploadAuth } from "@/applications/eccard-uac/api";
import { iotCfg } from "@/applications/eccard-iot/api";
import { makeTree, filterDictionary } from "@/utils/index.js";
import door from "@/assets/uac_img/console_door.svg";
import lock from "@/assets/uac_img/console_lock.svg";
import face from "@/assets/uac_img/console_face.svg";
import accessAIO from "@/assets/uac_img/console_accessAIO.svg";

const defaultProps = {
  children: "children",
  label: "areaName",
};

const colorFnc = (val) => {
  if (val === "FALSE") {
    return {
      color: "#999999",
      background: "#eeeeee",
    };
  } else if (val === "TRUE") {
    return {
      color: "#02D200",
      background: "#02d2001a",
    };
  }
};

const iconFnc = (val) => {
  if (val === "ACCESS_CONTROLLER") {
    return door
  } else if (val === "DOOR_LOCK") {
    return lock
  } else if (val === "FACE") {
    return face
  } else if (val === "FINGER_PRINT") {
    return accessAIO
  } else {
    return door
  }
}

export default {
  components: {
    ElDivider,
    ElInput,
    ElTree,
    ElForm,
    ElFormItem,
    ElSelect,
    ElOption,
    ElCheckbox,
    ElButton,
    ElPagination
  },
  setup() {
    const state = reactive({
      activeIndex: 0,
      areaKeyword: "",
      areaCheckTreeList_copy: '',
      loading: false,
      checkedAll: false,
      deviceTypeList: [],
      rowArea: {},
      form: {
        currentPage: 1,
        pageSize: 10
      },
      censusList: [
        { color: "#409EFF", label: "全部设备", filed: "totalCount", type: "" },
        { color: "#67C23A", label: "在线设备", filed: "onLineCount", type: "TRUE" },
        { color: "#666", label: "离线设备", filed: "offLineCount", type: "FALSE" },
      ],
      totalData: {},
      dataList: [
        /* { checked: false, deviceOnline: 'FALSE' },
        { checked: false, deviceOnline: 'TRUE' }, */
      ],
      total: 0
    });
    const getDeviceTypeList = async () => {
      let { data } = await iotCfg({ cfgType: 'ACCESS_DEVICE_TYPE' });
      state.deviceTypeList = data
    }
    const getList = async () => {
      let { data: { list, total } } = await acsDeviceConsoleList(state.form)
      state.dataList = list.map(item => {
        return {
          ...item,
          checked: false
        }
      })
      state.total = total
    }
    const getOnlineCountData = async (deviceOnline) => {
      let params = { deviceOnline }
      if (state.rowArea.areaPath) {
        params.areaPath = state.rowArea.areaPath
      }
      let { data } = await getOnlineCount(params)
      console.log(data);
      state.totalData = data
    }
    const deviceTypeChange = (val) => {
      if (!val) {
        delete state.form.deviceType
      }
      getList()
      getOnlineCountData()
    }

    const handleNodeClick = (val) => {
      state.rowArea = val
      state.form.areaPath = state.rowArea.areaPath
      state.form.currentPage = 1
      state.form.pageSize = 10
      getList()
      getOnlineCountData()
    }
    const handleEdit = async (type) => {
      let params = {
        devIds: state.dataList.filter(item => item.checked).map(item => item.deviceId),
        opType: type,
      }
      if (!params.devIds.length) {
        return ElMessage.error("请先选择设备！")
      }
      let fn = ""
      if (type === 'UPLOAD_AUTH') {
        fn = uploadAuth
      } else if (type === "CHECK_TIME") {
        fn = deviceCheckTime
      } else if (type === "OPEN_DOOR") {
        fn = openDoor
      } else if (type === "DELETE_AUTH") {
        fn = deleteAuth
      }
      state.loading = true
      try {
        let { message, code } = await fn(params)
        if (code === 0) {
          ElMessage.success(message)
          getList()
        }
        state.loading = false
      }
      catch {
        state.loading = false
      }
    }
    const queryAreaCheckList = () => {
      getUserAreaPurview().then((res) => {
        let arr = makeTree(res.data, "id", "areaParentId", "children");
        state.areaCheckTreeList = [...arr];
        state.areaCheckTreeList_copy = [...arr];
      });
    };
    const mapTree = (value, arr) => {
      let newarr = [];
      arr.forEach((element) => {
        if (element.areaName.indexOf(value) > -1) {
          // 判断条件
          newarr.push(element);
        } else {
          if (element.children && element.children.length > 0) {
            let redata = mapTree(value, element.children);
            if (redata && redata.length > 0) {
              let obj = {
                ...element,
                children: redata,
              };
              newarr.push(obj);
            }
          }
        }
      });
      return newarr;
    };

    const areaSearch = () => {
      state.areaCheckTreeList = mapTree(state.areaKeyword, state.areaCheckTreeList_copy)
    }
    const handleCensus = (item, index) => {
      if (index === state.activeIndex) return
      state.activeIndex = index
      if (item.type) {
        state.form.deviceOnline = item.type

      } else {
        delete state.form.deviceOnline
      }
      getList()
    }
    const handleDevice = (item) => {
      item.checked = !item.checked
    }
    const checkedAllChange = (val) => {
      if (val) {
        state.dataList = state.dataList.map(item => {
          return {
            ...item,
            checked: true
          }
        })
      } else {
        state.dataList = state.dataList.map(item => {
          return {
            ...item,
            checked: false
          }
        })
      }
    }
    const checkedChange = (item) => {
      item.checked = !item.checked
    }
    const handleSizeChange = (val) => {
      state.form.currentPage = 1
      state.form.pageSize = val
      getList()
    };
    const handleCurrentChange = (val) => {
      state.form.currentPage = val
      getList()

    };
    onMounted(() => {
      getList()
      getOnlineCountData()
      queryAreaCheckList();
      getDeviceTypeList()
    });
    return {
      filterDictionary,
      defaultProps,
      colorFnc,
      iconFnc,
      state,
      deviceTypeChange,
      handleNodeClick,
      handleEdit,
      areaSearch,
      handleCensus,
      handleDevice,
      checkedAllChange,
      checkedChange,
      handleSizeChange,
      handleCurrentChange
    };
  },
};
</script>
<style lang="scss" scoped>
.deviceRealTimeMonitor {
  height: 100%;
  display: flex;
  justify-content: space-between;

  .search-box {
    padding: 10px;
    height: 70vh;
  }

  .area-tree {
    margin-top: 20px;
    height: calc(100%-112px);
    overflow-y: auto;
  }

  .census-box {
    display: flex;
    align-items: center;
    margin-bottom: 20px;

    .census-item {
      box-sizing: border-box;
      padding: 10px;
      margin-right: 20px;
      width: 200px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      border: 1px solid #f2f2f2;
      border-radius: 5px;
    }

    .census-item-active {
      border: none !important;
      background: #409EFF !important;
      color: #fff !important;

    }
  }

  .device-box {
    width: 100%;
    height: 55vh;
    overflow-y: auto;

    .device-item {
      display: inline-block;
      box-sizing: border-box;
      min-width: 150px;
      width: 15%;
      // height: 100px;
      padding: 10px 10px;
      background: rgba(2, 210, 0, 0.101960784313725);
      border-radius: 10px;
      margin: 10px;
      border: 1px solid #fff;

      .status-box {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;

        .icon {
          box-sizing: border-box;
          flex-wrap: wrap;
          width: 40px;
          height: 40px;
          padding: 10px;
          background: #3399ff;
          border-radius: 16px;

          img {
            width: 100%;
            height: 100%
          }
        }
      }

      .device-msg {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .device-num {}

        .device-status {
          .status-icon {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 3px;
          }
        }
      }
    }

    .device-item-active {
      border: 1px solid #3399ff;
    }
  }

  .pagination-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-top: 15px;
  }
}

:deep(.el-divider--horizontal) {
  margin: 0;
}
</style>