<template>
  <el-cascader
    :options="state.AreaCheckList"
    :model-value="value"
    :props="{
      checkStrictly: true,
      value: 'areaId',
      label: 'areaName',
    }"
    :clearable="true"
    @change="cascaderChange"
  />
</template>
<script>
import { onMounted, reactive } from "vue";
import { getTgetAreaCheckListype } from "@/applications/eccard-iot/api";
import { makeTree } from "@/utils/index.js";
import { ElCascader } from "element-plus";
export default {
  components: {
    ElCascader,
  },
  props:{
    value:{
      types:String,
      default:"",
    }
  },
  setup(props, context) {
    const state = reactive({
      AreaCheckList: [],
      value:""
    });

    //获取所属区域
    const queryTgetAreaCheckList = async () => {
      let { data } = await getTgetAreaCheckListype();
      let arr = makeTree(data, "areaId", "areaParentId", "children");
        state.AreaCheckList = arr;
    };
    const cascaderChange = (val) => {
      if (val&&val.length) {
        context.emit("valChange", val.length?val[val.length - 1]:'');
      } else {
        context.emit("valChange", "");
      }
    };
    onMounted(() => {
      queryTgetAreaCheckList();
    });
    return {
      state,
      cascaderChange,
    };
  },
};
</script>