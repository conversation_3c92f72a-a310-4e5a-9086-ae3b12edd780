import { getDictionary, addOrEditApplyType, getAllApplyType } from '@/applications/unified_portal/api';
import { markRaw, defineAsyncComponent } from 'vue';
import {
  actions as mixinActions,
  state as mixinState,
  getters as mixinGettgers,
} from '@/utils/tab.mixin';

const dictionary = localStorage.getItem(`kade_cache_dictionary`);
const state = {
  ...mixinState,
  dictionary: dictionary ? JSON.parse(dictionary) : [],
  applyTypes: [],
  topTenants: [],
  componentMap: {
    building: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "bulidingManage" */ '../../views/bulidingManage/building'))),
    buildingType: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "bulidingManage" */ '../../views/bulidingManage/buildingType'))),
    roomPlace: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "bulidingManage" */ '../../views/bulidingManage/roomPlace'))),
    roomPlaceType: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "bulidingManage" */ '../../views/bulidingManage/roomPlaceType'))),

    deviceManage: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "accessControlManage" */ '../../views/accessControlManage/deviceManage'))),
    accessControlConsole: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "accessControlManage" */ '../../views/accessControlManage/accessControlConsole'))),
    ElectronicsMap: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "accessControlManage" */ '../../views/accessControlManage/ElectronicsMap'))),
    personAccessRecord: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "accessControlManage" */ '../../views/accessControlManage/personAccessRecord'))),
    accessPersonNum: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "accessControlManage" */ '../../views/accessControlManage/accessPersonNum'))),
    carAccessRecord: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "accessControlManage" */ '../../views/accessControlManage/carAccessRecord'))),

    accessControlGroup: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "authManage" */ '../../views/authManage/accessControlGroup'))),
    auth: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "authManage" */ '../../views/authManage/auth'))),
    authApply: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "authManage" */ '../../views/authManage/authApply'))),
    authDetailed: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "authManage" */ '../../views/authManage/authDetailed'))),
    authExamine: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "authManage" */ '../../views/authManage/authExamine'))),
    authLog: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "authManage" */ '../../views/authManage/authLog'))),
    personGroup: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "authManage" */ '../../views/authManage/personGroup'))),
    strategyAuth: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "authManage" */ '../../views/authManage/strategyAuth'))),
    time: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "authManage" */ '../../views/authManage/time'))),

    backLatentGyrus: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "expandFunctions" */ '../../views/expandFunctions/backLatentGyrus'))),
    firstCardOpenDoor: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "expandFunctions" */ '../../views/expandFunctions/firstCardOpenDoor'))),
    manyCardOpenDoor: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "expandFunctions" */ '../../views/expandFunctions/manyCardOpenDoor'))),
    manyDoorInterlock: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "expandFunctions" */ '../../views/expandFunctions/manyDoorInterlock'))),
    policeLinkage: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "expandFunctions" */ '../../views/expandFunctions/policeLinkage'))),
    pwd: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "expandFunctions" */ '../../views/expandFunctions/pwd'))),
    timingTask: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "expandFunctions" */ '../../views/expandFunctions/timingTask'))),

    //首页
    uacHome: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "expandFunctions" */ '../../views/home/<USER>'))),

    

  },
  customMenus: [],
};
const mutations = {
  updateState(state, { key, payload }) {
    state[key] = payload;
  },
};
const actions = {
  ...mixinActions,
  async loadDictionary({ commit }) {
    const { data } = await getDictionary();
    localStorage.setItem(`kade_cache_dictionary`, JSON.stringify(data));
    commit('updateState', { key: 'dictionary', payload: data });
  },
  async loadApplyType({ commit }) {
    const { data } = await getAllApplyType();
    commit('updateState', { key: 'applyTypes', payload: data });
  },
  async createApplyType({ dispatch }, payload) {
    await addOrEditApplyType(payload);
    dispatch('loadApplyType');
  }
};

const getters = {
  ...mixinGettgers,
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters,
}
