<template>
    <div class="message">
        <kade-route-card>           
            <kade-table-filter @search="handleSearch" @reset="handleReset">
                <el-form :inline="true" label-width="80px" size="small">
                    <el-form-item label="标题:">
                        <el-input style="width: 200px" size="small" v-model="querys.title" placeholder="请输入" />
                    </el-form-item>
                    <el-form-item label="类型:">
                        <el-select clearable style="width: 200px" size="small" placeholder="请选择" v-model="querys.type">
                            <el-option v-for="item in messageTypes" :key="item.value" :value="item.value" :label="item.label"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="发布时间:">
                        <el-date-picker
                            style="width: 250px"
                            size="small"
                            v-model="state.dateRange" 
                            type="daterange"
                            placeholder="请选择"
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            @change="dateChange"
                        />
                    </el-form-item>                            
                </el-form>                
            </kade-table-filter>
            <kade-table-wrap title="消息列表">
                <el-table style="width: 100%" :data="options.dataList" v-loading="options.loading" border stripe>
                    <el-table-column
                        label="标题"
                        prop="applyName"
                        width="150"
                    ></el-table-column>
                    <el-table-column
                        label="类型"
                        prop="status"
                        width="150"
                    ></el-table-column>
                    <el-table-column
                        label="发布时间"
                        prop="applyLogo"
                    ></el-table-column>
                    <el-table-column
                        label="内容"
                        prop="applyLogo"
                    ></el-table-column>                
                    <el-table-column
                        label="操作"
                        align="right"
                        width="150"
                    >
                        <template #default="scope">
                            <el-button @click="handleShowInfo(scope.row)" type="text" size="mini">详情</el-button>
                        </template>
                    </el-table-column>                                    
                </el-table>
                <div class="pagination">
                    <el-pagination
                        background
                        v-model:current-page="options.currentPage"
                        layout="total, sizes, prev, pager, next, jumper"
                        :page-sizes="[10, 20, 50, 100, 200]"        
                        :total="options.total"
                        v-model:page-size="options.pageSize"
                        @current-change="(val) => pageChange(val)"
                        @size-change="(val) => sizeChange(val)"
                    >
                    </el-pagination>         
                </div>                 
            </kade-table-wrap>           
        </kade-route-card>
        <message-info title="消息详情" v-model="state.showInfoModal" :id="state.id" />
    </div>
</template>
<script>
import { reactive } from 'vue';
import { 
    ElTable,
    ElTableColumn,
    ElButton,
    ElForm,
    ElFormItem,
    ElInput,
    ElSelect,
    ElOption,
    ElDatePicker,
    ElPagination,
} from 'element-plus';
import moment from 'moment';
import { getMyMessages } from '@/applications/unified_portal/api';
import { usePagination } from '@/hooks/usePagination';
import { useDict } from '@/hooks/useDict';
import MessageInfo from '@/applications/unified_portal/views/home/<USER>/messageInfo';
const getDefaultQuery = (today) => ({
    beginTime: today,
    endTime: today,
    title: '',
    type: ''    
});
export default {
    components: {
        'el-table': ElTable,
        'el-button': ElButton,
        'el-table-column': ElTableColumn,
        'el-form': ElForm,
        'el-form-item': ElFormItem,
        'el-input': ElInput,
        'el-select': ElSelect,
        'el-option': ElOption,
        'el-date-picker': ElDatePicker,
        'el-pagination': ElPagination,
        'message-info': MessageInfo,
    },
    setup() {
        const today = moment().format('YYYY-MM-DD');
        const state = reactive({
            showInfoModal: false,
            dateRange: [today, today],
            id: null,
        });
        const messageTypes = useDict('SYS_DATA_SOURCE');
        const { options, loadData, querys, search,pageChange,sizeChange } = usePagination(getMyMessages, getDefaultQuery(today));
        const dateChange = (v) => {
            if(!v) {
                querys.beginTime = '';
                querys.endTime = '';
            } else {
                querys.beginTime = moment(v[0]).format('YYYY-MM-DD');
                querys.endTime = moment(v[1]).format('YYYY-MM-DD');
            }
        };
        const handleShowInfo = (row) => {
            state.id = row.id;
            state.showInfoModal = true;
        }
        const handleReset = () => {
            Object.assign(querys, getDefaultQuery(today));
        }
        return {
            state,
            options,
            querys,
            loadData,
            pageChange,
            sizeChange,
            dateChange,
            messageTypes,
            handleReset: handleReset,
            handleSearch: search,
            handleShowInfo,
        };
    }
}
</script>
<style lang="scss" scoped>
.message{
    width: 100%;
    height: 100%;
}
</style>