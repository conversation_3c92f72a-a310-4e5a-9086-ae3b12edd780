<template>
  <kade-route-card>
    <kade-table-filter @search="handleSearch" @reset="handleReset">
      <el-form inline label-width="100px" size="mini">
        <el-form-item label="权限策略">
          <el-select v-model="state.form.authPolicyType" @change="authPolicyTypeChange" clearable>
            <el-option v-for="(item, index) in authStrategyList" :key="index" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="授权身份" v-if="state.form.authPolicyType == 'ROLE_AUTHORIZE'">
          <el-select v-model="state.form.authPersonId" clearable>
            <el-option v-for="(item, index) in state.roleList" :key="index" :label="item.roleName" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="授权机构" v-if="state.form.authPolicyType == 'DEPT_AUTHORIZE'">
          <kade-dept-select-tree style="width: 100%" :value="state.form.authPersonId" valueKey="id" :multiple="false"
            @valueChange="(val) => (state.form.authPersonId = val.id)" />
        </el-form-item>
        <el-form-item label="授权人员组" v-if="state.form.authPolicyType == 'USER_GROUP_AUTHORIZE'">
          <el-select v-model="state.form.authPersonId" clearable>
            <el-option v-for="(item, index) in state.personGroupList" :key="index" :label="item.groupName"
              :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="授权门禁组">
          <el-select v-model="state.form.authDevice" clearable>
            <el-option v-for="(item, index) in state.deviceGroupList" :key="index" :label="item.groupName"
              :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
<!--         <el-form-item label="授权门禁">
          <el-select v-model="state.form.authDevice">
            <el-option v-for="(item, index) in state.deviceList" :key="index" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item> -->

      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="授权列表" v-loading="state.loading">
      <template #extra>
        <el-button @click="state.isEdit = true" class="btn-green" icon="el-icon-plus" size="mini">添加权限</el-button>
        <el-button @click="batchDel" type="primary" icon="el-icon-close" size="mini">批量删除权限</el-button>
      </template>

      <el-table :data="state.dataList" border height="55vh"  @selection-change="handleSelectChange">
        <el-table-column type="selection" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="authPolicyType" label="权限策略" align="center">
          <template #default="scope">
            {{ dictionaryFilter(scope.row.authPolicyType) }}
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip prop="authPersonName" label="授权人员" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="authDeviceName" label="授权门禁" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="authPeriod" label="授权时段" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="oneCard" label="一次刷卡限制" align="center">
          <template #default="scope">
            {{ dictionaryFilter(scope.row.oneCard+'') }}
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip prop="authBeginTime" label="授权时间" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="authEndTime" label="过期时间" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip label="操作" align="center">
          <template #default="scope">
            <el-button @click="handleDel(scope.row)" type="text" size="mini">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination v-model:currentPage="state.form.pageNum" v-model:page-size="state.form.pageSize"
          :page-sizes="[10, 20, 30, 40]" :small="small" :disabled="disabled" background
          layout="total, sizes, prev, pager, next, jumper" :total="state.total" @size-change="handleSizeChange"
          @current-change="handleCurrentChange" />
      </div>
    </kade-table-wrap>
    <kade-dorm-manager-edit v-model:modelValue="state.isEdit" :editType="state.editType" :rowData="state.rowData"
      @update:modelValue="close" />
  </kade-route-card>
</template>

<script>
import { reactive } from '@vue/reactivity';
import { ElForm, ElFormItem, ElSelect, ElOption, ElButton, ElTable, ElTableColumn, ElPagination, ElMessageBox, ElMessage } from "element-plus"
import { onMounted } from '@vue/runtime-core';
import { useDict } from "@/hooks/useDict";
import { getRolelist } from '@/applications/eccard-basic-data/api';
import { acsGroupingInfoList, acsPolicyAuthorizeList, acsPolicyAuthorizeDel,acsPolicyAuthorizeDeleteBatch } from '@/applications/eccard-uac/api';
import deptSelectTree from "@/components/tree/deptSelectTree.vue";
import edit from './components/edit'

export default {
  components: {
    ElForm,
    ElFormItem,
    ElSelect,
    ElOption,
    ElButton,
    ElTable,
    ElTableColumn,
    ElPagination,
    "kade-dorm-manager-edit": edit,
    "kade-dept-select-tree": deptSelectTree,
  },
  setup() {
    const authStrategyList = useDict("ACS_AUTHORIZE_STRATEGY");
    const state = reactive({
      loading: false,
      roleList: [],
      personGroupList: [],
      deviceGroupList: [],
      deviceList:[],
      form: {
        pageSize: 10,
        pageNum: 1,
      },
      dataList: [],
      selectList:[],
      isEdit: false,
      isBatchAdd: false,
      rowData: {},
      editType: "",
      total: 0,
      selectRowList: []
    });
    //获取角色列表
    const queryRolelist = async () => {
      let { data } = await getRolelist();
      state.roleList = data;
    };
    //获取人员组列表
    const queryPersonGrouplist = async () => {
      let { data } = await acsGroupingInfoList({
        groupType: "userGroup"
      });
      state.personGroupList = data;
    };
    //获取人员组列表
    const queryDeviceGrouplist = async () => {
      let { data } = await acsGroupingInfoList({
        groupType: "deviceGroup"
      });
      state.deviceGroupList = data;
    };

    const getList = async () => {
      state.loading = true
      try {
        let { data: { list, total } } = await acsPolicyAuthorizeList(state.form)
        state.dataList = list
        state.total = total
        state.loading = false
      }
      catch {
        state.loading = false
      }
    };
    const authPolicyTypeChange=()=>{
      state.form.authPersonId=""
    }
    const handleSelectChange=(val)=>{
      state.selectList=val
    }
    const handleDel = row => {
      ElMessageBox.confirm("确认删除?", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        let { code, message } = await acsPolicyAuthorizeDel(row.id);
        if (code === 0) {
          ElMessage.success(message);
          getList();
        }
      });
    }
    const batchDel=()=>{
      if (!state.selectList) {
        return ElMessage.error("请选择需要删除的权限策略！")
      }
      ElMessageBox.confirm("确认删除已选择策略?", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        let { code, message } = await acsPolicyAuthorizeDeleteBatch(state.selectList.map(item=>item.id).join(","));
        if (code === 0) {
          ElMessage.success(message);
          getList();
        }
      });
    }
    const handleSearch = () => {
      getList()
    };
    const handleReset = () => {
      state.form = {
        pageSize: 10,
        pageNum: 1
      }
    };
    const handleSizeChange = (val) => {
      state.form.pageNum = 1
      state.form.pageSize = val
      getList()
    };
    const handleCurrentChange = (val) => {
      state.form.pageNum = val
      getList()
    }
    const close = (val) => {
      if (val) {
        getList()
      }
      state.isEdit = false
    }
    onMounted(() => {
      getList();
      queryRolelist()
      queryPersonGrouplist()
      queryDeviceGrouplist()
    });
    return {
      state,
      authStrategyList,
      authPolicyTypeChange,
      handleSelectChange,
      handleDel,
      batchDel,
      handleSearch,
      handleReset,
      handleSizeChange,
      handleCurrentChange,
      close
    }
  }
}
</script>

<style lang="scss" scoped>
:deep(.el-dialog__footer) {
  text-align: center;
}
</style>