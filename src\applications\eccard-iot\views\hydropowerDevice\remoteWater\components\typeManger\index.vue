<template>
  <el-dialog :model-value="modelValue" title="远传水表类型管理" width="90%" :before-close="handleClose">
    <div class="top-search">
      <el-form size="small" inline label-width="100px">
        <el-form-item label="类型名称">
          <el-input v-model="state.form.typeName" placeholder="请输入" clearable></el-input>
        </el-form-item>
        <el-button @click="handleSearch" size="small" icon="el-icon-delete" type="primary">搜索</el-button>
      </el-form>
      <div>
        <el-button @click="handleEdit({},'add')" size="small" icon="el-icon-plus" type="success">新增</el-button>
        <el-button @click="handleBatchDel" size="small" icon="el-icon-delete-solid" type="primary">批量删除</el-button>
      </div>
    </div>
    <el-table style="width: 100%" height="55vh" v-loading="state.loading" :data="state.dataList" @rowClick="rowClick" @selection-change="handleSelectChange" highlight-current-row border stripe>
      <el-table-column prop="" show-overflow-tooltip type="selection" align="center"></el-table-column>
      <el-table-column prop="typeName" label="类型名称" align="center" show-overflow-tooltip></el-table-column>
      <el-table-column prop="maxValue" label="最大值" align="center" show-overflow-tooltip></el-table-column>
      <el-table-column prop="makeFactory" label="生产厂家" align="center" show-overflow-tooltip></el-table-column>
      <el-table-column prop="deviceSpecifications" label="规格" align="center" show-overflow-tooltip></el-table-column>
      <el-table-column prop="factoryAddress" label="厂家地址" align="center" show-overflow-tooltip></el-table-column>
      <el-table-column label="操作" align="center" width="180px">
        <template #default="scope">
          <el-button class="green" size="mini" @click="handleEdit(scope.row,'details')" type="text">详情</el-button>
          <el-button class="green" size="mini" @click="handleEdit(scope.row,'edit')" type="text">编辑</el-button>
          <el-button class="green" size="mini" @click="del(scope.row)" type="text">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="pagination">
      <el-pagination background :current-page="state.form.currentPage" layout="total, sizes, prev, pager, next, jumper" :page-sizes="[10, 20, 50, 100, 200]" :total="state.total" :page-size="state.form.pageSize" @current-change="handlePageChange" @size-change="handleSizeChange">
      </el-pagination>
    </div>
    <div style="height:20px"></div>
    <kade-device-type-manger-edit v-model="state.isEdit" :type="state.type" :rowData="state.rowData" @update:modelValue="close" />
  </el-dialog>
</template>

<script>
import { ElDialog, ElButton, ElForm, ElFormItem, ElInput, ElTable, ElTableColumn, ElPagination, ElMessageBox, ElMessage } from "element-plus"
import { reactive, watch, } from 'vue'
import { waterTypeList, waterTypeDel,waterTypeBatchDel } from "@/applications/eccard-iot/api";

import edit from "./edit.vue"
export default {
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    listFnc: {
      type: Function,
      default: null
    }
  },
  components: {
    ElDialog, ElButton, ElForm, ElFormItem, ElInput, ElTable, ElTableColumn, ElPagination,
    "kade-device-type-manger-edit": edit
  },
  setup(props, context) {
    const state = reactive({
      loading: false,
      isEdit: false,
      form: {
        pageNum: 1,
        pageSize: 10
      },
      total: 0,
      dataList: [],
      rowData: {},
      selectRowList: []
    });
    watch(() => props.modelValue, val => {
      if (val) {
        state.dataList = []
        state.total = 0
        state.form = {
          pageNum: 1,
          pageSize: 10
        }
        getList()
      }
    })
    const getList = async () => {
      for (let key in state.form) {
        if (!state.form[key]) {
          delete state.form[key]
        }
      }
      state.loading = true
      try {
        let { data: { list, total } } = await waterTypeList(state.form)
        state.dataList = list
        state.total = total
        state.loading = false
      }
      catch {
        state.loading = false
      }
    }
    const handleEdit = (row, type) => {
      state.rowData = row
      state.type = type
      state.isEdit = true
    }
    const del = (row) => {
      ElMessageBox.confirm("确认删除?", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        let { code, message } = await waterTypeDel(row.id);
        if (code === 0) {
          ElMessage.success(message);
          getList();
          props.listFnc()
        }
      });
    }
    const handleSelectChange = (val) => {
      state.selectRowList = val
    }
    const handleBatchDel = () => {
      if (!state.selectRowList.length) {
        return ElMessage.error("请先选择需要删除的类型！")
      }
      ElMessageBox.confirm("确认删除已选择类型?", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        let param = state.selectRowList.map(item => item.id)
        let { code, message } = await waterTypeBatchDel({ids:param});
        if (code === 0) {
          ElMessage.success(message);
          getList();
          props.listFnc()
        }
      });
    }
    const handleSearch = () => {
      getList();
    }
    const handlePageChange = (val) => {
      state.form.pageNum = val
      getList();
    }
    const handleSizeChange = val => {
      state.form.pageNum = 1
      state.form.pageSize = val
      getList();
    }
    const handleClose = () => {
      context.emit("update:modelValue", false)
    };
    const close = (val) => {
      if (val) {
        getList()
        props.listFnc()
      }
      state.isEdit = false
    }
    return {
      state,
      handleEdit,
      del,
      handleSelectChange,
      handleBatchDel,
      handleClose,
      handleSearch,
      handlePageChange,
      handleSizeChange,
      close
    }
  }
}
</script>

<style lang="scss" scoped>
.top-search {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.pagination {
  padding: 10px;
  border: 1px solid #ebeef5;
  border-top: 0;
  border-radius: 0 0 5px 5px;
}
.el-form {
  margin-top: 20px;
}
</style>