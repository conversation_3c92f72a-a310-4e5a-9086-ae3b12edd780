<template>
  <kade-route-card>
    <kade-table-filter @search="handleSearch" @reset="handleReset">
      <el-form inline label-width="120px" size="small">
        <el-form-item label="组织机构">
          <kade-dept-select-tree style="width: 100%" :value="state.form.deptPath" valueKey="deptPath" :multiple="false"
            @valueChange="(val) => (state.form.deptPath = val.deptPath)" />
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="设置列表">
      <template #extra>
        <el-button icon="el-icon-plus" @click="handleEdit({})" size="small" class="btn-green">新增</el-button>
      </template>
      <el-table v-loading="state.loading" style="width: 100%" :data="state.dataList" height="55vh" border stripe>
        <el-table-column prop="deptName" label="组织机构" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column prop="beginTime" label="开始自助选宿时间" align="center" show-overflow-tooltip>
          <template #default="scope">
            {{ timeStr(scope.row.beginTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="endTime" label="结束自助选宿时间" align="center" show-overflow-tooltip>
          <template #default="scope">
            {{ timeStr(scope.row.endTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="remarks" label="备注" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column prop="createTime" label="添加时间" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column label="操作" align="center" width="180px">
          <template #default="scope">
            <el-button class="green" size="mini" @click="handleEdit(scope.row)" type="text">编辑</el-button>
            <el-button class="green" size="mini" @click="handleDel(scope.row)" type="text">删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination background :current-page="state.form.currentPage"
          layout="total, sizes, prev, pager, next, jumper" :page-sizes="[10, 20, 50, 100, 200]" :total="state.total"
          :page-size="state.form.pageSize" @current-change="handlePageChange" @size-change="handleSizeChange">
        </el-pagination>
      </div>
    </kade-table-wrap>
    <kade-self-choice-edit :isShow="state.isEdit" :data="state.rowData" @close="close" />
  </kade-route-card>
</template>
<script>
import { ElButton, ElForm, ElFormItem, ElTable, ElTableColumn, ElPagination,ElMessageBox,ElMessage } from "element-plus";
import { onMounted, reactive } from 'vue';
import { selfChoiceList,delSelfChoice } from "@/applications/eccard-dorm/api";
import { timeStr } from "@/utils/date.js"
import deptSelectTree from "@/components/tree/deptSelectTree.vue";
import edit from "./components/edit"
export default {
  components: {
    ElButton,
    ElForm,
    ElFormItem,
    ElTable,
    ElTableColumn,
    ElPagination,
    "kade-dept-select-tree": deptSelectTree,
    "kade-self-choice-edit": edit
  },
  setup() {
    const state = reactive({
      loading: false,
      isEdit: false,
      form: {
        currentPage: 1,
        pageSize: 10
      },
      total: 0,
      dataList: [],
      rowData: {}
    })
    const getList = async () => {
      state.loading = true
      try {
        let { data: { list, total } } = await selfChoiceList(state.form)
        state.dataList = list
        state.total = total
        state.loading = false
      }
      catch {
        state.loading = false
      }
    }
    const handleEdit = (row) => {
      state.rowData = row
      state.isEdit = true
    }
    const handleDel = (row) => {
      ElMessageBox.confirm("确认删除?", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        let { code, message } = await delSelfChoice(row.id);
        if (code === 0) {
          ElMessage.success(message);
          getList();
        }
      });
    }
    const handleSearch = () => {
      getList()
    }
    const handleReset = () => {
      state.form = {
        currentPage: 1,
        pageSize: 10
      }
      getList()
    }
    const handlePageChange = (val) => {
      state.form.currentPage = val
      getList()
    }
    const handleSizeChange = (val) => {
      state.form.currentPage = 1
      state.form.pageSize = val
      getList()
    }
    const close = (val) => {
      if (val) {
        getList()
      }
      state.isEdit = false
    }
    onMounted(() => {
      getList()
    })
    return {
      timeStr,
      state,
      handleEdit,
      handleDel,
      handleSearch,
      handleReset,
      handlePageChange,
      handleSizeChange,
      close
    }
  }
};
</script>
<style lang="scss" scoped>
:deep(.el-form-item__content) {
  .el-select {
    width: 200px;
  }

  .el-input__inner {
    width: 200px;
  }
}

:deep(.el-dialog){
  border-radius: 10px;
}

:deep(.el-dialog__header) {
  border-bottom: 1px solid #efefef;
}

:deep(.el-overlay) {
  position: absolute;
  background-color: rgba(0, 0, 0, 0.3);
}

:deep(.el-dialog__footer) {
  text-align: center;
}
</style>