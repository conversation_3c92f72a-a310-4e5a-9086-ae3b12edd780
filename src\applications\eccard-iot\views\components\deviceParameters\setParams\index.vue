<template>
  <el-dialog :model-value="isShow" title="参数设置" width="90%" :before-close="beforeClose">
    <div class="padding-box">
      <component :is='component'></component>
    </div>
  </el-dialog>
</template>

<script>
import { reactive } from '@vue/reactivity'
import { useStore } from "vuex"
import { ElDialog } from "element-plus";
import machineParameters from "./components/machineParameters"
import terminalDeviceParameters from "./components/terminalDeviceParameters"
import faceParameters from "./components/faceParameters"
import saveWaterParameters from "./components/saveWaterParameters"
import waterParameters from "./components/waterParameters"
import infiniteWaterParams from "./components/infiniteWaterParameters"
import { computed } from '@vue/runtime-core'

export default {
  components: {
    ElDialog,
    machineParameters,
    terminalDeviceParameters,
    faceParameters,
    waterParameters,
    saveWaterParameters,
    infiniteWaterParams
  },
  setup() {
    const store = useStore()
    const state = reactive({
    })  
    const component = computed(() => {
      return store.state.app.activeTab
    })
    const isShow = computed(() => {
      let data= store.state.deviceParameters[store.state.app.activeTab]
      return data?.isSetParams
    })
    const beforeClose = () => {
      console.log(store.state.deviceParameters, store.state.app.activeTab);
      store.commit("deviceParameters/updateState", {
        key: store.state.app.activeTab,
        childKey: "isSetParams",
        payload: false,
      });
    };
    return {
      state,
      component,
      isShow,
      beforeClose
    }
  }
}
</script>