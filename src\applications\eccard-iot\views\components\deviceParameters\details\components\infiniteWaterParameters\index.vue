<template>
  <kade-tab-wrap :tabs="tabs" v-model="state.tab">
    <template #jccs>
      <kade-basic-params />
    </template>
    <template #gjcs>
      <kade-senior-params />
    </template>
    <template #klcs>
      <kade-card-params />
    </template>
  </kade-tab-wrap>
</template>
<script>
import { reactive, onMounted } from 'vue'
import { useStore } from "vuex";
import basicParams from "./basicParams";
import seniorParams from "./seniorParams"
import cardParams from "./cardParams"
const tabs = [
  { name: "jccs", label: "基础参数" },
  { name: "klcs", label: "卡类参数" },
  { name: "gjcs", label: "高级参数" },
];
export default {
  components: {
    "kade-basic-params": basicParams,
    "kade-senior-params": seniorParams,
    "kade-card-params": cardParams,
  },
  setup() {
    const store = useStore();
    const state = reactive({
      tab: "jccs",
    });
    onMounted(async () => {
      await store.dispatch("deviceParameters/getParams", store.state.deviceParameters[store.state.app.activeTab].selectRow.id);
      state.tab = "jccs"
    });
    return {
      tabs,
      state,
    };
  }
}
</script>