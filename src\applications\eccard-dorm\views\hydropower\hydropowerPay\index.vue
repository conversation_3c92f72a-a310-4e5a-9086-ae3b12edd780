<template>
  <kade-route-card>
    <kade-table-filter @search="handleSearch" @reset="handleReset">
      <el-form label-width="90px" size="mini" inline>
        <kade-linkage-select :data="linkageData" :value="state.form" @change="linkageChange"></kade-linkage-select>
        <el-form-item label="缴费类型">
          <el-select v-model="state.form.hydropowerPayType" placeholder="全部">
            <el-option v-for="(item,index) in payTypeList" :key="index" :label="item.label" :value="item.index"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="缴费时间">
          <el-date-picker
            v-model="state.requsetDate"
            :defaultTime="defaultTime"
            type="datetimerange"
            range-separator="~"
            start-placeholder="请选择"
            end-placeholder="请选择"
          >
          </el-date-picker>
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <div class="total-amount">
      <div class="water-charge">水费缴存总额:&nbsp;&nbsp;{{state.totalList.totalWaterBalance?state.totalList.totalWaterBalance:0}}元</div>
      <div>电费缴存总额:&nbsp;&nbsp;{{state.totalList.totalElectricBalance?state.totalList.totalElectricBalance:&nbsp;&nbsp;0}}元</div>
    </div>
    <kade-table-wrap title="记录列表">
      <el-table :data="state.dataList" border>
        <el-table-column v-for="(item,index) in column" :key="index" :width="item.width" :prop="item.prop" :label="item.label" align="center" show-overflow-tooltip></el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination
          background
          :current-page="state.form.currentPage"
          layout="total, sizes, prev, pager, next, jumper"
          :page-sizes="[10, 20, 50, 100, 200]"
          :total="state.total"
          :page-size="state.form.pageSize"
          @current-change="handlePageChange"
          @size-change="handleSizeChange"
        >
        </el-pagination>
      </div>
    </kade-table-wrap>
  </kade-route-card>
</template>
<script>
import {
  ElForm,
  ElFormItem,
  ElSelect,
  ElOption,
  ElDatePicker,
  ElTable,
  ElTableColumn,
  ElPagination,
} from "element-plus";
import { reactive } from '@vue/reactivity';
import { requestDate } from "@/utils/reqDefaultDate.js";
import { timeStr } from "@/utils/date.js";
import { useDict } from "@/hooks/useDict.js"
import { onMounted } from '@vue/runtime-core';
import { getHydropowerPay,getPayTotal } from '@/applications/eccard-dorm/api.js'
import linkageSelect from "@/applications/eccard-dorm/components/linkageSelect";
const linkageData={
  area:{label:'区域',valueKey:'areaId',key:'id'},
  buildingType:{label:'楼栋类型',valueKey:'buildType'},
  building:{label:'楼栋',valueKey:'buildId'},
  unit:{label:'单元',valueKey:'unitNum'},
  floor:{label:'楼层',valueKey:'floorNum'},
  room:{label:'房间',valueKey:'roomId'}
}
const column =[
  {label:"区域",prop:"areaId"},
  {label:"房间",prop:"roomId"},
  {label:"缴费类型",prop:"hydropowerPayType"},
  {label:"缴费人编号",prop:"userCode"},
  {label:"缴费人姓名",prop:"userName"},
  {label:"缴费时间",prop:"tradeDate"},
  {label:"缴费金额",prop:"tradeAmount"},
  {label:"缴费前余额",prop:"tradeBeforeBalance"},
  {label:"缴费后余额",prop:"tradeAfterBalance"},
]
const defaultTime=[
  new Date(new Date(new Date().toLocaleDateString()).getTime()),
        new Date(
            new Date(new Date().toLocaleDateString()).getTime() +
            24 * 60 * 60 * 1000 -
            1
        )
]
export default {
  components: {
    ElForm,
    ElFormItem,
    ElSelect,
    ElOption,
    ElDatePicker,
    ElTable,
    ElTableColumn,
    ElPagination,
    "kade-linkage-select":linkageSelect
  },
  setup(){
    const payTypeList = useDict('DORM_HYDROPOWER_PAY_TYPE')
    const state=reactive({
      form:{
        currentPage:1,
        pageSize:10,
      },
      dataList:[],
      totalList:{},
      total:0,
      requestDate:[requestDate()[0], requestDate()[1]]
    });
    const linkageChange = (val)=>{
        state.form={...state.form,...val}
    }
    const getList=()=>{
      if(state.requestDate&&state.requestDate.length){
        state.form.startTime=timeStr(state.requestDate[0])
        state.form.endtTime=timeStr(state.requestDate[1])

      }else{
        delete state.form.startTime
        delete state.form.endtTime
      }
      getHydropowerPay(state.form).then((res)=>{
        console.log(res)
      })
    };
    //获取水电费余额汇总
    const queryPay=()=>{
      getPayTotal().then((res)=>{
        console.log(res)
      })
    }
    const handleSearch=()=>{
      getList()
    };
    const handleReset=()=>{
      state.form={
        currentPage:1,
        pageSize:0
      }
      state.requestDate=[requestDate()[0], requestDate()[1]]
    };
    const handlePageChange=(val)=>{
      state.form.currentPage=val
    };
    const handleSizeChange=(val)=>{
      state.form.currentPage=1
      state.form.pageSize=val
    };
    onMounted(()=>{
      getList()
    })
    return {
      column,
      state,
      queryPay,
      payTypeList,
      linkageData,
      defaultTime,
      handleSearch,
      handleReset,
      linkageChange,
      handlePageChange,
      handleSizeChange
    }
  }
};
</script>
<style lang="scss" scoped>
.total-amount{
  display: flex;
  align-items: center;
  padding: 0px 0 15px 10px;
  .water-charge{
    margin-right: 100px;
  }
}
  :deep(.el-form-item__content){
    .el-input__inner{
      width: 182px;
    }
    .el-date-editor{
      width: 400px;
    }
  }
</style>