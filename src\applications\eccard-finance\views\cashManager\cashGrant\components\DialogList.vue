<template>
  <div>
    <el-dialog :model-value="isDetailsList" title="人员清单" width="70%" :before-close="beforeClose"
      :close-on-click-modal="false">
      <div>
        <div class="padding-form-box">
          <el-form inline size="small" label-width="100px">
            <el-form-item label="关键字">
              <el-input placeholder="请输入" v-model="state.form.keyWord"></el-input>
            </el-form-item>
            <el-form-item label="组织机构">
              <el-select clearable v-model="state.form.deptPath" placeholder="请选择">
                <el-option v-for="(item, index) in state.form.departCheckList" :key="index" :label="item.deptName"
                  :value="item.deptId">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="身份类别:">
              <el-select clearable v-model="state.form.deptPath" placeholder="请选择">
                <el-option v-for="(item, index) in state.form.departCheckList" :key="index" :label="item.deptName"
                  :value="item.deptId">
                </el-option>
              </el-select>
            </el-form-item>
            <el-button @click="search()" size="small" type="primary" icon="el-icon-search">搜索</el-button>
          </el-form>
          <el-table style="width: 100%" :data="state.personList" v-loading="false" highlight-current-row border stripe>
            <el-table-column label="用户编号" prop="userCode" align="center"></el-table-column>
            <el-table-column label="姓名" prop="userName" align="center"></el-table-column>
            <el-table-column label="组织结构" prop="deptName" align="center"></el-table-column>
            <el-table-column label="身份类别" prop="postName" align="center"></el-table-column>
            <el-table-column label="充值金额" prop="rechargeAmount" align="center"></el-table-column>
            <el-table-column label="到账状态" prop="" align="center"></el-table-column>
          </el-table>
          <div class="pagination">
            <el-pagination background :current-page="state.form.currentPage" :page-size="state.form.pageSize"
              layout="total, sizes, prev, pager, next, jumper" :page-sizes="[6, 10, 20, 50, 100]" :total="state.total"
              @current-change="handlePageChange" @size-change="handleSizeChange">
            </el-pagination>
          </div>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="beforeClose()" size="mini">关&nbsp;&nbsp;闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script>
import { reactive, computed, watch } from "vue";
import { useStore } from "vuex";
import { getCashGrantList } from "@/applications/eccard-finance/api";
import {
  ElDialog,
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElTable,
  ElTableColumn,
  ElPagination,
} from "element-plus";
export default {
  components: {
    "el-dialog": ElDialog,
    "el-button": ElButton,
    "el-input": ElInput,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    "el-select": ElSelect,
    "el-option": ElOption,
    "el-table": ElTable,
    "el-table-column": ElTableColumn,
    "el-pagination": ElPagination,
  },
  setup() {
    const store = useStore();
    const state = reactive({
      form: {
        currentPage: 1,
        pageSize: 6,
        projectId: store.state.cashData.selectRow.projectId,
      },
      total: 0,
    });
    const isDetailsList = computed(() => {
      return store.state.cashData.isDetailsList;
    });

    watch(
      () => store.state.cashData.isDetailsList,
      (val) => {
        if (val) {
          state.form = {
            currentPage: 1,
            pageSize: 6,
            projectId: store.state.cashData.selectRow.projectId,
          };
          // state.form.projectNo = store.state.cashData.selectRow.projectId;
          getDataList();
        }
      }
    );
    const getDataList = () => {
      getCashGrantList(state.form).then((res) => {
        state.personList = res.data.pageInfo.list;
        state.total = res.data.pageInfo.total;
      });
    };
    const search=()=>{
      getDataList()
    }
    const handlePageChange = (val) => {
      state.form.currentPage = val;
      getDataList();
    };
    const handleSizeChange = (val) => {
      state.form.currentPage = 1;
      state.form.pageSize = val;
      getDataList();
    };

    const beforeClose = () => {
      store.commit("cashData/updateState", {
        key: "isDetailsList",
        payload: false,
      });
    };
    return {
      state,
      search,
      isDetailsList,
      getDataList,
      beforeClose,
      handlePageChange,
      handleSizeChange,
    };
  },
};
</script>
<style lang="scss" scoped>
 :deep(.el-dialog__header) {
   border-bottom: 1px solid #efefef;
 }

 :deep(.el-overlay) {
   position: absolute;
   background-color: rgba(0, 0, 0, 0);
 }

 :deep(.el-dialog__footer) {
   text-align: center;
 }
</style>