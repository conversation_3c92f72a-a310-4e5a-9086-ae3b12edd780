<template>
  <div class="padding-form-box setCardAuthParam">
    <el-button icon="el-icon-xinzeng" @click="addCard()" size="mini" type="success" style="margin-bottom: 10px">添加卡类</el-button>
    <el-table style="width: 100%" ref="multipleTable" :data="tableList" v-loading="state.loading" border stripe>
      <el-table-column type="expand">
        <template #default="scope">
          <el-table style="width: 100%" :data="scope.row.listTime" border stripe>
            <el-table-column v-for="(item, index) in state.tableChildItemList" :key="index" :label="item.label" :prop="item.prop" align="center">
              <template v-if="item.rander" #default="scope">
                {{ item.rander(scope.row[item.prop]) }}
              </template>
            </el-table-column>
          </el-table>
        </template>
      </el-table-column>
      <el-table-column v-for="(item, index) in state.tableItemList" :key="index" :label="item.label" :prop="item.prop" align="center">
        <template v-if="item.rander" #default="scope">
          {{ item.rander(scope.row[item.prop]) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template #default="scope">
          <span @click="detailClick(scope)" class="operation">详情 </span>
          <span @click="editClick(scope)" class="operation">编辑 </span>
          <span @click="delClick(scope)" class="operation">删除 </span>
        </template>
      </el-table-column>
    </el-table>
  </div>

  <div style="text-align: center; margin-top: 10px">
    <el-button size="mini" @click="beforeClose()">取消</el-button>
    <el-button size="mini" type="primary" @click="saveClick()">保存</el-button>
  </div>
  <kade-card-auth-edit :cardTypeList="state.cardTypeList" />
</template>

<script>
/**
 * kade-card-auth-edit组件内的保存为本页面保存或编辑，并没有调用接口，通过vuex将保存的值存入cardAuthList
 */

import { ElButton, ElMessage, ElTable, ElTableColumn } from "element-plus";
import { reactive } from "@vue/reactivity";
import { useStore } from "vuex";
import { computed, onMounted } from "@vue/runtime-core";
import { getCardTypeList, saveCard } from "@/applications/eccard-iot/api";
import cardAuthEdit from "./components/cardAuthEdit.vue";
export default {
  components: {
    ElButton,
    ElTable,
    ElTableColumn,
    "kade-card-auth-edit": cardAuthEdit,
  },
  name: "cardAuthParam",
  setup() {
    const store = useStore();
    const state = reactive({
      loading: false,
      id: "",
      isShow: false,
      isDetail: false,
      title: "",
      editScope: "",
      detailRow: "",
      tableItemList: [
        {
          prop: "card_num",
          label: "卡类",
          rander: function (val) {
            let arr = state.cardTypeList.filter((item) => val == item.ctCode);
            if (arr.length) {
              return arr[0].ctName;
            }
          },
        },
        {
          prop: "card_discount",
          label: "卡类折扣率(%)",
        },
        {
          prop: "card_single_quota",
          label: "卡类单次限额(元)",
        },
        {
          prop: "card_day_quota",
          label: "卡类日限额(元)",
        },
        {
          prop: "card_day_xianci",
          label: "卡类日限次",
        },
      ],
      tableChildItemList: [
        {
          prop: "label",
          label: "餐(时段)",
        },
        {
          prop: "card_is_trade",
          label: "是否允许交易",
          rander: function (val) {
            return val === 0 ? "否" : "是";
          },
        },
        {
          prop: "card_dzcs",
          label: "次数定值",
        },
        {
          prop: "card_jedz",
          label: "金额定值(第一次)",
        },
        {
          prop: "card_jedz1",
          label: "金额定值(第二次)",
        },
        {
          prop: "card_jedz2",
          label: "金额定值(第三次)",
        },
        {
          prop: "card_is_free",
          label: "免费使用次数",
        },
        {
          prop: "card_dzjycs",
          label: "次数交易限次",
        },
        {
          prop: "card_jyxe",
          label: "交易限额",
        },
        {
          prop: "card_jyxc",
          label: "餐交易总限次",
        },
      ],
      cardTypeList: [], //卡片列表
    });

    const tableList = computed(() => {
      return store.state.deviceParameters[store.state.app.activeTab].cardAuthList;
    });


    //获取卡片类型
    const queryCardTypeList = () => {
      getCardTypeList().then((res) => {
        state.cardTypeList = res.data;
      });
    };

    const addCard = () => {
      store.commit("deviceParameters/updateState", {
        key: store.state.app.activeTab,
        childKey: "isCardAuthEdit",
        payload: {
          title: "新增卡类权限参数",
          value: "",
          isShow: true,
        },
      });
    };

    const detailClick = (scope) => {
      store.commit("deviceParameters/updateState", {
        key: store.state.app.activeTab,
        childKey: "isCardAuthEdit",
        payload: {
          title: "卡类权限参数",
          disabled: true,
          value: scope,
          isShow: true,
        },
      });
    };

    const editClick = (scope) => {
      store.commit("deviceParameters/updateState", {
        key: store.state.app.activeTab,
        childKey: "isCardAuthEdit",
        payload: {
          title: "编辑卡类权限参数",
          value: scope,
          isShow: true,
        },
      });
    };

    const saveClick = async () => {
      let data = {};
      data.cardDetailEntity = JSON.parse(
        JSON.stringify(store.state.deviceParameters[store.state.app.activeTab].cardAuthList)
      );
      data.id = state.id;
      data.paramType = "card";
      data.paramId = store.state.deviceParameters[store.state.app.activeTab].selectRow.id;
      state.loading = true;
      let { code, message } = await saveCard(data);
      if (code === 0) {
        setTimeout(() => {
          ElMessage.success(message);
          store.dispatch("deviceParameters/getParams", store.state.deviceParameters[store.state.app.activeTab].selectRow.id);
          state.loading = false;
        }, 1000);
      }
    };

    const delClick = (scope) => {
      let list = store.state.deviceParameters[store.state.app.activeTab].cardAuthList;
      console.log(list);
      list.splice(scope.$index, 1);
      store.commit("deviceParameters/updateState", {
        key: store.state.app.activeTab,
        childKey: "cardAuthList",
        payload: list,
      });
    };
    const beforeClose = () => {
      store.commit("deviceParameters/updateState", {
        key: store.state.app.activeTab,
        childKey: "isSetParams",
        payload: false,
      });
    };
    onMounted(() => {
      // store.dispatch("deviceParameters/getParams",store.state.deviceParameters[store.state.app.activeTab].selectRow.id);
      queryCardTypeList();
      let val = store.state.deviceParameters[store.state.app.activeTab].detailsParams

      if (val && val.length) {
        //判断数组中有没有card对应的项
        let cardList = val.filter((item) => item.paramType == "card");
        if (cardList.length) {
          state.id = cardList[0].id;
          if (JSON.parse(cardList[0].paramContent).length) {
            store.commit("deviceParameters/updateState", {

              key: store.state.app.activeTab,
              childKey: "cardAuthList",
              payload: JSON.parse(cardList[0].paramContent),
            });
          } else {
            store.commit("deviceParameters/updateState", {
              key: store.state.app.activeTab,
              childKey: "cardAuthList",
              payload: [],
            });
          }
        } else {
          store.commit("deviceParameters/updateState", {
            key: store.state.app.activeTab,
            childKey: "cardAuthList",
            payload: [],
          });
        }
      } else {
        store.commit("deviceParameters/updateState", {
          key: store.state.app.activeTab,
          childKey: "cardAuthList",
          payload: [],
        });
      }
    });
    return {
      state,
      tableList,
      addCard,
      delClick,
      editClick,
      saveClick,
      detailClick,
      beforeClose,
    };
  },
};
</script>

<style lang="scss" scoped>
.setCardAuthParam {
  border: 1px solid #dcdfe6;
  border-radius: 5px;
  margin-bottom: 10px;
}

.operation {
  margin: 0 6px;
  color: #1abc9c;
}
</style>
