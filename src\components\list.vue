<template>
  <div class="kade-list">
    <div class="kade-item" v-for="item in data" :key="item[uniqueKey]">
      <div class="ki-left">
        <kade-avatar :size="appImageSize" icon="iconyingyong" :is-app-image="true" :src="item[imageKey]" />
        <div class="ki-text">
          <slot name="text">
            <span class="title">{{ item[labelKey] }}</span>           
          </slot>          
        </div>      
      </div>
      <div class="ki-right">
        <el-switch @click="handleClick(item)" :model-value="item[valueKey]" active-color="#13ce66" inactive-color="#ff4949" />
      </div>
    </div>
  </div>  
</template>
<script>
import Avatar from '@/components/avatar';
import { ElSwitch } from 'element-plus';
export default {
  emits: ['change'],
  components: {
    'kade-avatar': Avatar,
    'el-switch': ElSwitch,
  },
  props: {
    data: {
      type: Array,
      default: () => [],
    },
    labelKey: {
      type: String,
      default: 'label',
    },
    valueKey: {
      type: String,
      default: 'value',
    },
    imageKey: {
      type: String,
      default: 'image',
    },
    uniqueKey: {
      type: String,
      default: 'id'
    }
  },
  setup(props, context) {
    const handleClick = (item) => {
      context.emit('change', item.id, item);
    }
    return {
      appImageSize: THEMEVARS.appImageSize,
      handleClick,
    }
  },
}
</script>
<style lang="scss" scoped>
.kade-list{
  .kade-item{
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    box-sizing: border-box;
  }
  .ki-left{
    display: flex;
    justify-content: flex-start;
    align-items: center;
  }
  .ki-text{
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: flex-start;
    padding-left: 20px;
  }
  .kade-item + .kade-item{
    border-top: 1px solid $border-color;
  }
}
</style>