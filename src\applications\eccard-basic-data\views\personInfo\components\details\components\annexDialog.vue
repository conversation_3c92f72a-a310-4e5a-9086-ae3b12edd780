<template>
  <el-dialog :model-value="isShow" title="上传附件" width="500px" :before-close="beforeClose" :append-to-body="true">
    <div style="padding-top: 20px">
      <el-form size="mini" label-width="120px" ref="formDom" :rules="state.rules" :model="state.form">
        <el-form-item label="附件名称:" prop="annexName">
          <el-input placeholder="请输入" v-model="state.form.annexName" :maxlength="20"></el-input>
        </el-form-item>
        <el-form-item label="附件:" prop="annexUrl">
          <el-upload ref="upload" class="upload-demo" :headers="{ Authorization: `bearer ${state.requestHeader}` }" :action="state.requestUrl" :limit="1" :on-exceed="handleExceed" :on-success="handleSuccess">
            <template #default>
              <el-button size="mini" type="primary">上传</el-button>
            </template>
          </el-upload>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="beforeClose" size="mini">取消</el-button>
        <el-button type="primary" @click="submit()" size="mini">提交</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script>
import {
  ElDialog,
  ElForm,
  ElFormItem,
  ElInput,
  ElButton,
  ElUpload,
  ElMessage,
} from "element-plus";
import { reactive, ref } from "@vue/reactivity";
import { nextTick, watch } from "@vue/runtime-core";
import { useStore } from "vuex";
import { getToken } from "@/utils";
import { addAnnex } from "@/applications/eccard-basic-data/api";
export default {
  components: {
    ElDialog,
    ElForm,
    ElFormItem,
    ElInput,
    ElButton,
    ElUpload,
  },
  props: {
    isShow: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Object,
      default: null,
    },
  },
  setup(props, context) {
    const store = useStore();
    const formDom = ref(null);
    const state = reactive({
      requestHeader: getToken(),
      requestUrl: `${CONFIG.BASE_API_PATH}eccard-partal-manage/applicationManage/uploadApplyLogo`,
      form: {},
      rules: {
        annexName: [
          {
            required: true,
            message: "请输入附件名称",
            trigger: "blur",
          },
        ],
        annexUrl: [
          {
            required: true,
            message: "请上传附件",
            trigger: "change",
          },
        ],
      },
    });
    watch(
      () => props.isShow,
      (val) => {
        if (val) {
          state.form = {};
        }
      }
    );
    const handleExceed = () => {
      ElMessage.error("最多上传一个文件！");
    };

    const handleSuccess = (res) => {
      console.log(res);
      state.form.annexUrl = res.data;
    };

    const submit = () => {
      formDom.value.validate(async (valid) => {
        if (valid) {
          let params = {
            ...state.form,
            userId: store.state.userInfo.rowData.id,
          };
          let { code, message } = await addAnnex(params);
          if (code === 0) {
            ElMessage.success(message);
            context.emit("close", true);
          }
        } else {
          return false;
        }
      });
    };
    const beforeClose = () => {
      context.emit("close", false);
      nextTick(() => {
        formDom.value.clearValidate();
      });
    };
    return {
      formDom,
      state,
      handleExceed,
      handleSuccess,
      submit,
      beforeClose,
    };
  },
};
</script>
<style lang="scss" scoped>
:deep(.el-form-item__content) {
  .el-select {
    width: 100% !important;
  }
  .el-input__inner {
    width: 100% !important;
  }
  .el-date-editor.el-input {
    width: 100%;
  }
}
</style>