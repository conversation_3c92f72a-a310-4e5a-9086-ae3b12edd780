<template>
  <el-dialog :model-value="dialogVisible" :title="title+'工作站'" width="500px" :before-close="handleClose" :close-on-click-modal="false">
    <el-form label-width="120px" size="small" :rules="rules" :model="state.form" ref="formRef">
      <el-form-item label="工作站名称：" prop="name">
        <el-input placeholder="请输入" v-model="state.form.name"></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose" size="mini">取消</el-button>
        <el-button type="primary" v-if="type!='details'" @click="submit" size="mini">保存</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import {
  ElDialog,
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElMessage
} from "element-plus";
import { reactive, computed, watch, ref,nextTick } from "vue";
import { workStationAdd, workStationEdit } from "@/applications/eccard-iot/api";

export default {
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Object,
      default: null
    },
    type: {
      type: String,
      default: ""
    }
  },
  components: {
    ElDialog,
    ElButton,
    ElForm,
    ElFormItem,
    ElInput,
  },
  setup(props, context) {
    const formRef = ref(null)
    const state = reactive({
      form: {},
    });
    const rules = {
      name: [
        { required: true, message: "请输入工作站名称", },
        { max: 20, message: "工作站名称长度不能超过20字符", },
      ],
    }
    watch(() => props.dialogVisible, val => {
      if (val) {
        nextTick(() => {
          formRef.value.clearValidate()
        })
        state.form = { ...props.data }
      }
    })
    const title = computed(() => {
      if (props.type == 'add') {
        return '新增'
      } else if (props.type == 'edit') {
        return '编辑'
      } else {
        return ''
      }
    })
    const submit = () => {
      let fn = props.data.id ? workStationEdit : workStationAdd
      formRef.value.validate(async (valid) => {
        if (valid) {
          let { code, message } = await fn(state.form)
          if (code === 0) {
            ElMessage.success(message)
            context.emit("close", true);
          }
        } else {
          return false
        }
      })
    }
    const handleClose = () => {
      context.emit("close", false);
    };
    return {
      formRef,
      state,
      rules,
      title,
      submit,
      handleClose,
    };
  },
};
</script>

<style lang="scss" scoped>
.el-form {
  margin-top: 10px;
}
.table-box {
  border: 1px solid #eeeeee;
  border-radius: 0 0 8px 8px;
  .pagination {
    margin: 10px;
  }
}
.footer-box {
  margin-top: 10px;
  border: 1px solid #eeeeee;
  border-radius: 4px;
  .text-box {
    margin: 15px;
  }
  .el-form {
    margin-top: 20px;
  }
}
</style>