<template>
  <el-dialog
    :model-value="dialogVisible"
    :title="dialogTitle"
    width="40%"
    :before-close="handleClose"
  >
    <div class="bottom-box">
      <el-form inline label-width="300px" size="large" :disabled="dialogTitle==='通知公告详情'">
        <el-form-item label="消息类型：">
          <el-select :readonly="isReadOnly" v-model="state.form.CPU" placeholder="请选择">
            <el-option></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="接收区域：">
          <el-select :readonly="isReadOnly" v-model="state.form.CPU" placeholder="请选择">
            <el-option></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="接收楼栋：">
          <el-select :readonly="isReadOnly" v-model="state.form.CPU" placeholder="请选择">
            <el-option></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="接收单元：">
          <el-select :readonly="isReadOnly" v-model="state.form.CPU" placeholder="请选择">
            <el-option></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="消息标题：">
          <el-input :readonly="isReadOnly" v-model="state.form.CPU" placeholder="请输入">

          </el-input>
        </el-form-item>
        <el-form-item label="消息内容：">
          <el-input :readonly="isReadOnly" type="textarea" v-model="state.form.disk" placeholder="输入消息内容"></el-input>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button
          type="primary"
          v-if="dialogTitle !== '通知公告详情'"
          @click="handleClose"
          size="mini"
        >确定</el-button>
        <el-button v-else @click="$emit('edit')" size="mini">编辑</el-button>
        <el-button @click="handleClose" size="mini">返回</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import {ElDialog, ElButton, ElForm, ElFormItem, ElInput, ElSelect, ElOption} from "element-plus";
import {reactive} from "@vue/reactivity";
import {computed, watch} from "@vue/runtime-core";

export default {
  components: {
    ElDialog,
    ElButton,
    ElForm,
    ElFormItem,
    ElInput,
    ElSelect,
    ElOption,
  },
  props: {
    dialogVisible: {
      types: Boolean,
      default: false,
    },
    dialogTitle: {
      types: String,
      default: "服务器",
    },
    dialogData: {
      types: Object,
      default: {},
    },
  },
  setup(prop, context) {
    const state = reactive({
      form: {},
    });
    const isReadOnly = computed(() => {
      if (prop.dialogTitle === "违规违纪详情") {
        return true;
      } else {
        return false;
      }
    });

    watch(
      () => prop.dialogVisible,
      (val) => {
        if (val) {
          state.form = {...prop.dialogData};
          if (state.form.server) {
            state.form.server = state.form.server.split(",")
            state.form.server = state.form.server.map((item) => {
              return {
                value: item,
                checked: false
              }
            })
          }
        }

      }
    );

    const handleClose = () => {
      context.emit("close", false);
      state.form = {};
    };
    return {
      state,
      handleClose,
      isReadOnly,
    };
  },
};
</script>

<style lang="scss" scoped>
.server-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  min-width: 120px;

  .server-item {
    width: 20%;
    min-width: 120px;
    margin-bottom: 15px;
    border: 1px solid #eeeeee;
    border-radius: 5px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 4px;
  }
}

.bottom-box {
  margin-top: 10px;
  padding-left: 5px;
}

:deep(.el-dialog__header) {
  border-bottom: 1px solid #eeeeee;
}

</style>
