<template>
  <div class="login-details">
    <el-dialog :model-value="isShow" title='设备故障处理记录' width="600px" :before-close="handleClose">
      <el-form size="mini" label-width="120px" ref="formRef" :model="state.form" :rules="state.rules">
        <el-form-item label="设备机号：" prop="deviceNo">
          <el-input v-model.trim="state.form.deviceNo" maxlength="20" clearable disabled=true></el-input>
        </el-form-item>
        <el-form-item label="报修时间：" prop="lastModifyTime">
          <el-input v-model.trim="state.form.lastModifyTime" maxlength="20" clearable disabled=true></el-input>
        </el-form-item>
        <el-form-item label="报修内容：" prop="failureDetail">
          <el-input v-model.trim="state.form.failureDetail" maxlength="20" clearable disabled=true></el-input>
        </el-form-item>
        <el-form-item label="处理状态：">
          <el-select v-model="state.form.status" clearable style="width: 100%;" >
            <el-option v-for="(item, index) in statusList" :key="index" :label="item.label"
              :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="处理人员：">
          <el-input v-model.trim="state.form.userName" maxlength="20" clearable placeholder="请输入处理人员"></el-input>
        </el-form-item>
        <el-form-item label="处理说明：">
          <el-input type="textarea" :rows="2" placeholder="请输入处理说明" v-model="state.form.describe">
          </el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleClose" size="small">取消</el-button>
          <el-button type="primary" @click="handleSubmit" size="small" :loading="state.loading">确认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script>
import { reactive, ref, watch, nextTick } from "vue";
import {
  ElButton,
  ElDialog,
  ElForm,
  ElFormItem,
  ElInput,
  ElMessage,
  ElSelect,
  ElOption,
} from "element-plus";
import { useDict } from '@/hooks/useDict'
import {
  DeviceRepairManageEdit
} from "@/applications/eccard-iot/api";
export default {
  components: {
    ElDialog,
    ElForm,
    ElFormItem,
    ElInput,
    ElButton,
    ElSelect,
    ElOption,
  },
  props: {
    isShow: {
      types: Boolean,
      default: false,
    },
    rowData: {
      type: Object,
      default: () => { },
    },
  },
  setup(props, context) {
    const statusList = useDict('DORM_REPAIR_STATUS')
    const formRef = ref(null);
    const state = reactive({
      rules: {
        a: [{ required: true}],
      },
      loading: false,
      form: {

      },
    });
    watch(
      () => props.isShow,
      (val) => {
        if (val) {
          state.form = {
            ...props.rowData,
          };
          nextTick(() => {
            formRef.value.clearValidate();
          });
        }
      }
    );
    const handleClose = () => {
      context.emit("close", false);
    };
    const handleSubmit = () => {
      formRef.value.validate(async (valid) => {
        if (valid) {
          state.loading = true;
          try {
            let { code, message } = await DeviceRepairManageEdit(state.form);
            if (code === 0) {
              ElMessage.success(message);
              context.emit("close", true);
            }
            state.loading = false;
          } catch {
            state.loading = false;
          }
        }
      });
    };
    return {
      statusList,
      formRef,
      state,
      handleClose,
      handleSubmit,
    };
  },
};
</script>
<style lang="scss" scoped>
.el-form {
  margin-top: 20px;
}
</style>