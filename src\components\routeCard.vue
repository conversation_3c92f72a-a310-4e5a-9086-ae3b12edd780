<template>
  <el-card :class="['kade-route-card', className, { full }]">
    <template v-if="$slots.header" #header>
      <slot name="header" />
    </template>
    <slot />
  </el-card>
</template>
<script>
// 路由包裹组件，现在改成tab形式，纯包裹组件
import { ElCard } from 'element-plus';
export default {
  name: 'kade-route-card',
  components: {
    'el-card': ElCard,
  },
  props: {
    full: {
      type: Boolean,
      default: false,
    },
    className: {
      type: String,
      default: '',
    } 
  }
}
</script>
<style lang="scss">
  .kade-route-card{
    width: 100%;
    height: 100%;
    border: none;
    &.full{
      .el-card__body{
        height: calc(100% - 40px);
      }
    }
    .el-card__body{
      overflow-y: auto;
      box-sizing: border-box;
      height: 100%;
    }
    .kade-card-header{
      display: flex;
      justify-content: flex-start;
      align-items: center;
      position: relative;
      i{
        margin-right: 5px;
      }
      .extra{
        position: absolute;
        margin: auto;
        height: 56px;
        top: 0;
        bottom: 0;
        right: 0;
        display: flex;
        justify-content: flex-end;
        align-items: center;
      }
    }    
  }
</style>