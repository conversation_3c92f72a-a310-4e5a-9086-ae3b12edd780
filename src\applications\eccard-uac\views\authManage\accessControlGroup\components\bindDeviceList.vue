<template>
  <div class="bind-device-box" v-loading="state.loading">
    <kade-table-filter @search="handleSearch" @reset="handleReset">
      <el-form inline label-width="90px" size="mini">
        <el-form-item label="区域">
          <kade-area-select-tree style="width: 100%" :value="state.form.areaPath" valueKey="areaPath" :multiple="false"
            @valueChange="(val) => (state.form.areaPath = val.areaPath)" />
        </el-form-item>
        <el-form-item label="设备类型">
          <el-select v-model="state.form.deviceType" clearable>
            <el-option v-for="(item, index) in state.deviceTypeList" :key="index" :label="item.cfgValue"
              :value="item.cfgKey">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="设备机号">
          <el-input placeholder="请输入" v-model="state.form.deviceNo" clearable></el-input>
        </el-form-item>
        <el-form-item label="设备名称">
          <el-input placeholder="请输入" v-model="state.form.deviceName" clearable></el-input>
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="组内门禁列表" v-loading="state.loading">
      <template #extra>
        <el-button @click="state.isBind = true" class="btn-green" icon="el-icon-plus" size="mini"
          :disabled="!rowData.id">添加门禁</el-button>
        <el-button @click="handleBatchDel()" class="btn-purple" icon="el-icon-close" size="mini">批量删除</el-button>
      </template>
      <el-table :data="state.dataList" @selection-change="handleSelectChange" border>
        <el-table-column prop="" show-overflow-tooltip type="selection" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="areaName" label="区域" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="deviceTypeName" label="设备类型" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="deviceNo" label="设备机号" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="deviceName" label="设备名称" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="doorNo" label="控制门编号" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="createTime" label="绑定时间" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip label="操作" align="center">
          <template #default="scope">
            <el-button @click="handleDel(scope.row)" type="text" size="mini">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination v-model:currentPage="state.form.currentPage" v-model:page-size="state.form.pageSize"
          :page-sizes="[6,10, 20, 30, 40]" :small="small" :disabled="disabled" background
          layout="total, sizes, prev, pager, next, jumper" :total="state.total" @size-change="handleSizeChange"
          @current-change="handleCurrentChange" />
      </div>
    </kade-table-wrap>
    <kade-bind-device v-model="state.isBind" :rowData="rowData" @update:modelValue="close" />
  </div>
</template>
<script>
import { ElButton, ElForm, ElFormItem, ElInput, ElSelect, ElOption, ElTable, ElTableColumn, ElPagination, ElMessageBox, ElMessage } from "element-plus"
import { reactive, onMounted } from 'vue'
import { acsDeviceGroupList, acsDeviceGroupDel, acsDeviceGroupBatchDel } from "@/applications/eccard-uac/api";
import { iotCfg } from "@/applications/eccard-iot/api";
import bind from "./bind.vue"
import { watch } from '@vue/runtime-core';
import areaSelectTree from "@/components/tree/areaSelectTree.vue";
export default {
  components: {
    ElButton, ElTable, ElTableColumn, ElPagination, ElForm, ElFormItem, ElInput, ElSelect, ElOption,
    "kade-bind-device": bind,
    "kade-area-select-tree": areaSelectTree,
  },
  props: {
    rowData: {
      type: Object,
      default: null
    }
  },
  setup(props) {
    const state = reactive({
      loading: false,
      isBind: false,
      deviceTypeList: [],
      dataList: [],
      selectList: [],
      form: {
        currentPage: 1,
        pageSize: 6
      },
      total: 0
    })

    watch(() => props.rowData, val => {
      if (val.id) {
        state.isBind = false
        getList()
      }
    })
    const getDeviceTypeList = async () => {
      let { data } = await iotCfg({ cfgType: 'ACCESS_DEVICE_TYPE' });
      state.deviceTypeList = data
    }
    const getList = async () => {
      for (let key in state.form) {
        if (!state.form[key]) {
          delete state.form[key]
        }
      }
      state.form.groupId = props.rowData.id
      state.loading = true
      try {
        let { data: { list, total } } = await acsDeviceGroupList(state.form)
        state.dataList = list
        state.total = total
        state.loading = false
      }
      catch {
        state.loading = false
      }
    }
    const handleSelectChange = (val) => {
      state.selectList = val
    }
    const handleDel = row => {
      ElMessageBox.confirm("确认删除?", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        let { code, message } = await acsDeviceGroupDel(row.id);
        if (code === 0) {
          ElMessage.success(message);
          getList();
        }
      });
    }
    const handleBatchDel = () => {
      if (!state.selectList.length) {
        return ElMessage.error("请选择需要删除的设备！")
      }
      ElMessageBox.confirm("确认删除已选择设备?", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        let { code, message } = await acsDeviceGroupBatchDel(state.selectList.map(item => item.id));
        if (code === 0) {
          ElMessage.success(message);
          getList();
        }
      });
    }
    const handleSearch = () => {
      getList()
    }
    const handleReset = () => {
      state.form = {
        currentPage: 1,
        pageSize: 6
      }
    }
    const handleCurrentChange = (val) => {
      state.form.currentPage = val
      getList()
    };
    const handleSizeChange = (val) => {
      state.form.currentPage = 1
      state.form.pageSize = val
      getList()
    };
    const close = val => {
      if (val) {
        getList()
      }
      state.isBind = false
    }
    onMounted(() => {
      if (props.rowData.id) {
        getList()
      }
      getDeviceTypeList()
    })
    return {
      state,
      handleSelectChange,
      handleDel,
      handleBatchDel,
      handleSearch,
      handleReset,
      handleCurrentChange,
      handleSizeChange,
      close
    }
  }
}
</script>
<style lang="scss" scoped>
.bind-device-box {
  width: 100%;
  height: 100%;
}
</style>