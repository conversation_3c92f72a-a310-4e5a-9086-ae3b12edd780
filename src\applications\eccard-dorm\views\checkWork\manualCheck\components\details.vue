<template>
  <el-dialog :model-value="dialogAdd" title="考勤补卡" width="1200px" :before-close="handleClose">
    <el-form inline size="mini" label-width="80px">
      <el-form-item label="区域">
        <el-select clearable v-model="state.form.area" placeholder="全部">
          <el-option v-for="(item, index) in 10" :key="index" :label="item" :value="index"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="楼栋">
        <el-select clearable v-model="state.form.area" placeholder="全部">
          <el-option v-for="(item, index) in 10" :key="index" :label="item" :value="index"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="单元">
        <el-select clearable v-model="state.form.area" placeholder="全部">
          <el-option v-for="(item, index) in 10" :key="index" :label="item" :value="index"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="楼层">
        <el-select clearable v-model="state.form.area" placeholder="全部">
          <el-option v-for="(item, index) in 10" :key="index" :label="item" :value="index"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="人员编号">
        <el-input clearable v-model="state.form.id"></el-input>
      </el-form-item>
      <el-form-item label="人员姓名">
        <el-input clearable v-model="state.form.name"></el-input>
      </el-form-item>
      <el-form-item label="考勤日期">
        <el-date-picker v-model="state.form.date" type="date"></el-date-picker>
      </el-form-item>
      <el-form-item label="考勤时段">
        <el-select clearable v-model="state.form.area" placeholder="全部">
          <el-option v-for="(item, index) in 10" :key="index" :label="item" :value="index"></el-option>
        </el-select>
      </el-form-item>
      <el-button @click="search()" size="mini" icon="el-icon-search" type="primary">搜索</el-button>
    </el-form>
    <div class="table-box">
      <el-table border>
        <el-table-column label="人员编号" align="center"></el-table-column>
        <el-table-column label="人员姓名" align="center"></el-table-column>
        <el-table-column label="组织机构" align="center"></el-table-column>
        <el-table-column label="考勤日期" align="center"></el-table-column>
        <el-table-column label="考勤时段" align="center"></el-table-column>
        <el-table-column label="考勤结果" align="center"></el-table-column>
        <el-table-column label="区域" align="center"></el-table-column>
        <el-table-column label="房间" align="center"></el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination v-model:currentPage="currentPage4" v-model:page-size="pageSize4" :page-sizes="[10, 20, 30, 40]"
          background layout="total, sizes, prev, pager, next, jumper" :total="100" @size-change="handleSizeChange"
          @current-change="handleCurrentChange">
        </el-pagination>
      </div>
    </div>
    <div class="form-box">
      <div class="top-box">复核信息</div>
      <el-form inline label-width="80px" size="mini" :rules="rules" :model="state.form" ref="formRef">
        <el-form-item label="人员编号">
          <el-input clearable v-model="state.form"></el-input>
        </el-form-item>
        <el-form-item label="人员姓名">
          <el-input clearable v-model="state.form"></el-input>
        </el-form-item>
        <el-form-item label="复核日期">
          <el-date-picker v-model="state.form.date" type="date"></el-date-picker>
        </el-form-item>
        <el-form-item label="组织机构">
          <kade-dept-select-tree style="width: 100%" :value="state.form.deptPath" valueKey="deptPath" :multiple="false"
            @valueChange="(val) => (state.form.deptPath = val.deptPath)" />
        </el-form-item>
        <el-form-item label="复核时段">
          <el-input clearable v-model="state.form.time"></el-input>
        </el-form-item>
        <el-form-item label="复核结果" prop="result">
          <el-select clearable v-model="state.form.result" placeholder="全部">
            <el-option v-for="(item, index) in 10" :key="index" :label="item" :value="index"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="复核人员" prop="personal">
          <el-input clearable v-model="state.form.personal"></el-input>
        </el-form-item>
      </el-form>
    </div>
    <div class="text">备注：复核之后需在考勤分析中手动重新分析，产生新的考勤结果</div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose" size="mini">取消</el-button>
        <el-button type="primary" @click="add()" size="mini">确认</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import {
  ElButton,
  ElDialog,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElDatePicker,
  ElTable,
  ElTableColumn,
  ElPagination,
} from "element-plus";
import { reactive, ref } from "@vue/reactivity";
import deptSelectTree from '@/components/tree/deptSelectTree'
const rules = {
  personal: [
    {
      required: true,
      message: '请输入人员',
      trigger: 'blur',
    },
  ],
  result: [
    {
      required: true,
      message: '请选择结果',
      trigger: 'change',
    },
  ]
}
export default {
  props: {
    dialogAdd: {
      type: Boolean,
      default: false,
    },
  },
  components: {
    ElDialog,
    ElButton,
    ElForm,
    ElFormItem,
    ElInput,
    ElSelect,
    ElOption,
    ElDatePicker,
    ElTable,
    ElTableColumn,
    ElPagination,
    "kade-dept-select-tree": deptSelectTree
  },
  setup(props, context) {
    const formRef = ref(null)
    const state = reactive({
      form: {},
    });
    const handleClose = () => {
      context.emit("off", false);
      formRef.value.clearValidate()
    };
    const search = () => { };
    const add = () => {
      formRef.value.validate(valid => {
        if (valid) {
          console.log(state.form);
        } else {
          return false
        }
      })
    }

    return {
      rules,
      formRef,
      state,
      search,
      add,
      handleClose,
    };
  },
};
</script>
<style lang="scss" scoped>
.el-form {
  margin-top: 20px;
}

.table-box {
  border: 1px solid #eeeeee;
  margin-top: 20px;

  .pagination {
    margin: 10px;
  }
}

.form-box {
  border: 1px solid #eeeeee;
  margin-top: 15px;

  .top-box {
    border-bottom: 1px solid #eeeeee;
  }
}

.text {
  color: rgb(199, 8, 8);
  margin-top: 15px;
  font-size: 12px;
}

:deep(.el-input--mini .el-input__inner) {
  width: 180px !important;
}

:deep(.el-date-editor.el-input, .el-date-editor.el-input__inner) {
  width: 180px !important;
}

:deep(.el-pagination__editor.el-input .el-input__inner) {
  width: 46px;
}

:deep(.el-pagination .el-select .el-input .el-input__inner) {
  width: 100px !important;
}

:deep(.el-pagination__editor.el-input .el-input__inner) {
  width: 46px !important;
}

.el-table {
  border-right: none;
  border-top: none;
}
</style>