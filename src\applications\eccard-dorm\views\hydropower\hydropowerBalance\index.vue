<template>
    <kade-route-card>
        <kade-table-filter @search="handleSearch" @reset="handleReset">
            <el-form inline label-width="90px" size="mini">
                <el-form-item label="区域">
                    <kade-area-select-tree placeholder="全部" style="width: 100%" :value="state.form.areaPath" valueKey="areaPath" :multiple="true" @valueChange="(val)=>state.form.areaPath=val" />
                </el-form-item>
                <el-form-item label="楼栋类型">
                    <el-select v-model="state.form.b" placeholder="全部">
                        <el-option :label="item" :value="item" v-for="(item,index) in 10" :key="index"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="楼栋">
                    <el-select v-model="state.form.c" placeholder="全部">
                        <el-option :label="item" :value="item" v-for="(item,index) in 10" :key="index"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="单元">
                    <el-select v-model="state.form.e" placeholder="全部">
                        <el-option :label="item" :value="item" v-for="(item,index) in 10" :key="index"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="楼层">
                    <el-select v-model="state.form.f" placeholder="全部">
                        <el-option :label="item" :value="item" v-for="(item,index) in 10" :key="index"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="房间">
                    <el-input v-model="state.form.g" placeholder="请输入"></el-input>
                </el-form-item>
            </el-form>
        </kade-table-filter>
        <div class="total-box">
            <div class="total-item">
                <span>用水总量：</span>
                <div></div>
            </div>
            <div>
                <span>用电总量：</span>
                <div></div>
            </div>
        </div>
        <kade-table-wrap title="记录列表">
            <el-table :data="state.dataList" border>
                <el-table-column show-overflow-tooltip v-for="(item,index) in column" :key="index" :label="item.label" :prop="item.prop" :width="item.width" align="center"></el-table-column>
            </el-table>
            <div class="pagination">
                <el-pagination
                background
                :current-page="state.form.currentPage"
                layout="total, sizes, prev, pager, next, jumper" 
                :page-sizes="[10, 20, 50, 100, 200]" :total="state.total" 
                :page-size="state.form.pageSize" 
                @current-change="handlePageChange" 
                @size-change="handleSizeChange">
                </el-pagination>
            </div>
        </kade-table-wrap>
    </kade-route-card>
</template>

<script>
import { reactive } from "@vue/reactivity";
import {
    ElForm,
    ElFormItem,
    ElInput,
    ElSelect,
    ElOption,
    ElTable,
    ElTableColumn,
    ElPagination,
} from "element-plus";
import areaSelectTree from "@/components/tree/areaSelectTree";
import { onMounted } from "@vue/runtime-core";

const column = [
    { label: "区域", prop: "" },
    { label: "房间", prop: "a", width: "300px" },
    { label: "水费余额", prop: "" },
    { label: "水费余额更新时间", prop: "" },
    { label: "电费余额", prop: "" },
    { label: "电费余额更新时间", prop: "" },
];

export default {
    components: {
        ElForm,
        ElFormItem,
        ElInput,
        ElSelect,
        ElOption,
        ElTable,
        ElTableColumn,
        ElPagination,
        "kade-area-select-tree": areaSelectTree,
    },
    setup() {
        const state = reactive({
            form: {
                pageSize: 10,
                currentPage: 1,
            },
            dataList: [{ a: "1" }],
            total: 0,
        });
        const getList = () => {
            console.log(state.form);
        };
        const handleSearch = () => {
            getList();
        };
        const handleReset = () => {
            state.form = {
                currentPage: 1,
                pageSize: 10,
            };
        };
        const handlePageChange = (val) => {
            state.form.currentPage = 1;
            state.form.pageSize = val;
            getList;
        };
        const handleCurrentChange = (val) => {
            state.form.currentPage = val;
            getList();
        };
        onMounted(() => {
            getList();
        });
        return {
            state,
            column,
            handleSearch,
            handleReset,
            handlePageChange,
            handleCurrentChange,
        };
    },
};
</script>

<style lang="scss" scoped>
.total-box {
    display: flex;
    align-items: center;
    padding: 0 0 15px 10px;
    .total-item {
        margin-right: 60px;
    }
}
:deep(.el-input--mini .el-input__inner) {
    width: 182px;
}
:deep(.el-pagination .el-select .el-input .el-input__inner) {
    width: 100px;
}
:deep(.el-pagination__editor.el-input .el-input__inner) {
    width: 46px;
}
</style>