<template>
  <div class="room-msg">
    <div class="room-msg-title">房间考勤结果</div>
    <el-carousel height="100%" direction="vertical" :autoplay="true" :interval="1000" indicator-position="none">
      <el-carousel-item v-for="item in 4" :key="item" style="color:#fff">
        <div class="room-box">
          <div class="room-item" v-for="(item, index) in 60" :key="index">
            <div class="room">{{item}}</div>
            <div class="room-num">归寝：6/6</div>
          </div>
        </div>
      </el-carousel-item>
    </el-carousel>
  </div>
</template>
<script>
import { ElCarousel, ElCarouselItem } from "element-plus"
export default {
  components: {
    ElCarousel, ElCarouselItem
  },
  setup() {

  }
}
</script>
<style lang="scss" scoped>
.room-msg {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .room-msg-title {
    font-weight: 700;
    font-size: 18px;
    color: #0EE4F9;
    margin: 5px 0 10px;
  }

  .room-box {
  background: #001034;

    width: 100%;
    height: 100%;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    overflow-y: auto;
    .room-item {
      box-sizing: border-box;
      padding: 5px;
      width: 16%;
      background: #0f9a6c;
      margin-bottom: 10px;

      .room {
        font-size: 36px;
        color: #FFFFFF;
        text-align: center;
      }

      .room-num {
        font-size: 18px;
        color: #FFFFFF;
        text-align: center;
      }

    }
  }

}

.el-carousel {
  width: 100%;
  height: 100%;
}
</style>