<template>
  <div class="box-dialog" v-loading="state.loading">
    <kade-table-wrap title="退款人员信息">
      <el-table style="width: 100%" :data="[selectPerson]" border stripe>
        <el-table-column show-overflow-tooltip width="150" label="用户编号" prop="userCode" align="center">
        </el-table-column>
        <el-table-column show-overflow-tooltip prop="userName" label="姓名" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip label="组织机构" prop="deptName" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip label="身份类别" prop="userRoleName" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip label="账户状态" prop="acctStatus" align="center">
          <template #default="scope">
            {{
                filterDictionary(scope.row.acctStatus, state.accountStatusList)
            }}
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip label="卡片类别" prop="ctName" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip label="卡片状态" align="center"><template #default="scope">
            {{ filterDictionary(scope.row.cardStatus, state.cardStatusList) }}
          </template></el-table-column>
        <el-table-column width="153" label="联系方式" prop="userTel" align="center"></el-table-column>
      </el-table>
    </kade-table-wrap>
    <kade-table-wrap title="退款清单" style="margin-top: 10px">
      <el-table style="width: 100%" :data="refundList" v-loading="false" border stripe>
        <el-table-column label="退款项目" prop="walletName" align="center"></el-table-column>
        <el-table-column label="项目余额" prop="walletBalance" align="center">
          <template #default="scope">
            <div>
              {{ scope.row.walletBalance + "元" }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="退款比例" prop="refundRatio" align="center">
          <template #default="scope">
            {{ scope.row.refundRatio + "%" }}
          </template>
        </el-table-column>
        <el-table-column label="实退金额" prop="actualRefundAmount" align="center"><template #default="scope">
            <div>
              {{ scope.row.actualRefundAmount + "元" }}
            </div>
          </template></el-table-column>
      </el-table>
      <el-form label-width="120px">
        <el-form-item label="退款合计：">
          <span>{{ refundTotalAmount }}元</span>
        </el-form-item>
        <el-form-item label="退款方式：">
          <el-radio-group v-model="state.form.refundMode">
            <el-radio v-for="(item, index) in state.refundModeList" :key="index" :label="item.dictCode">{{
                item.dictValue
            }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="退款说明：">
          <el-input v-model="state.form.refundRemark" type="textarea" placeholder="请输入退款说明"></el-input>
        </el-form-item>
      </el-form>
    </kade-table-wrap>
    <div class="submit-btn">
      <el-button @click="off()" size="mini">取&nbsp;&nbsp;消</el-button>
      <el-button @click="submitForm()" size="mini" type="primary">确&nbsp;&nbsp;认</el-button>
    </div>
  </div>
</template>
<script>
import {
  ElTable,
  ElTableColumn,
  ElRadioGroup,
  ElRadio,
  ElForm,
  ElFormItem,
  ElButton,
  ElInput,
  ElMessage,
} from "element-plus";
import { reactive } from "@vue/reactivity";
import { dateStr } from "@/utils/date.js";
import { personRefund } from "@/applications/eccard-finance/api";
import { computed, onMounted, watch } from "@vue/runtime-core";
import { useStore } from "vuex";
export default {
  components: {
    ElTable,
    ElTableColumn,
    ElForm,
    ElFormItem,
    ElButton,
    ElRadioGroup,
    ElRadio,
    ElInput,
  },
  props: {
    accountStrategyList: {
      types: Array,
      default: [],
    },
    cardTypeList: {
      types: Array,
      default: [],
    },
    walletList: {
      types: Array,
      default: [],
    },
  },
  setup(props, context) {
    const store = useStore();
    const state = reactive({
      loading: false,
      form: {
        refundMode: null,
        refundRemark: null,
        refundTotalAmount: null,
        userId: store.state.refundData.selectPerson.userId,
      },
      accountStrategy: "",
      accountStatusList: JSON.parse(
        localStorage.getItem("kade_cache_dictionary")
      ).filter((item) => item.dictType == "PERSON_ACCOUNT_STATUS"), //账户状态
      walletTypeList: JSON.parse(
        localStorage.getItem("kade_cache_dictionary")
      ).filter((item) => item.dictType == "PERSON_WALLET_TYPE"), //钱包类型
      walletStatusList: JSON.parse(
        localStorage.getItem("kade_cache_dictionary")
      ).filter((item) => item.dictType == "PERSON_WALLET_STATUS"), //钱包状态
      refundModeList: JSON.parse(
        localStorage.getItem("kade_cache_dictionary")
      ).filter((item) => item.dictType == "PERSON_REFUND_MODE"), //退款方式
      cardStatusList: JSON.parse(
        localStorage.getItem("kade_cache_dictionary")
      ).filter((item) => item.dictType == "PERSON_CARD_STATUS"), //卡片状态
    });
    const timeStrDate = computed(() => {
      return dateStr;
    });

    const selectPerson = computed(() => {
      return store.state.refundData.selectPerson;
    });

    const refundList = computed(() => {
      return store.state.refundData.refundList;
    });
    const refundTotalAmount = computed(() => {
      return store.state.refundData.refundTotalAmount;
    });

    watch(
      () => store.state.refundData.isOff,
      (val) => {
        console.log(val);
        if (!val) {
          state.form = {
            refundMode: null,
            userId: null,
            refundRemark: null,
            refundTotalAmount: null,
          };
        } else {
          queryPersonRefundList();
        }
      }
    );
    const queryPersonRefundList = () => {
      let data = {
        userId: store.state.refundData.selectPerson.userId,
      };
      store.dispatch("refundData/queryPersonRefundList", data);
    };
    const filterDictionary = (val, dictionaryList) => {
      for (let i of dictionaryList) {
        if (val == i.dictCode) {
          return i.dictValue;
        }
      }
    };

    const off = () => {
      context.emit("off", false);
    };
    const submitForm = () => {
      if (!state.form.refundMode) {
        return ElMessage.error("请选择退款方式！");
      }
      if (!state.form.refundRemark) {
        return ElMessage.error("请输入退款说明！");
      }
      state.form.userId = store.state.refundData.selectPerson.userId;
      state.form.refundList = store.state.refundData.refundList;
      state.form.refundTotalAmount = store.state.refundData.refundTotalAmount;
      state.loading = true
      personRefund(state.form)
        .then(({ code, message }) => {
          if (code === 0) {
            ElMessage.success(message);
            context.emit("success", true);
            store.dispatch("refundData/queryRefundRecordList", {
              userId: store.state.refundData.selectPerson.userId,
            });

            state.form = {
              refundMode: null,
              userId: null,
              refundRemark: null,
              refundTotalAmount: null,
            };
          } else {
            ElMessage.error(message);
          }
          state.loading = false
        })
        .catch(() => {
          state.loading = false

        });
    };
    onMounted(() => {
      queryPersonRefundList();
    });
    return {
      state,
      timeStrDate,
      selectPerson,
      refundList,
      refundTotalAmount,
      filterDictionary,
      off,
      submitForm,
    };
  },
};
</script>
<style lang="scss" scoped >
.box-dialog {
  padding: 10px 0;

  .el-table__row {
    height: 30px !important;
  }

  .account-tips {
    text-align: center;
    margin-bottom: 10px;
    color: #f00;
  }
}

.table-box {
  padding: 10px;
}

.submit-btn {
  text-align: center;
  margin: 10px auto;
}

.el-form-item {
  margin-bottom: 10px;
}
</style>