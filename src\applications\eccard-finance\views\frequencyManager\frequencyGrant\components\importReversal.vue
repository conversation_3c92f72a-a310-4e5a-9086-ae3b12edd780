<template>
  <div>
    <el-dialog :model-value="dialogVisible" title="导入冲正" width="90%" :before-close="beforeClose"
      :close-on-click-modal="false">
      <div>
        <div class="padding-form-box">
          <el-form inline size="small" ref="ruleForm" :model="state.form" :rules="state.rules" label-width="100px">
            <el-form-item label="次数类型:" prop="frequencyType">
              <el-select clearable v-model="state.form.frequencyType" placeholder="请选择">
                <el-option v-for="(item, index) in frequencyTypeList" :key="index" :label="item.ftName"
                  :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="项目名称:" prop="projectName">
              <el-input placeholder="请输入" v-model="state.form.projectName"></el-input>
            </el-form-item>

            <el-form-item label="交易类型:" prop="costType">
              <el-select disabled v-model="state.form.costType" placeholder="请选择">
                <el-option v-for="(item, index) in state.tradeTypeList" :key="index" :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-form>
          <el-form size="small" label-width="100px">
            <el-form-item label="发放说明:">
              <el-input placeholder="请输入" type="textarea" v-model="state.form.grantRemark"></el-input>
            </el-form-item>
          </el-form>
          <el-form inline size="small" label-width="100px">
            <el-form-item label=" 上传文件:">
              <el-upload class="upload-demo" :action="state.requestUrl"
                :headers="{ Authorization: `bearer ${state.requestHeader}` }" :on-preview="handlePreview"
                :on-remove="handleRemove" :on-success="handleSuccess" :before-remove="beforeRemove" :limit="3"
                :on-exceed="handleExceed" :file-list="fileList" ref="uploadDom">
                <el-button size="small" type="primary">上传充值清单</el-button>
              </el-upload>
            </el-form-item>
            <el-form-item>
              <div class="blue" @click="uploadSample()">下载样例</div>
            </el-form-item>
          </el-form>
          <div v-if="state.abnormalData > 0">
            <span class="red">•</span> 异常数据
            <span class="red">{{ state.abnormalData }}</span> 条，请修改确认
          </div>
          <el-table style="width: 100%" :data="state.WaitFrequencyList" v-loading="WaitFrequencyLoading" border stripe>
            <el-table-column label="校检结果" prop="verifyResult" align="center"></el-table-column>
            <el-table-column label="用户编号" prop="userCode" align="center"></el-table-column>
            <el-table-column label="姓名" prop="userName" align="center"></el-table-column>
            <el-table-column label="组织机构" prop="deptName" align="center"></el-table-column>
            <el-table-column label="身份类别" prop="roleName" align="center"></el-table-column>
            <el-table-column label="生效时间" prop="takeEffectTime" align="center"></el-table-column>
            <el-table-column label="发放金额（元）" prop="rightingAmount" align="center"></el-table-column>
            <el-table-column label="有效期至" prop="invalidTime" align="center"></el-table-column>
            <el-table-column label="操作" prop="userName" align="center">
              <template #default="scope">
                <div @click="delClick(scope.row)">删除</div>
              </template>
            </el-table-column>
          </el-table>
          <div class="pagination-box">
            <div>
              <span>合计：<span>{{ state.totalCount }}</span>人，<span>{{ state.totalAmount }}</span>元</span>
            </div>
            <div class="pagination">
              <el-pagination background :current-page="state.WaitFrequencyForm.currentPage"
                :page-size="state.WaitFrequencyForm.pageSize" layout="total, sizes, prev, pager, next, jumper"
                :page-sizes="[6, 10, 20, 50, 100]" :total="state.WaitFrequencyListTotal"
                @current-change="handlePageChange" @size-change="handleSizeChange">
              </el-pagination>
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="beforeClose()" size="mini">关&nbsp;&nbsp;闭</el-button>
          <el-button @click="submitForm()" size="mini" type="primary">确&nbsp;&nbsp;认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script>
import { onMounted, reactive, ref, watch, computed } from "vue";
import { getToken } from "@/utils";
import {
  saveImportFrequencyRightingList,
  getWaitFrequencyRightingList,
  deleteWaitFrequencyRighting,
  exportFrequencyRightingList,
} from "@/applications/eccard-finance/api";

import { useStore } from "vuex";

import {
  ElDialog,
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElTable,
  ElTableColumn,
  ElPagination,
  ElUpload,
  ElMessage,
} from "element-plus";
export default {
  components: {
    "el-dialog": ElDialog,
    "el-button": ElButton,
    "el-input": ElInput,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    "el-select": ElSelect,
    "el-option": ElOption,
    "el-table": ElTable,
    "el-table-column": ElTableColumn,
    "el-pagination": ElPagination,
    ElUpload,
  },
  props: {
    dialogVisible: {
      types: Boolean,
      default: false,
    },
    personListData: {
      types: Object,
      default: {},
    },
  },
  setup(props, context) {
    const uploadDom = ref(null);
    const store = useStore();
    let ruleForm = ref(null);
    const state = reactive({
      WaitFrequencyLoading: false,
      grantType: "IMPORT",
      form: {
        costType:102
      },
      generateFrequencyForm: {},
      rules: {
        frequencyType: [
          {
            required: true,
            message: "请选择次数类型",
            trigger: "change",
          },
        ],
        projectName: [
          {
            required: true,
            message: "请输入项目名称",
            trigger: "blur",
          },
        ],
        costType: [
          {
            required: true,
            message: "请选择交易类型",
            trigger: "change",
          },
        ],
        grantType: [
          {
            required: true,
            message: "请选择发放方式",
            trigger: "change",
          },
        ],
      },
      WaitFrequencyForm: {
        currentPage: 1,
        pageSize: 6,
      },
      WaitFrequencyList: [],
      WaitFrequencyListTotal: 0,
      totalCount: 0,
      totalAmount: 0,
      requestHeader: getToken(),
      requestUrl: `${CONFIG.BASE_API_PATH}eccard-finance/frequencyRecord/importFrequencyRightingList`,
      generateListForm: {
        deptPaths: [],
        rechargeAmount: 0,
        userRole: 0,
        cardTypes: [],
      },

      personList: props.personListData.list,
      total: props.personListData.total,
      roleList: [],
      departCheckList: [],
      frequencyTypeList: [],
      tradeTypeList: [
        // { label: "充值", value: 101 },
        {
          label: "冲正",
          value: 102,
        },
      ],
    });
    const frequencyTypeList = computed(() => {
      return store.state.frequencyData.frequencyTypeList;
    });
    watch(
      () => props.dialogVisible,
      (val) => {
        if (!val) {
          state.WaitFrequencyForm = {
            currentPage: 1,
            pageSize: 6,
          };
          state.WaitFrequencyListTotal = 0;
          state.totalCount = 0;
          state.totalAmount = 0;
          state.WaitFrequencyList = [];
          ruleForm.value.resetFields();
          uploadDom.value.clearFiles();
          state.WaitFrequencyListTotal = 0;
          state.totalCount = 0;
          state.totalAmount = 0;
          state.abnormalData = 0;
        }
      }
    );

    //获取名单
    const queryWaitFrequencyListByPage = () => {
      state.WaitFrequencyLoading = true;
      getWaitFrequencyRightingList(state.WaitFrequencyForm)
        .then(({ data: { generateFrequency, pageInfo } }) => {
          state.totalCount = generateFrequency.totalCount;
          state.totalAmount = generateFrequency.totalAmount;
          state.abnormalData = generateFrequency.errorCount;
          state.WaitFrequencyList = pageInfo;
          state.WaitFrequencyListTotal = generateFrequency.totalCount;
          state.WaitFrequencyLoading = false;
        })
        .catch(() => { });
      state.WaitFrequencyLoading = false;
    };

    const delClick = (val) => {
      console.log(val);
      let data = {
        projectNo: state.form.projectNo,
        id: parseInt(val.id),
      };
      deleteWaitFrequencyRighting(data).then((res) => {
        let { totalCount, totalAmount, errorCount } = res.data.data;
        state.totalCount = totalCount;
        state.totalAmount = totalAmount;
        state.abnormalData = errorCount;
        queryWaitFrequencyListByPage();
      });
    };

    const uploadSample = async () => {
      let res = await exportFrequencyRightingList();
      let url = window.URL.createObjectURL(
        new Blob([res], { type: "application/vnd.ms-excel" })
      );
      let link = document.createElement("a");
      link.style.display = "none";
      link.href = url;
      link.setAttribute("download", "导入次数冲正清单样例.xlsx");
      document.body.appendChild(link);
      link.click();
    };

    const submitForm = async () => {
      console.log(ruleForm);
      ruleForm.value.validate(async (valid) => {
        if (valid) {
          if (!state.form.projectNo) {
            return ElMessage.error("请先生成冲正清单！");
          }
          state.form.grantMode = "IMPORT";
          let data = { ...state.form, ...state.generateFrequencyForm };
          data.relationProjectId = store.state.frequencyData.selectRow.id;
          console.log(data);
          let { code, message } = await saveImportFrequencyRightingList(data);
          if (code === 0) {
            ElMessage.success(message);
            context.emit("offImportReversal", true);
          } else {
            ElMessage.error(message);
          }
        } else {
          return false;
        }
      });
    };
    const handleSuccess = (res) => {
      console.log(res);
      state.WaitFrequencyForm.projectNo = res.data.projectNo;
      state.form.projectNo = res.data.projectNo;
      state.abnormalData = res.data.errorCount;
      state.totalCount = res.data.totalCount;
      state.totalAmount = res.data.totalAmount;
      queryWaitFrequencyListByPage();
    };
    const handleRemove = () => {
      state.WaitFrequencyList = [];
      state.WaitFrequencyListTotal = 0;
      state.abnormalData = 0;
      state.totalCount = 0;
      state.totalAmount = 0;
    };

    const beforeClose = () => {
      context.emit("offImportReversal", false);
    };

    const handlePageChange = (val) => {
      state.WaitFrequencyForm.currentPage = val;
      queryWaitFrequencyListByPage();
    };
    const handleSizeChange = (val) => {
      state.WaitFrequencyForm.currentPage = 1;
      state.WaitFrequencyForm.pageSize = val;
      queryWaitFrequencyListByPage();
    };

    onMounted(() => { });
    return {
      state,
      frequencyTypeList,
      uploadDom,
      ruleForm,
      submitForm,
      beforeClose,
      uploadSample,
      delClick,
      handlePageChange,
      handleSizeChange,
      handleSuccess,
      handleRemove,
    };
  },
};
</script>
<style lang="scss" scoped>
:deep(.el-dialog__header) {
  border-bottom: 1px solid #efefef;
}

:deep(.el-overlay) {
  position: absolute;
  background-color: rgba(0, 0, 0, 0);
}

:deep(.el-dialog__footer) {
  text-align: center;
}

.select-input-lang {
  .el-select {
    width: 500px;
  }
}

.pagination-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
