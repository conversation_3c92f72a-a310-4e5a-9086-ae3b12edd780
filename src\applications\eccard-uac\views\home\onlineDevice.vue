<template>
  <kade-route-card>
    <template #header>
      <div class="header">
        <span class="header-title">设备在线状态监控</span>
        <span class="header-right">单位：台</span>
      </div>
    </template>
    <div id="onlineDeviceCharts" class="chartline"></div>
  </kade-route-card>
</template>
<script>
import * as echarts from "echarts";
import { onMounted, nextTick } from "vue";
export default {
  setup() {
    const echartInit = () => {
      var chartDom = document.getElementById('onlineDeviceCharts');
      var myChart = echarts.init(chartDom);
      var option;
      option = {
        tooltip: {
          trigger: 'item'
        },
        legend: {
          orient: 'vertical',
          right: '0%',
          top: '30%'
        },

        series: [
          {
            name: '考勤结果',
            type: 'pie',
            radius: '80%',
            right: "40%",
            label: {
              position: "inside",
              formatter: '{b}: {c}'
            },
            data: [
              { value: 1048, name: '在线', itemStyle: { color: "#f7c73a", borderRadius: 20 } },
              { value: 735, name: '离线', itemStyle: { color: "#73deb4", borderRadius: 20 } },
            ],
            emphasis: {
              scale: false,

              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      };
      nextTick(() => {
        myChart.resize();
        myChart.setOption(option, true);
      });
    };
    onMounted(() => {
      echartInit();
    });
    return {
      echartInit,
    };
  }
}
</script>
<style scoped lang="scss">
.chartline {
  height: 260px;
  width: 100%;
}
</style>