<template>
  <el-select clearable v-bind="options" :model-value="modelValue" @change="handleChange">
    <template v-for="item in state.list" :key="item.value">
      <el-option :label="item.label" :value="item.value"></el-option>
    </template>
  </el-select>
</template>
<script>
// 银行列表下拉选择组件
import { ElSelect, ElOption } from 'element-plus';
import { reactive, useEffect } from 'vue';
import { getBankList } from '@/applications/eccard-finance/api';
export default {
  components: {
    'el-select': ElSelect,
    'el-option': ElOption,
  },
  emits: ['change', 'update:modelValue'],
  props: {
    type: {
      type: String,
      required: true,
    },
    modelValue: {
      type: [String, Number],
    },
    options: {
      type: Object,
      default: () => ({})
    }
  },
  setup(props, context) {
    const state = reactive({
      list: [],
    })
    const load = async () => {
      let banks = localStorage.getItem('kade_cache_banklist');
      if (banks) {
        banks = JSON.parse(banks);
      } else {
        const { data } = await getBankList();
        banks = data;
      }
      state.list = banks;
    }
    const handleChange = (v) => {
      context.emit('update:modelValue', v);
      context.emit('change', v);
    }
    useEffect(() => {
      load();
    });
    return {
      state,
      handleChange,
    }
  }
}
</script>