<template>
  <kade-route-card style="height: 100%" v-loading="state.loading">
    <div class="deviceRealTimeMonitor">
      <kade-table-wrap title="区域" style="width: 300px; height: 100%; ">
        <el-divider></el-divider>
        <div class="search-box">
          <el-input v-model="state.areaKeyword" placeholder="请输入关键字搜索" size="small" clearable>
            <template #append>
              <el-button icon="el-icon-sousuo" @click="areaSearch()">查询</el-button>
            </template>
          </el-input>
          <el-tree class="area-tree" :data="state.areaCheckTreeList" :props="defaultProps" default-expand-all
            @node-click="handleNodeClick">
            <template #default="{ node }">
              <div class="custom-tree-node hasbtn">
                <span class="left">
                  <i class="el-icon-location-information"></i>
                  <span style="margin-left: 5px; color: #333">{{
                      node.label
                  }}</span>
                </span>
              </div>
            </template>
          </el-tree>
        </div>
      </kade-table-wrap>
      <kade-table-wrap title="设备监控列表" style="width: 1280px; height: 100%;">
        <template #extra>
          <el-upload :disabled="state.isEdit || !state.rowArea.id || state.isMonitor" style="margin:0 10px"
            class="upload-demo" :show-file-list="false" :before-upload="beforeUpload" action="">
            <el-button :class="state.isEdit || !state.rowArea.id || state.isMonitor ? 'btn-ash' : 'btn-purple'"
              icon="el-icon-location-outline" size="mini"
              :disabled="state.isEdit || !state.rowArea.id || state.isMonitor">上传地图
            </el-button>
          </el-upload>

          <el-button @click="handleAddDevice"
            :class="!state.rowArea.id || state.isEdit || state.isMonitor || !state.mapImgData.mapUrl ? 'btn-ash' : 'btn-green'"
            icon="el-icon-plus" size="mini"
            :disabled="!state.rowArea.id || state.isEdit || state.isMonitor || !state.mapImgData.mapUrl">添加设备
          </el-button>


          <el-button :disabled="!state.rowArea.id || state.isMonitor || !state.mapImgData.mapUrl" @click="handleEdit()"
            :class="state.isEdit ? 'btn-yellow' : ((state.isMonitor || !state.rowArea.id || !state.mapImgData.mapUrl) ? 'btn-ash' : 'btn-blue')"
            :icon="state.isEdit ? 'el-icon-queding_huaban1' : 'el-icon-edit'" size="mini">{{ state.isEdit ? '保存' : '编辑'
            }}数据
          </el-button>
          <el-button @click="handleMonitor"
            :class="state.isEdit || !state.rowArea.id || !state.mapImgData.mapUrl ? 'btn-ash' : 'btn-deep-blue'"
            icon="el-icon-nested" size="mini" :disabled="state.isEdit || !state.rowArea.id || !state.mapImgData.mapUrl">
            {{ state.isMonitor ? "停止监控" : "启动监控" }}</el-button>
        </template>
        <el-divider></el-divider>
        <div class="img" >
          <img ref="imgRef" v-if="state.mapImgData.mapUrl" :src="state.mapImgData.mapUrl" alt="">
          <el-empty v-else description="暂无地图！" />
          <template v-for="(item, index) in state.dataList" :key="index">
            <div class="dian-box" v-if="!item.isDelete"
              :style="`left:${item.doorX}px;top:${item.doorY}px;background:${backgroundFnc(item.deviceStatus)}`"
              :draggable="item.isDraggable" @dragstart.stop="dragstart($event, item)"
              @dragend.stop="dragEnd($event, item)" @dragenter="($event) => $event.preventDefault()"
              @dragover="($event) => $event.preventDefault()">
              <el-tooltip effect="light" :content="item.deviceName" :hide-after="200" placement="top">
                <img src="../../../../../assets/uac_img/map_door.svg" alt="">
              </el-tooltip>
              <div v-if="state.isEdit && item.isAdd || item.isEditDel" class="close" @click="item.isDelete = true">×
              </div>
            </div>
          </template>
        </div>
        <!--         <div class="dataBox" @dragleave.stop="dragleave($event, 'main')">
          <div v-for="(item, i) in dataOrigin" :key="i" class="dataList" draggable @dragenter.prevent @dragover.prevent
            @dragstart.stop="dragstart($event, item)" @dragend.stop="dragEnd($event, item)">
            {{ item.Name }}
          </div>
        </div> -->
      </kade-table-wrap>
    </div>
    <kade-bind-device v-model="state.isBind" :data="state.rowArea" @modelValue:update="close" />
  </kade-route-card>
</template>
<script>
import { onMounted, onBeforeUnmount, reactive, ref } from "vue";
import {
  ElDivider,
  ElInput,
  ElTree,
  ElButton,
  ElTooltip,
  ElUpload,
  ElEmpty,
  ElMessage,
} from "element-plus";
import { getUserAreaPurview } from "@/applications/eccard-basic-data/api";
import { acsAreaMap, updateAcsAreaMap, getAreaDeviceList, saveAreaDevice } from "@/applications/eccard-uac/api";
import { uploadApplyLogo } from "@/applications/unified_portal/api";
import { makeTree, filterDictionary } from "@/utils/index.js";
import bindDevice from "./components/bind.vue";
const defaultProps = {
  children: "children",
  label: "areaName",
};

const backgroundFnc = (val) => {
  if (val === "DEVICE_NORMAL") {
    return '#15ce2b'
  } else if (val === "DEVICE_DAMAGE") {
    return '#eb1010'
  } else {
    return '#8c8c8c'
  }
};
export default {
  components: {
    ElDivider,
    ElInput,
    ElTree,
    ElButton,
    ElTooltip,
    ElUpload,
    ElEmpty,
    "kade-bind-device": bindDevice
  },
  setup() {
    const imgRef = ref(null)
    const state = reactive({
      isBind: false,
      activeIndex: 0,
      areaKeyword: "",
      areaCheckTreeList_copy: '',
      loading: false,
      isEdit: false,
      isMonitor: false,
      timer: null,
      rowArea: {},
      mapImgData: {},
      form: {},
      dataList: [
        /* { isAdd: false, isEdit: false, isDraggable: false, isDelete: false, label: "一号门", left: 10, top: 10 },
        { isAdd: false, isEdit: false, isDraggable: false, isDelete: false, label: "二号门", left: 60, top: 100 },
        { isAdd: false, isEdit: false, isDraggable: false, isDelete: false, label: "三号门", left: 40, top: 180 },
        { isAdd: false, isEdit: false, isDraggable: false, isDelete: false, label: "四号门", left: 300, top: 150 },
        { isAdd: false, isEdit: false, isDraggable: false, isDelete: false, label: "五号门", left: 600, top: 560 }, */
      ],
      startPosition: {
        clientX: 0,
        clientY: 0
      }
    });
    const getMapImg = async (areaId) => {
      state.dataList = []
      state.loading = true
      try {
        let { data } = await acsAreaMap(areaId)
        state.mapImgData = data ? data : {}
        state.loading = false
      }
      catch {
        state.loading = false
      }
    }
    const getDeviceList = async (areaId) => {
      state.loading = true
      try {
        let { data } = await getAreaDeviceList({ areaId, isMonitor: true })
        state.dataList = data.map(item => {
          return { ...item, isAdd: false, isEdit: false, isEditDel: false, isDraggable: false, isDelete: false }
        })
        state.loading = false
      }
      catch {
        state.loading = false
      }
    }
    const handleMonitor = () => {
      state.isMonitor = !state.isMonitor
      if (state.isMonitor) {
        state.timer = setInterval(() => {
          getDeviceList(state.rowArea.id)
        }, 5000)
      } else {
        if (state.timer) {
          clearInterval(state.timer)
        }
      }
    }
    const handleNodeClick = async (val) => {
      if (state.timer) {
        clearInterval(state.timer)
        state.isMonitor = false
      }
      state.rowArea = val
      await getMapImg(val.id)
      await getDeviceList(val.id)
    }
    const beforeUpload = async (f) => {
      state.loading = true
      const formData = new FormData();
      formData.append('file', f);
      const { data } = await uploadApplyLogo(formData);
      console.log(data);
      console.log(state.mapImgData);
      let params = {
        areaId: state.rowArea.id,
        mapUrl: data,
        mapHeight: 0,
        mapWidth: 0,
        id: state.mapImgData.id ? state.mapImgData.id : 0
      }
        console.log(1231123123);
      try {
        console.log(1231);
        let { message, code } = await updateAcsAreaMap(params)
        if (code === 0) {
          ElMessage.success(message)
          getMapImg(state.rowArea.id)
          getDeviceList(state.rowArea.id)
        }
        state.loading = false
      }
      catch {
        state.loading = false

      }
    }
    const handleAddDevice = () => {
      state.isBind = true
    }
    const handleEdit = async () => {
      state.isEdit = !state.isEdit
      if (state.isEdit) {
        state.dataList = state.dataList.map(item => {
          return {
            ...item,
            isDraggable: true,
            isAdd: false,
            isEditDel: true,
            isDelete: false,
          }
        })
      } else {
        console.log(state.dataList);
        state.loading = true
        let params = {
          areaId: state.rowArea.id,
          delList: state.dataList.filter(item => item.isDelete && !item.isAdd).map(item => item.id),
          editDeviceList: state.dataList.filter(item => item.isEdit && !item.isAdd && !item.isDelete),
          addDeviceList: state.dataList.filter(item => item.isAdd),
        }
        try {
          let { code, message } = await saveAreaDevice(params)
          if (code === 0) {
            ElMessage.success(message)
            await getDeviceList(state.rowArea.id)
          }
          state.loading = false
        }
        catch {
          state.loading = false
        }

      }

    }
    const close = val => {
      if (val) {
        state.isEdit = true
        state.dataList.push({ isAdd: true, isEdit: true, isEditDel: false, isDraggable: true, isDelete: false, doorX: 10, doorY: 10, ...val })
      }
      state.isBind = false
    }
    const queryAreaCheckList = () => {
      getUserAreaPurview().then((res) => {
        let arr = makeTree(res.data, "id", "areaParentId", "children");
        state.areaCheckTreeList = [...arr];
        state.areaCheckTreeList_copy = [...arr];
      });
    };
    const mapTree = (value, arr) => {
      let newarr = [];
      arr.forEach((element) => {
        if (element.areaName.indexOf(value) > -1) {
          // 判断条件
          newarr.push(element);
        } else {
          if (element.children && element.children.length > 0) {
            let redata = mapTree(value, element.children);
            if (redata && redata.length > 0) {
              let obj = {
                ...element,
                children: redata,
              };
              newarr.push(obj);
            }
          }
        }
      });
      return newarr;
    };
    const areaSearch = () => {
      state.areaCheckTreeList = mapTree(state.areaKeyword, state.areaCheckTreeList_copy)
    }

    const dragstart = (event, item) => {
      console.log(event, item);
      if (!item.isDraggable) return
      event.dataTransfer.dropEffect = "copy"
      state.startPosition.clientX = event.clientX
      state.startPosition.clientY = event.clientY
    }
    const dragEnd = (event, item) => {
      if (!item.isDraggable) return
      console.log(event, item, imgRef);
      let x = event.clientX - state.startPosition.clientX + item.doorX
      let y = event.clientY - state.startPosition.clientY + item.doorY
      if (x > 0 && x < imgRef.value.clientWidth) {
        item.doorX = x
      }
      if (y > 0 && y < (imgRef.value.clientHeight - 30)) {
        item.doorY = y
      }
      item.isEdit = true
    }
    onMounted(() => {
      queryAreaCheckList();
    });
    onBeforeUnmount(() => {
      if (state.timer) {
        clearInterval(state.timer)
        state.isMonitor = false

      }
    })
    return {
      imgRef,
      filterDictionary,
      defaultProps,
      backgroundFnc,
      state,
      handleNodeClick,
      handleMonitor,
      beforeUpload,
      handleAddDevice,
      handleEdit,
      close,
      areaSearch,
      dragstart,
      dragEnd
    };
  },
};
</script>
<style lang="scss" scoped>
.deviceRealTimeMonitor {
  height: 100%;
  width: 1600px;
  display: flex;
  justify-content: space-between;

  .search-box {
    box-sizing: border-box;
    padding: 10px;
    height: calc(100% - 40px);
  }

  .area-tree {
    margin-top: 20px;
    height: calc(100% - 50px);
    overflow-y: auto;
  }

  .img {
    position: relative;
    width: 100%;
    box-sizing: border-box;
    padding: 0px;
    height: calc(100% - 40px);
    overflow-y: auto;
    img {
      width: 100%;
      // height: 100%;
    }
  }

  .dian-box {
    position: absolute;
    padding: 5px 10px;
    top: 10px;
    left: 10px;
    cursor: pointer;
    border-radius: 15px;

    img {
      width: 19px;
      height: 25px;
    }

    .close {
      position: absolute;
      right: -5px;
      top: -5px;
      width: 15px;
      line-height: 15px;
      text-align: center;
      background: #eb1010;
      color: #fff;
      border-radius: 50%;
    }
  }

}

:deep(.el-empty) {
  width: 100%;
  height: 100%;
}

:deep(.el-divider--horizontal) {
  margin: 0;
}
</style>