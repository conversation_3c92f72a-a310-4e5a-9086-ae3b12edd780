<template>
  <div id="mychart3" class="echartDiv"></div>

</template>
<script>
import * as echarts from "echarts";
import { onMounted, ref, watch } from "vue";
import { getWaterMainPageTrend } from "../../api/home.js";

export default {
  props: {
    queryDate: {
      type: Object,
      default: () => ({
        startTime: '', 
        endTime: '', 
        type: '1' // 按天
      })
    }
  },
  setup(props) {
    const chartData = ref({
      xAxisData: [],
      waterData: [],
      electricData: []
    });

    // 获取趋势数据
    const fetchTrendData = async () => {
      console.log('父组件传递的参数:', props.queryDate);
      const params = props.queryDate
      const response = await getWaterMainPageTrend(params);
      console.log('用户消费变化趋势数据:', response);
      if (response && response.code === 0 && response.data) {
          // 处理接口返回的数据结构
          const waterRes = response.data?.waterRes || [];
          const elecRes = response.data?.elecRes || [];
          // 提取日期作为X轴数据
          const xAxisData = waterRes.map(item => item.date);
          const waterData = waterRes.map(item => item.num);
          const electricData = elecRes.map(item => item.num);
          chartData.value = {
            xAxisData:xAxisData,
            waterData:waterData,
            electricData: electricData
          };

        // 重新初始化图表
        echartInit();
      }
    };

    const echartInit = () => {
      var chartDom = document.getElementById("mychart3");
      var myChart = echarts.init(chartDom);
      var option = {
        title: {
          text: '用户消费变化趋势'
        },
        tooltip: {
          trigger: "axis",
        },
        grid: {
          top: "20%",
          left: "0%",
          right: "3%",
          bottom: "0%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          boundaryGap: false,
          data: chartData.value.xAxisData,
          axisLine: {
            lineStyle: {
              color: "#9498b0s",
            },
          },
          axisLabel: {
            margin: 18,
            textStyle: {
              fontSize: 12,
            },
          },
        },
        yAxis: {
          type: "value",
          splitLine: {
            show: true,
            lineStyle: {
              type: "dashed",
            },
          },
          axisLabel: {
            margin: 20,
            textStyle: {
              fontSize: 12,
            },
          },
        },
        series: [
          {
            smooth: true,
            symbol: "circle",
            name: "水费:",
            data: chartData.value.waterData,
            type: "line",
            color: "#1890ff",
          },
          // {
          //   smooth: true,
          //   symbol: "circle",
          //   name: "电费:",
          //   data: chartData.value.electricData,
          //   type: "line",
          //   color: "#2fc25b",
          // },

        ],
      };
      myChart.setOption(option);
    };

    // 监听父组件传递的参数变化
    watch(() => props.queryDate, () => {
      if (props.queryDate) {
        fetchTrendData();
      }
    }, { deep: true });
    //挂载
    onMounted(() => {
      // fetchTrendData();
    });
    return {
      echartInit,
      fetchTrendData,
    };
  },
};
</script>
<style lang="scss" scoped>
.echartDiv {
  height: 340px;
}
</style>
