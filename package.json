{"name": "eccard2.0", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@babel/polyfill": "^7.12.1", "@element-plus/icons-vue": "^1.1.4", "axios": "^0.21.1", "china-region": "^1.3.0", "code-inspector-plugin": "^0.20.17", "core-js": "^3.21.0", "cropperjs": "^1.5.12", "echarts": "^5.1.1", "element-plus": "1.0.2-beta.37", "i": "^0.3.7", "install": "^0.13.0", "js-cookie": "^2.2.1", "md5": "^2.3.0", "mitt": "^3.0.0", "moment": "^2.29.1", "nprogress": "^0.2.0", "postcss-px2rem": "^0.3.0", "px2rem-loader": "^0.1.9", "vue": "^3.0.0", "vue-echarts": "^6.0.2", "vue-router": "^4.0.0-0", "vuex": "^4.0.0-0", "wangeditor": "^4.7.11", "yarn": "^1.22.18"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-router": "^4.5.13", "@vue/cli-plugin-vuex": "^4.5.13", "@vue/cli-service": "~4.5.0", "@vue/compiler-sfc": "^3.0.0", "babel-eslint": "^10.1.0", "babel-plugin-component": "^1.1.1", "babel-plugin-import": "^1.13.3", "eslint": "^6.7.2", "eslint-plugin-vue": "^7.0.0", "postcss-sass-unicode": "^0.1.0", "sass": "1.32.12", "sass-loader": "9.0.0", "vue-cli-plugin-element-plus": "0.0.13"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/vue3-essential", "eslint:recommended"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {}, "globals": {"THEMEVARS": true, "APP_NAME": true, "CONFIG": true, "CACHE_PREFIX": true, "ELEMENTICONS": true}}, "browserslist": ["ie 10"]}