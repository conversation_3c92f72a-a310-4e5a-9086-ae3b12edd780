﻿
const { codeInspectorPlugin } = require('code-inspector-plugin');

const path = require('path');
const fs = require('fs');
const webpack = require('webpack');
const apiServer = require('./datas');
const isDevelopment = process.env.NODE_ENV === 'development';
const applicationName = require('./package.json').name;
const CopyWebpackPlugin = require('copy-webpack-plugin');
/* // 引入等比适配插件
const px2rem = require('postcss-px2rem')

// 配置基本大小
const postcss = px2rem({
  // 基准大小 baseSize，需要和rem.js中相同
  remUnit: 16
}) */
const getElementIcons = () => {
  const data = fs.readFileSync('node_modules/element-plus/lib/theme-chalk/el-icon.css');
  const str = data.toString();
  const arr = [];
  str.replace(/(?<=\.)el-icon-[a-zA-Z0-9-]+(?=:before)/gim, function (s) {
    arr.push({
      name: s,
      class: s,
    });
    return '';
  });
  return arr;
}

const basePath = path.resolve(__dirname, 'src/applications');
const getApps = function () {
  const arr = process.env.npm_config_name ? [process.env.npm_config_name] : fs.readdirSync(basePath);
  return arr;
}

const getEntrys = function () {
  const arr = getApps();
  const entrys = {};
  arr.forEach(appName => {
    entrys[appName] = {
      entry: path.join(basePath, `${appName}/index.js`),
      template: appName === 'login' ? 'public/login.html' : 'public/index.html',
      filename: process.env.npm_config_name ? 'index.html' : `${appName}/index.html`,
      title: applicationName,
      name: process.env.npm_config_name ? '' : appName,
      minify: false,
    };
  });
  return entrys;
}
module.exports = {
   chainWebpack: (config) => {
    config.plugin("code-inspector-plugin").use(
      codeInspectorPlugin({
        bundler: "webpack"
      })
    );
  },
  publicPath: process.env.npm_config_name ? './' : '/',
  assetsDir: 'static',
  pages: getEntrys(),
  css: {
    sourceMap: isDevelopment,
    loaderOptions: {
      sass: {
        additionalData: `@import "~@/assets/styles/variable.scss";`
      },
      postcss: {
        plugins: [require('postcss-sass-unicode')]
      }
    }
  },
  configureWebpack: {
    name: applicationName,
    output: {
      filename: 'static/js/[id]-[hash].js',
      chunkFilename: 'static/js/[id]-[chunkhash].js',
    },
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src'),
      }
    },
    devtool: process.env.NODE_ENV === 'development' ? 'source-map' : 'cheap-module-source-map',
    plugins: [
      new webpack.DefinePlugin({
        APP_NAME: JSON.stringify(applicationName),
        CACHE_PREFIX: JSON.stringify('kade_cache_'),
        ELEMENTICONS: JSON.stringify(getElementIcons()),
      }),
      new webpack.ProvidePlugin({
        THEMEVARS: path.resolve(__dirname, './src/assets/styles/export.scss'),
      }),
      new CopyWebpackPlugin(
        [...getApps().map(app => {
          return {
            from: path.join(basePath, `${app}/config.js`),
            to: process.env.npm_config_name ? `config.js` : `${app}/config.js`
          }
        }), {
          from: path.join(__dirname, 'src/print'),
          to: path.join(__dirname, 'dist/print'),
        }]
      ),
    ],
  },
  devServer: {
    port: 8888,
    open: true,
    openPage: 'unified_portal',
    // proxy: {
    // "/eccard": {
    //     target: "http://*************:8121",
    //     changeOrigin: true,
    // },
    // "/eccard/purview_manage": {
    //     //target: "http://*************:8121",
    //     target: "http://*************:8004",
    //     changeOrigin: true,
    //     // pathRewrite: {
    //     //   ['^' + apiPath]: ''
    //     // }
    // },
    // "/eccard/basicData": {
    //   //target: "http://*************:8121",
    //   target: "http://*************:8003",
    //   changeOrigin: true,
    //   // pathRewrite: {
    //   //   ['^' + apiPath]: ''
    //   // }
    // },
    // "/eccard/partal_manage": {
    //   //target: "http://*************:8121",
    //   target: "http://*************:8002",
    //   changeOrigin: true,
    //   // pathRewrite: {
    //   //   ['^' + apiPath]: ''
    //   // }
    // },
    // '/micro': {
    //   target: "http://localhost:8889",
    //   changeOrigin: true,
    //   pathRewrite: {
    //     '^/micro': ''
    //   }
    // }
    // },
    before(app) {
      apiServer(app);
    }
  }
}
