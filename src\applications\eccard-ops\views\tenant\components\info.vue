<template>
  <kade-modal v-bind="attrs" :modelValue="modelValue" @update:modelValue="update">
    <el-form class="teant-info-form" label-width="170px" size="small" >
      <el-form-item label="上级租户名称:" v-show="!(state.model.tenantId===1)">
        {{ state.model.tenantParentName }}
      </el-form-item>
      <el-form-item label="租户名称:">
        {{ state.model.tenantName }}
      </el-form-item>
      <el-form-item label="手机号码:">
        {{ state.model.tenantMobile }}
      </el-form-item>
      <el-form-item label="邮箱地址:">
        {{ state.model.tenantEmail }}
      </el-form-item>
      <el-form-item label="授权终端数:">
        {{ state.model.tenantAuthDevice }}
      </el-form-item>
      <el-form-item label="授权用户数:">
        {{ state.model.tenantAuthUser }}
      </el-form-item>
      <el-form-item label="AppID(小程序ID):">
        {{ state.model.tenantEappid }}
      </el-form-item>
      <el-form-item label="AppSecret(小程序密钥):">
        {{ state.model.tenantEappsecret }}
      </el-form-item>
      <el-form-item label="租户类型:">
        {{ dictionaryFilter(state.model.tenantType) }}
      </el-form-item>
      <el-form-item label="租户类别:">
        {{ dictionaryFilter(state.model.tenantCategory) }}
      </el-form-item>
      <el-form-item label="租户性质:">
        {{ dictionaryFilter(state.model.tenantNature) }}
      </el-form-item>
      <el-form-item label="机构有效期:">
        {{ state.model.tenantActivationTime }} ~ {{ state.model.tenantExpirationTime }}
      </el-form-item>
      <el-form-item label="机构联系地址:">
        {{ state.model.tenantAddress }}
      </el-form-item>
      <el-form-item label="操作员姓名:">
        {{ state.model.tenantOperName }}
      </el-form-item>
      <el-form-item label="操作员联系地址:">
        {{ state.model.tenantOperAddress }}
      </el-form-item>
      <el-form-item label="备注:">
        {{ state.model.tenantRemark }}
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button size="small" @click="cancel">关闭</el-button>
      <el-button size="small" @click="submit" type="primary">编辑</el-button>
    </template>
  </kade-modal>
</template>
<script>
import Modal from '@/components/modal';
import { computed, reactive, watch } from 'vue';
import { useStore } from 'vuex';
import {
  ElButton,
  ElForm,
  ElFormItem,
} from 'element-plus';
import {
  getTenantDetails,
} from "@/applications/eccard-ops/api";
export default {
  emits: ['update:modelValue', 'edit'],
  props: {
    title: {
      type: String,
      default: ''
    },
    modelValue: {
      type: Boolean,
      default: false,
    },
    id: {
      type: [String, Number],
      default: null
    }
  },
  components: {
    'kade-modal': Modal,
    'el-button': ElButton,
    'el-form': ElForm,
    'el-form-item': ElFormItem,
  },
  setup(props, context) {
    const state = reactive({
      model: {},
      loading: false,
    });
    const store = useStore();
    const submit = () => {
      context.emit('edit', props.id);
    }
    const cancel = () => {
      context.emit('update:modelValue', false);
    }
    const attrs = computed(() => {
      // eslint-disable-next-line no-unused-vars
      const { modelValue, id, ...attrs } = props;
      return attrs;
    });
    const update = (v) => {
      context.emit('update:modelValue', v);
    }
    const loadInfo = async () => {
      try {
        state.loading = true;
        const { data } = await getTenantDetails(props.id);
        state.model = data;
      } catch(e) {
        throw new Error(e.message);
      } finally {
        state.loading = false;
      }
    }
    const topTenantName = computed(() => {
      return store.state.app.topTenants.find(it => it.tenantId === state.model.tenantId)?.tenantName;
    });
    watch(() => props.modelValue, (v) => {
      if(v && props.id) {
        loadInfo();
      }
    });
    return {
      attrs,
      update,
      submit,
      cancel,
      labelWidth: THEMEVARS.formLabelWidth,
      state,
      topTenantName,
    }
  }
}
</script>
<style lang="scss">
  .teant-info-form{
    .el-form-item{
      margin-bottom: 10px;
    }
    .el-form-item__label{
      color: #999;
    }
  }
</style>
