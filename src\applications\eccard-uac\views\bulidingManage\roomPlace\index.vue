<template>
  <kade-route-card>
    <kade-table-filter @search="handleSearch" @reset="handleReset">
      <el-form inline label-width="90px" size="mini">
        <el-form-item label="关键字">
          <el-input v-model="state.form.keyWord" placeholder="房间号或房间名称关键字搜索"></el-input>
        </el-form-item>
        <kade-linkage-select :value="state.form" :data="linkageData" @change="linkageChange" />
      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="房间列表" v-loading="state.loading">
      <template #extra>
        <el-button @click="handleEdit({},'add')" class="btn-green" icon="el-icon-plus" size="mini">新建房间</el-button>
        <el-button @click="state.isBatchAdd=true" class="btn-blue" icon="el-icon-plus" size="mini">批量新建</el-button>
        <el-button @click="batchDel" type="danger" icon="el-icon-close" size="mini">批量删除</el-button>
        <el-button @click="state.isImport=true" type="primary" icon="el-icon-daoru" size="mini">导入</el-button>
        <el-button @click="handleExport" class="btn-purple" icon="el-icon-daochu" size="mini">导出</el-button>
      </template>
      <el-table :data="state.dataList" border height="55vh" @selection-change="handleSelectChange">
        <el-table-column prop="" show-overflow-tooltip type="selection" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="areaName" label="所属区域" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip width="250px" prop="roomName" label="房间号" align="center">
          <template #default="scope">
            {{`${scope.row.buildName}>${scope.row.unitNum}单元>${scope.row.floorNum}楼`}}
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip prop="roomName" label="房间名称" align="center"></el-table-column>

        <el-table-column show-overflow-tooltip prop="roomTypeName" label="房间类型" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="capacityCount" label="容纳人数" align="center"></el-table-column>
        <el-table-column label="操作" align="center" width="200px">
          <template #default="scope">
            <el-button class="green" type="text" @click="handleEdit(scope.row,'edit')" size="mini">编辑</el-button>
            <el-button class="green" type="text" @click="handleDel(scope.row)" size="mini">删除</el-button>
            <el-button class="green" type="text" @click="handleEdit(scope.row,'details')" size="mini">查看详情</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination v-model:currentPage="state.form.currentPage" v-model:page-size="state.form.pageSize" :page-sizes="[10, 20, 30, 40]" :small="small" :disabled="disabled" background layout="total, sizes, prev, pager, next, jumper" :total="state.total" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      </div>
    </kade-table-wrap>
    <kade-room-edit v-model:modelValue="state.isEdit" :roomTypeList="state.roomTypeList" :editType="state.editType" :rowData="state.rowData" @update:modelValue="close" />
    <kade-room-batch-add v-model:modelValue="state.isBatchAdd" :roomTypeList="state.roomTypeList" @update:modelValue="batchAddClose" />
    <kade-room-import v-model:modelValue="state.isImport" @update:modelValue="importClose" />
  </kade-route-card>
</template>

<script>
import { reactive } from '@vue/reactivity';
import { ElForm, ElFormItem, ElInput, ElButton, ElTable, ElTableColumn, ElPagination, ElMessageBox, ElMessage } from "element-plus"
import { onMounted } from '@vue/runtime-core';
import {downloadXlsx} from "@/utils"
import { getRoomTypeSelectList, getRoomList, roomDelete, roomBatchDelete,exportRoom } from "@/applications/eccard-uac/api";
import linkageSelect from "@/applications/eccard-uac/views/components/linkageSelect.vue"
import edit from './components/edit'
import batchAdd from './components/batchAdd'
import roomImport from './components/roomImport'
const linkageData = {
  area: { label: '所属区域', valueKey: "areaPath", key: "areaPath" },
  building: { label: '所属楼栋', valueKey: "buildId" },
  unit: { label: '所属单元', valueKey: "unitNum" },
  floor: { label: '所属楼层', valueKey: "floorNum" },
}
export default {
  components: {
    ElForm,
    ElFormItem,
    ElInput,
    ElButton,
    ElTable,
    ElTableColumn,
    ElPagination,
    "kade-linkage-select": linkageSelect,
    "kade-room-edit": edit,
    "kade-room-batch-add": batchAdd,
    "kade-room-import": roomImport
  },
  setup() {
    const state = reactive({
      loading: false,
      roomTypeList: [],
      form: {
        pageSize: 10,
        currentPage: 1,
      },
      dataList: [],
      isEdit: false,
      isBatchAdd: false,
      isImport: false,
      rowData: {},
      editType: "",
      total: 0,
      selectRowList: [],
    });
    const getList = async () => {
      state.loading = true
      try {
        let { data: { list, total } } = await getRoomList(state.form)
        state.dataList = list
        state.total = total
        state.loading = false
      }
      catch {
        state.loading = false
      }
    };
    const getRoomTypeList = async () => {
      let { data } = await getRoomTypeSelectList()
      state.roomTypeList = data
    }
    const handleEdit = (row, type) => {
      state.rowData = row
      state.editType = type
      state.isEdit = true
    }
    const linkageChange = (val) => {
      state.form = { ...state.form, ...val }
      console.log(state.form);
    }
    const handleDel = (row) => {
      ElMessageBox.confirm("确认删除?", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        let { code, message } = await roomDelete(row.id);
        if (code === 0) {
          ElMessage.success(message);
          getList();
        }
      });
    };

    const handleSelectChange = (val) => {
      state.selectRowList = val
    }
    const batchDel = () => {
      if (!state.selectRowList.length) {
        return ElMessage.error("请先选择需要删除的房间！")
      }
      ElMessageBox.confirm("确认删除已选择房间?", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        let param = state.selectRowList.map(item => item.id).join(",")
        let { code, message } = await roomBatchDelete(param);
        if (code === 0) {
          ElMessage.success(message);
          getList();
        }
      });
    }
    const handleExport = async () => {
      for (let key in state.form) {
        if (!state.form[key]) {
          delete state.form[key]
        }
      }
      state.loading = true
      let res = await exportRoom(state.form)
      downloadXlsx(res, "房间列表.xlsx")
      state.loading = false
    }
    const handleSearch = () => {
      getList()
    };
    const handleReset = () => {
      state.form = {
        pageSize: 10,
        currentPage: 1
      }
    };
    const handleSizeChange = (val) => {
      state.form.currentPage = 1
      state.form.pageSize = val
      getList()
    };
    const handleCurrentChange = (val) => {
      state.form.currentPage = val
      getList()
    }
    const close = val => {
      if (val) {
        getList()
      }
      state.isEdit = false
    }
    const importClose = val => {
      if (val) {
        getList()
      }
      state.isImport = false
    }
    const batchAddClose = (val) => {
      if (val) {
        getList()
      }
      state.isBatchAdd = false
    }
    onMounted(() => {
      getList();
      getRoomTypeList()
    });
    return {
      linkageData,
      state,
      getList,
      handleEdit,
      linkageChange,
      handleDel,
      handleSelectChange,
      batchDel,
      handleExport,
      handleSearch,
      handleReset,
      handleSizeChange,
      handleCurrentChange,
      close,
      importClose,
      batchAddClose
    }
  }
}
</script>

<style lang="scss" scoped>
.green :hover {
  text-decoration: underline;
}
</style>