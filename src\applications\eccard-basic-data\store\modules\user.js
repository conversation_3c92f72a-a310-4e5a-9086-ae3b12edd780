import { removeToken, getCacheUser, removeCache<PERSON>ser, removeRefreshToken } from '@/utils';
import { loginOuted } from '@/service';
const state = {
	userMenus: [{
		label: '个人信息',
		action: 'MyInfo'
	},/* {
		label: '修改密码',
		action: 'ChangePass'
	} */],
	userInfo: getCacheUser(),
	isInitRoute: false,
};
const mutations = {
	updateState(state, { key, payload }) {
		state[key] = payload;
	},	
};
const actions = {
	async loginout() {
		await loginOuted();
		removeToken();
		removeRefreshToken();
		removeCacheUser();
		localStorage.removeItem('kade_cache_dictionary');
		sessionStorage.clear();
		location.reload();
	},
};
export default {
	namespaced: true,
	state,
	mutations,
	actions
}