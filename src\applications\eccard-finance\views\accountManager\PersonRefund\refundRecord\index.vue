<template>
  <div class="refund-record">
    <div class="header-flexbox">
      <kade-tab-wrap :tabs="tabs" style="margin-top: 20px" v-model="state.tab">
        <template #tkjl>
          <el-table style="width: 100%" :data="refundRecordList" v-loading="false" border stripe>
            <el-table-column width="160" label="退款时间" prop="refundDate" align="center">
              <template #default="scope">
                <div>
                  {{ timeStrDate(scope.row.refundDate) }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="退款方式" align="center">
              <template #default="scope">
                {{
                    filterDictionary(scope.row.refundMode, state.refundModeList)
                }}
              </template>
            </el-table-column>

            <el-table-column label="退款合计(元)" prop="actualTotalAmount" align="center"></el-table-column>
            <el-table-column label="操作员" prop="createUserName" align="center"></el-table-column>
            <el-table-column label="操作" align="center">
              <template #default="scope">
                <div @click="handleClick(scope.row)" style="color: rgb(26, 188, 156)">
                  查看详情
                </div>
              </template>
            </el-table-column>
          </el-table>
        </template>
      </kade-tab-wrap>
      <div class="header-btn">
        <el-button icon="el-icon-daoru" type="primary" size="mini" @click="upload()" class="shop-upload"
          :disabled="isDisabled">下载查询明细</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { ElButton, ElTable, ElTableColumn } from "element-plus";
import { downloadXlsx } from "@/utils";
import { reactive } from "@vue/reactivity";
import { getRefundRecordListExport } from "@/applications/eccard-finance/api";
import { onMounted, computed } from "@vue/runtime-core";
import { useStore } from "vuex";
import { timeStr } from "@/utils/date.js";
const tabs = [
  {
    name: "tkjl",
    label: "退款记录",
  },
];
export default {
  components: {
    "el-button": ElButton,
    "el-table": ElTable,
    "el-table-column": ElTableColumn,
  },

  setup() {
    const store = useStore();
    const state = reactive({
      tab: "tkjl",
      isRighting: false,
      total: 0,
      form: {
        userId: null,
      },
      systemUserList: [],
      refundModeList: JSON.parse(
        localStorage.getItem("kade_cache_dictionary")
      ).filter((item) => item.dictType == "PERSON_REFUND_MODE"), //退款类型
    });
    const isDisabled = computed(() => {
      return store.state.refundData.selectPerson ? false : true;
    });
    const refundRecordList = computed(() => {
      console.log("!!!!!!!!!!!", store.state.refundData.refundRecordList);
      return store.state.refundData.refundRecordList;
    });

    const filterDictionary = (val, dictionaryList) => {
      for (let i of dictionaryList) {
        if (val == i.dictCode) {
          return i.dictValue;
        }
      }
    };
    const timeStrDate = computed(() => {
      return timeStr;
    });
    const upload = () => {

      state.form.userId = store.state.refundData.selectPerson.userId;
      getRefundRecordListExport(state.form).then((res) => {
        console.log(res);
        downloadXlsx(res, store.state.refundData.selectPerson.userName + "退款记录.xlsx");
      });
    };

    const handleClick = (row) => {
      store.dispatch("refundData/queryPersonRefundDetails", {
        userId: store.state.refundData.selectPerson.userId,
        refundId: row.refundId
      });
    };

    onMounted(() => { });
    return {
      state,
      tabs,
      refundRecordList,
      isDisabled,
      timeStrDate,
      upload,
      handleClick,
      filterDictionary,
    };
  },
};
</script>
<style lang="scss" scoped>
.date {
  margin: 0 10px;
  color: rgba(0, 0, 0, 0.847058823529412);
}

.refund-record {
  .el-divider--horizontal {
    margin: 0 0 10px;
  }

  .el-form-item--small.el-form-item {
    margin-bottom: 10px;
  }

  .search-box {
    padding: 0 20px;
    margin-top: 20px;
  }

  .el-dialog__header {
    border-bottom: 1px solid #efefef;
  }

  .el-dialog__headerbtn {
    font-size: 20px;
    top: 10px;
    right: 10px;
  }

  .el-dialog__header {
    padding: 10px 20px;
  }

  .el-overlay {
    position: absolute;
    background-color: rgba(0, 0, 0, 0.2);
  }

  .el-dialog__footer {
    text-align: center;
  }

  .el-dialog__title {
    font-size: 14px;
  }

  .kade-tab-wrap {
    margin-top: 0 !important;
  }

  .header-flexbox {
    position: relative;
  }

  .header-btn {
    position: absolute;
    top: 8px;
    right: 20px;
  }
}
</style>
