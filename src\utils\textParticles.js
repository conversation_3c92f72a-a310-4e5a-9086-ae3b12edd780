

// 文字粒子特效
let Particle = function(x, y, ctx) {
  this.ctx = ctx;
  this.x =  ctx.getRandomInt(ctx.cw);
  this.y =  ctx.getRandomInt(ctx.ch);
  this.coord = {x:x, y:y};
  this.r =  Math.min((ctx.getRandomInt((ctx.cw / ctx.dpi)) + 1), 6);
  this.vx = (Math.random() - 0.5) * 100;
  this.vy = (Math.random() - 0.5) * 100;
  this.accX = 0;
  this.accY = 0;
  this.friction = Math.random() * 0.05 + 0.90;
  this.color = ctx.colors[Math.floor(Math.random() * 6)];
}

Particle.prototype.render = function(isDisableMouse) {
  this.accX = (this.coord.x - this.x) / 100;
  this.accY = (this.coord.y - this.y) / 100;
  this.vx += this.accX;
  this.vy += this.accY;
  this.vx *= this.friction;
  this.vy *= this.friction;
  this.x += this.vx;
  this.y +=  this.vy;
  if (!isDisableMouse) {
    let a = this.x - this.ctx.mouseCoord.x;
    let b = this.y - this.ctx.mouseCoord.y;
    var distance = Math.sqrt(a * a + b * b);
    if(distance < (this.ctx.cw / 15)) {
      this.accX = (this.x - this.ctx.mouseCoord.x) / 100;
      this.accY = (this.y - this.ctx.mouseCoord.y) / 100;
      this.vx += this.accX;
      this.vy += this.accY;
    }
  }
  this.ctx.context.fillStyle = this.color;
  this.ctx.context.beginPath();
  this.ctx.context.arc(this.x, this.y, this.r, 0, Math.PI * 2, false);
  this.ctx.context.fill();
  this.ctx.context.closePath();
}


export default class TextParticles {
  constructor(canvas, text) {
    this.canvas = canvas;
    this.text = text;
    this.ch = canvas.height = canvas.getBoundingClientRect().height;
    this.cw = canvas.width = canvas.getBoundingClientRect().width;
    this.sceneBackground = 'black';
    this.context = canvas.getContext('2d');
    this.previousMouseCoord = {x:0, y:0};
    this.mouseCoord = {x:0, y:0};
    this.sceneResize = false;
    this.particlesCount = 0;
    this.particles = [];
    this.colors = [
      'hsl(200, 100%, 40%)',
      'hsl(200, 100%, 45%)',
      'hsl(200, 100%, 60%)',
      'hsl(200, 100%, 65%)',
      'hsl(200, 100%, 75%)'
    ];
    this.dpi = 500;

    this.initScene();
    this.renderScene();
    window.addEventListener('mousemove', this.onmouseCoordMove.bind(this));
    window.addEventListener('touchmove', this.onTouchMove.bind(this));
    window.addEventListener('touchend', this.onTouchEnd.bind(this));
    window.addEventListener('resize', this.onResize.bind(this));

  }
  destroyed() {
    window.removeEventListener('mousemove', this.onmouseCoordMove);
    window.removeEventListener('touchmove', this.onTouchMove);
    window.removeEventListener('touchend', this.onTouchEnd);
    window.removeEventListener('resize', this.onResize);    
  }
  onResize() {
    if (!this.sceneResize) {
      requestAnimationFrame(() => {
        this.initScene();
        this.sceneResize = false;
      });
      this.sceneResize = true;
    }    
  }
  getRandomInt(max) {
    return Math.floor(Math.random() * Math.floor(max));
  }


  renderScene() {
    this.context.clearRect(0, 0, this.canvas.width, this.canvas.height);
    let isDisableMouse = false;
    if ((this.previousMouseCoord.x === this.mouseCoord.x) && (this.previousMouseCoord.x === this.mouseCoord.x)) {
      isDisableMouse = true;
    } else {
      this.previousMouseCoord.x = this.mouseCoord.x;
      this.previousMouseCoord.x = this.mouseCoord.x;
      isDisableMouse = false;
    }
    for (let i = 0; i < this.particlesCount; i++) {
      this.particles[i].render(isDisableMouse);
    }
    requestAnimationFrame(this.renderScene.bind(this));
  }
  
  onmouseCoordMove(e) {
    this.mouseCoord.x = e.clientX;
    this.mouseCoord.y = e.clientY;
  }
  
  onTouchMove(e) {
    if(e.touches.length > 0 ) {
      this.mouseCoord.x = e.touches[0].clientX;
      this.mouseCoord.y = e.touches[0].clientY;
    }
  }
  
  onTouchEnd() {
    this.mouseCoord.x = -9999;
    this.mouseCoord.y = -9999;
  }
  
  initScene() {
    this.ch = this.canvas.height = this.canvas.getBoundingClientRect().height;
    this.cw = this.canvas.width = this.canvas.getBoundingClientRect().width;
    this.context.clearRect(0, 0, this.canvas.width, this.canvas.height);
    this.context.font = 'bold ' + (this.cw / 10) + 'px sans-serif';
    this.context.fillStyle = this.sceneBackground;
    this.context.textAlign = 'center';
    this.context.fillText(this.text, this.cw / 2, this.ch / 2);
    let imageData = this.context.getImageData(0, 0, this.cw, this.ch).data;
    this.context.clearRect(0, 0, this.canvas.width, this.canvas.height);
    this.context.globalCompositeOperation = 'screen';
    this.particles = [];
    for(let y = 0; y < this.ch; y += Math.round(this.cw / this.dpi)) {
      for(let x = 0; x < this.cw; x += Math.round(this.cw / this.dpi)) {
        if(imageData[((x + y * this.cw) * 4) + 3] > 128){
          this.particles.push(new Particle(x, y, this));
        }
      }
    }
    this.particlesCount = this.particles.length;
  }  
  
}