<template>
  <el-dialog :model-value="modelValue" title="床位导入分配" width="1000px" :before-close="handleClose">
    <div style="padding-bottom:30px" v-loading="state.loading">
      <div class="top-button">
        <el-button type="text" size="small" @click="exportModel" style="text-decoration: underline">导入模板下载</el-button>
        <div style="display:flex">
          <el-upload style="margin:0 10px" ref="uploadRef" :headers="{ Authorization: `bearer ${state.uploadHeader}` }"
            class="upload-demo" :show-file-list="false" :action="state.uploadUrl" :before-upload="beforeUpload"
            :on-success="uploadSuccess" :on-error="uploadError">
            <el-button type="success" icon="el-icon-daoru" size="small">导入数据</el-button>
          </el-upload>
          <el-button @click="save" type="primary" size="small" :disabled="!state.ImportRoomInfoRes.projectNo">保存
          </el-button>
        </div>
      </div>
      <div class="table-box">
        <el-table :data="state.dataList" border>
          <!-- <el-table-column show-overflow-tooltip prop="" type="selection" align="center"></el-table-column> -->
          <el-table-column show-overflow-tooltip prop="buildName" label="楼栋" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip prop="unitName" label="单元" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip prop="floorName" label="楼层" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip prop="roomNo" label="房间号" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip prop="bedNum" label="床位号" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip prop="userCode" label="人员编号" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip prop="userName" label="人员姓名" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip prop="deptName" label="组织机构" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip width="200px" prop="resultMsg" label="校验结果" align="center">
            <template #default="scope">
              <div :style="{ color: scope.row.resultMsg!='校验通过' ? 'red' : 'green' }">
                {{ scope.row.resultMsg }}
              </div>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination">
          <el-pagination :currentPage="state.form.currentPage" :page-size="state.form.pageSize"
            :page-sizes="[10, 20, 30, 40]" background layout="total, sizes, prev, pager, next, jumper"
            :total="state.total" @size-change="handleSizeChange" @current-change="handleCurrentChange">
          </el-pagination>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { ElDialog, ElButton, ElTable, ElTableColumn, ElPagination, ElUpload, ElMessage } from "element-plus"
import { reactive, watch } from 'vue'
import { getToken, downloadXlsx } from "@/utils";
import { bedAssignImportTemplate, getImportBedAssignList, saveImportBedAssign } from "@/applications/eccard-dorm/api";

export default {
  props: {
    modelValue: {
      type: Boolean,
      default: false
    }
  },
  components: {
    ElDialog,
    ElButton,
    ElTable,
    ElTableColumn,
    ElPagination,
    ElUpload,
  },
  setup(props, context) {
    const state = reactive({
      loading: false,
      uploadHeader: getToken(),
      uploadUrl: `${CONFIG.BASE_API_PATH}eccard-dorm/bedAlloc/importRoomBedAllocate`,
      form: {
        currentPage: 1,
        pageSize: 10
      },
      dataList: [],
      total: 0,
      ImportRoomInfoRes: {}
    });
    watch(() => props.modelValue, val => {
      if (val) {
        state.dataList = []
        state.total = 0
        state.ImportRoomInfoRes = {}
      }
    })
    const getList = async () => {
      state.loading = true
      try {
        let { data: { pageInfo: { dataList, totalCount }, errorCount, projectNo } } = await getImportBedAssignList(state.form)
        state.dataList = dataList
        state.total = totalCount
        state.ImportRoomInfoRes = {
          errorCount,
          projectNo
        }
        state.loading = false
      }
      catch {
        state.loading = false
      }
    }
    const beforeUpload = () => {
      console.log(1);
      state.loading = true
    }
    const uploadSuccess = ({ code, data, message }) => {
      if (code === 0) {
        state.form.projectNo = data.projectNo
        getList();
      } else {
        ElMessage.error(message);
      }
      state.loading = false
    };
    const uploadError = () => {
      state.loading = false
    }
    const save = async () => {
      if (!state.dataList.length) {
        return ElMessage.error("导入列表为空，请重新导入！")
      }
      if (state.ImportRoomInfoRes.errorCount !== 0) {
        return ElMessage.error('当前床位分配数据有错误信息，请检查后重新上传！')
      }
      state.loading = true
      try {
        let { code, message } = await saveImportBedAssign(state.ImportRoomInfoRes.projectNo)
        if (code === 0) {
          ElMessage.success(message)
          context.emit("update:modelValue", true)
        }
        state.loading = false
      }
      catch {
        state.loading = false
      }

    }
    const exportModel = async () => {
      state.loading = true
      let res = await bedAssignImportTemplate()
      downloadXlsx(res, "床位分配导入列表.xlsx")
      state.loading = false
    }
    const handleSizeChange = (val) => {
      state.form.currentPage = 1
      state.form.pageSize = val
      getList()
    }
    const handleCurrentChange = (val) => {
      state.form.currentPage = val
      getList()
    }
    const handleClose = () => {
      context.emit("update:modelValue", false)
    };
    return {
      state,
      beforeUpload,
      uploadSuccess,
      uploadError,
      exportModel,
      save,
      handleSizeChange,
      handleCurrentChange,
      handleClose,
    }
  }
}
</script>

<style lang="scss" scoped>
.top-button {
  display: flex;
  justify-content: space-between;
  align-content: center;
  margin: 20px 0;
}

.table-box {

  // border: 1px solid #eeeeee;
  .pagination {
    padding: 10px;
    border: 1px solid #eeeeee;
    border-top: 0;
  }
}
</style>