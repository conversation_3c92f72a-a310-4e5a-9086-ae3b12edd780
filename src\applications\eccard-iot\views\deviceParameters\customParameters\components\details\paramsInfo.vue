<template>
  <el-tabs v-model="state.tab" class="demo-tabs" @tab-click="handleClick" v-if="state.tabs.length">
    <el-tab-pane v-for="(item, index) in state.tabs" :key="index" :label="item.paramsName" :name="item.id">
      <kade-route-card>
        <el-form label-width="120px" size="mini" inline>
          <el-form-item :label="v.paramsName" v-for="(v, i) in item.children" :key="i">
            <el-input readonly v-model="state.form[v.paramsCode]"></el-input>
          </el-form-item>
        </el-form>
      </kade-route-card>
    </el-tab-pane>
  </el-tabs>
  <el-empty v-else description="暂无数据"></el-empty>
</template>
<script>
import { reactive, onMounted } from 'vue'
import {
  ElTabs, ElTabPane, ElForm, ElFormItem,
  ElInput, ElEmpty
} from "element-plus";
import { makeTree } from "@/utils"
export default {
  components: {
    ElTabs, ElTabPane, ElForm, ElFormItem,
    ElInput, ElEmpty
  },
  props: {
    data: {
      type: Object,
      default: null
    },
  },
  setup(props) {
    const state = reactive({
      form: {},
      tabs:[]
    })
    const handleClick = (val) => {
      state.form = {}
      let a = state.tabs.find(item => item.id == val.props.name)
      state.tabData = a
      if (a && a.children && a.children.length) {
        a.children.forEach(item => {
          state.form[item.paramsCode] = item.paramsFormat
        })
      }
      let list = JSON.parse(sessionStorage.getItem("customParamsList"))
      let listCutItem = list.find(item => item.id == props.data.id)
      console.log(listCutItem, a);
      if (listCutItem && listCutItem.paramsData) {
        state.form = { ...listCutItem.paramsData[a.paramsCode] }
      }
    }
    onMounted(() => {
      let data = JSON.parse(sessionStorage.getItem('templateList')).find(item => item.templateName == props.data.paramsTemplate)
      console.log(data)

      state.tabs = makeTree(data.paramsList, "id", "parentId", "children");
      if (state.tabs.length) {
        state.tab = state.tabs[0].id
      }
      handleClick({
        props: {
          name: state.tab
        }
      })
    })
    return {
      state,
      handleClick
    }
  }
}
</script>