<template>
  <el-input
    :model-value="modelValue"
    readonly
    suffix-icon="el-icon-arrow-down"
    :placeholder="placeholder"
    @click="handleToggle"
    class="kade-select-icon"
  >
    <template #suffix v-if="modelValue">
      <i class="el-icon-circle-close" @click.stop="handleClear"></i>
    </template>  
  </el-input>
  <el-dialog
    :title="placeholder"
    v-model="state.visible"
    :width="600"
    custom-class="kade-icon-dialog"
    append-to-body
  >  
    <div class="list">
      <div
        :class="['item', { selected: state.selected === item.class }]"
        v-for="item in state.icons"
        :key="item.class"
        @click="handleSelect(item.class)"
      >
        <el-tooltip effect="dark" :content="item.name" placement="left-start">
          <i :class="item.class"></i>
        </el-tooltip>
      </div>
    </div>  
    <template #footer>
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSure">确定</el-button>
    </template>
  </el-dialog>  
</template>
<script>
// 图标选择

import { 
  ElInput,
  ElDialog,
  ElButton,
  ElTooltip,
} from 'element-plus';
import { reactive } from 'vue';
import icons from '@/assets/font/iconfont.json';
export default {
  emits: ['update:modelValue', 'change'],
  components: {
    'el-input': ElInput,
    'el-dialog': ElDialog,
    'el-button': ElButton,
    'el-tooltip': ElTooltip,
  },
  props: {
    placeholder: {
      type: String,
      default: '请选择'
    },
    modelValue: {
      type: String,
    }
  },
  setup(props, context) {
    const state = reactive({
      visible: false,
      selected: null,
      icons: [...icons.glyphs.map(it => {
        return {
          class: it.font_class.includes('el-icon-') ? it.font_class : `el-icon-${it.font_class}`,
          name: it.name,
        };
      }), ...ELEMENTICONS],
    });
    const handleCancel = () => {
      state.visible = false;
    }
    const handleSure = () => {
      state.visible = false;
      if (state.selected !== props.modelValue && state.selected) {
        context.emit('update:modelValue', state.selected);
        context.emit('change', state.selected);
      }
    }
    const handleToggle = () => {
      state.visible = true;
    }
    const handleSelect = (cls) => {
      state.selected = cls;
    }

    const handleClear = () => {
      context.emit('update:modelValue', '');
      context.emit('change', '');
    }    
    return {
      handleCancel,
      handleSure,
      handleClear,
      state,
      handleToggle,
      handleSelect,
    }
  }
}
</script>
<style lang="scss">
.kade-select-icon{
  .el-icon-circle-close{
    display: none;
  }
  &:hover{
    .el-icon-circle-close{
      display: inline-block;
    }
  }
}
.kade-icon-dialog{
  .el-dialog__body{
    max-height: 400px;
    overflow-y: auto;
  }
  .list{
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    flex-wrap: wrap;
    .item{
      width: 40px;
      height: 40px;
      border: 1px solid $border-color;
      margin: 2px;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      i{
        font-size: 24px;
      }
      &.selected{
        border-color: $primary-color;
      }
    }
  }
}
</style>