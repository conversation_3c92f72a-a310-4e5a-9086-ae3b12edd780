<template>
  <el-dialog :model-value="modelValue" title="首卡设置详情" width="1700px" :before-close="handleClose"
    :close-on-click-modal="false">
    <div style="padding:20px 0 0">
      <kade-table-wrap title="首卡开门" icon="none">
        <el-divider></el-divider>
        <div class="padding-box">
          <el-checkbox v-model="state.form.checked" size="large" style="margin:10px 0">启用首卡开门</el-checkbox>
          <el-row :gutter="20">
            <el-col :span="6">
              <kade-table-wrap title="开始" icon="none" style="height:100%; padding: 0;">
                <el-divider></el-divider>
                <div class="padding-box">
                  <el-form size="mini" label-width="100px">
                    <el-form-item label="开始时间：">
                      <el-input v-model="state.form.b"></el-input>
                    </el-form-item>
                    <el-form-item label="控制方式1：">
                      <el-input v-model="state.form.b"></el-input>
                    </el-form-item>
                  </el-form>
                  <div style="color:#FF8726">提示：在开始时间后，用户刷首卡后，会自动切换到控制方式1；</div>
                </div>
              </kade-table-wrap>
            </el-col>
            <el-col :span="6">
              <kade-table-wrap title="结束" icon="none" style="height:100%; padding: 0;">
                <el-divider></el-divider>
                <div class="padding-box">
                  <el-form size="mini" label-width="100px">
                    <el-form-item label="结束时间：">
                      <el-input v-model="state.form.b"></el-input>
                    </el-form-item>
                    <el-form-item label="控制方式2：">
                      <el-input v-model="state.form.b"></el-input>
                    </el-form-item>
                  </el-form>
                  <div style="color:#FF8726">提示：在结束时间后，门回到控制方式2，并且用户刷首卡不会切换到控制方式1；</div>
                </div>
              </kade-table-wrap>
            </el-col>
            <el-col :span="12">
              <kade-table-wrap title="星期控制" icon="none" style="height:100%; padding: 0;">
                <el-divider></el-divider>
                <div class="padding-box week-box">
                  <el-checkbox-group v-model="state.form.checkList">
                    <el-checkbox v-for="(item, index) in weekList" :key="index" :label="item.value">{{ item.label }}
                    </el-checkbox>
                  </el-checkbox-group>
                </div>
              </kade-table-wrap>
            </el-col>
          </el-row>
        </div>
      </kade-table-wrap>
      <kade-table-wrap title="首卡用户选择" icon="none">
        <el-divider></el-divider>
        <div class="padding-box">
          <el-form size="mini" label-width="100px" inline>
            <el-form-item label="组织机构">
              <el-select>
                <el-option v-for="(item, index) in 10" :key="index" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="身份类别">
              <el-select>
                <el-option v-for="(item, index) in 10" :key="index" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="关键字">
              <el-input v-model="state.form.b" placeholder="姓名或编号关键字搜索"></el-input>
            </el-form-item>
        <el-button type="primary" size="mini">搜索</el-button>

          </el-form>
          <el-table border v-loading="state.loading" :data="state.dataList">
            <el-table-column show-overflow-tooltip prop="name" label="组织机构" align="center"></el-table-column>
            <el-table-column show-overflow-tooltip prop="ip" label="身份类别" align="center"></el-table-column>
            <el-table-column show-overflow-tooltip prop="ip" label="用户编号" align="center"></el-table-column>
            <el-table-column show-overflow-tooltip prop="ip" label="用户名称" align="center"></el-table-column>
          </el-table>
          <div class="pagination">
            <el-pagination v-model:currentPage="state.form.pageNum" v-model:page-size="state.form.pageSize"
              :page-sizes="[10, 20, 30, 40]" background layout="total, sizes, prev, pager, next, jumper"
              :total="state.total" @size-change="handleSizeChange" @current-change="handleCurrentChange">
            </el-pagination>
          </div>
        </div>
      </kade-table-wrap>

    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose" size="mini">取消</el-button>
        <el-button type="primary" v-if="type != 'details'" @click="submit" size="mini">保存</el-button>
        <el-button type="primary" v-else @click="$emit('edit')" size="mini">编辑</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script>
import { ElDialog, ElDivider, ElRow, ElCol, ElButton, ElForm, ElFormItem, ElSelect, ElOption, ElInput, ElCheckboxGroup, ElCheckbox,ElTable,ElTableColumn,ElPagination } from "element-plus"
import { reactive } from '@vue/reactivity'
const weekList = [
  { label: '星期一', value: 1 },
  { label: '星期二', value: 2 },
  { label: '星期三', value: 3 },
  { label: '星期四', value: 4 },
  { label: '星期五', value: 5 },
  { label: '星期六', value: 6 },
  { label: '星期日', value: 7 },
]
export default {
  components: {
    ElDialog, ElDivider, ElRow, ElCol, ElButton, ElForm, ElFormItem, ElSelect, ElOption, ElInput, ElCheckboxGroup, ElCheckbox,ElTable,ElTableColumn,ElPagination
  },
  emits: ["update:modelValue"],
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
  },
  setup(props, context) {
    const state = reactive({
      form: {
        checkList: []
      },
      dataList:[],
      total:0
    })
    const handleClose = () => {
      context.emit("update:modelValue", false)
    }
    return {
      weekList,
      state,
      handleClose
    }
  }
}
</script>
<style lang="scss" scoped>
:deep(.kade-table-wrap) {
  margin-bottom: 20px;
  // padding: 0;
}

.padding-box {
  padding: 10px 10px 0
}

.el-divider--horizontal {
  margin: 0
}

.week-box {
  height: 140px;
  display: flex;
  // justify-content: center;
  align-items: center;
}

:deep(.box) {
  padding: 10px 10px 0;

  .el-form {
    .el-select {
      width: 150px;
    }

    .el-input {
      width: 150px
    }
  }
}

:deep(.el-select) {
  width: 100%;
}

.el-table {
  border-left: 1px solid #EBEEF5;
  border-right: 1px solid #EBEEF5;
}
</style>