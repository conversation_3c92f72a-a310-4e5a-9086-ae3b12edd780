<template>
  <kade-modal
    v-bind="attrs"
    :modelValue="modelValue"
    @update:modelValue="update"
  >
    <el-form ref="formRef" label-width="120px" :rules="rules" :model="state.model" size="small">
      <el-form-item label="权限名称" prop="permName">
        <el-input placeholder="请输入" v-model="state.model.permName" />
      </el-form-item>
      <el-form-item label="URL权限标识" prop="requestPath">
        <el-input v-model="state.model.requestPath" placeholder="请输入" class="input-with-select">
          <template #prepend>
            <el-select v-model="state.model.serviceName" placeholder="所属服务" style="width: 130px">
              <el-option :label="item.menuName" :value="item.routePath" v-for="(item,index) in appList" :key="index"></el-option>
            </el-select>
            <el-select v-model="state.model.requestMethod" placeholder="请求方式" style="width: 130px;margin-left: 20px;">
              <el-option :label="item" :value="item" v-for="(item,index) in requestList" :key="index"></el-option>
            </el-select>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item label="按钮权限标识" prop="btnPerm">
        <el-input placeholder="请输入" v-model="state.model.btnPerm" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button icon="el-icon-circle-close" @click="handleCancel" size="small">取消</el-button>
      <el-button icon="el-icon-circle-check" :loading="state.loading" @click="handleSubmit" size="small" type="primary">保存</el-button>
    </template>
  </kade-modal>
</template>
<script>
import { reactive,computed,watch, ref, nextTick } from 'vue'
import { useDict } from "@/hooks/useDict";
import { addMenuAuth,editMenuAuth } from '@/applications/eccard-ops/api';

import { ElForm,ElFormItem,ElInput,ElButton,ElSelect,ElOption, ElMessage } from "element-plus"
import Modal from "@/components/modal";
const requestList=["GET","POST","PUT","PATCH","DELETE"]
const rules = {
  requestPath:[
    {
      required: true,
      message: "请输入URL权限标识",
    },
  ],
  permName: [
    {
      required: true,
      message: "请输入权限名称",
    },
    {
      max: 20,
      message: "权限名称长度不得超过20字符",
    },
  ],
};
export default {
  components:{
    "kade-modal": Modal,
    ElForm,
    ElFormItem,
    ElInput,
    ElButton,
    ElSelect,
    ElOption
  },
  emits: ["update:modelValue", "change"],
  props: {
    title: {
      type: String,
      default: "",
    },
    modelValue: {
      type: Boolean,
      default: false,
    },
    authMenu: {
      type: Object,
      default: null,
    },
    menu: {
      type: Object,
      default: null,
    },
    type:{
      type: String,
      default: "",
    },
    appList:{
      type:Array,
      default:null
    }
  },
  setup(props,context){
    const formRef=ref(null)
    const status = useDict("SYS_ENABLE");
    const state=reactive({
      loading:false,
      model:{}
    })
    const attrs = computed(() => {
      // eslint-disable-next-line no-unused-vars
      const { modelValue, user, ...attrs } = props;
      return attrs;
    });

    watch(()=>props.authMenu,val=>{
      console.log(props.authMenu);
      if(props.type=='add'){
        nextTick(()=>{
          state.model={}
        })
      }else{
        nextTick(()=>{
          let {id,permName,requestMethod,serviceName,btnPerm,requestPath}={...val}
          state.model={id,permName,requestMethod,serviceName,btnPerm,requestPath}
        })
      }
    },
    )
    const handleSubmit=()=>{
      formRef.value.validate(async valid=>{
        if(valid){
          if(!state.model.serviceName){
            return ElMessage.error("请选择所属服务！")
          }
          if(!state.model.requestMethod){
            return ElMessage.error("请选择请求方式！")
          }
          state.loading=true
          let fn =props.type=='add'?addMenuAuth:editMenuAuth
          let params={
            menuId:props.menu.id,
            form:state.model
          }
          let {code,message}=await fn(params)
          if(code===0){
            ElMessage.success(message)
            context.emit("save")
            context.emit("update:modelValue", false);
          }
          state.loading=false
        }else{
          return false
        }
      })
    }
    const handleCancel=()=>{
      formRef.value?.resetFields();
      context.emit("update:modelValue", false);
    }
    const update = (v) => {
      context.emit("update:modelValue", v);
    };
    return {
      requestList,
      rules,
      status,
      formRef,
      state,
      attrs,
      handleSubmit,
      handleCancel,
      update
    }
  }
}
</script>
<style lang="scss" scoped>
  :deep(.el-input__inner){
    margin-right: 1px;
  }
</style>