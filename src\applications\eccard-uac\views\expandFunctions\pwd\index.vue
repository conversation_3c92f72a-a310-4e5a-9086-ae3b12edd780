<template>
  <div class="padding-box">
    <kade-tab-wrap :tabs="tabs" v-model="state.tab">
      <template #skmm>
        <kade-pay-card-pwd />
      </template>
      <template #yhmm>
        <kade-user-pwd />
      </template>
      <template #cjtxmm>
        <kade-super-pass-pwd />
      </template>
      <template #khmm>
        <kade-card-pwd />
      </template>
    </kade-tab-wrap>
  </div>
</template>
<script>
import { reactive } from '@vue/reactivity'
import payCardPwd from './payCardPwd'
import userPwd from './userPwd'
import superPassPwd from './superPassPwd'
import cardPwd from './cardPwd'
const tabs = [
  {
    label: '刷卡+密码',
    name: 'skmm'
  },
  {
    label: '用户密码',
    name: 'yhmm'
  },
  {
    label: '超级通行密码',
    name: 'cjtxmm'
  },
  {
    label: '卡号+密码',
    name: 'khmm'
  },
]
export default {
  name: 'auth',
  components: {
    "kade-pay-card-pwd": payCardPwd,
    "kade-user-pwd": userPwd,
    "kade-super-pass-pwd": superPassPwd,
    "kade-card-pwd": cardPwd,
  },
  setup() {
    const state = reactive({
      tab: 'skmm'
    })
    return {
      tabs,
      state
    }
  },
}
</script>
<style lang="scss" scoped>
:deep(.el-dialog__footer) {
  text-align: center;
}
</style>