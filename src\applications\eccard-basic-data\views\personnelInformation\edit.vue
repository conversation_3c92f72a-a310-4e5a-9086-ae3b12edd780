<template>
  <kade-route-card full>
    <el-tabs v-model="state.activeName">
      <el-tab-pane label="个人信息" name="info">
        <kade-edit v-if="state.activeName === 'info'" />
      </el-tab-pane>
      <!-- <el-tab-pane label="身份识别" name="identification">
        <kade-identification v-if="state.activeName === 'identification'" />
      </el-tab-pane> -->
      <el-tab-pane label="家庭成员" name="family" v-if="id">
        <kade-family v-if="state.activeName === 'family'" />
      </el-tab-pane>
    </el-tabs>
  </kade-route-card>
</template>
<script>
import {
  ElTabPane,
  ElTabs,
} from 'element-plus';
import { reactive } from 'vue';
import { useStore } from 'vuex';
import Edit from './components/edit';
import Family from './components/family';
// import Identification from './components/identification';
export default {
  name: 'PersonnelEdit',
  components: {
    'el-tabs': ElTabs,
    'el-tab-pane': ElTabPane,
    'kade-edit': Edit,
    'kade-family': Family,
    // 'kade-identification': Identification,
  },
  setup() {
    const state = reactive({
      activeName: 'info'
    });
    const store = useStore();
    return {
      state,
      id: store.getters['app/query'].id,
    }
  }
}
</script>