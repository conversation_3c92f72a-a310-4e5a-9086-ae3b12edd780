<template>
  <el-dialog :model-value="modelValue" title="添加门权限" width="1660px" :before-close="handleClose">
    <div v-loading="state.loading">
      <el-form label-width="100px" size="mini" inline style="margin-top:20px">
        <el-form-item label="权限策略">
          <el-select v-model="state.form.authPolicyType" @change="authStrategyChange">
            <el-option v-for="(item, index) in authStrategyList" :key="index" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div class="auth-box">
        <kade-table-wrap title="授权人员" v-loading="state.loading" icon="none">
          <el-divider></el-divider>
          <div class="auth-list" v-if="state.form.authPolicyType !== 'DEPT_AUTHORIZE'" style="width:800px">
            <el-form inline size="mini" label-width="100px" style="margin-top:20px">
              <el-form-item label="类别名称" v-if="state.form.authPolicyType == 'ROLE_AUTHORIZE'">
                <el-input placeholder="输入类别名称" v-model="state.personForm.roleName" />
              </el-form-item>
              <el-form-item label="分组名称" v-else>
                <el-input placeholder="输入组名称" v-model="state.personForm.groupName" />
              </el-form-item>
              <el-button type="primary" size="mini" @click="personSearch">搜索</el-button>
            </el-form>
            <el-table :data="state.personDataList" @selection-change="handlePersonSelectChange" border height="35vh">
              <el-table-column type="selection" width="55" align="center" />
              <el-table-column v-if="state.form.authPolicyType == 'ROLE_AUTHORIZE'" show-overflow-tooltip
                prop="roleName" label="身份类别" align="center"></el-table-column>
              <el-table-column v-else show-overflow-tooltip prop="groupName" label="人员分组" align="center">
              </el-table-column>
            </el-table>
          </div>
          <div class="tree" v-else>
            <el-tree class="area-tree" ref="treeRef" :data="state.departCheckTreeList" :props="defaultProps"
              default-expand-all show-checkbox @check-change="handleCheckChange">
            </el-tree>
          </div>
        </kade-table-wrap>
        <kade-table-wrap title="授权门禁" v-loading="state.loading" icon="none"
          :style="{ width: state.form.authPolicyType == 'DEPT_AUTHORIZE' ? '1200px' : '800px' }">
          <template #extra>
            <el-radio-group v-model="state.form.authDeviceMode" @change="authDeviceModeChange">
              <el-radio :label="item.value" v-for="(item, index) in groupList" :key="index">{{ item.label }}</el-radio>
            </el-radio-group>
          </template>
          <el-divider></el-divider>
          <div class="auth-list">
            <el-form inline size="mini" label-width="100px" style="margin-top:20px">
              <el-form-item label="组名称" v-if="state.form.authDeviceMode == 'ACCESS_GROUP_AUTHORIZE'">
                <el-input placeholder="输入组名称" v-model="state.deviceForm.groupName" clearable />
              </el-form-item>
              <el-form-item label="区域" v-else>
                <kade-area-select-tree style="width: 100%" :value="state.deviceForm.areaPath" valueKey="areaPath"
                  :multiple="false" @valueChange="(val) => (state.deviceForm.areaPath = val.areaPath)" />
              </el-form-item>
              <el-button type="primary" size="mini" @click="deviceSearch">搜索</el-button>
            </el-form>
            <el-table :data="state.deviceDataList" @selection-change="handleDeviceSelectChange" border height="35vh">
              <el-table-column type="selection" width="55" align="center" />
              <el-table-column show-overflow-tooltip v-if="state.form.authDeviceMode == 'ACCESS_GROUP_AUTHORIZE'"
                prop="groupName" label="门禁组名称" align="center"></el-table-column>
              <el-table-column show-overflow-tooltip v-else prop="userName" label="区域" align="center">
                <template #default="{ row }">
                  {{ `${row.areaName}>${row.buildName ? row.buildName +
                      '>' : ''}${row.unitNumName ? row.unitNumName + '>' : ''}${row.floorNumName ? row.floorNumName + '>' :
                        ''}${row.deviceName}${row.doorName ? '>' + row.doorName : ''}`
                  }}
                </template>
              </el-table-column>
            </el-table>
          </div>
        </kade-table-wrap>
      </div>
      <kade-table-wrap title="其它选项" icon="none" style="margin:20px 0">
        <el-divider></el-divider>
        <el-form style="margin-top:20px" label-width="120px" size="mini" ref="formRef" :rules="rules"
          :model="state.form" inline>
          <el-form-item label="时段：" prop="authPeriod">
            <el-select v-model="state.form.authPeriod">
              <el-option v-for="(item, index) in state.periodNoList" :key="index" :label="item.periodNo"
                :value="item.periodNo"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="一次刷卡限制：" prop="oneCard">
            <el-radio-group v-model="state.form.oneCard">
              <el-radio :label="item.value" v-for="(item, index) in boolList" :key="index">{{ item.label }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="授权时间：" prop="authBeginTime">
            <el-date-picker v-model="state.form.authBeginTime" type="datetime" placeholder="请选择时间" />
          </el-form-item>
          <el-form-item label="过期时间：" prop="authEndTime">
            <el-date-picker v-model="state.form.authEndTime" type="datetime" placeholder="请选择时间" />
          </el-form-item>
        </el-form>
        <div class="red" style="margin-left:20px">说明：时段和一次刷卡限制仅支持门禁控制器和通道控制器设备</div>
      </kade-table-wrap>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button type="primary" @click="handleSave" size="mini">保存</el-button>
        <el-button @click="handleClose" size="mini">返回</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { ElDialog, ElDivider, ElButton, ElForm, ElFormItem, ElInput, ElSelect, ElOption, ElTree, ElRadioGroup, ElRadio, ElDatePicker, ElTable, ElTableColumn, ElMessage } from "element-plus"
import { reactive, ref, watch, nextTick, onMounted } from 'vue'
import { useDict } from "@/hooks/useDict";
import { makeTree } from "@/utils/index.js";
import { timeStr } from "@/utils/date.js";
import { getUserOrgPurview, getRolelist } from '@/applications/eccard-basic-data/api';
import { acsGroupingInfoList, getDevicePeriod, acsPolicyAuthorizeAdd, acsDeviceList } from "@/applications/eccard-uac/api";
import areaSelectTree from "@/components/tree/areaSelectTree.vue";
const defaultProps = {
  children: "children",
  label: "deptName",
};
const rules = {
  authPeriod: [
    {
      required: true,
      message: "请选择时段",
      trigger: "change",
    },
  ],
  oneCard: [
    {
      required: true,
      message: "请选择一次刷卡限制",
      trigger: "change",
    },
  ],
  authBeginTime: [
    {
      required: true,
      message: "请选择授权时间",
      trigger: "change",
    },
  ],
  authEndTime: [
    {
      required: true,
      message: "请选择过期时间",
      trigger: "change",
    },
  ],
}
export default {
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    rowData: {
      types: Object,
      default: null
    },
    editType: {
      types: String,
      default: ''
    }
  },
  components: {
    ElDialog,
    ElDivider,
    ElButton,
    ElForm,
    ElFormItem,
    ElInput,
    ElTree, ElRadioGroup, ElRadio, ElSelect, ElOption, ElDatePicker, ElTable, ElTableColumn,
    "kade-area-select-tree": areaSelectTree,
  },
  setup(props, context) {
    const treeRef = ref(null)
    const formRef = ref(null)
    const boolList = useDict("SYS_BOOL_INT");
    const authStrategyList = useDict("ACS_AUTHORIZE_STRATEGY");
    const groupList = useDict("ACS_AUTHORIZE_ACCESS");
    const state = reactive({
      loading: false,
      isPersoner: false,
      departCheckTreeList: [],
      periodNoList: [],
      personForm: {},
      deviceForm: {},
      personDataList: [],
      deviceDataList: [],
      personSelectList: [],
      deviceSelectList: [],
      form: {
        authPolicyType: "ROLE_AUTHORIZE",
        authDeviceMode: "ACCESS_GROUP_AUTHORIZE"
      },
    });
    watch(() => props.modelValue, val => {
      if (val) {
        state.form = {
          authPolicyType: "ROLE_AUTHORIZE",
          authDeviceMode: "ACCESS_GROUP_AUTHORIZE"
        }
        queryRolelist()
        queryDeviceGrouplist()
        nextTick(() => {
          formRef.value.clearValidate()
        })
      }
    })
    const getPeriodNoList = () => {
      getDevicePeriod().then((res) => {
        state.periodNoList = res.data
      })
    }
    const queryDepartCheckList = () => {
      getUserOrgPurview().then((res) => {
        let arr = makeTree(res.data, "id", "deptParentId", "children");
        state.departCheckTreeList = [...arr];
      });
    };
    const handleCheckChange = (val, isSelect) => {
      if (isSelect) {
        state.personSelectList.push({ deptName: val.deptName, id: val.id })
      } else {
        state.personSelectList = state.personSelectList.filter(item => item.id != val.id)
      }
    }
    const authStrategyChange = (val) => {
      state.personForm = {}
      state.personDataList = []
      state.personSelectList = []
      if (val === 'ROLE_AUTHORIZE') {
        queryRolelist()
      } else if (val === 'USER_GROUP_AUTHORIZE') {
        queryPersonGrouplist()
      }
    }
    const authDeviceModeChange = val => {
      state.deviceForm = {}
      state.deviceDataList = []
      state.deviceSelectList = []
      if (val === 'ACCESS_GROUP_AUTHORIZE') {
        queryDeviceGrouplist()
      } else {
        getDeviceList()
      }
    }
    //获取角色列表
    const queryRolelist = async () => {
      let { data } = await getRolelist();
      if (state.personForm.roleName) {
        state.personDataList = data.filter(item => item.roleName.includes(state.personForm.roleName))
      } else {
        state.personDataList = data;
      }
    };
    //获取人员组列表
    const queryPersonGrouplist = async () => {
      let params = {
        ...state.personForm,
        groupType: "userGroup"
      }
      let { data } = await acsGroupingInfoList(params);
      state.personDataList = data;
    };
    //获取设备组列表
    const queryDeviceGrouplist = async () => {
      let params = {
        ...state.deviceForm,
        groupType: "deviceGroup"
      }
      let { data } = await acsGroupingInfoList(params);
      state.deviceDataList = data;
    };
    const getDeviceList = async () => {
      let { data } = await acsDeviceList(state.deviceForm)
      state.deviceDataList = data
    }
    const personSearch = () => {
      if (state.form.authPolicyType === 'ROLE_AUTHORIZE') {

        queryRolelist()

      } else if (state.form.authPolicyType === 'USER_GROUP_AUTHORIZE') {
        queryPersonGrouplist()
      }
    }
    const deviceSearch = () => {
      if (state.form.authDeviceMode === 'ACCESS_GROUP_AUTHORIZE') {
        queryDeviceGrouplist()
      } else {
        getDeviceList()
      }
    }
    const handlePersonSelectChange = (val) => {
      state.personSelectList = val
    }
    const handleDeviceSelectChange = (val) => {
      state.deviceSelectList = val
    }
    const handleSave = () => {
      if (!state.personSelectList.length) {
        return ElMessage.error("请选择人员组或角色或组织机构！")
      }
      if (!state.deviceSelectList.length) {
        return ElMessage.error("请选择门禁组或门禁！")
      }
      let personValueKeyData = {}
      if (state.form.authPolicyType == 'ROLE_AUTHORIZE') {
        personValueKeyData = {
          labelKey: "roleName",
          valueKey: "id"
        }
      } else if (state.form.authPolicyType == 'USER_GROUP_AUTHORIZE') {
        personValueKeyData = {
          labelKey: "groupName",
          valueKey: "id"
        }
      } else {
        personValueKeyData = {
          labelKey: "deptName",
          valueKey: "id"
        }
      }
      let deviceValueKeyData = {}
      if (state.form.authDeviceMode == 'ACCESS_GROUP_AUTHORIZE') {
        deviceValueKeyData = {
          labelKey: "groupName",
          valueKey: "id"
        }
      } else {
        deviceValueKeyData = {
          labelKey: "deviceName",
          valueKey: "id"
        }
      }

      let params = {
        authPersonVos: state.personSelectList.map(item => {
          return {
            authPersonName: item[personValueKeyData.labelKey],
            authPersonId: item[personValueKeyData.valueKey],
          }
        }),
        authDeviceVos: state.deviceSelectList.map(item => {
          return {
            authDeviceName: item[deviceValueKeyData.labelKey],
            authDevice: item[deviceValueKeyData.valueKey],
          }
        }),
        ...state.form,
        authBeginTime: timeStr(state.form.authBeginTime),
        authEndTime: timeStr(state.form.authEndTime),
      }
      formRef.value.validate(async valid => {
        if (valid) {
          console.log(params)
          try {
            state.loading = true
            let { code, message } = await acsPolicyAuthorizeAdd(params)
            if (code === 0) {
              ElMessage.success(message)
              context.emit("update:modelValue", true)
              state.personForm = {}
              state.deviceForm = {}
              state.personDataList = []
              state.deviceDataList = []
              state.personSelectList = []
              state.deviceSelectList = []
            }
            state.loading = false
          }
          catch {
            state.loading = false
          }
        } else {
          return false
        }
      })
    }
    const handleClose = () => {
      context.emit("update:modelValue", false)
    };
    onMounted(() => {
      queryDepartCheckList()
      getPeriodNoList()
    })
    return {
      defaultProps,
      treeRef,
      formRef,
      boolList,
      groupList,
      state,
      authStrategyList,
      rules,
      handleCheckChange,
      authStrategyChange,
      authDeviceModeChange,
      personSearch,
      deviceSearch,
      handlePersonSelectChange,
      handleDeviceSelectChange,
      handleSave,
      handleClose,
    }
  }
}
</script>

<style lang="scss" scoped>
.auth-box {
  width: 100%;
  display: flex;
  justify-content: space-between;
  height: 100%;

  .tree {
    box-sizing: border-box;
    width: 400px;
    height: 500px;
    padding: 10px;
    overflow-y: auto;
  }
}

:deep(.auth-list) {
  width: 100%;

  .kade-table-wrap {
    width: 46%;
    flex: none !important;
  }
}

:deep(.box) {
  width: 100%
}

.el-divider--horizontal {
  margin: 0
}
</style>