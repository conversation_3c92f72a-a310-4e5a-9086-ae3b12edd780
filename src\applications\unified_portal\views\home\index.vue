<template>
  <div class="app-box">
    <div class="app-box-left">
      <div class="portal-panel">
        <div class="portal-panel-header blue">
          <div class="left">
            <span class="title">应用中心</span>
            <span class="icon" @click="handleToggleShowAll">
              <i class="el-icon-quanbu"></i>
              <span class="text">全部应用</span>
            </span>
          </div>
          <div class="right">
            <el-input v-model="state.keyword" suffix-icon="el-icon-sousuo" placeholder="输入应用关键字搜索" size="small">
            </el-input>
          </div>
        </div>
        <div class="portal-panel-body">
          <div class="app-list" ref="appListRef">
            <div class="app-item" v-for="item in list" :key="item.id" @click="handleAppClick(item)">
              <el-image @load="handleImageLoad" :src="item.menuIcon" :alt="item.menuName" fit="cover">
                <template #placeholder>
                  <div class="image-slot">
                    加载中<span class="dot">...</span>
                  </div>
                </template>
                <template #error>
                  <div class="image-slot" :style="{ height: state.height + 'px' }">
                    <i class="el-icon-picture-outline"></i>
                    <span class="text">{{ item.menuName }}</span>
                  </div>
                </template>
              </el-image>
            </div>
            <div class="app-item slot"></div>
            <div style="width: 100%" v-if="!list.length">
              <el-empty description="无数据" />
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- <div class="app-box-right">
      <div class="portal-panel top">
        <div class="portal-panel-header green">
          <div class="left">
            <span class="title">待办事项</span>
          </div>
          <div class="right" @click="handleTodo">
            <span class="more"><span class="text">MORE</span><i class="el-icon-fenzu"></i></span>
          </div>
        </div>
        <div class="portal-panel-body">
          <div class="todo-list">
            <div class="todo-item" :class="index > 3 && 'unred'" v-for="(item, index) in todoList" :key="index">
              <div class="til">
                <i :class="item.icon"></i>
                <span class="text">{{ item.label }}</span>
              </div>
              <div class="tir">
                <span class="num">{{ item.value }}</span>
                <i class="el-icon-arrow-right"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="portal-panel bottom">
        <div class="portal-panel-header yellow">
          <div class="left">
            <span class="title">系统通知</span>
          </div>
          <div class="right" @click="handleNotice">
            <span class="more"><span class="text">MORE</span><i class="el-icon-fenzu"></i></span>
          </div>
        </div>
        <div class="portal-panel-body">
          <div class="notice-list">
            <div class="notice-item" :class="index > 5 && 'read'" v-for="(item, index) in 10" :key="index">
              <div class="nil">
                <i class="el-icon-tongzhi"></i>
              </div>
              <div class="nir">
                <div class="title">
                  <span class="text">系统通知标题</span>
                  <el-tag size="mini" type="success">未读</el-tag>
                </div>
                <div class="desc">
                  <i class="el-icon-shijian"></i>
                  <span class="text">2021.7.14 07:30:00</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div> -->
  </div>
  <!--   <div class="copyright">
    成都卡德智能科技有限公司 Copyright @ 2021-2022 All right Reserved
  </div> -->
</template>
<script>
import { ElInput, ElImage, ElMessage, ElEmpty } from "element-plus";
import { getUserApplyList } from "@/applications/unified_portal/api";
import { reactive, onMounted, computed, ref, onUnmounted } from "vue";
import { useRouter } from "vue-router"
// import { getToken } from "@/utils";
import Logo from "@/assets/u18.png";

// import request from '@/service';
const todoList = [
  { label: "人员开户", value: 0, icon: "el-icon-ren" },
  { label: "商家结算", value: 0, icon: "el-icon-caiwuchazhang" },
  { label: "充值审核", value: 0, icon: "el-icon-chongzhi" },
  { label: "补助审核", value: 0, icon: "el-icon-buzhuxiangmu" },
  { label: "次数审核", value: 0, icon: "el-icon-cishu1" },
  { label: "设备未绑定", value: 0, icon: "el-icon-shebei" },
]
export default {
  components: {
    "el-input": ElInput,
    // "el-tag": ElTag,
    "el-image": ElImage,
    "el-empty": ElEmpty,
  },
  setup() {
    const router = useRouter()
    const appListRef = ref(null);
    const state = reactive({
      loading: false,
      appList: [],
      showAll: false,
      height: 0,
      keyword: "",
    });
    const load = async () => {
      try {
        state.loading = true;
        const { data } = await getUserApplyList({});
        state.appList = data;
      } catch (e) {
        throw new Error(e.message);
      } finally {
        state.loading = false;
      }
    };
    const handleAppClick = (item) => {
      if (!item.routePath) {
        ElMessage.warning("applyUrl 为空");
      } else {
        if (item.menuEnName == 'diningtable') {
          window.open(item.routePath)
        } else {
          window.open(
            `${location.origin}${item.routePath}`,
            item.openMode === "AOS_CURRENT_WINDOW" ? "_self" : "_blank"
          );
        }
 
      }
    };
    const handleToggleShowAll = () => {
      state.showAll = !state.showAll;
    };
    const list = computed(() => {
      const arr = state.showAll ? state.appList : state.appList.slice(0, 12);
      if (state.keyword) {
        return arr.filter(({ menuName }) => {
          return menuName.includes(state.keyword);
        });
      }
      console.log(arr);
      return arr;
    });

    const handleTodo = () => {
      router.push("calendar")
    }
    const handleNotice = () => {
      router.push("messageList")
    }


    const handleImageLoad = () => {
      state.height = Array.from(appListRef.value.querySelectorAll(".app-item"))
        .map((it) => {
          if (it.querySelector(".image-slot")) {
            return 0;
          }
          return parseInt(window.getComputedStyle(it).height) || 0;
        })
        .reduce((a, b) => (a > b ? a : b));
    };
    onMounted(() => {
      load();
      window.addEventListener("resize", handleImageLoad);
    });
    onUnmounted(() => {
      window.removeEventListener("resize", handleImageLoad);
    });
    return {
      todoList,
      state,
      appListRef,
      list,
      logo: Logo,
      handleImageLoad,
      handleAppClick,
      handleTodo,
      handleNotice,
      handleToggleShowAll,
    };
  },
};
</script>
<style lang="scss">
.app-box {
  width: 100%;
  height: 100%;
  background-color: $panel-main-bg;
  display: flex;
  justify-content: space-between;

  .app-box-left {
    flex: 1;
    height: 100%;
    // margin-right: 20px;

    .portal-panel {
      height: 100%;
    }
  }

  .app-box-right {
    flex-basis: 420px;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;

    .portal-panel {
      height: calc(50% - 10px);
      width: 100%;
    }
  }


}

.copyright {
  text-align: center;
  color: #999;
  font-size: 12px;
  line-height: 20px;
}

.todo-list {
  .todo-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    height: 52px;
    border-bottom: 1px solid #eee;

    .til {
      i {
        font-size: 18px;
        color: #397fff;
        margin-right: 12px;
      }

      .text {
        color: #000;
      }
    }

    .tir {
      display: flex;
      justify-content: flex-end;

      .num {
        color: #f68020;
        font-size: 16px;
      }

      i {
        color: #999;
        margin-left: 12px;
      }
    }

    &.unred {

      .til .text,
      .tir .num {
        color: #999;
      }
    }
  }
}

.notice-list {
  .notice-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    height: 52px;
    padding: 10px 0;

    .nil {
      border-radius: 50%;
      background-color: rgba(57, 127, 255, 0.1);
      width: 45px;
      height: 45px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 12px;

      i {
        font-size: 18px;
        color: #397fff;
      }
    }

    .nir {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: flex-start;
      height: 45px;

      .title {
        display: flex;
        justify-content: flex-start;
        align-items: center;

        .text {
          color: #000;
        }

        .el-tag {
          margin-left: 5px;
        }
      }

      .desc {
        display: flex;
        justify-content: flex-start;
        align-items: center;

        .text {
          color: #666;
        }

        i {
          margin-right: 5px;
          color: #666;
          font-size: 18px;
        }
      }
    }

    &.read {

      .nir .title .text,
      .nir .desc .text,
      .nir .desc i {
        color: #999;
      }
    }
  }
}

.app-list {
  width: 100%;
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  flex-wrap: wrap;

  .app-item {
    margin-left: 20px;
    margin-bottom: 20px;
    border-radius: 7px;
    overflow: hidden;
    cursor: pointer;

    .el-image__inner {
      border-radius: 7px;
    }

    &.slot {
      visibility: hidden;
    }

    .image-slot {
      width: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      box-shadow: 0 0 1px 1px $border-color inset;
      border-radius: 7px;

      i {
        font-size: 30px;
        margin-bottom: 15px;
      }

      .text,
      i {
        color: #999;
      }
    }

    .el-image {
      width: 100%;
    }
  }
}

@media screen and (max-width: 1200px) {
  .app-box {
    .app-list {
      .app-item {
        width: calc(33.33% - 13.33px);

        &:nth-child(3n + 1) {
          margin-left: 0;
        }
      }
    }
  }
}

@media screen and (min-width: 1201px) {
  .app-box {
    .app-list {
      .app-item {
        width: calc(25% - 15px);

        &:nth-child(3n + 1) {
          margin-left: 20px;
        }

        &:nth-child(4n + 1) {
          margin-left: 0;
        }
      }
    }
  }
}

@media screen and (min-width: 1520px) {
  .app-box {
    .app-list {
      .app-item {
        width: calc(20% - 16px);

        &:nth-child(4n + 1),
        &:nth-child(3n + 1) {
          margin-left: 20px;
        }

        &:nth-child(5n + 1) {
          margin-left: 0;
        }
      }
    }
  }
}
</style>