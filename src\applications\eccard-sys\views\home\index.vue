<template>
    <div class="home">
        <div class="home-card">
            <el-row :gutter="20">
                <el-col class="card-col" v-bind="layout">
                    <el-card class="home-card-item">
                        <template #header>
                          <div class="card-header">
                            <span>项目概况</span>
                          </div>
                        </template>
                        <div class="card-content" ref="projectInfo"></div>             
                    </el-card>                    
                </el-col>
                <el-col class="card-col" v-bind="layout">
                    <el-card class="home-card-item">
                        <template #header>
                          <div class="card-header">
                            <span>分布告警</span>
                          </div>
                        </template>
                        <div class="card-content" ref="distributionWarning"></div>               
                    </el-card>                    
                </el-col>
                <el-col class="card-col" v-bind="layout">
                    <el-card class="home-card-item">
                        <template #header>
                          <div class="card-header">
                            <span>服务器CPU告警TOP10</span>
                          </div>
                        </template>
                        <div class="card-content" ref="cpu"></div>            
                    </el-card>                    
                </el-col>
                <el-col class="card-col" v-bind="layout">
                    <el-card class="home-card-item">
                        <template #header>
                          <div class="card-header">
                            <span>服务器内存告警TOP10</span>
                          </div>
                        </template>
                        <div class="card-content" ref="memory"></div>
                    </el-card>                  
                </el-col>
            </el-row>                                   
        </div>
    </div>
</template>
<script>
import { ElCard, ElRow, ElCol } from 'element-plus';
import { ref } from 'vue';
import { homeCardLayout } from '@/service/dictionary';
import { useProjectInfo, useDistributionWarning, useCpuChart, useMemoryChart } from './chart';
export default {
    components: {
      'el-card': ElCard,
      'el-row': ElRow,
      'el-col': ElCol,
    },
    setup() {
      const projectInfo = ref(null);
      const distributionWarning = ref(null);
      const cpu = ref(null);
      const memory = ref(null);
      useProjectInfo(projectInfo);
      useDistributionWarning(distributionWarning);
      useCpuChart(cpu);
      useMemoryChart(memory);
      return {
        projectInfo,
        distributionWarning,
        cpu,
        memory,
        layout: homeCardLayout,
      }
    }
}
</script>
<style lang="scss" scoped>
.home{
    width: 100%;
    height: 100%;
    .home-card{
      width: 100%;
      height: 100%;
      box-sizing: border-box;
      overflow-x: hidden;
      overflow-y: auto;      
    }
    .card-content{
        height: 318px;
        height: calc(50vh - 150px);
        overflow-y: auto;
        position: relative;
        &.no-padding{
          height: 358px;
          height: calc(50vh - 110px);          
        }
    }
}
@media screen and (max-width: 1200px) {
  .home .card-col{
    padding-bottom: 20px;
  }  
  .home .card-col:last-child{
      padding-bottom: 0;
  }  
}
@media screen and (min-width: 1200px) {
  .home .card-col:nth-child(1),.home .card-col:nth-child(2){
    padding-bottom: 18px;
  }  
}
</style>