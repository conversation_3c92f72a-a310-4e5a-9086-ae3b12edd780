<template>
  <div class="income-detail border-box">
    <kade-table-wrap title="交易来源">
      <div class="padding-form-box">
        <el-form inline size="small" label-width="100px"> </el-form>
        <div>
          <el-button icon="el-icon-daoru" size="small" type="success" @click="edit('add')">新增</el-button>
          <el-button icon="el-icon-daoru" size="small" type="primary" @click="edit('edit')">编辑</el-button>
          <el-button icon="el-icon-daoru" size="small" type="danger" @click="handleDel">删除</el-button>
        </div>
      </div>
      <el-table style="width: 100%" :data="state.dataList" highlight-current-row border stripe @row-click="rowClick">
        <el-table-column label="名称" prop="tradeName" align="center"></el-table-column>
        <el-table-column label="属性值" prop="tradeCode" align="center"></el-table-column>
        <el-table-column label="是否系统预置" prop="sysPreset" align="center">
          <template #default="scope">
            {{
                filterDictionary(scope.row.sysPreset, state.SYS_BOOL_STRINGList)
            }}
          </template>
        </el-table-column>
        <el-table-column label="是否启用" property="useStatus" align="center">
          <template #default="scope">
            <el-switch v-model="scope.row.useStatus" active-value="ENABLE_TRUE" inactive-value="ENABLE_FALSE" disabled>
            </el-switch>
          </template>
        </el-table-column>
        <el-table-column label="添加人" prop="createUserName" align="center"></el-table-column>
        <el-table-column label="创建时间" prop="createTime" align="center"><template #default="scope">
            {{ scope.row.createTime && timeStr(scope.row.createTime) }}
          </template></el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination background :current-page="state.form.currentPage" :page-size="state.form.pageSize"
          layout="total, sizes, prev, pager, next, jumper" :page-sizes="[5, 10, 20, 50, 100]"
          :total="state.dataListTotal" @current-change="handlePageChange" @size-change="handleSizeChange">
        </el-pagination>
      </div>
    </kade-table-wrap>
    <el-dialog v-model="state.dialogVisible" :title="state.title" width="30%"
      :before-close="() => (state.dialogVisible = false)" :close-on-click-modal="false">
      <el-form>
        <el-form-item label="交易来源名称:">
          <el-input placeholder="输入分类名称" v-model="state.editForm.tradeName" size="small"></el-input>
        </el-form-item>
        <el-form-item label="是否启用:">
          <el-switch v-model="state.editForm.useStatus" active-value="ENABLE_TRUE" inactive-value="ENABLE_FALSE">
          </el-switch>
        </el-form-item>
        <el-form-item label="属性值:">
          <el-input-number v-model="state.editForm.tradeCode" :min="1" :max="1000" />
        </el-form-item>
        <el-form-item label="是否系统预置:">
          <el-radio v-model="state.editForm.sysPreset" label="TRUE">是</el-radio>
          <el-radio v-model="state.editForm.sysPreset" label="FALSE">否</el-radio>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="state.dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="AddOrUpdate(state.title)">提交</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { onMounted, reactive } from "vue";
import { timeStr } from "@/utils/date";
import {
  ElInputNumber,
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElTable,
  ElSwitch,
  ElTableColumn,
  ElPagination,
  ElMessageBox,
  ElMessage,
  ElDialog,
  ElRadio,
} from "element-plus";
import {
  saveTradeSource,
  updateTradeSource,
  getTradeSourceList,
  deleteTradeSource,
} from "@/applications/eccard-finance/api";

export default {
  components: {
    "el-dialog": ElDialog,
    "el-button": ElButton,
    "el-input": ElInput,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    "el-table": ElTable,
    "el-table-column": ElTableColumn,
    "el-pagination": ElPagination,
    "el-switch": ElSwitch,
    "el-radio": ElRadio,
    "el-input-number": ElInputNumber,
  },
  setup() {
    const state = reactive({
      dialogVisible: false,
      dialogVisibleInfo: false,
      title: "",
      typeInfo: {},
      isShow: false,
      dataList: [],
      dataListTotal: 0,
      stSort: 0,
      stStatus: "",
      stName: "",
      selectRow: "",
      form: {
        currentPage: 1,
        pageSize: 10,
      },
      editForm: {
        //是否启用
        useStatus: "FALSE",
        //是否系统预置
        sysPreset: "",
        tradeName: "",
        tradeCode: 1,
      },
      SYS_BOOL_STRINGList: JSON.parse(
        localStorage.getItem("kade_cache_dictionary")
      ).filter((item) => item.dictType == "SYS_BOOL_STRING"), //是否
    });
    const getList = () => {
      getTradeSourceList(state.form).then((res) => {
        state.dataList = res.data.list;
        state.dataListTotal = res.data.total;
      });
    };
    const rowClick = (row) => {
      state.selectRow = row;
    };
    const edit = (val) => {
      if (val == "edit") {
        if (state.selectRow) {
          state.editForm = { ...state.selectRow };
          state.title = "编辑交易来源";
          state.dialogVisible = true;
        } else {
          ElMessage.error("请选择交易来源！");
        }
      } else if (val == "add") {
        state.editForm = {
          //是否启用
          useStatus: "ENABLE_FALSE",
          //是否系统预置
          sysPreset: "TRUE",
          tradeName: "",
          tradeCode: 0,
        };
        state.title = "新增交易来源";
        state.dialogVisible = true;
      }
    };

    const AddOrUpdate = (val) => {
      if (val === "编辑交易来源") {
        updateTradeSource(state.editForm).then((res) => {
          if (res.code === 0) {
            ElMessage.success(res.message);
            getList();
          }
        });
      } else if (val == "新增交易来源") {
        saveTradeSource(state.editForm).then((res) => {
          if (res.code === 0) {
            ElMessage.success(res.message);
            getList();
          }
        });
        console.log(val);
      }
    };
    const filterDictionary = (val, dictionaryList) => {
      for (let i of dictionaryList) {
        if (val == i.dictCode) {
          return i.dictValue;
        }
      }
    };

    const search = () => {
      getList();
      state.selectRow = "";
    };
    const reset = () => {
      state.form = {
        currentPage: 1,
        pageSize: 10,
      };
    };
    const off = () => {
      state.isShow = false;
    };
    const handlePageChange = (val) => {
      state.form.currentPage = val;
      getList();
    };
    const handleSizeChange = (val) => {
      state.form.currentPage = 1;
      state.form.pageSize = val;
      getList();
    };
    const handleDel = () => {
      if (state.selectRow) {
        state.editForm = { ...state.selectRow };
        ElMessageBox.confirm(`确认删除交易来源${state.editForm.tradeName}?`, {
          type: "warning",
          closeOnPressEscape: false,
          closeOnClickModal: false,
        }).then(async () => {
          try {
            const { message } = await deleteTradeSource(state.editForm.id);
            ElMessage.success(message);
            getList();
          } catch (e) {
            throw new Error(e.message);
          }
        });
      } else {
        ElMessage.error("请选择交易来源！");
      }
    };
    onMounted(() => {
      getList();
    });
    return {
      state,
      timeStr,
      rowClick,
      edit,
      search,
      reset,
      off,
      filterDictionary,
      AddOrUpdate,
      handleDel,
      handlePageChange,
      handleSizeChange,
    };
  },
};
</script>
<style lang="scss" scoped>
.income-detail {
  height: 680px;
}

.padding-form-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
