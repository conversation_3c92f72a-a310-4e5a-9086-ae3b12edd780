<template>
  <kade-route-card>
    <template #header>
      <div class="header">
        <span class="header-title">近一周门禁通行人次</span>
        <span class="header-right">更多>></span>
      </div>
    </template>
    <div class="data-list">
      <div class="list-item" v-for="(item, index) in state.dataList" :key="index">
        <img :src="index < 3 ? acRedIcon : acYellowIcon" alt="">
        <div class="item-area">{{ item.area }}</div>
        <div class="item-num">{{ item.type }}次</div>
      </div>
    </div>
  </kade-route-card>
</template>
<script>
import { onMounted, reactive } from "vue";
import acRedIcon from "@/assets/uac_img/ac-red.png"
import acYellowIcon from "@/assets/uac_img/ac-yellow.png"

export default {
  setup() {
    const state = reactive({
      dataList: [
        { area: "天府校区A区南大门", type: "强行闯入报警" },
        { area: "天府校区A区南大门", type: "强行闯入报警" },
        { area: "天府校区A区南大门", type: "强行闯入报警" },
        { area: "天府校区A区南大门", type: "强行闯入报警" },
        { area: "天府校区A区南大门", type: "强行闯入报警" },
        { area: "天府校区A区南大门", type: "强行闯入报警" },
        { area: "天府校区A区南大门", type: "强行闯入报警" },
      ]
    })
    onMounted(() => {
    });
    return {
      acRedIcon,
      acYellowIcon,
      state
    };
  }
}
</script>
<style scoped lang="scss">
.header-right {
  text-decoration: underline;
}

.data-list {
  height: 260px;
  overflow: hidden;
  padding: 0 30px;

  .list-item {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    img{
      width: 20px;
      height: 20px;
      margin-right: 30px;
    }

    .item-area {
      flex: 1;
    }

    .item-num {
      width: 100px;
    }
  }
}
</style>