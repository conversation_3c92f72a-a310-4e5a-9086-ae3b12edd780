<template>
  <el-dialog :model-value="modelValue" title="首卡设置详情" width="1700px" :before-close="handleClose"
    :close-on-click-modal="false">
    <div style="padding:20px 0 0">
      <kade-table-wrap title="多卡开门" icon="none" style="margin-bottom:20px">
        <el-divider></el-divider>
        <div class="padding-box">
          <el-checkbox v-model="state.form.checked" size="large" style="margin:10px 0">启用多卡开门</el-checkbox>
          <el-row :gutter="20">
            <el-col :span="6">
              <kade-table-wrap title="开始" icon="none" style=" padding: 0;">
                <el-divider></el-divider>
                <div class="padding-box">
                  <el-form size="mini" label-width="100px">
                    <el-form-item label="">
                      <el-checkbox-group v-model="state.form.checkList">
                        <el-checkbox v-for="(item, index) in accessList" :key="index" :label="item.value">{{ item.label
                        }}
                        </el-checkbox>
                      </el-checkbox-group>
                    </el-form-item>
                    <el-form-item label="刷卡人数：">
                      <el-input v-model="state.form.b"></el-input>
                    </el-form-item>
                  </el-form>
                </div>
              </kade-table-wrap>
            </el-col>
            <el-col :span="18">
              <kade-table-wrap title="人数设置" icon="none" style=" padding: 0;height: 100%;">
                <el-divider></el-divider>
                <div class="padding-box">
                  <el-form size="mini" label-width="100px" inline>
                    <el-form-item label="第一组人数：" v-for="(item, index) in 8" :key="index">
                      <el-input v-model="state.form.b"></el-input>
                    </el-form-item>
                  </el-form>
                </div>
              </kade-table-wrap>
            </el-col>
          </el-row>
        </div>
      </kade-table-wrap>
      <kade-table-wrap title="多卡用户选择" icon="none" style="margin-bottom:20px">
        <el-divider></el-divider>
        <div class="padding-box">
          <el-form size="mini" label-width="100px" inline>
            <el-form-item label="组织机构">
              <el-select>
                <el-option v-for="(item, index) in 10" :key="index" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="身份类别">
              <el-select>
                <el-option v-for="(item, index) in 10" :key="index" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="关键字">
              <el-input v-model="state.form.b" placeholder="姓名或编号关键字搜索"></el-input>
            </el-form-item>
            <el-button type="primary" size="mini">搜索</el-button>

          </el-form>
          <el-table border v-loading="state.loading" :data="state.dataList">
            <el-table-column show-overflow-tooltip prop="name" label="组织机构" align="center"></el-table-column>
            <el-table-column show-overflow-tooltip prop="ip" label="身份类别" align="center"></el-table-column>
            <el-table-column show-overflow-tooltip prop="ip" label="用户编号" align="center"></el-table-column>
            <el-table-column show-overflow-tooltip prop="ip" label="用户名称" align="center"></el-table-column>
          </el-table>
          <div class="pagination">
            <el-pagination v-model:currentPage="state.form.pageNum" v-model:page-size="state.form.pageSize"
              :page-sizes="[10, 20, 30, 40]" background layout="total, sizes, prev, pager, next, jumper"
              :total="state.total" @size-change="handleSizeChange" @current-change="handleCurrentChange">
            </el-pagination>
          </div>
        </div>
      </kade-table-wrap>

    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose" size="mini">取消</el-button>
        <el-button type="primary" v-if="type != 'details'" @click="submit" size="mini">保存</el-button>
        <el-button type="primary" v-else @click="$emit('edit')" size="mini">编辑</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script>
import { ElDialog, ElDivider, ElRow, ElCol, ElButton, ElForm, ElFormItem, ElSelect, ElOption, ElInput, ElCheckboxGroup, ElCheckbox, ElTable, ElTableColumn, ElPagination } from "element-plus"
import { reactive } from '@vue/reactivity'
const accessList = [
  { label: '进门要求多卡', value: 1 },
  { label: '出门要求多卡', value: 2 },
]
export default {
  components: {
    ElDialog, ElDivider, ElRow, ElCol, ElButton, ElForm, ElFormItem, ElSelect, ElOption, ElInput, ElCheckboxGroup, ElCheckbox, ElTable, ElTableColumn, ElPagination
  },
  emits: ["update:modelValue"],
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
  },
  setup(props, context) {
    const state = reactive({
      form: {
        checkList: []
      },
      dataList: [],
      total: 0
    })
    const handleClose = () => {
      context.emit("update:modelValue", false)
    }
    return {
      accessList,
      state,
      handleClose
    }
  }
}
</script>
<style lang="scss" scoped>

.padding-box {
  padding: 10px 10px 0
}

.el-divider--horizontal {
  margin: 0
}

.week-box {
  height: 140px;
  display: flex;
  // justify-content: center;
  align-items: center;
}

:deep(.box) {
  padding: 10px 10px 0;

  .el-form {
    .el-select {
      width: 150px;
    }

    .el-input {
      width: 150px
    }
  }
}

:deep(.el-select) {
  width: 100%;
}

.el-table {
  border-left: 1px solid #EBEEF5;
  border-right: 1px solid #EBEEF5;
}
</style>