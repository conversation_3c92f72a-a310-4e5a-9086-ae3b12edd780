<template>
  <div class="padding-form-box setBasicParam" v-loading="state.loading">
    <el-form inline size="mini" label-width="230px">
      <el-col style="width:100%">
        <el-row :span="24">
          <el-form-item label="口罩模式开关:">
            <el-select v-model="state.form.paramContent.facemaskSwith">
              <el-option v-for="item in dictListFnc().facemaskSwith" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="口罩检测异常播报开关:">
            <el-select v-model="state.form.paramContent.nomalFacemaskSwith">
              <el-option v-for="item in dictListFnc().nomalFacemaskSwith" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-row>
        <el-row :span="24">
          <el-form-item label="口罩检测异常语音自定义">
            <el-input v-model="state.form.paramContent.voiceCustomization" placeholder="请输入"></el-input>
          </el-form-item>
        </el-row>
        <el-row :span="24">
          <div style="text-align: center;width:100%">
            <el-button size="mini" @click="beforeClose()">取消</el-button>
            <el-button size="mini" type="primary" @click="saveClick()">保存</el-button>
          </div>
        </el-row>
      </el-col>

    </el-form>
  </div>

</template>

<script>
import {
  ElCol,
  ElRow,
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElMessage,
} from "element-plus";
import { onMounted, reactive } from "vue";
import { useStore } from "vuex";

const option = [
  {
    value: 1,
    label: "是",
  },
  {
    value: 0,
    label: "否",
  },
];

const defaultParamsFnc = () => {
  return {
    facemaskSwith: '1',//口罩模式开关	string	
    nomalFacemaskSwith: '1',	//口罩检测异常播报开关	string	
    voiceCustomization: "",	//口罩检测异常语音自定义	string
  };
};
export default {
  components: {
    ElCol,
    ElRow,
    ElForm,
    ElFormItem,
    ElButton,
    ElInput,
    ElSelect,
    ElOption,
  },
  setup() {
    const store = useStore();
    const state = reactive({
      loading: false,
      form: {
        paramContent: defaultParamsFnc(),
        id: "",
        paramId: "",
        paramType: "facemask",
      },
    });
    const dictListFnc = () => {
      return store.state.deviceParameters[store.state.app.activeTab].dict
    }
    const saveClick = async () => {
      state.loading = true;
      let storeData = store.state.deviceParameters[store.state.app.activeTab]
      state.form.paramId = storeData.selectRow.id;
      let fn = state.form.id ? storeData.TabModule.detailsEditFnc : storeData.TabModule.detailsAddFnc
      let params = { ...state.form }
      params.paramContent = JSON.stringify(params.paramContent)
      let { message, code } = await fn(params);
      if (code === 0) {
        setTimeout(() => {
          ElMessage.success(message);
          store.dispatch("deviceParameters/getParams", store.state.deviceParameters[store.state.app.activeTab].selectRow.id);
          state.loading = false;
        }, 1500);
      }
    };

    const beforeClose = () => {
      store.commit("deviceParameters/updateState", {
        key: store.state.app.activeTab,
        childKey: "isSetParams",
        payload: false,
      });
    };
    onMounted(() => {
      let val = store.state.deviceParameters[store.state.app.activeTab].detailsParams
      if (val) {
        if (val.facemask && val.facemask.id) {
          state.form = { ...val.facemask }
          state.form.paramContent = JSON.parse(state.form.paramContent)
        } else {
          state.form.id = "";
          state.form.paramContent = defaultParamsFnc();
        }
      } else {
        //设置默认选项
        state.form.id = "";
        state.form.paramContent = defaultParamsFnc();
      }
    });
    return {
      option,
      state,
      dictListFnc,
      saveClick,
      beforeClose,
    };
  },
};
</script>

<style lang="scss" scoped>
:deep(.el-form-item__content) {
  width: 200px;
  .el-input-number {
    width: 100%;
  }
  .el-input {
    width: 640px;
  }
  .el-select .el-input {
    width: 200px;
  }
}
</style>
