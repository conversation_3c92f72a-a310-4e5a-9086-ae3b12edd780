import {
  getPersonRefundList,
  getRefundRecordList,
  getPersonRefundDetails
} from "@/applications/eccard-finance/api";
const state = {
  isOff:false,
  selectPerson: "",
  refundList: [],
  refundTotalAmount: 0,
  refundRecordList: [],
  refundDetail: "",
  isRefundDetail: false,
}
const mutations = {
  updateState(state, { key, payload }) {
    state[key] = payload;
  },
};



const actions = {
  queryPersonRefundList({ commit }, data) {
    getPersonRefundList(data).then(res => {
      commit("updateState", { key: "refundList", payload: res.data.walletList })
      commit("updateState", { key: "refundTotalAmount", payload: res.data.refundTotalAmount })
    })
  },
  queryRefundRecordList({ commit }, data) {
    getRefundRecordList(data).then(res => {
      commit("updateState", { key: "refundRecordList", payload: res.data })
    })
  },
  queryPersonRefundDetails({ commit }, data) {

    getPersonRefundDetails(data).then(res => {
      if (res.data.refundList&&res.data.refundList.length) {
        res.data.refundList.push({
          actualRefundAmount: res.data.actualTotalAmount,
          walletName: "退款合计"
        })
      }
      console.log(res.data.refundList);
      commit("updateState", { key: "refundDetail", payload: res.data })
      commit("updateState", { key: "isRefundDetail", payload: true })
    })
  }

};
export default {
  namespaced: true,
  state,
  mutations,
  actions
}