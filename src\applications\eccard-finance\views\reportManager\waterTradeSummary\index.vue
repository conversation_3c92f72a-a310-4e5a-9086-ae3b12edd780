<template>
  <div class="padding-box">
    <kade-table-filter @search="getList()" @reset="reset('AREA')">
      <el-form inline size="mini" label-width="100px">
        <el-form-item label="日期">
          <el-date-picker v-model="state.requestDate" type="datetimerange" unlink-panels :default-time="defaultTime" range-separator="~" start-placeholder="请选择日期" end-placeholder="请选择日期" @change="changeDate">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="统计类型">
          <el-select v-model="state.form.countType" @change="countTypeChange">
            <el-option v-for="item in countTypeList" :label="item.label" :value="item.value" :key="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="区域">
          <kade-area-select-tree style="width: 100%" :value="state.form.areaPath" valueKey="areaPath" :multiple="false" @valueChange="(val) => state.form.areaPath = val.areaPath" />
        </el-form-item>
        <el-form-item label="设备编号" v-if="state.form.countType == 'DEVICE_NAME'">
          <el-input placeholder="请输入设备编号" v-model="state.form.deviceNo" maxlength="30" clearable></el-input>
        </el-form-item>
        <el-form-item label="设备类型" v-if="state.form.countType == 'DEVICE_TYPE'">
          <el-select v-model="state.form.deviceType" placeholder="请选择">
            <el-option v-for="(item, index) in deviceTypeList" :key="index" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="消费类型" v-if="state.form.countType == 'CONSUME_TYPE'">
          <el-select v-model="state.form.consumeType" placeholder="请选择">
            <el-option v-for="(item, index) in consumerTypeList" :key="index" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="水控交易汇总" v-loading="state.loading">
      <template #extra>
        {{ `设备合计${state.count.deviceCount || 0}台，交易笔数合计${state.count.tradeCount ||
          0}笔，交易金额合计${state.count.tradeAmountCount || 0}元` }}
      </template>
      <el-table style="width: 100%" :data="state.dataList" ref="tableRef" height="55vh" highlight-current-row border stripe>
        <el-table-column v-for="(item) in column(state.form.countType)" :key="item.prop" :width="item.width" :label="item.label" :prop="item.prop" align="center" show-overflow-tooltip>
          <template v-if="item.render" #default="scope">
            {{ item.render(scope.row[item.prop]) }}
          </template>
        </el-table-column>
        <el-table-column label="交易笔数合计" prop="tradeCount" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column label="交易金额合计" prop="tradeAmountCount" align="center" show-overflow-tooltip></el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination background :current-page="state.form.currentPage" :page-size="state.form.pageSize" layout="total, sizes, prev, pager, next, jumper" :page-sizes="[6, 10, 20, 50, 100]" :total="state.total" @current-change="handlePageChange" @size-change="handleSizeChange">
        </el-pagination>
      </div>
    </kade-table-wrap>
  </div>
</template>

<script>
import {
  ElForm,
  ElFormItem,
  ElInput,
  ElDatePicker,
  ElSelect,
  ElOption,
  ElTable,
  ElTableColumn,
  ElPagination,
  ElMessage
} from "element-plus";
import { reactive, ref, onMounted, onBeforeUpdate, nextTick } from 'vue'
import { timeStr } from "@/utils/date.js";
import { requestDate,defaultTime } from "@/utils/reqDefaultDate";
import {
  waterTradeSummary
} from "@/applications/eccard-finance/api";
import areaSelectTree from "@/components/tree/areaSelectTree";
export default {
  components: {
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    ElInput,
    ElDatePicker,
    ElSelect,
    ElOption,
    ElTable,
    ElTableColumn,
    ElPagination,
    "kade-area-select-tree": areaSelectTree,
  },
  setup() {
    const tableRef = ref(null)
    const consumerTypeList = [
      { value: 3, label: "刷卡" },
      { value: 1, label: "扫码" },
      { value: 5, label: "密码支付" },
    ]
    const deviceTypeList = [
      { label: "智能节水控制器", value: "WATER_CONTROLLER" },
      { label: "无线智能节水控制器", value: "WIRELESS_WATER_CONTROLLER" },
      { label: "自助洗衣机", value: "WASHER" },
      { label: "自助洗鞋机", value: "WASHING_SHOES" },
      { label: "自助烘干机", value: "BAKE" },
    ]
    const column = (val) => {
      if (val === 'AREA') {
        return [
          { label: "所属区域", prop: "areaName", width: "" },
          { label: "设备数量", prop: "deviceCount", width: "" },
        ]
      } else if (val === 'DEVICE_NAME') {
        return [
          { label: "设备编号", prop: "deviceNo", width: "" },
          { label: "设备名称", prop: "deviceName", width: "" },
          { label: "所属区域", prop: "areaName", width: "" },
        ]
      } else if (val === 'DEVICE_TYPE') {
        return [
          { label: "设备类型", prop: "deviceTypeName", width: "" },
          { label: "设备数量", prop: "deviceCount", width: "" },
        ]
      } else if (val === 'CONSUME_TYPE') {
        return [
          { label: "消费类型", prop: "tradeModeName", width: "" },

        ]
      }
    }
    const countTypeList = [
      { label: "按区域统计", value: "AREA" },
      { label: "按设备名称统计", value: "DEVICE_NAME" },
      { label: "按设备类型统计", value: "DEVICE_TYPE" },
      { label: "按消费类型统计", value: "CONSUME_TYPE" },
    ]

    const state = reactive({
      loading: false,
      form: {
        countType: "AREA",
        currentPage: 1,
        pageSize: 10,
      },
      requestDate: requestDate(),
      dataList: [],
      total: 0,
      count: {
        deviceCount: 0,
        tradeAmountCount: 0,
        tradeCount: 0
      }
    })
    const getList = async () => {
      let params = { ...state.form }
      if (state.requestDate && state.requestDate.length) {
        params.beginTime = timeStr(state.requestDate[0]);
        params.endTime = timeStr(state.requestDate[1]);
      } else {
        return ElMessage.error("请选择统计日期")
      }
      let { data: { pageInfo: { list, total }, generate } } = await waterTradeSummary(params)
      state.dataList = list
      state.total = total
      state.count = generate || {}

    };

    const countTypeChange = async (e) => {
      await reset(e)
    }
    const reset = (e) => {
      state.form = {
        countType: e,
        currentPage: 1,
        pageSize: 10,
      },
        state.requestDate = requestDate()
      getList()
    };
    onMounted(() => {
      // getList()
    })
    onBeforeUpdate(() => {
      nextTick(() => {
        tableRef.value.doLayout()
      })
    })
    return {
      defaultTime,
      tableRef,
      consumerTypeList,
      deviceTypeList,
      column,
      countTypeList,
      state,
      countTypeChange,
      reset,
      getList,
    }
  }
}
</script>

<style lang="scss" scoped></style>