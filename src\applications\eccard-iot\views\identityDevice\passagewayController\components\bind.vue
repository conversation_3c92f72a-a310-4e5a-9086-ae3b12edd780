<template>
  <el-dialog :model-value="isShow" title="绑定读头" width="90%" v-loading="state.loading" :before-close="beforeClose"
    :close-on-click-modal="false">
    <div class="dialog-box">
      <div class="title">选择读头</div>
      <el-divider></el-divider>
      <kade-select-table :isShow="isShow" :value="[]" :reqFnc="accessControlReadRelationList"
        :selectCondition="selectCondition" :column="column" :params="params" @change="deviceChange" />
    </div>
    <div class="dialog-box">
      <div class="title">选择绑定门</div>
      <el-divider></el-divider>
      <div class="padding-form-box">
        <el-form label-width="120px" inline size="mini">
          <el-form-item label="门号：">
            <el-select v-model="state.door" size="mini">
              <el-option v-for="(item, index) in state.doorList" :key="index" :label="item.doorName"
                :value="item.doorNo"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="submit()" type="primary" size="mini">保&nbsp;&nbsp;存</el-button>
        <el-button @click="beforeClose()" size="mini">返&nbsp;&nbsp;回</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script>
import { ElDialog, ElButton, ElDivider, ElForm, ElFormItem, ElSelect, ElOption, ElMessage } from "element-plus"
import { onMounted, reactive, watch } from 'vue'
import { useDict } from "@/hooks/useDict";
import selectTable from "@/components/table/selectTable.vue"
import { getModel, getWorkStationList, accessControlReadRelationList, accessControlDoorList, accessControlDeviceBind } from "@/applications/eccard-iot/api";

const column = [
  { label: "所属区域", prop: "areaName", isDict: false, width: "" },
  { label: "读头类型", prop: "relationTypeName", isDict: false, width: "" },
  { label: "设备机号", prop: "deviceNo", isDict: false, width: "" },
  { label: "设备名称", prop: "deviceName", isDict: false, width: "" },
  { label: "终端型号", prop: "deviceModel", isDict: false, width: "" },
  { label: "设备状态", prop: "deviceStatus", isDict: true, width: "" },
  { label: "连接类型", prop: "deviceConnectType", isDict: true, width: "" },
  { label: "设备IP", prop: "deviceIp", isDict: false, width: "" },
];
const params = {
  currentPageKey: "pageNum",
  pageSizeKey: "pageSize",
  resListKey: "list",
  resTotalKey: "total",
  value: {
    // deviceType: "智能消费终端",
    // bind: 'false'
    bindFlag: "0"
  },
  tagNameKey: "deviceName",
  valueKey: "id"
}
export default {
  components: {
    ElDialog,
    ElButton,
    ElDivider,
    ElForm,
    ElFormItem,
    ElSelect,
    ElOption,
    "kade-select-table": selectTable,
  },
  props: {
    isShow: {
      type: Boolean,
      default: false
    },
    data: {
      type: String,
      default: ""
    },
    listData: {
      type: Object,
      default: null
    }
  },
  setup(props, context) {
    const state = reactive({
      loading: false,
      model: {},
      door: "",
      doorList: [],
      deviceList: [],
    })
    const selectCondition = [
      {
        label: "读头类型", valueKey: "relationType", placeholder: "请选择", isSelect: true, select: {
          list: [
            { label: '摄像头', value: 2 },
            { label: '指纹门禁一体机', value: 3 },
            { label: '人脸识别终端', value: 1 },
          ],
          option: {
            label: "label",
            value: "value",
          },
        },
      },
      {
        label: "终端型号", valueKey: "deviceModel", placeholder: "请选择", isSelect: true, select: {
          list: props.listData.modelList,
          option: {
            label: "productMode",
            value: "productMode",
          },
        },
      },
      { label: "所属区域", valueKey: "areaId", dataKey: "id", placeholder: "请选择", isSelect: false, isTree: "area" },
      { label: "机号", valueKey: "deviceNo", placeholder: "请输入", isSelect: false },

      {
        label: "设备状态", valueKey: "deviceStatus", placeholder: "请选择", isSelect: true, select: {
          list: useDict("SYS_DEVICE_STATICE"),
          option: {
            label: "label",
            value: "value",
          },
        },
      },
      { label: "设备名称", valueKey: "deviceName", placeholder: "请输入", isSelect: false },
      {
        label: "所属工作站", valueKey: "workstationId", placeholder: "请选择", isSelect: true, select: {
          list: props.listData.workStationList,
          option: {
            label: "name",
            value: "id",
          },
        },
      },
      {
        label: "连接类型", valueKey: "deviceConnectType", placeholder: "请选择", isSelect: true, select: {
          list: useDict("SYS_DEVICE_CONNECT_TYPE"),
          option: {
            label: "label",
            value: "value",
          },
        },
      },
      { label: "设备IP", valueKey: "deviceIp", placeholder: "请输入", isSelect: false },
    ]

    watch(() => props.isShow, val => {
      if (val) {
        state.door = ""
        state.deviceList = []
        getDoorList()
      }
    })
    //获取终端型号
    const queryModel = async () => {
      let { data } = await getModel();
      selectCondition[1].select.list = data;
    };
    //获取工作站
    const queryWorkStationList = async () => {
      let { data: { list } } = await getWorkStationList();
      selectCondition[6].select.list = list;
    };
    const getDoorList = async () => {
      let params = {
        accessControlDeviceId: props.data.id,
        pageNum: 1,
        pageSize: 1000
      }
      let { data: { list } } = await accessControlDoorList(params)
      state.doorList = list
    }
    const deviceChange = val => {
      console.log(val);
      if (val.list && val.list.length) {
        state.deviceList = val.list.map(item => {
          return {
            contorlParamId: props.data.id,
            deviceId: item.id,
            relationType: item.relationType
          }
        })
      }
    }
    const submit = async () => {
      if (!state.deviceList.length) {
        return ElMessage.error("请选择读头设备！")
      }
      if (!state.door) {
        return ElMessage.error("请选择门号！")
      }
      let params = state.deviceList.map(item => {
        return {
          ...item,
          doorParamId: state.door
        }
      })
      state.loading = true
      try {
        let { code, message } = await accessControlDeviceBind(params)
        if (code === 0) {
          ElMessage.success(message)
          context.emit("close", true)
        }
        state.loading = false
      }
      catch {
        state.loading = false
      }
    }
    const beforeClose = () => {
      context.emit("close", false)
    }
    onMounted(() => {

      queryModel()
      queryWorkStationList()
    })
    return {
      accessControlReadRelationList,
      column,
      params,
      state,
      selectCondition,
      deviceChange,
      submit,
      beforeClose
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-box {
  border: 1px solid #dcdfe6;
  border-radius: 5px;
  margin: 10px;

  .title {
    padding: 10px;
  }
}

.el-divider--horizontal {
  margin: 0 0 20px;
}

.device-position-width {
  :deep(.el-input__inner) {
    width: 100% !important;
  }
}

.pagination {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 10px;
}
</style>