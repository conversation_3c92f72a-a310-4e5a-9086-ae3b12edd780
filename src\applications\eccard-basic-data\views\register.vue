<template>
  <div class="register">
    <el-card>
      <template #header>
          <div class="card-header">
            <el-alert :closable="false" effect="dark" type="info" title="请填写单位信息" show-icon center></el-alert>
          </div>
      </template>
      <div class="card-content">
        <el-form
          :model="state.model" 
          :rules="rules" 
          ref="formRef" 
          :label-width="labelWidth"
          size="small"
          @keyup.enter="submit"
          class="register-form"
        >
          <el-form-item label="单位名称" prop="company_name">
            <el-input type="text" placeholder="请输入" v-model="state.model.company_name" />
          </el-form-item>            
          <el-form-item label="单位所在地" prop="company_city">
            <kade-select-city style="width: 100%" :model-value="state.model.company_city" @change="selectCityChange" />
          </el-form-item>
          <el-form-item label="单位类型" prop="company_type">
            <el-select clearable placeholder="请选择" v-model="state.model.company_type">
              <el-option value="1">类型1</el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="联系人姓名" prop="company_contacts">
            <el-input type="text" placeholder="请输入" v-model="state.model.company_contacts" />
          </el-form-item>   
          <el-form-item label="单位电话" prop="company_tel">
            <el-input type="text" placeholder="请输入" v-model="state.model.company_tel" />
          </el-form-item>
          <el-form-item label="人员规模" prop="company_scale">
            <el-select clearable placeholder="请选择" v-model="state.model.company_scale">
              <el-option value="1">类型1</el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="详细地址" prop="company_address">
            <el-input type="text" placeholder="请输入" v-model="state.model.company_address" />
          </el-form-item>                                        
          <el-form-item>
            <el-button :loading="state.loading" type="primary" @click="submit">注册</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>    
  </div>
</template>
<script>
import { 
  ElCard,
  ElForm,
  ElFormItem,
  ElInput,
  ElButton,
  ElSelect,
  ElOption,
  ElAlert,
  ElMessage,
 } from 'element-plus';
import { reactive, ref } from 'vue';
import { useRouter } from 'vue-router';
import { addCompany } from '../api';
import SelectCity from '../components/selectCity';
export default {
  components: {
    'el-card': ElCard,
    'el-form': ElForm,
    'el-form-item': ElFormItem,
    'el-input': ElInput,
    'el-button': ElButton,
    'el-select': ElSelect,
    'el-option': ElOption,
    'el-alert': ElAlert,
    'kade-select-city': SelectCity,
  },
  setup() {
    const formRef = ref(null);
    const router = useRouter();
    const state = reactive({
      model: {
        company_address: '',
        company_city: '',
        company_contacts: '',
        company_name: '',
        company_scale: '',
        company_tel: '',
        company_type: '',
      },
      loading: false,
    });
    const rules = {
      company_name: [
        { required: true, message: '请输入单位名称' },
        { max: 20, message: '单位名称长度不能超过20' },
      ],
      company_city: [{ required: true, message: '请选择城市', trigger: 'change' }],
      company_type: [{ required: true, message: '请选择单位类型', trigger: 'change' }],
      company_scale: [{ required: true, message: '请选择人员规模', trigger: 'change' }],
      company_contacts: [{ required: true, message: '请输入联系人姓名' }],
      company_tel: [{ required: true, message: '请输入联系人电话' }],
      company_address: [{ required: true, message: '请输入详细地址' }],
    };
    const submit = () => {
      formRef.value.validate(async (valid) => {
        if(valid) {
          console.log(state.model, addCompany);
          try {
            state.loading = true;
            const { message } = await addCompany(state.model);
            ElMessage.success(message);
            router.push('../');
          } catch(e) {
            throw new Error(e.message);
          } finally {
            state.loading = false;
          }
        }
      });
    }
    const selectCityChange = (v) => {
      state.model.company_city = v;
    }
    return {
      rules,
      formRef,
      submit,
      labelWidth: THEMEVARS.formLabelWidth,
      state,
      selectCityChange,
    }
  }
}
</script>
<style lang="scss" scoped>
.register{
  width: 100%;
  max-width: 800px;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  padding: 20px;
  box-sizing: border-box;
  .register-form{
    width: 500px;
    .el-select{
      width: 100%;
    }
  }
}
</style>