<template>
  <kade-tab-wrap :tabs="tabs" model-value="zhlb" extra>
    <template #extra>
      <el-button :disabled="!id" @click="() => handleBindManager()" type="primary" size="small" icon="el-icon-plus">绑定管理员</el-button>
    </template>
    <template #zhlb>
      <el-table style="width: 100%" :data="state.dataList" v-loading="state.loading" border stripe>
        <el-table-column label="管理员名称" prop="userName" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column label="人员编号" align="center" prop="baseUserId" show-overflow-tooltip>
        </el-table-column>
        <el-table-column label="管理员类型" align="center" show-overflow-tooltip prop="tenantEmail">
        </el-table-column>
        <el-table-column label="联系方式" align="center" show-overflow-tooltip prop="userTel">
        </el-table-column>
        <el-table-column label="是否启用" align="center" show-overflow-tooltip prop="moState">
          <template #default="scope">
            <el-switch @change="(v) => handleSwitchChange(scope.row, v)" :model-value="scope.row.moState === 'ENABLE_TRUE'"></el-switch>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="160" fixed="right">
          <template #default="scope">
            <el-button @click="() => handleBindManager(scope.row)" type="text" size="mini">编辑</el-button>
            <el-button @click="() => handleCancel(scope.row)" type="text" size="mini">取消绑定</el-button>
          </template>
        </el-table-column>
      </el-table>
    </template>
  </kade-tab-wrap>
  <kade-bind-manager v-model="state.showModal" :merchantOperatorId="state.merchantOperatorId" :merchantId="id" :isInfo="state.isInfo" @change="() => loadData()" />
</template>
<script>
import {
  ElTable,
  ElTableColumn,
  ElButton,
  ElMessageBox,
  ElMessage,
  ElSwitch,
} from "element-plus";
import { reactive, watchEffect } from "vue";
import {
  getMerchantOperatorList,
  deleteMerchantOperator,
  updateMerchantOperatorState,
} from "@/applications/eccard-finance/api";
import BindManager from "./bindManager.vue";
const tabs = [
  {
    label: "账号列表",
    name: "zhlb",
  },
];
export default {
  components: {
    "el-button": ElButton,
    "el-table": ElTable,
    "el-table-column": ElTableColumn,
    "el-switch": ElSwitch,
    "kade-bind-manager": BindManager,
  },
  props: {
    id: {
      type: [String, Number],
    },
  },
  setup(props) {
    const state = reactive({
      dataList: [],
      loading: false,
      showModal: false,
      merchantOperatorId: null,
      isInfo: false,
    });
    const loadData = async () => {
      try {
        state.loading = true;
        const { data } = await getMerchantOperatorList({
          merchantId: props.id,
        });
        state.dataList = data;
      } catch (e) {
        throw new Error(e.message);
      } finally {
        state.loading = false;
      }
    };
    const handleBindManager = (row) => {
      if (row) {
        state.merchantOperatorId = row.baseUserId;
      } else {
        state.merchantOperatorId = null;
      }
      state.showModal = true;
    };
    const handleCancel = (row) => {
      ElMessageBox.confirm(`确认取消绑定?`, {
        type: "warning",
        closeOnPressEscape: false,
        closeOnClickModal: false,
      }).then(async () => {
        try {
          const { message } = await deleteMerchantOperator({
            merchantId: row.merchantId,
            merchantOperatorId: row.baseUserId,
          });
          ElMessage.success(message);
          loadData();
        } catch (e) {
          throw new Error(e.message);
        }
      });
    };
    const handleSwitchChange = (row, v) => {
      ElMessageBox.confirm(`确认${v ? "启用" : "禁用"}管理员?`, {
        type: "warning",
        closeOnPressEscape: false,
        closeOnClickModal: false,
      }).then(async () => {
        try {
          const { message } = await updateMerchantOperatorState({
            merchantId: row.merchantId,
            merchantOperatorId: row.baseUserId,
            moState: v ? "ENABLE_TRUE" : "ENABLE_FALSE",
          });
          ElMessage.success(message);
          loadData();
        } catch (e) {
          throw new Error(e.message);
        }
      });
    };
    watchEffect(() => {
      if (props.id) {
        loadData();
      }
    });
    return {
      state,
      tabs,
      handleBindManager,
      handleCancel,
      loadData,
      handleSwitchChange,
    };
  },
};
</script>