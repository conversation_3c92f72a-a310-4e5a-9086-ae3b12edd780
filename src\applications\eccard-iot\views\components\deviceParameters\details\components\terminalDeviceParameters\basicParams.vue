<template>
  <div class="padding-box">
    <div v-if="details" class="basic-info-box">
      <div class="box-item" v-for="(item, index) in list" :key="index">
        <div class="item-label">{{ item.label }}</div>
        <div class="item-value" v-if="item.render">{{item.render(details[item.valueKey])}}</div>
        <div class="item-value" v-else>{{details[item.valueKey]}}</div>
      </div>
    </div>
    <el-empty v-else description="当前设备参数未设定"></el-empty>
  </div>
</template>
<script>
import { computed } from "@vue/runtime-core";
import { useStore } from "vuex";
import { ElEmpty } from "element-plus"

const list =[
  {label:"是否启用用户密码",valueKey:"dev_user_password",render:val=>val==1?'是':'否'},
  {label:"是否统计日消费次",valueKey:"dev_rxfctj",render:val=>val==1?'是':'否'},
  {label:"是否统计日消费",valueKey:"dev_rxfetj",render:val=>val==1?'是':'否'},
  {label:"是否统计餐消费次",valueKey:"dev_cxfctj",render:val=>val==1?'是':'否'},
  {label:"是否统计餐消费额",valueKey:"dev_cxfetj",render:val=>val==1?'是':'否'},
  {label:"允许使用主钱包交易",valueKey:"dev_zqbjyms",render:val=>val==1?'允许':'禁止'},
  {label:"允许使用补助交易",valueKey:"dev_sudsidy",render:val=>val==1?'允许':'禁止'},
  {label:"允许使用次数交易",valueKey:"dev_ci",render:val=>val==1?'允许':'禁止'},
  {label:"钱包最大余额",valueKey:"dev_maxbal"},
  {label:"补助现金交易比例",valueKey:"dev_xper"},
  {label:"启用权限名单",valueKey:"dev_useauth",render:val=>val==1?'允许':'禁止'},
  {label:"黑名单锁卡",valueKey:"dev_blacklock",render:val=>val==1?'允许':'禁止'},
  {label:"同卡时间间隔",valueKey:"dev_scardtime"},
  {label:"人脸支付确认",valueKey:"dev_face",render:val=>val==1?'允许':'禁止'},
]
export default {
  components:{
    ElEmpty,
  },
  setup() {
    const store = useStore();
    const details = computed(() => {
      let data = "";
      for (let item of store.state.deviceParameters[store.state.app.activeTab].detailsParams) {
        if (item.paramType == "base") {
          data = JSON.parse(item.paramContent);
          break;
        }
      }
      if(!data){
        data=''
      }
      return data;
    });
    return {
      list,
      details,
    };
  },
};
</script>
<style lang="scss" scoped>
.basic-info-box {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  border-left: 1px solid #ccc;
  border-top: 1px solid #ccc;
  min-width: 900px;
  .box-item {
    box-sizing: border-box;
    width: 33.333%;
    min-width: 300px;
    line-height: 50px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #ccc;
    .item-label {
      width: 50%;
      text-align: center;
      background: #f6f6f6;
      border-right: 1px solid #ccc;
    }
    .item-value {
      text-align: center;
      width: 50%;
      background: #fff;
      border-right: 1px solid #ccc;
    }
  }
}
</style>