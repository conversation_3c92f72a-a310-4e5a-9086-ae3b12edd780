<template>
  <div class="padding-box">
    <kade-table-filter @search="handleSearch" @reset="handleReset">
      <el-form inline size="small" label-width="80px">
        <el-form-item label="菜单名称">
          <el-input v-model="state.form.name" placeholder="请输入菜单名称"></el-input>
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <div class="table-box" v-loading="state.loading">
      <kade-table-wrap title="菜单列表">
        <template #extra>
          <el-button @click="handleEdit({roles:''})" icon="el-icon-plus" size="small" class="btn-green">新建菜单</el-button>
        </template>
        <el-table border :data="state.dataList" @selection-change="handleSelectChange">
          <el-table-column width="100px" prop="type" label="类型" align="center"></el-table-column>
          <el-table-column width="100px" prop="name" label="名称" align="center"></el-table-column>
          <el-table-column width="150px" prop="icon" label="图标" align="center"></el-table-column>
          <el-table-column width="300px" prop="url" label="路由" align="center"></el-table-column>
          <el-table-column prop="roles" label="权限" align="center">
            <template #default="scope">
              <el-tag style="margin-right:10px;margin-bottom:10px" type="primary" v-for="(item, index) in scope.row.authNameList" :key="index">{{ item }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column width="200px" label="操作" align="center">
            <template #default="scope">
              <el-button size="mini" type="text" @click="handleEdit(scope.row)">编辑</el-button>
              <el-button size="mini" type="text" @click="handleDel(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </kade-table-wrap>
    </div>
    <kade-auth-edit v-model="state.isEdit" :roleList="state.roleList" :rowData="state.rowData" @success="getList()" />
  </div>
</template>

<script>
import {
  ElForm,
  ElFormItem,
  ElInput,
  ElButton,
  ElTable,
  ElTableColumn,
  ElTag,
  ElMessageBox,
  ElMessage
} from "element-plus";
import { onMounted, reactive } from "vue";
import { opsWaterAuthList, opsWaterAuthDelete } from "@/applications/eccard-water-ops/api/auth"
import { getRole } from "@/applications/eccard-sys/api.js"
import edit from "./components/edit.vue"
export default {
  components: {
    ElForm,
    ElFormItem,
    ElButton,
    ElInput,
    ElTable,
    ElTableColumn,
    ElTag,
    "kade-auth-edit": edit
  },
  setup() {
    const state = reactive({
      loading: false,
      isEdit: false,
      roleList: [],
      rowData: {
        roles: ''
      },
      form: {},
      dataList: [],
    });

    const getList = async () => {
      state.loading = true
      let { data, code } = await opsWaterAuthList(state.form)
      state.loading = false
      if (code === 0) {
        const roleIdList = state.roleList.map(item => item.value)
        state.dataList = data.map(item => {
          item.authNameList = item.roles.split(',').reduce((pre, cur) => {
            if (roleIdList.includes(cur)) {
              pre.push(state.roleList.find(v => v.value === cur).label)
            }
            return pre
          }, [])
          return item
        })
      }
    }
    const getRoleList = async () => {
      let { data, code } = await getRole()
      if (code === 0) {
        state.roleList = data.map(item => {
          return {
            label: item.roleName,
            value: item.id + ""
          }
        })
        getList()
      }
    }

    const handleEdit = (row) => {
      state.rowData = row
      state.isEdit = true
    }

    const handleSearch = () => {
      getList()
    }
    const handleReset = () => {
      state.form = {
        currentPage: 1,
        pageSize: 10
      }
    }
    const handleDel = (row) => {
      ElMessageBox.confirm("确认删除？", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        let { code, message } = await opsWaterAuthDelete(row.id);
        if (code === 0) {
          ElMessage.success(message);
          getList();
        }
      });
    };
    onMounted(() => {
      getRoleList()
    })
    return {
      state,
      getList,
      handleEdit,
      handleSearch,
      handleReset,
      handleDel,
    };
  },
};
</script>

<style lang="scss" scoped>
</style>