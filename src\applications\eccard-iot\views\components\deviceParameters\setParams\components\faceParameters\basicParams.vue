<template>
  <div class="padding-form-box setBasicParam" v-loading="state.loading">
    <el-form inline size="small" label-width="200px">
      <el-form-item label="语音模式:">
        <el-select v-model="state.form.paramContent.speechPatterns">
          <el-option v-for="item in dictListFnc().speechPatterns" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="识别距离:">
        <el-select v-model="state.form.paramContent.readRange">
          <el-option v-for="item in dictListFnc().readRange" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="语音自定义:">
        <el-input v-model="state.form.paramContent.voiceCustomization" placeholder="请输入"></el-input>
      </el-form-item>
      <el-form-item label="识别分数:">
        <el-input-number :min="0" :max="100" v-model="state.form.paramContent.recognitionScores"></el-input-number>
      </el-form-item>
      <el-form-item label="显示模式:">
        <el-select v-model="state.form.paramContent.displayMode">
          <el-option v-for="item in dictListFnc().displayMode" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="识别间隔(秒):">
        <el-input-number :min="0" :max="100" v-model="state.form.paramContent.identifyInterval"></el-input-number>
      </el-form-item>
      <el-form-item label="显示自定义:">
        <el-input v-model="state.form.paramContent.displayCustomization" placeholder="请输入"></el-input>
      </el-form-item>
      <el-form-item label="陌生人开关:">
        <el-select v-model="state.form.paramContent.strangerSwitch">
          <el-option v-for="item in dictListFnc().strangerSwitch" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="韦根输出:">
        <el-select v-model="state.form.paramContent.wigginsOutput">
          <el-option v-for="item in dictListFnc().wigginsOutput" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="补光灯类型:">
        <el-select v-model="state.form.paramContent.supplementaryLight">
          <el-option v-for="item in dictListFnc().supplementaryLight" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="补光灯开始时间:">
        <el-time-picker v-model="state.form.paramContent.fillingStartTime" arrow-control placeholder="请选择" />
      </el-form-item>
      <el-form-item label="补光灯结束时间:">
        <el-time-picker v-model="state.form.paramContent.fillingEndTime" arrow-control placeholder="请选择" />
      </el-form-item>
    </el-form>
  </div>
  <div style="text-align: center">
    <el-button size="mini" @click="beforeClose()">取消</el-button>
    <el-button size="mini" type="primary" @click="saveClick()">保存</el-button>
  </div>
</template>


<script>
import {
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElInputNumber,
  ElMessage,
  ElOption,
  ElSelect,
  ElTimePicker,
} from "element-plus";
import { onMounted, reactive } from "vue";
import { useStore } from "vuex";
import {timeStr} from "@/utils/date.js"
/* const option = [
  {
    value: 2,
    label: "播报名字",
  },
  {
    value: 100,
    label: "自定义",
  },
];
const option2 = [
  {
    value: 0,
    label: "不限制",
  },
  {
    value: 1,
    label: "0.5米以内",
  },
  {
    value: 2,
    label: "1米以内",
  },
  {
    value: 4,
    label: "2米以内",
  },
];
const option3 = [
  {
    value: 1,
    label: "显示名字",
  },
  {
    value: 100,
    label: "自定义",
  },
];
const option4 = [
  {
    value: "识别",
    label: "识别",
  },
  {
    value: "不识别",
    label: "不识别",
  },
];
const option5 = [
  {
    value: "输出卡号（WG34）",
    label: "输出卡号（WG34）",
  },
  {
    value: "输出卡号（WG26）",
    label: "输出卡号（WG26）",
  },
];
const option6 = [
  {
    value: "时间段内开启",
    label: "时间段内开启",
  },
  {
    value: "常开",
    label: "常开",
  },
  {
    value: "常闭",
    label: "常闭",
  },
]; */
const defaultParamsFnc = () => {
  return {
    displayCustomization: "",	//显示自定义	string	
    displayMode: "1",	//显示模式	integer	
    fillingEndTime: "",	//补光灯结束时间	string	
    fillingStartTime: "",	//补光灯开始时间	string	
    identifyInterval: "60",	//识别间隔	integer	
    readRange: "100",	//识别距离	integer	
    recognitionScores: "80",	//识别分数	integer	
    speechPatterns: "1",	//语音模式	integer	
    strangerSwitch: "2",	//陌生人开关	string	
    supplementaryLight: "1",	//补光灯类型	string	
    voiceCustomization: "",	//语音自定义	string	
    wigginsOutput: "2",	//韦根输出	string
  };
};

export default {
  components: {
    ElInputNumber,
    ElForm,
    ElFormItem,
    ElInput,
    ElSelect,
    ElOption,
    ElButton,
    ElTimePicker,
  },
  setup() {
    const store = useStore();
    const state = reactive({
      loading: false,
      form: {
        paramContent: defaultParamsFnc(),
        id: "",
        paramId: "",
        paramType: "base",
      },
    });
    const dictListFnc = () => {
      return store.state.deviceParameters[store.state.app.activeTab].dict
    }
    const saveClick = async () => {
      state.loading = true;
      let storeData = store.state.deviceParameters[store.state.app.activeTab]
      state.form.paramId = storeData.selectRow.id;
      let fn = state.form.id ? storeData.TabModule.detailsEditFnc : storeData.TabModule.detailsAddFnc
      
      let params = { ...state.form }
      console.log(params.paramContent);
      params.paramContent.fillingStartTime=timeStr(params.paramContent.fillingStartTime)
      params.paramContent.fillingEndTime=timeStr(params.paramContent.fillingEndTime)
      params.paramContent = JSON.stringify(params.paramContent)
      let { message, code } = await fn(params);
      if (code === 0) {
        setTimeout(() => {
          ElMessage.success(message);
          store.dispatch("deviceParameters/getParams", store.state.deviceParameters[store.state.app.activeTab].selectRow.id);
          state.loading = false;
        }, 1500);
      }
    };

    const beforeClose = () => {
      store.commit("deviceParameters/updateState", {
        key: store.state.app.activeTab,
        childKey: "isSetParams",
        payload: false,
      });
    };
    onMounted(() => {
      let val = store.state.deviceParameters[store.state.app.activeTab].detailsParams
      if (val) {
        if (val.base && val.base.id) {
          state.form = { ...val.base }
          state.form.paramContent = JSON.parse(state.form.paramContent)
        } else {
          state.form.id = "";
          state.form.paramContent = defaultParamsFnc();
        }
      } else {
        //设置默认选项
        state.form.id = "";
        state.form.paramContent = defaultParamsFnc();
      }
    });
    return {
      state,
      dictListFnc,
      saveClick,
      beforeClose,
    };
  },
  name: "basicParam",
};
</script>

<style lang="scss" scoped>
.setBasicParam {
  border: 1px solid #dcdfe6;
  border-radius: 5px;
  margin-bottom: 10px;
}
:deep(.el-form-item__content) {
  width: 200px;
  .el-input-number {
    width: 100%;
  }
  .el-select {
    width: 100% !important;
  }
  .el-date-editor.el-input,
  .el-date-editor.el-input__inner {
    width: 200px;
  }
}
</style>
