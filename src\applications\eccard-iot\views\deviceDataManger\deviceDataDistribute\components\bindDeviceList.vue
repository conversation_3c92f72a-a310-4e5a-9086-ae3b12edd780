<template>
  <div class="bind-device-box" v-loading="state.loading">
    <div class="bind-btn">
      <el-button icon="el-icon-plus" size="mini" type="success" @click="state.isBind=true">绑定</el-button>
    </div>
    <el-table :data="state.dataList" border stripe v-loading="state.loading" height='60vh'>
      <el-table-column show-overflow-tooltip label="所属区域" prop="areaName" align="center"></el-table-column>
      <el-table-column show-overflow-tooltip label="设备类型" prop="deviceType" align="center"></el-table-column>
      <el-table-column show-overflow-tooltip label="设备状态" prop="deviceStatus" align="center">
        <template #default="scope">
          {{dictionaryFilter(scope.row.deviceStatus)}}
        </template>
      </el-table-column>
      <el-table-column show-overflow-tooltip label="机号" prop="deviceNo" align="center"></el-table-column>
      <el-table-column show-overflow-tooltip label="设备名称" prop="deviceName" align="center"></el-table-column>
      <el-table-column show-overflow-tooltip label="设备型号" prop="deviceModel" align="center"></el-table-column>
      <el-table-column show-overflow-tooltip label="连接类型" prop="deviceConnectType" align="center">
        <template #default="scope">
          {{dictionaryFilter(scope.row.deviceConnectType)}}
        </template>
      </el-table-column>
      <el-table-column show-overflow-tooltip label="设备IP" prop="deviceIp" align="center"></el-table-column>
      <el-table-column label="操作" align="center">
        <template #default="scope">
          <el-button class="green" size="mini" type="text" @click="cancelBind(scope.row)">取消绑定</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="pagination">
      <el-pagination background :current-page="state.form.pageNum" :page-size="state.form.pageSize" layout="total, sizes, prev, pager, next, jumper" :page-sizes="[6, 10, 20, 50, 100]" :total="state.total" @current-change="handlePageChange" @size-change="handleSizeChange">
      </el-pagination>
    </div>
    <kade-bind-device v-model="state.isBind" :rowData="rowData" @update:modelValue="close" />
  </div>
</template>
<script>
import { ElButton, ElTable, ElTableColumn, ElPagination, ElMessageBox, ElMessage } from "element-plus"
import { reactive, onMounted } from 'vue'
import { dataDistributeDeviceList, dataDistributeBindCancel } from "@/applications/eccard-iot/api";
import bind from "./bind.vue"
import { watch } from '@vue/runtime-core';
export default {
  components: {
    ElButton, ElTable, ElTableColumn, ElPagination,
    "kade-bind-device": bind
  },
  props: {
    rowData: {
      type: Object,
      default: null
    }
  },
  setup(props) {
    const state = reactive({
      loading: false,
      isBind: false,
      dataList: [],
      form: {
        pageNum: 1,
        pageSize: 10
      },
      total: 0
    })

    watch(() => props.rowData, val => {
      if (val.id) {
        state.isBind = false
        getList()
      }
    })

    const getList = async () => {
      state.form.RDistributeId = props.rowData.id
      state.loading = true
      try {
        let { data: { list, total } } = await dataDistributeDeviceList(state.form)
        state.dataList = list
        state.total = total
        state.loading = false
      }
      catch {
        state.loading = false
      }
    }
    const cancelBind = row => {
      ElMessageBox.confirm("确认删除?", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        let { code, message } = await dataDistributeBindCancel(row.id);
        if (code === 0) {
          ElMessage.success(message);
          getList();
        }
      });
    }
    const handlePageChange = (val) => {
      state.form.pageNum = val
      getList()
    };
    const handleSizeChange = (val) => {
      state.form.pageNum = 1
      state.form.pageSize = val
      getList()
    };
    const close = val => {
      if (val) {
        getList()
      }
      state.isBind = false
    }
    onMounted(() => {
      if (props.rowData.id) {
        getList()
      }
    })
    return {
      state,
      cancelBind,
      handlePageChange,
      handleSizeChange,
      close
    }
  }
}
</script>
<style lang="scss" scoped>
.bind-device-box {
  .bind-btn {
    margin-bottom: 10px;
  }
}
</style>