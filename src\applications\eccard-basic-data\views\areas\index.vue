<template>
  <el-row :gutter="20" class="areas-row" style="padding: 20px; box-sizing: border-box">
    <el-col :md="24" :lg="11" :xl="8" class="areas-col">
      <el-card>
        <template #header>
          <div class="card-head">
            <div class="left">
              <i class="el-icon-location-information"></i>
              <span class="areas-htit">区域</span>
            </div>
          </div>
        </template>
        <div class="filter">
          <el-input size="small" style="width: 250px; padding-bottom: 20px" placeholder="请输入区域名称"
            v-model="state.keyword">
            <template #append>
              <el-button icon="el-icon-search" @click="handleSearch" size="small" type="primary">查询</el-button>
            </template>
          </el-input>
        </div>
        <div class="tree">
          <el-tree :props="areaTreeOpts.props" :data="state.data" :filter-node-method="filterNode"
            :default-expanded-keys="state.expandKeys" :expand-on-click-node="false" :auto-expand-parent="false"
            node-key="areaId" @node-click="(node) => handleNodeClick(node, true)" @node-expand="handleExpand"
            @node-collapse="handleCollapse" ref="treeRef">
            <template #default="{ node }">
              <div class="custom-tree-node hasbtn">
                <span class="left">
                  <kade-icon name="iconquyuguanli" />
                  <span style="margin-left: 5px; color: #333">{{
    node.label
}}</span>
                </span>
                <span class="right"  v-if="node.data.checked">
                  <span class="a-btn" @click.stop="handleAdd(node, 1)" v-show="node.data.areaParentId !== 0">
                    <i class="el-icon-plus"></i>
                    <span class="text">本级</span>
                  </span>
                  <span class="a-btn" @click.stop="handleAdd(node, 2)">
                    <i class="el-icon-plus"></i>
                    <span class="text">下级</span>
                  </span>
                  <span class="a-btn red" @click.stop="handleDel(node)">
                    <i class="el-icon-close"></i>
                    <span class="text">删除</span>
                  </span>
                </span>
              </div>
            </template>
          </el-tree>
        </div>
      </el-card>
    </el-col>
    <el-col :md="24" :lg="13" :xl="16" class="areas-col">
      <el-card>
        <template #header>
          <i class="el-icon-xinxi"></i>
          <span class="areas-htit">{{ cardTitle }}</span>
        </template>
        <el-form ref="formRef" :model="state.model" :rules="rules" :label-width="labelWidth" size="small"
          v-loading="loading">
          <el-row>
            <el-col :md="24" :lg="12">
              <el-form-item label="区域名称:" :prop="state.isInfo ? '' : 'areaName'">
                <span class="label" v-if="state.isInfo">
                  {{ state.model.areaName }}
                </span>
                <el-input v-else placeholder="请输入" v-model="state.model.areaName"></el-input>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <!-- <el-form-item label="区域标签:" :prop="state.isInfo ? '' : 'areaLabel'"> -->
              <el-form-item label="区域标签:">
                <span class="label" v-if="state.isInfo">
                  {{ state.model.areaLabelName }}
                </span>
                <el-select multiple v-else placeholder="请选择" v-model="state.model.areaLabel">
                  <el-option v-for="(item, index) in state.areaLabelList" :key="index" :label="item.labelName"
                    :value="item.id"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="上级区域:" :prop="state.isInfo ? '' : 'areaParentId'">
                <span class="label" v-if="state.isInfo">
                  {{ state.model.areaParentName }}
                </span>
                <kade-area-select-tree v-else style="width:100%" :value="state.model.areaParentId" valueKey="id"
                  :multiple="false" @valueChange="val => state.model.areaParentId = val.id" />
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="负责人:" :prop="state.isInfo ? '' : 'areaDirector'">
                <span class="label" v-if="state.isInfo">{{ state.model.areaDirector }}</span>
                <el-input v-else placeholder="请选择" v-model="state.model.areaDirector" readonly
                  @click="state.isPersoner = true"></el-input>
                <select-person-dialog :isMultiple="false" :isShow="state.isPersoner" @close="closePersonSelect" />
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="联系电话:" :prop="state.isInfo ? '' : 'areaTel'">
                <span class="label" v-if="state.isInfo">
                  {{ state.model.areaTel }}
                </span>
                <el-input v-else placeholder="请输入" v-model="state.model.areaTel"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="区域地址:" :prop="state.isInfo ? '' : 'areaAddress'">
                <span class="label" v-if="state.isInfo">
                  {{ state.model.areaAddress }}
                </span>
                <el-input show-word-limit maxlength="100" type="textarea" :rows="3" v-else placeholder="请输入"
                  v-model="state.model.areaAddress"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24" align="center">
              <el-button size="small" icon="el-icon-circle-close" @click="handleCancel">取消</el-button>
              <el-button size="small" icon="el-icon-edit" type="primary"
                :disabled="state.model.areaParentId === 0 || !state.model.checked" v-if="state.isInfo"
                @click="handleEdit()">编辑</el-button>
              <el-button v-else size="small" icon="el-icon-circle-check" :loading="btnLoading" type="primary"
                @click="handleSubmit">保存</el-button>
            </el-col>
          </el-row>
        </el-form>
      </el-card>
    </el-col>
  </el-row>
</template>
<script>
import {
  ElCard,
  ElRow,
  ElCol,
  ElButton,
  ElTree,
  ElInput,
  ElMessage,
  ElMessageBox,
  ElForm,
  ElFormItem,
  ElSelect,
  ElOption,
} from "element-plus";
import { reactive, ref, onMounted, computed, nextTick } from "vue";
import { useStore } from "vuex";
import {
  getAreaMenuList,
  addArea,
  delArea,
  editArea,
  getAreaDetailByID,
  getAreaLabelCheckList,
  getUserAreaPurview
} from "@/applications/eccard-basic-data/api";
import areaSelectTree from "@/components/tree/areaSelectTree.vue";
import selectPersonDialog from "@/components/table/selectPersonDialog.vue";
import { validateMobileAndFixTel } from "@/utils/validate";
import { makeTree } from "@/utils/index.js";
const getDefaultModel = () => ({
  areaAddress: "",
  areaDirector: "",
  areaName: "",
  areaParentId: "",
  areaParentName: "",
  areaTel: "",
});
export default {
  components: {
    "el-card": ElCard,
    "el-col": ElCol,
    "el-row": ElRow,
    "el-button": ElButton,
    "el-tree": ElTree,
    "el-input": ElInput,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    ElSelect,
    ElOption,
    "kade-area-select-tree": areaSelectTree,
    "select-person-dialog": selectPersonDialog,
  },
  setup() {
    const loading = ref(false);
    const btnLoading = ref(false);
    const treeRef = ref(null);
    const formRef = ref(null);
    const store = useStore();
    const state = reactive({
      isPersoner: false,
      model: getDefaultModel(),
      keyword: "",
      hasArea: false,
      isInfo: false,
      data: [],
      expandKeys: [],
      selectUsers: "",
      areaLabelList: []
    });

    const rules = {
      areaName: [
        { required: true, message: "请输入区域名称" },
        { max: 20, message: "区域名称长度不能超过20字符" },
      ],
      areaParentId: [
        {
          required: true,
          message: "请选择上级区域",
          trigger: "change",
        },
      ],
      areaLabel: [
        {
          required: true,
          message: "请选择区域标签",
          trigger: "change",
        },
      ],
      areaDirector: [
        {
          required: true,
          message: "请选择区域负责人",
          trigger: "change",
        },
      ],
      areaTel: [
        {
          required: true,
          message: "请输入联系电话",
        },
        {
          validator: validateMobileAndFixTel,
        },
      ],
      areaAddress: [
        {
          required: true,
          message: "请输入区域地址",
        },
        {
          max: 100,
          message: "区域地址长度不得超过100字符",
        },
      ],
    };
    const closePersonSelect = (val) => {
      if (val) {
        state.model.areaDirector = val.userName
      }
      state.isPersoner = false;
    };
    const getAreaLabelList = async () => {
      let { data } = await getAreaLabelCheckList()
      state.areaLabelList = data
    }
    const loadData = async () => {
      const {
        data
      } = await getUserAreaPurview({ areaParentId: 0 });
      state.hasArea = data.length === 0;
      state.expandKeys.push(data[0].id);
      state.data = makeTree(data, "id", "areaParentId", "children")
    };

    const filterNode = (value, data) => {
      if (!value) {
        return true;
      }
      return data.areaName?.indexOf(value) !== -1;
    };

    const handleSearch = () => {
      treeRef.value.filter(state.keyword);
    };

    const handleSubmit = () => {
      formRef.value.validate(async (valid) => {
        if (valid) {
          try {
            btnLoading.value = true;
            const fn = state.model.areaId ? editArea : addArea;
            // eslint-disable-next-line no-unused-vars
            const { areaParentName, ...fields } = state.model;
            const { message } = await fn({
              ...fields,
              operId: store.state.user.userInfo.id,
            });
            ElMessage.success(message);
            if (!state.model.areaId) {
              handleAdd();
            }
            loadData();
          } catch (e) {
            throw new Error(e.message);
          } finally {
            btnLoading.value = false;
          }
        }
      });
    };

    const handleNodeClick = async (target, isInfo) => {
      try {
        loading.value = true;
        formRef.value.clearValidate();
        const {
          data: { parentAreaId, parentAreaName, areaLabel, ...fields },
        } = await getAreaDetailByID({ areaId: target.id });
        state.model = Object.assign(getDefaultModel(), {
          ...fields,
          checked: target.checked,
          areaLabel,
          areaParentId: parentAreaId,
          areaParentName: parentAreaName,
          areaLabelName: areaLabel ? state.areaLabelList.filter(item => areaLabel.includes(item.id)).map(item => item.labelName).join(",") : ''
        });

        state.isInfo = !!isInfo;
      } catch (e) {
        throw new Error(e.message);
      } finally {
        loading.value = false;
      }
    };

    const handleAdd = (node, type) => {
      state.isInfo = false;
      if (!node) {
        state.model = getDefaultModel();
      } else if (type === 1) {
        state.model = Object.assign(getDefaultModel(), {
          areaParentId: node.data.areaParentId,
          areaParentName: node.data.parentAreaName,
        });
      } else {
        state.model = Object.assign(getDefaultModel(), {
          areaParentId: node.data.id,
          areaParentName: node.data.areaName,
        });
      }
      nextTick(() => {
        formRef.value?.clearValidate?.();
      });
    };

    const handleEdit = (node) => {
      if (node) {
        handleNodeClick(node.data);
      } else {
        state.isInfo = false;
      }
    };

    const handleDel = ({ data }) => {
      ElMessageBox.confirm(`确认删除?`, {
        type: "warning",
        closeOnPressEscape: false,
        closeOnClickModal: false,
      }).then(async () => {
        try {
          const { message, code } = await delArea({
            areaId: data.areaId,
          });
          if (code === 0) {
            ElMessage.success(message);
            const node = treeRef.value.getNode(state.model.areaId);
            treeRef.value.remove(node);
            handleAdd();
          } else {
            ElMessage.error(message);
          }
        } catch (e) {
          throw new Error(e.message);
        }
      });
    };

    const selectAreaChange = (data) => {
      Object.assign(state.model, {
        areaParentName: data?.areaName || "",
        areaParentId: data?.areaId || "",
      });
    };

    const handleCancel = () => {
      if (state.isInfo) {
        handleAdd();
      } else if (state.model.id) {
        state.isInfo = true;
        handleNodeClick(state.model, true);
      } else {
        handleAdd();
      }
    };

    const handleExpand = (data) => {
      state.expandKeys.push(data.id);
    };
    const handleCollapse = (data) => {
      state.expandKeys = state.expandKeys.filter((it) => it !== data.areaId);
    };

    const getParentAreas = (list, areaId) => {
      const arr = [];
      list.forEach((it) => {
        if (it.areaId !== areaId) {
          const target = { ...it };
          if (it.children && it.children.length) {
            target.children = getParentAreas(it.children, areaId);
          }
          arr.push(target);
        }
      });
      return arr;
    };

    const parentAreas = computed(() => {
      if (state.model.areaId && !state.isInfo) {
        return getParentAreas(state.data, state.model.areaId);
      }
      return state.data;
    });

    const cardTitle = computed(() => {
      if (state.isInfo) {
        return "区域信息";
      } else if (state.model.areaId) {
        return "编辑区域";
      } else {
        return "新增区域";
      }
    });

    onMounted(() => {
      loadData();
      getAreaLabelList()
    });

    return {
      labelWidth: THEMEVARS.formLabelWidth,
      state,
      rules,
      filterNode,
      loading,
      btnLoading,
      treeRef,
      formRef,
      closePersonSelect,
      handleSearch,
      handleAdd,
      handleEdit,
      handleExpand,
      handleCollapse,
      handleDel,
      handleSubmit,
      handleNodeClick,
      handleCancel,
      selectAreaChange,
      cardTitle,
      parentAreas,
      areaTreeOpts: {
        props: {
          children: "children",
          label: "areaName",
          isLeaf: (data) => {
            if (data["isLeaf"] === undefined) {
              return true;
            }
            return !!data.isLeaf;
          },
        },
      },
      selectAreaAction: async (params) => {
        const {
          data: { areaMenuList },
        } = await getAreaMenuList(params);
        return areaMenuList;
      },
    };
  },
};
</script>
<style lang="scss" scoped>
.areas-row {
  height: 100%;
  overflow-y: auto;

  .areas-col {
    height: 100%;
  }

  .areas-htit {
    margin-left: 5px;
  }

  .el-card {
    height: calc(100% - 2px);
  }



  :deep(.el-card__body) {
    height: calc(100% - 70px);
    box-sizing: border-box;
    overflow-y: auto;
    overflow-x: auto;

  }

  .card-head {
    display: flex;
    justify-content: space-between;

    .right {
      display: flex;
      justify-content: flex-end;
    }
  }

  .tree {

    .el-tree {
      width: auto;
      display: inline-block;
      min-width: 100%;

      .right {
        padding-left: 10px;
      }
    }
  }
}

:deep(.el-select) {
  width: 100%;
}

@media screen and(max-width: 1200px) {
  .areas-row {
    .areas-col+.areas-col {
      margin-top: 20px;
    }
  }
}
</style>