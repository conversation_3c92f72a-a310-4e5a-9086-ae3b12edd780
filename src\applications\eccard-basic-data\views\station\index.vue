<template>
  <kade-route-card>
    <kade-table-filter>
      <el-form label-width="100px" inline>
        <el-form-item label="关键字">
          <el-input v-model="state.form.keyword" placeholder="岗位编号或名称关键字搜索"></el-input>
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="岗位列表">
      <template #extra>
        <el-button @click="handleEdit('')" size="mini" type="success">新建岗位</el-button>
        <!-- <el-button size="mini" type="primary">批量删除</el-button> -->
      </template>
      <el-table v-loading="state.loading" height="55vh" :data="state.dataList" border>
        <el-table-column label="岗位编号" prop="postCode" align="center"></el-table-column>
        <el-table-column label="岗位名称" prop="postName" align="center"></el-table-column>
        <el-table-column label="备注" prop="postRemark" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column label="操作" prop="" align="center">
          <template #default="scope">
            <el-button @click="handleEdit(scope.row)" type="text" size="mini">编辑</el-button>
            <el-button @click="handleDel(scope.row)" type="text" size="mini">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination background :current-page="state.form.currentPage" layout="total, sizes, prev, pager, next, jumper" :page-sizes="[10, 20, 50, 100, 200]" :total="state.total" :page-size="state.form.pageSize" @current-change="(val) => pageChange(val)" @size-change="(val) => sizeChange(val)">
        </el-pagination>
      </div>
    </kade-table-wrap>
    <kade-station-edit :isShow="state.isEdit" :rowData="state.rowData" @close="close" />
  </kade-route-card>
</template>
<script>
import { ref, reactive, onMounted } from "vue";
import {
  getStationList,
  delStation,
} from "@/applications/eccard-basic-data/api";
import stationEdit from "./components/edit.vue";
import {
  ElForm,
  ElFormItem,
  ElInput,
  ElTable,
  ElTableColumn,
  ElButton,
  ElPagination,
  ElMessageBox,
  ElMessage,
} from "element-plus";
export default {
  components: {
    ElForm,
    ElFormItem,
    ElInput,
    ElTable,
    ElTableColumn,
    ElButton,
    ElPagination,
    "kade-station-edit": stationEdit,
  },
  setup() {
    const showCreateModal = ref(false);
    const state = reactive({
      loading: false,
      isEdit: false,
      form: {
        pageNum: 1,
        pageSize: 10,
      },
      dataList: [],
      total: 0,
      rowData: "",
    });
    const getList = async () => {
      state.loading = true;
      let {
        data: { list, total },
      } = await getStationList(state.form);
      state.dataList = list;
      state.total = total;
      state.loading = false;
    };
    const handleEdit = (row) => {
      state.rowData = row;
      state.isEdit = true;
    };
    const handleDel = (row) => {
      ElMessageBox.confirm(`确认删除?`, {
        type: "warning",
        closeOnPressEscape: false,
        closeOnClickModal: false,
      }).then(async () => {
        try {
          const { message, code } = await delStation(row.id);
          if (code === 0) {
            ElMessage.success(message);
            getList();
          } else {
            ElMessage.error(message);
          }
        } catch (e) {
          throw new Error(e.message);
        }
      });
    };
    const close = (val) => {
      if (val) {
        getList();
      }
      state.isEdit = false;
    };
    onMounted(() => {
      getList();
    });
    return {
      showCreateModal,
      state,
      handleEdit,
      handleDel,
      close,
    };
  },
};
</script>
<style lang="scss" scoped>
.role {
  width: 100%;
  height: 100%;
}
:deep(.el-dialog__header) {
  border-bottom: 1px solid #efefef;
}

:deep(.el-overlay) {
  position: absolute;
  background-color: rgba(0, 0, 0, 0.3);
}

:deep(.el-dialog__footer) {
  text-align: center;
}
</style>