/* 现金充值明细表 */
<template>
  <div class="cashRecharge" v-loading="state.loading">
    <kade-route-card style="height: auto">
      <kade-table-filter @search="getList()" @reset="reset()">
        <el-form inline size="mini" label-width="100px">
          <el-form-item label="用户编号:">
            <el-input placeholder="编号关键字搜索" v-model="state.form.userCode"></el-input>
          </el-form-item>
          <el-form-item label="用户姓名:">
            <el-input placeholder="姓名关键字搜索" v-model="state.form.userName"></el-input>
          </el-form-item>
          <el-form-item label="组织机构:">
            <kade-dept-select-tree style="width: 100%" :value="state.form.deptPath" valueKey="deptPath" :multiple="false" @valueChange="(val) => (state.form.deptPath = val.deptPath)" />
          </el-form-item>
          <el-form-item label="充值钱包:">
            <el-select clearable multiple collapse-tags v-model="state.form.walletCode" placeholder="请选择">
              <el-option v-for="(item, index) in state.allWalletList" :key="index" :label="item.walletName" :value="item.walletCode">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="交易类型:">
            <el-select clearable multiple collapse-tags v-model="state.form.costType" placeholder="请选择" @change="costTypeChange">
              <el-option v-for="(item, index) in state.costTypeList" :key="index" :label="item.costName" :value="item.costCode">
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="交易方式:">
            <el-select clearable multiple collapse-tags v-model="state.form.tradeMode" placeholder="请选择">
              <el-option v-for="(item, index) in state.tradeModeList" :key="index" :label="item.name" :value="item.code">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="操作员:">
            <el-select clearable filterable allow-create multiple collapse-tags v-model="state.form.operator" placeholder="请选择">
              <el-option v-for="(item, index) in state.systemUserList" :key="index" :label="item.userName" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="交易来源:">
            <el-select clearable v-model="state.form.tradeSource" placeholder="请选择" @change="tradeSourceChange">
              <el-option v-for="(item, index) in state.tradeSourceList" :key="index" :label="item.tradeName" :value="item.tradeCode">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="充值终端:">
            <el-select clearable filterable multiple collapse-tags v-model="state.form.deviceId" placeholder="请选择">
              <el-option v-for="(item, index) in state.deviceByTerminalTypeList" :key="index" :label="item.label" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="统计时间:">
            <el-date-picker v-model="state.requestDate" type="datetimerange" unlink-panels :default-time="state.defaultTime" range-separator="~" start-placeholder="请选择日期" end-placeholder="请选择日期" @change="changeDate">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="按入账时间:">
            <el-checkbox v-model="state.timeType"></el-checkbox>
          </el-form-item>
        </el-form>
      </kade-table-filter>
      <kade-table-wrap title="充值明细表">
        <template #extra>
          <el-button @click="exportClick()" icon="el-icon-daoru" size="mini" class="btn-blue">导出查询明细</el-button>
        </template>
        <el-table style="width: 100%" :data="state.detailList" ref="multipleTable" height="55vh" highlight-current-row border stripe>
          <el-table-column v-for="(item,index) in column" :key="index" :width="item.width" :label="item.label" :prop="item.prop" align="center" show-overflow-tooltip>
            <template v-if="item.render" #default="scope">
              {{item.render(scope.row[item.prop])}}
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination">
          <el-pagination background :current-page="state.form.currentPage" :page-size="state.form.pageSize" layout="total, sizes, prev, pager, next, jumper" :page-sizes="[6, 10, 20, 50, 100]" :total="state.total" @current-change="handlePageChange" @size-change="handleSizeChange">
          </el-pagination>
        </div>
      </kade-table-wrap>
    </kade-route-card>
  </div>
</template>
<script>
import {
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElTable,
  ElTableColumn,
  ElPagination,
  ElDatePicker,
  ElMessage,
  ElCheckbox,
} from "element-plus";
import { timeStr } from "@/utils/date.js";
import { reactive } from "@vue/reactivity";
import {
  getRechargeCashList,
  costType,
  tradeSource,
  tradeMode,
  getSystemUser,
  rechargeCashExport,
  getWalletActiveList,
} from "@/applications/eccard-finance/api";
import { getDeviceStatus } from "@/applications/eccard-iot/api";
import { onMounted, watch } from "@vue/runtime-core";
import deptSelectTree from "@/components/tree/deptSelectTree.vue";
const column = [
  { label: "用户编号", prop: "userCode", width: "" },
  { label: "用户姓名", prop: "userName", width: "" },
  { label: "组织机构", prop: "deptName", width: "" },
  { label: "充值钱包", prop: "walletName", width: "" },
  { label: "充值金额", prop: "tradeAmount", width: "" },
  {
    label: "充值时间",
    prop: "rechargeTime",
    width: "170",
    render: (val) => val && timeStr(val),
  },
  {
    label: "入账时间",
    prop: "reconciliationTime",
    width: "170",
    render: (val) => val && timeStr(val),
  },
  { label: "交易类型", prop: "costTypeName", width: "" },
  { label: "交易来源", prop: "tradeSourceName", width: "" },
  { label: "交易方式", prop: "tradeModeName", width: "" },
  { label: "操作员", prop: "operatorName", width: "" },
  { label: "充值终端", prop: "deviceName", width: "" },
];
export default {
  components: {
    "el-button": ElButton,
    "el-input": ElInput,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    "el-select": ElSelect,
    "el-option": ElOption,
    "el-table": ElTable,
    "el-table-column": ElTableColumn,
    "el-pagination": ElPagination,
    ElCheckbox,
    ElDatePicker,
    "kade-dept-select-tree": deptSelectTree,
  },
  setup() {
    const state = reactive({
      loading: false,
      form: {
        currentPage: 1,
        pageSize: 6,
      },
      timeType: true,
      detailList: [],
      total: 0,
      requestDate: "",
      costTypeList: [],
      allWalletList: [],
      departCheckList: [],
      tradeSourceList: [],
      tradeModeList: [],
      systemUserList: [],
      defaultTime: [
        new Date(new Date(new Date().toLocaleDateString()).getTime()),
        new Date(
          new Date(new Date().toLocaleDateString()).getTime() +
          24 * 60 * 60 * 1000 -
          1
        ),
      ],
    });
    watch(
      () => state.requestDate,
      (val) => {
        if (!val) {
          delete state.form.startTime;
          delete state.form.endTime;
        }
      }
    );
    //获取交易类型
    const getCostTypeList = () => {
      costType().then((res) => {
        state.costTypeList = res.data.filter(
          (item) => item.costCode == 101 || item.costCode == 102
        );
      });
    };

    //获取钱包列表
    const queryWalletActiveList = () => {
      getWalletActiveList().then((res) => {
        state.allWalletList = res.data.filter(
          (item) => item.walletCode != 2 && item.walletCode != 3
        );
      });
    };
    //获取交易来源
    const getTradeSource = () => {
      tradeSource().then((res) => {
        state.tradeSourceList = res.data.filter((item) => item.tradeCode != 4);
      });
    };
    //获取交易方式
    const getTradeModeList = (val) => {
      tradeMode({ costType: val }).then((res) => {
        state.tradeModeList = res.data.filter((item) => item.code != 8);
      });
    };
    //获取操作员
    const querySystemUser = () => {
      getSystemUser().then((res) => {
        state.systemUserList = res.data;
      });
    };
    //获取终端设备
    const queryDeviceByTerminalTypeList = () => {
      getDeviceStatus({}).then((res) => {
        state.deviceByTerminalTypeList = res.data.map((item) => {
          return {
            ...item,
            label: item.deviceNo + "-" + item.deviceName,
          };
        });
      });
    };
    const getList = async () => {
      state.loading = true;
      state.form.timeType = state.timeType ? "BY_CREATE_DATE" : "BY_TRADE_DATE";
      if (state.form.deviceNo) {
        state.form.deviceNo = Number(state.form.deviceNo);
        console.log(state.form.deviceNo);
      }
      let { code, data, message } = await getRechargeCashList(state.form);
      try {
        if (code === 0) {
          let {
            page: { total, list },
            totalAmount,
          } = data;
          state.detailList = list;
          state.total = total;
          if (state.detailList.length) {
            state.detailList.push({ tradeAmount: totalAmount, userCode: "合计" });
          }
          state.loading = false;
        } else {
          ElMessage.error(message);
          state.loading = false;
        }
      }
      catch {
        state.loading = false;
      }
    };
    const changeDate = (val) => {
      if (val && val.length) {
        state.form.startTime = timeStr(val[0]);
        state.form.endTime = timeStr(val[1]);
      } else {
        delete state.form.startTime;
        delete state.form.endTime;
      }
    };

    const costTypeChange = (val) => {
      if (val && val.length) {
        getTradeModeList(val);
      } else {
        state.tradeModeList = [];
      }
    };

    const tradeSourceChange = (val) => {
      state.form.terminalDeviceId = "";
      if (val === 3) {
        queryDeviceByTerminalTypeList();
      } else {
        state.deviceByTerminalTypeList = [];
      }
    };

    const exportClick = async () => {
      state.form.timeType = state.timeType ? "BY_CREATE_DATE" : "BY_TRADE_DATE";
      if (state.form.deviceNo) {
        state.form.deviceNo = Number(state.form.deviceNo);
        console.log(state.form.deviceNo);
      }
      state.loading = true

      try {
        let res = await rechargeCashExport(state.form);
        let url = window.URL.createObjectURL(
          new Blob([res], { type: "application/vnd.ms-excel" })
        );
        let link = document.createElement("a");
        link.style.display = "none";
        link.href = url;
        link.setAttribute("download", "充值明细报表.xlsx");
        document.body.appendChild(link);
        link.click();
        state.loading = false
      }
      catch {
        state.loading = false
      }
    };

    const handlePageChange = (val) => {
      state.form.currentPage = val;
      getList();
    };
    const handleSizeChange = (val) => {
      state.form.currentPage = 1;
      state.form.pageSize = val;
      getList();
    };

    const reset = () => {
      state.form = {
        currentPage: 1,
        pageSize: 6,
      };
      state.requestDate = "";
    };
    onMounted(() => {
      getList();
      getCostTypeList();
      getTradeSource();
      // getTradeModeList();
      querySystemUser();
      queryWalletActiveList();
    });
    return {
      column,
      state,
      timeStr,
      exportClick,
      getList,
      changeDate,
      costTypeChange,
      tradeSourceChange,
      handlePageChange,
      handleSizeChange,
      reset,
    };
  },
};
</script>
<style lang="scss" scoped>
:deep(.el-divider--horizontal) {
  margin: 0 0 10px 0;
}
.cashRecharge {
  :deep(.search-box) {
    .el-input__inner {
      width: 193px;
    }
    .el-date-editor {
      width: 400px;
    }
  }
}
</style>