<template>
  <div class="padding-box"  v-if="details">
    <div class="params-title">网络参数</div>
    <div class="params-box">
      <div class="params-item" v-for="(item,index) in netWorkParams" :key="index">
        <div class="params-label">{{item.label}}</div>
        <div class="params-value" v-if="item.render">{{item.render(details[item.filed])}}</div>
        <div class="params-value" v-else>{{details[item.filed]}}</div>
      </div>
    </div>
    <div class="params-title" style="margin-top:20px">wifi参数</div>
    <div class="params-box">
      <div class="params-item" v-for="(item,index) in wifiParams" :key="index">
        <div class="params-label">{{item.label}}</div>
        <div class="params-value" v-if="item.render">{{item.render(details[item.filed])}}</div>
        <div class="params-value" v-else>{{details[item.filed]}}</div>
      </div>
    </div>
  </div>
  <el-empty v-else description="当前设备参数未设定"></el-empty>
</template>
<script>
import { computed } from "vue";
import { useStore } from "vuex";
import { ElEmpty } from "element-plus"

export default {
  components: {
    ElEmpty
  },
  setup() {
    const store = useStore();
    const details = computed(() => {
      let data = {}
      if (store.state['identityDevice/faceDistingDevice'].detailsParams.netWork) {
        data = JSON.parse(store.state['identityDevice/faceDistingDevice'].detailsParams.netWork.paramContent);
      } else {
        data = ''
      }
      return data;
    });
    const dictListFnc = () => {
      return store.state['identityDevice/faceDistingDevice'].faceDict
    }
    const netWorkParams = [
      { label: "设备IP", filed: "deviceIP" },
      { label: "子网掩码", filed: "subnetMask" },
      { label: "网关", filed: "gateway" },
      { label: "DNS", filed: "dns" },
      { label: "DHCP", filed: "dhcp", render: val => dictListFnc().displayMode.filter(item => item.value == val)[0]?.label },
    ]
    const wifiParams = [
      { label: "wifi名称", filed: "wifiName" },
      { label: "wifi密码", filed: "wifiPwd" },
      { label: "wifi IP", filed: "wifiIP" },
      { label: "网关", filed: "wifiGateway" },
      { label: "DNS", filed: "wifiDNS" },
      { label: "DHCP", filed: "wifiDHCP", render: val => dictListFnc().displayMode.filter(item => item.value == val)[0]?.label },
    ]
    return {
      netWorkParams,
      wifiParams,
      details
    }
  }
}
</script>
<style lang="scss" scoped>
.params-title {
  font-size: 14px;
  text-align: left;
  margin-bottom: 10px;
}
.params-box {
  display: flex;
  justify-content: space-around;
  align-items: center;
  border: 1px solid #eeeeee;
  line-height: 50px;
  .params-item {
    text-align: center;
    width: 100%;
    border-right: 1px solid #eeeeee;
    &:last-child {
      border-right: 0;
    }
    .params-label {
      width: 100%;
      border-bottom: 1px solid #eeeeee;
      background: #f6f6f6;
    }
    .params-value{
      height: 50px;
    }
  }
}
</style>