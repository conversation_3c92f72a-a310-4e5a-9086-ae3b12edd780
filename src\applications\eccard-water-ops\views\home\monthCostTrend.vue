<template>
  <div id="mychart4" class="echartDiv"></div>
</template>
<script>
import * as echarts from "echarts";
import { onMounted, ref, nextTick, watch } from "vue";
import { getMonthCost } from "../../api/home.js";
export default {
  props: {
    queryDate: {
      type: Object,
      default: () => ({
        startTime: '',
        endTime: '',
        type: '1' // 按天
      })
    }
  },
  setup(props) {
    const chartData = ref({
      xAxisData: [],
      waterData: [],
      electricData: []
    });

    // 获取月度成本数据
    const fetchMonthCostData = async () => {
      console.log('父组件传递的参数:', props.queryDate);
      const params = props.queryDate
      const response = await getMonthCost(params);
      console.log('月度运营成本对比:', response);
      if (response && response.code === 0 && response.data) {
          // 处理接口返回的数据结构
          const data = response.data || [];

          // 按月份排序数据
          const sortedData = data.sort((a, b) => new Date(a.month) - new Date(b.month));

          // 提取月份作为X轴数据，格式化为 "YYYY年MM月" 格式
          const xAxisData = sortedData.map(item => {
            const date = new Date(item.month);
            const year = date.getFullYear();
            const month = (date.getMonth() + 1).toString().padStart(2, '0');
            return `${year}年${month}月`;
          });

          // 计算水费总成本 (数量 * 单价)
          const waterData = sortedData.map(item => {
            const waterNum = parseFloat(item.waterNum) || 0;
            const waterPrice = parseFloat(item.waterPrice) || 0;
            return waterNum * waterPrice;
          });

          // 计算电费总成本 (数量 * 单价)
          const electricData = sortedData.map(item => {
            const elecNum = parseFloat(item.elecNum) || 0;
            const elecPrice = parseFloat(item.elecPrice) || 0;
            return elecNum * elecPrice;
          });

          chartData.value = {
            xAxisData: xAxisData,
            waterData: waterData,
            electricData: electricData
          };
        // 重新初始化图表
        echartInit();
      }
    };
    const echartInit = () => {
      var chartDom = document.getElementById("mychart4");
      var myChart = echarts.init(chartDom);
      // 指定图表的配置项和数据
      var option = {
        title: {
          text: '月度运营成本对比'
        },
        grid: {
          top: "20%",
          left: "0%",
          right: "0%",
          bottom: "5%",
          containLabel: true,
        },
        xAxis: {
          data: chartData.value.xAxisData,
          axisTick: {
            alignWithLabel: true,
          },
          axisLine: {
            lineStyle: {
              color: "#9498b0s",
            },
          },
          axisLabel: {
            margin: 20,
            textStyle: {
              fontSize: 14,
            },
          },
        },
        yAxis: {
          type: "value",
          splitLine: {
            show: true,
            lineStyle: {
              type: "dashed",
            },
          },
          axisLabel: {
            margin: 20,
            textStyle: {
              fontSize: 12,
            }
          }
        },
        tooltip: {
          trigger: "axis",
        },
        series: [
          {
            name: "水费:",
            data: chartData.value.waterData,
            type: "bar",
            color: "#3ba1ff",
            barWidth: 18,
          },
          // {
          //   name: "电费:",
          //   data: chartData.value.electricData,
          //   type: "bar",
          //   color: "#2fc25b",
          //   barWidth: 18,
          // },
        ],
      };
      // 使用刚指定的配置项和数据显示图表。
      // myChart.setOption(option);
      nextTick(() => {
        myChart.resize();
        myChart.setOption(option, true);
      });
    };

    // 监听父组件传递的参数变化
    watch(() => props.queryDate, () => {
      if (props.queryDate) {
        fetchMonthCostData();
      }
    }, { deep: true });

    //挂载
    onMounted(() => {
      if (props.queryDate) {
        fetchMonthCostData();
      } else {
        echartInit();
      }
    });
    return {
      echartInit,
      fetchMonthCostData
    };
  },
};
</script>
<style lang="scss" scoped>
.echartDiv {
  height: 350px;
}
</style>
