<template>
  <el-dialog :model-value="modelValue" title="添加门禁权限" width="1550px" :before-close="handleClose">
    <el-row :gutter="10">
      <el-col :span="9">
        <div class="select-access">
          <div class="title">选择门禁</div>
          <el-divider></el-divider>
          <kade-select-table :isShow="true" :value=[] :column="accessColumn" :linkage="linkage" :selectCondition="accessSelection" :params="accessParams" :reqFnc="()=>{}" :isMultiple="true" :isCurrentSelect="true" />
        </div>
      </el-col>
      <el-col :span="15">
        <div class="select-user">
          <div class="title">选择用户</div>
          <el-divider></el-divider>
          <kade-select-table :isShow="true" :value=[] :column="userColumn" :selectCondition="userSelection" :params="userParams" :reqFnc="()=>{}" :isMultiple="true" :isCurrentSelect="true" />
        </div>
      </el-col>
    </el-row>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose" size="mini">取消</el-button>
        <el-button type="primary" @click="submit" size="mini">确认</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { reactive } from 'vue'
import selectTable from '@/components/table/selectTable'
import { ElDialog, ElButton, ElRow, ElCol,ElDivider } from "element-plus"
const accessColumn = [
  { label: '区域', prop: '' },
  { label: '楼栋', prop: '' },
  { label: '单元', prop: '' },
  { label: '门禁名称', prop: '' },
  { label: '门禁SN', prop: '' }
]
const userColumn = [
  { label: '组织机构', prop: '' },
  { label: '身份类别', prop: '' },
  { label: '用户编号', prop: '' },
  { label: '用户名称', prop: '' },
  { label: '性别', prop: '' }
]
const linkage = {
  linkageData: {
    area: { label: '区域', valueKey: 'areaId', key: 'id' },
    building: { label: '楼栋', valueKey: 'buildId' },
    unit: { label: '单元', valueKey: 'unitNum' }
  }
}
export default {
  props: {
    modelValue: {
      type: Boolean,
      default: false
    }
  },
  components: {
    ElDialog,
    ElButton,
    ElRow,
    ElCol,
    ElDivider,
    "kade-select-table": selectTable
  },
  setup(props, context) {
    const state = reactive({})
    const accessSelection = [
  {
    label: "SN",
    valueKey: "deviceModel",
    placeholder: "请输入",
    isSelect: false,
  },
]
const userSelection = [
  { label: '组织机构', valueKey: '', placeholder: '请选择', isTree: 'dept' },
  { label: '身份类别', valueKey: '', placeholder: '请选择', isSelect: true, select: { list: [], option: { label: '', value: '' } } },
  { label: '关键字', valueKey: '', placeholder: '姓名或编号关键字搜索', isSelect: false },
  { label: '性别', valueKey: '', placeholder: '请选择', isSelect: true, select: { list: [], option: { label: '', value: '' } } },
  { label: '是否住宿', valueKey: '', placeholder: '请选择', isSelect: true, select: { list: [], option: { label: '', value: '' } } }

]
const accessParams = {
  currentPageKey: "pageNum",
  pageSizeKey: "pageSize",
  resListKey: "list",
  resTotalKey: "total",
  value: {
  },
  tagNameKey: "deviceName",
  valueKey: "id",
}
const userParams = {
  currentPageKey: "pageNum",
  pageSizeKey: "pageSize",
  resListKey: "list",
  resTotalKey: "total",
  value: {},
  tagNameKey: "deviceName",
  valueKey: "id",
}
    const handleClose = () => {
      context.emit("update:modelValue", false)
    }
    const submit = () => { 
      
    }
    return {
      state,
      submit,
      handleClose,
      accessColumn,
      userColumn,
      linkage,
      accessSelection,
      userSelection,
      accessParams,
      userParams,
    }
  }
}
</script>

<style lang="scss" scoped>
.el-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin: 20px 0;
  width: 100%;
  .title{
    margin: 10px 20px;
  }
  :deep(.select-access) {
    border: 1px solid #eeeeee;
    border-radius: 3px;
    .el-input__inner{
      width: 140px;
    }
    .el-form-item__content{
      width: 170px !important;
    }
    .select-trigger{
      width: 140px;
    }
    .el-select .el-select--mini{
      width: 140px !important;
    }
    .el-form-item__content .el-input{
      width:140px;
    }
    .el-form-item__label{
      width: 70px !important;
    }
    .pagination{
      width: 100%;
      height: 52px;
      position: relative;
      .el-pagination{
      position: absolute;
      left: 70px;
    }
    }
    
  }
  :deep(.select-user) {
    border: 1px solid #eeeeee;
    border-radius: 3px;
    .el-input__inner{
      width: 180px;
    }
    .select-trigger{
      width: 180px;
    }
    .el-input__inner{
      width:192px;
    }
    .el-select .el-select--mini{
      width: 180px !important;
    }
    .pagination{
      width: 100%;
      height: 52px;
      position: relative;
      .el-pagination{
      position: absolute;
      left: 400px;
    }
    }
    
  }
}
</style>