<template>
  <div class="padding-form-box setBasicParam" v-loading="state.loading">
    <el-form inline size="small" label-width="200px">
      <el-form-item :label="item.label" v-for="(item,index) in formList" :key="index">
        <el-input-number :min="0" :max="100000" v-model="state.form.paramContent[item.valueKey]"></el-input-number>
      </el-form-item>
    </el-form>
    <div>单位：元/计费单位</div>
  </div>
  <div style="text-align: center">
    <el-button size="mini" @click="beforeClose()">取消</el-button>
    <el-button size="mini" type="primary" @click="saveClick()">保存</el-button>
  </div>
</template>

<script>
import {
  ElButton,
  ElForm,
  ElFormItem,
  ElInputNumber,
  ElMessage,
} from "element-plus";
import { onMounted, reactive } from "vue";
import { useStore } from "vuex";
const defaultParamsFnc = () => {
  return {
    cardType1: 1,
    cardType2: 1,
    cardType3: 1,
    cardType4: 1,
    cardType5: 1,
    cardType6: 1,
    cardType7: 1,
    cardType8: 1,
    cardType9: 1,
    cardType10: 1,
    cardType11: 1,
    cardType12: 1,
    cardType13: 1,
    cardType14: 1,
    cardType15: 1,
    cardType16: 1,
    cardType17: 1,
    cardType18: 1,
    cardType19: 1,
    cardType20: 1,
    cardType21: 1,
    cardType22: 1,
    cardType23: 1,
    cardType24: 1,
    cardType25: 1,
    cardType26: 1,
    cardType27: 1,
    cardType28: 1,
    cardType29: 1,
    cardType30: 1,
    cardType31: 1,
    cardType32: 1,
  };
};
const formList = [
  { label: "卡类1：", valueKey: "cardType1" },
  { label: "卡类2：", valueKey: "cardType2" },
  { label: "卡类3：", valueKey: "cardType3" },
  { label: "卡类4：", valueKey: "cardType4" },
  { label: "卡类5：", valueKey: "cardType5" },
  { label: "卡类6：", valueKey: "cardType6" },
  { label: "卡类7：", valueKey: "cardType7" },
  { label: "卡类8：", valueKey: "cardType8" },
  { label: "卡类9：", valueKey: "cardType9" },
  { label: "卡类10：", valueKey: "cardType10" },
  { label: "卡类11：", valueKey: "cardType11" },
  { label: "卡类12：", valueKey: "cardType12" },
  { label: "卡类13：", valueKey: "cardType13" },
  { label: "卡类14：", valueKey: "cardType14" },
  { label: "卡类15：", valueKey: "cardType15" },
  { label: "卡类16：", valueKey: "cardType16" },
  { label: "卡类17：", valueKey: "cardType17" },
  { label: "卡类18：", valueKey: "cardType18" },
  { label: "卡类19：", valueKey: "cardType19" },
  { label: "卡类20：", valueKey: "cardType20" },
  { label: "卡类21：", valueKey: "cardType21" },
  { label: "卡类22：", valueKey: "cardType22" },
  { label: "卡类23：", valueKey: "cardType23" },
  { label: "卡类24：", valueKey: "cardType24" },
  { label: "卡类25：", valueKey: "cardType25" },
  { label: "卡类26：", valueKey: "cardType26" },
  { label: "卡类27：", valueKey: "cardType27" },
  { label: "卡类28：", valueKey: "cardType28" },
  { label: "卡类29：", valueKey: "cardType29" },
  { label: "卡类30：", valueKey: "cardType30" },
  { label: "卡类31：", valueKey: "cardType31" },
  { label: "卡类32：", valueKey: "cardType32" },
]
export default {
  components: {
    ElInputNumber,
    ElForm,
    ElFormItem,
    ElButton,
  },
  setup() {
    const store = useStore();
    const state = reactive({
      loading: false,
      form: {
        paramContent: defaultParamsFnc(),
        id: "",
        paramId: "",
        paramType: "CARDMODE",
      },
    });
    const saveClick = async () => {
      state.loading = true;
      let storeData = store.state.deviceParameters[store.state.app.activeTab]
      state.form.paramId = storeData.selectRow.id;
      let fn = state.form.id ? storeData.TabModule.detailsEditFnc : storeData.TabModule.detailsAddFnc

      let params = { ...state.form }
      console.log(params.paramContent);
      params.paramContent = JSON.stringify(params.paramContent)
      let { message, code } = await fn(params);
      if (code === 0) {
        setTimeout(() => {
          ElMessage.success(message);
          store.dispatch("deviceParameters/getParams", store.state.deviceParameters[store.state.app.activeTab].selectRow.id);
          state.loading = false;
        }, 1500);
      }
    };

    const beforeClose = () => {
      store.commit("deviceParameters/updateState", {
        key: store.state.app.activeTab,
        childKey: "isSetParams",
        payload: false,
      });
    };
    onMounted(() => {
      let val = store.state.deviceParameters[store.state.app.activeTab].detailsParams
      let arr = val.filter(item => item.paramType == 'CARDMODE')
      if (arr && arr.length) {
        state.form = { ...arr[0] }
        state.form.paramContent = JSON.parse(state.form.paramContent)
      } else {
        //设置默认选项
        state.form.id = "";
        state.form.paramContent = defaultParamsFnc();
      }
    });
    return {
      formList,
      state,
      saveClick,
      beforeClose,
    };
  },
};
</script>

<style lang="scss" scoped>
.setBasicParam {
  border: 1px solid #dcdfe6;
  border-radius: 5px;
  margin-bottom: 10px;
}
:deep(.el-form-item__content) {
  width: 200px;
  .el-input-number {
    width: 100%;
  }
  .el-select {
    width: 100% !important;
  }
}
</style>
