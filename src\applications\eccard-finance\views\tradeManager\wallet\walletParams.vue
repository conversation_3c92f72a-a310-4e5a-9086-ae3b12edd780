<template>
  <div class="padding-box transaction-info-report wallet-params">
    <el-form
      size="mini"
      ref="form"
      inline
      label-width="150px"
      :model="state.form"
      :rules="isEditButton && rules"
    >
      <el-form-item
        v-for="(item, index) in state.formItemList"
        :key="index"
        :label="item.label"
        :prop="item.filed"
      >
        <el-select
          :disabled="!isEditButton"
          v-if="item.isSelect"
          v-model="state.form[item.filed]"
        >
          <el-option
            v-for="(i, v) in state.selectList"
            :key="v"
            :label="i.label"
            :value="i.value"
          >
          </el-option>
        </el-select>

        <el-input
          :disabled="!isEditButton"
          v-else
          v-model="state.form[item.filed]"
          :min="0"
          oninput="if(value<0)value=0"
          type="number"
        >
          <template v-if="item.IsAfterPrefix" #suffix> &nbsp;% </template>
          <template v-else #prefix> &nbsp;¥ </template>
        </el-input>
      </el-form-item>
    </el-form>
    <div><span style="color: #f00">•</span> 金额输入0表示不限制</div>
    <div style="text-align: center; width: 100%" v-if="isEditButton">
      <el-button size="mini" @click="cancel">取消</el-button>
      <el-button type="primary" size="mini" @click="save">保存</el-button>
    </div>
  </div>
</template>
<script>
import {
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElButton,
  ElMessage,
} from "element-plus";
import { computed, reactive, ref, watch } from "vue";
import { useStore } from "vuex";
import { updateWalletParamConfig } from "@/applications/eccard-finance/api";
const rules = {
  dailyRechargeLimit: [
    {
      required: true,
      message: "请输入每日充值金额上限",
      trigger: "blur",
    },
  ],
  dailyConsumeLimit: [
    {
      required: true,
      message: "请输入每日消费金额上限",
      trigger: "blur",
    },
  ],
  tradeNotifyAmount: [
    {
      required: true,
      message: "请输入交易通知金额",
      trigger: "blur",
    },
  ],
  allowOverpayment: [
    {
      required: true,
      message: "请选择是否允许超额支付",
      trigger: "change",
    },
  ],
  balanceUpperLimit: [
    {
      required: true,
      message: "请输入余额上限",
      trigger: "blur",
    },
  ],
  warnAmount: [
    {
      required: true,
      message: "请输入预警额度",
      trigger: "blur",
    },
  ],
  allowRefund: [
    {
      required: true,
      message: "请选择是否允许退款",
      trigger: "change",
    },
  ],
  refundRatio: [
    {
      required: true,
      message: "请输入退款比例",
      trigger: "blur",
    },
  ],
  allowTransferIn: [
    {
      required: true,
      message: "请选择是否允许转入",
      trigger: "change",
    },
  ],
  transferMinAmount: [
    {
      required: true,
      message: "请输入单次转入最低金额",
      trigger: "blur",
    },
  ],
  singleTradeUpperLimit: [
    {
      required: true,
      message: "请输入单笔交易上限",
      trigger: "blur",
    },
  ],
  allowPersonRecharge: [
    {
      required: true,
      message: "请选择是否允许个人充值",
      trigger: "change",
    },
  ],
  allowClearing: [
    {
      required: true,
      message: "请选择是否允许清零",
      trigger: "change",
    },
  ],
};

export default {
  components: {
    ElForm,
    ElFormItem,
    ElInput,
    ElSelect,
    ElOption,
    ElButton,
  },

  setup() {
    const store = useStore();
    const form = ref(null);
    const state = reactive({
      formItemList: [
        {
          label: "每日充值金额上限：",
          filed: "dailyRechargeLimit",
          isShow: true,
        },
        {
          label: "每日消费金额上限：",
          filed: "dailyConsumeLimit",
          isShow: true,
        },
        {
          label: "交易通知金额：",
          filed: "tradeNotifyAmount",
          isShow: true,
        },
        {
          label: "是否允许超额支付：",
          filed: "allowOverpayment",
          isShow: true,
          isSelect: true,
        },
        {
          label: "余额上限：",
          filed: "balanceUpperLimit",
          isShow: true,
        },
        {
          label: "预警额度：",
          filed: "warnAmount",
          isShow: true,
        },
        {
          label: "是否允许退款：",
          filed: "allowRefund",
          isShow: true,
          isSelect: true,
        },
        {
          label: "退款比例：",
          filed: "refundRatio",
          isShow: true,
          IsAfterPrefix: true,
        },
        {
          label: "是否允许转入：",
          filed: "allowTransferIn",
          isShow: true,
          isSelect: true,
        },
        {
          label: "单次转入最低金额：",
          filed: "transferMinAmount",
          isShow: true,
        },
        {
          label: "单笔交易上限：",
          filed: "singleTradeUpperLimit",
          isShow: true,
        },
        {
          label: "是否允许个人充值：",
          filed: "allowPersonRecharge",
          isShow: true,
          isSelect: true,
        },
        {
          label: "是否允许清零：",
          filed: "allowClearing",
          isShow: true,
          isSelect: true,
        },
      ],
      selectList: [
        { label: "是", value: true },
        { label: "否", value: false },
      ],
      form: {},
    });

    const isEditButton = computed(() => {
      return store.state.walletManagerData.isEditButton;
    });

    watch(
      () => state.form.allowTransferIn,
      (val) => {
        if (!val) {
          state.formItemList = [
            {
              label: "每日充值金额上限：",
              filed: "dailyRechargeLimit",
              isShow: true,
            },
            {
              label: "每日消费金额上限：",
              filed: "dailyConsumeLimit",
              isShow: true,
            },
            {
              label: "交易通知金额：",
              filed: "tradeNotifyAmount",
              isShow: true,
            },
            {
              label: "是否允许超额支付：",
              filed: "allowOverpayment",
              isShow: true,
              isSelect: true,
            },
            {
              label: "余额上限：",
              filed: "balanceUpperLimit",
              isShow: true,
            },
            {
              label: "预警额度：",
              filed: "warnAmount",
              isShow: true,
            },
            {
              label: "是否允许退款：",
              filed: "allowRefund",
              isShow: true,
              isSelect: true,
            },
            {
              label: "退款比例：",
              filed: "refundRatio",
              isShow: true,
              IsAfterPrefix: true,
            },
            {
              label: "是否允许转入：",
              filed: "allowTransferIn",
              isShow: true,
              isSelect: true,
            },
            {
              label: "是否允许个人充值：",
              filed: "allowPersonRecharge",
              isShow: true,
              isSelect: true,
            },
            {
              label: "是否允许清零：",
              filed: "allowClearing",
              isShow: true,
              isSelect: true,
            },
          ];
        } else {
          state.formItemList = [
            {
              label: "每日充值金额上限：",
              filed: "dailyRechargeLimit",
              isShow: true,
            },
            {
              label: "每日消费金额上限：",
              filed: "dailyConsumeLimit",
              isShow: true,
            },
            {
              label: "交易通知金额：",
              filed: "tradeNotifyAmount",
              isShow: true,
            },
            {
              label: "是否允许超额支付：",
              filed: "allowOverpayment",
              isShow: true,
              isSelect: true,
            },
            {
              label: "余额上限：",
              filed: "balanceUpperLimit",
              isShow: true,
            },
            {
              label: "预警额度：",
              filed: "warnAmount",
              isShow: true,
            },
            {
              label: "是否允许退款：",
              filed: "allowRefund",
              isShow: true,
              isSelect: true,
            },
            {
              label: "退款比例：",
              filed: "refundRatio",
              isShow: true,
              IsAfterPrefix: true,
            },
            {
              label: "是否允许转入：",
              filed: "allowTransferIn",
              isShow: true,
              isSelect: true,
            },
            {
              label: "单次转入最低金额：",
              filed: "transferMinAmount",
              isShow: true,
            },
            {
              label: "单笔交易上限：",
              filed: "singleTradeUpperLimit",
              isShow: true,
            },
            {
              label: "是否允许个人充值：",
              filed: "allowPersonRecharge",
              isShow: true,
              isSelect: true,
            },
            {
              label: "是否允许清零：",
              filed: "allowClearing",
              isShow: true,
              isSelect: true,
            },
          ];
        }
      }
    );

    watch(
      () => store.state.walletManagerData.selectRow,
      (val) => {
        if (val) {
          store.dispatch("walletManagerData/getWalletParam");
        }
      }
    );
    watch(
      () => store.state.walletManagerData.walletParam,
      (val) => {
        state.form = val;
      }
    );

    const cancel = () => {
      store.commit("walletManagerData/updateState", {
        key: "isEditButton",
        payload: false,
      });
    };

    const save = () => {
      form.value.validate(async (valid) => {
        if (valid) {
          let data = { ...state.form };
          for (let key in data) {
            if (typeof data[key] !== "boolean") {
              data[key] = Number(data[key]);
            }
          }
          let params = {
            walletId: store.state.walletManagerData.selectRow.walletId,
            walletParam: data,
          };
          let { message, code } = await updateWalletParamConfig(params);
          if (code === 0) {
            ElMessage.success(message);
            store.commit("walletManagerData/updateState", {
              key: "isEditButton",
              payload: false,
            });
          }
        } else {
          return false;
        }
      });
    };

    return {
      rules,
      form,
      state,
      isEditButton,
      cancel,
      save,
    };
  },
};
</script>
<style lang="scss" scoped>
.wallet-params {
  .el-form {
  }
  :deep(.el-form-item__content) {
    .el-input__inner {
      width: 200px;
    }
  }
}
</style>