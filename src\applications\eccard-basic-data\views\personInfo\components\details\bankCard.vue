<template>
  <el-form ref="formRef" label-width="120px" style="flex: 1" size="small">
    <el-row>
      <el-col :span="6">
        <el-form-item label="银行卡号：">
          <el-input v-model="state.form.bankNo" :readonly="!state.isEdit" :placeholder="state.isEdit?'请输入':''"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="开户姓名：">
          <el-input v-model="state.form.openAccountName" :readonly="!state.isEdit" :placeholder="state.isEdit?'请输入':''"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="开户银行：">
          <el-input v-model="state.form.openAccountBank" :readonly="!state.isEdit" :placeholder="state.isEdit?'请输入':''"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="开户网点：">
          <el-input v-model="state.form.openAccountNetwork" :readonly="!state.isEdit" :placeholder="state.isEdit?'请输入':''"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="18">
        <el-form-item label="网点地址：" class="url-input">
          <el-select v-model="state.form.networkProvince" :disabled="!state.isEdit" @change="provinceChange">
            <el-option v-for="(item,index) in state.provinceList" :key="index" :label="item.name" :value="item.code"></el-option>
          </el-select>
          <el-select v-model="state.form.networkCity" :disabled="!state.isEdit" @change="cityChange">
            <el-option v-for="(item,index) in state.cityList" :key="index" :label="item.name" :value="item.code"></el-option>
          </el-select>
          <el-select v-model="state.form.networkArea" :disabled="!state.isEdit">
            <el-option v-for="(item,index) in state.areaList" :key="index" :label="item.name" :value="item.code"></el-option>
          </el-select>
          <el-input v-model="state.form.networkAddress" :readonly="!state.isEdit" :placeholder="state.isEdit?'请输入':''"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item label="附件：">
          <kade-single-image-upload v-if="state.isEdit" v-model="state.form.annexFront" :action="upload" icon="el-icon-plus" />
          <el-image border v-else style="width: 100px; height: 100px" :src="state.form.annexFront" :preview-src-list="[state.form.annexFront]" :initial-index="0" fit="cover"></el-image>
        </el-form-item>
      </el-col>
      <el-col :span="24" style="text-align:center">
        <el-button v-if="state.isEdit" size="mini" @click="cancel()">取消</el-button>
        <el-button v-if="state.isEdit" type="primary" size="mini" @click="save()">保存</el-button>
        <el-button v-else type="primary" size="mini" @click="edit()">编辑</el-button>
      </el-col>
    </el-row>
  </el-form>
</template>
<script>
import {
  ElRow,
  ElCol,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElImage,
  ElButton,
  ElMessage,
} from "element-plus";
import { computed, onMounted, reactive } from "vue";
import { useStore } from "vuex";
import { uploadApplyLogo } from "@/applications/unified_portal/api";
import {
  banksDetail,
  getAddressList,
  addBanks,
  editBanks,
} from "@/applications/eccard-basic-data/api";
import SingleImageUpload from "@/components/singleImageUpload";
export default {
  components: {
    ElRow,
    ElCol,
    ElForm,
    ElFormItem,
    ElInput,
    ElSelect,
    ElOption,
    ElImage,
    ElButton,
    "kade-single-image-upload": SingleImageUpload,
  },
  setup() {
    const store = useStore();
    const state = reactive({
      isEdit: false,
      form: {},
      beforeEditForm: {},
    });
    const dataDetails = computed(() => {
      return store.state.userInfo.rowData;
    });

    const getDetailData = async () => {
      let { data } = await banksDetail(store.state.userInfo.rowData.id);
      console.log(data);
      if (data) {
        state.form = { ...data };
        getProvinceList();
        data.networkCity && getCityList(data.networkProvince);
        data.networkArea && getAreaList(data.networkCity);
      } else {
        state.form = {};
      }
    };
    const getProvinceList = async () => {
      let { data } = await getAddressList({ level: 1 });
      state.provinceList = data;
    };
    const getCityList = async (val) => {
      let { data } = await getAddressList({ parentCode: val, level: 2 });
      state.cityList = data;
    };
    const getAreaList = async (val) => {
      let { data } = await getAddressList({ parentCode: val, level: 3 });
      state.areaList = data;
    };

    const provinceChange = (val) => {
      state.form.networkCity = "";
      state.form.networkArea = "";
      getCityList(val);
    };
    const cityChange = (val) => {
      state.form.networkArea = "";
      getAreaList(val);
    };
    const edit = () => {
      state.isEdit = true;
      state.beforeEditForm = { ...state.form };
    };
    const save = async () => {
      let fn = state.form.id ? editBanks : addBanks;
      state.form.userId=store.state.userInfo.rowData.id
      let { code, message } = await fn(state.form);
      if (code === 0) {
        ElMessage.success(message);
        state.isEdit = false;
        state.beforeEditForm = { ...state.form };
      }
    };
    const cancel = () => {
      state.isEdit = false;
      state.form = { ...state.beforeEditForm };
    };
    onMounted(() => {
      getDetailData();
      getProvinceList();
    });
    return {
      upload: uploadApplyLogo,
      state,
      appImageSize: THEMEVARS.appImageSize,
      dataDetails,
      provinceChange,
      cityChange,
      edit,
      save,
      cancel,
    };
  },
};
</script>
<style lang="scss" scoped>
.flex-box {
  margin-top: 20px;
  display: flex;
  .upload-photo {
    width: 160px;
    margin-right: 20px;
    .img {
      width: 100%;
      background: #f00;
    }
  }
}

.url-input {
  :deep(.el-form-item__content) {
    display: flex;

    .el-select {
      margin-right: 5px;
    }
    .el-input {
      width: 100%;
      .el-input__inner {
        width: 100%;
      }
    }
  }
}
</style>
