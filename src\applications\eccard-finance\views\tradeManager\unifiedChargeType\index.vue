<template>
  <kade-route-card style="height: auto"
    ><kade-table-wrap title="统一收费类型">
      <template #extra>
        <el-button icon="el-icon-plus" size="small" class="btn-green" @click="edit('add')"
          >新增</el-button
        >
      </template>
      <el-divider></el-divider>
      <el-form style="margin-top: 20px" label-width="100px" inline size="mini">
        <el-form-item label="类型名称：">
          <el-input v-model="state.form.uctName" placeholder="请输入"></el-input>
        </el-form-item>
        <el-button @click="reset()" size="mini">重置</el-button>
        <el-button @click="getList()" size="mini" type="primary">搜索</el-button>
      </el-form>
      <el-divider></el-divider>
      <div class="padding-box transaction-info-report">
        <el-table :data="state.dataList" stripe border>
          <el-table-column label="类型名称" prop="uctName" align="center"></el-table-column>
          <el-table-column label="排序" prop="uctSort" align="center"></el-table-column>
          <el-table-column label="是否启用" prop="uctStatus" align="center">
            <template #default="scope">
              <el-switch disabled :model-value="scope.row.uctStatus" active-value="ENABLE_TRUE" inactive-value="ENABLE_FALSE" />
            </template>
          </el-table-column>
          <el-table-column label="创建人" prop="createUserName" align="center"></el-table-column>
          <el-table-column label="创建时间" prop="createTime" align="center" width="180"></el-table-column>
          <el-table-column label="操作" align="center" width="180">
            <template #default="scope">
              <el-button style="padding: 0" type="text" @click="edit('detail', scope.row)" size="mini" >查看详情</el-button>
              <el-button style="padding: 0" type="text" @click="edit('edit', scope.row)" size="mini">编辑</el-button>
              <el-button style="padding: 0" type="text" @click="del(scope.row)" size="mini" >删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination-box">
          <el-pagination
            background
            :current-page="state.form.currentPage"
            :page-size="state.form.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :page-sizes="[6, 10, 20, 50, 100]"
            :total="state.total"
            @current-change="handlePageChange"
            @size-change="handleSizeChange"
          >
          </el-pagination>
        </div>
      </div>
    </kade-table-wrap>
    <kade-charge-edit :title="state.title" :data="state.data" :dialogVisible="state.dialogVisible" @close="close" @edit="dialogEdit" />
  </kade-route-card>
</template>
<script>
import {
  ElButton,
  ElDivider,
  ElForm,
  ElFormItem,
  ElInput,
  ElTable,
  ElTableColumn,
  ElSwitch,
  ElPagination,
  ElMessageBox,
  ElMessage
} from "element-plus";
import { reactive,onMounted } from "vue";
import {useDict} from "@/hooks/useDict"
import { getUnifiedChargeTypeList,deleteUnifiedChargeType } from "@/applications/eccard-finance/api";
import chargeEdit from "./components/edit.vue"
export default {
  components: {
    ElButton,
    ElDivider,
    ElForm,
    ElFormItem,
    ElInput,
    ElTable,
    ElTableColumn,
    ElSwitch,
    ElPagination,
    "kade-charge-edit":chargeEdit
  },
  setup() {
    const enable=useDict("SYS_ENABLE")
    const state = reactive({
      dialogVisible:false,
      title:"",
      data:"",
      form: {
        currentPage: 1,
        pageSize: 10,
      },
      total: 0,
      dataList:[]
    });

    const getList=async ()=>{
      let {data:{list,total},code}=await getUnifiedChargeTypeList(state.form)
      if(code===0){
        state.dataList=list
        state.total=total
      }
    }

    const edit=(type,row)=>{
      if(type==="detail"){
        state.title="统一收费类型详情"
        state.data=row
      }else if(type==="edit"){
        state.title="编辑统一收费类型"
        state.data=row
      }else if(type==="add"){
        state.title="新增统一收费类型"
      }
      state.dialogVisible=true
    }

    const del=(row)=>{
      ElMessageBox.confirm('确认删除？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(async ()=>{
        let {message,code}=await deleteUnifiedChargeType({uctId:row.id})
        if(code===0){
          ElMessage.success(message)
          getList()
        }
      }).catch(()=>{
        ElMessage.info("已取消！")
      })
    }

    const handlePageChange = (val) => {
      state.form.currentPage = val;
      getList()
    };
    const handleSizeChange = (val) => {
      state.form.currentPage = 1;
      state.form.pageSize = val;
      getList()
    };

    const reset=()=>{
      state.form= {
        currentPage: 1,
        pageSize: 10,
      }
    }

    const dialogEdit=()=>{
      state.title="编辑统一收费类型"
    }

    const close=(val)=>{
      if(val){
        getList()
      }
      state.dialogVisible=false
      state.data=""
    }

    onMounted(()=>{
      getList()
    })
    return {
      enable,
      state,
      getList,
      edit,
      del,
      handlePageChange,
      handleSizeChange,
      reset,
      dialogEdit,
      close
    };
  },
};
</script>
<style lang="scss" scoped>
.el-divider--horizontal {
  margin: 0;
}
.el-table {
  border-left: 1px solid #ebeef5;
  border-right: 1px solid #ebeef5;
}
.pagination-box{
  margin-top: 10px;
  text-align: right;
}
 :deep(.el-dialog__header){
    border-bottom: 1px solid #eeeeee;
  }
  :deep(.el-dialog__footer){
    text-align: center;
  }
</style>