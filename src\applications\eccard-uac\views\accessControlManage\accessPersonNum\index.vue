<template>
  <kade-route-card>
    <kade-table-filter @search="handleSearch" @reset="handleReset">
      <el-form inline label-width="90px" size="mini">
        <el-form-item label="通行区域">
          <kade-area-select-tree style="width: 100%" :value="state.form.areaPath" valueKey="areaPath" :multiple="false"
            @valueChange="(val) => (state.form.areaPath = val.areaPath)" />
        </el-form-item>
        <el-form-item label="设备类型">
          <el-select v-model="state.form.deviceType" clearable>
            <el-option v-for="(item, index) in state.deviceTypeList" :key="index" :label="item.cfgValue"
              :value="item.cfgKey"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="设备名称">
          <el-input placeholder="请输入设备名称" v-model="state.form.deviceName"></el-input>
        </el-form-item>
        <el-form-item label="起始时间">
          <el-date-picker v-model="state.form.startDate" type="datetime" placeholder="请选择起始时间"
            :default-time="new Date(2000, 1, 1, 23, 59, 59)" />
        </el-form-item>
        <el-form-item label="截止时间">
          <el-date-picker v-model="state.form.endDate" type="datetime" placeholder="请选择截止日期"
            :default-time="new Date(2000, 1, 1, 23, 59, 59)" />
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="通行人次列表" v-loading="state.loading">
      <template #extra>
        <el-button @click="handleExport()" class="btn-green" icon="el-icon-daochu" size="mini">导出</el-button>
      </template>
      <el-table :data="state.dataList" @selection-change="handleSelectChange" border height="55vh">
        <el-table-column show-overflow-tooltip prop="areaName" label="通行区域" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="deviceTypeName" label="设备类型" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="deviceName" label="设备名称" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="passCount" label="通行人次" align="center"></el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination v-model:currentPage="state.form.currentPage" v-model:page-size="state.form.pageSize"
          :page-sizes="[10, 20, 30, 40]" :small="small" :disabled="disabled" background
          layout="total, sizes, prev, pager, next, jumper" :total="state.total" @size-change="handleSizeChange"
          @current-change="handleCurrentChange" />
      </div>
    </kade-table-wrap>
  </kade-route-card>
</template>

<script>
import { reactive } from '@vue/reactivity';
import { ElForm, ElFormItem, ElButton, ElTable, ElTableColumn, ElDatePicker, ElInput, ElSelect, ElOption, ElPagination } from "element-plus"
import { onMounted } from '@vue/runtime-core';
import { downloadXlsx } from "@/utils"
import { timeStr } from "@/utils/date.js"
import { iotCfg } from "@/applications/eccard-iot/api";
import { getStaticPage, exportStatic } from "@/applications/eccard-uac/api";
import areaSelectTree from "@/components/tree/areaSelectTree.vue";
export default {
  components: {
    ElForm,
    ElFormItem,
    ElButton,
    ElInput,
    ElSelect,
    ElOption,
    ElDatePicker,
    ElTable,
    ElTableColumn,
    ElPagination,
    "kade-area-select-tree": areaSelectTree,
  },
  setup() {
    const state = reactive({
      loading: false,
      isDetails: false,
      deviceTypeList: [],
      form: {
        pageSize: 10,
        currentPage: 1,
      },
      dataList: [],
      total: 0,
      rowData: {}
    });
    const getDeviceTypeList = async () => {
      let { data } = await iotCfg({ cfgType: 'ACCESS_DEVICE_TYPE' });
      state.deviceTypeList = data
    }
    const getList = async () => {
      state.loading = true
       let params = { ...state.form }
      if (params.startDate) {
        params.startDate = timeStr(params.startDate)
      }
      if (params.endDate) {
        params.endDate = timeStr(params.endDate)
      }
      try {
        let { data: { list, total } } = await getStaticPage(params)
        state.dataList = list
        state.total = total
        state.loading = false
      }
      catch {
        state.loading = false
      }
    }
    const handleSearch = () => {
      getList()
    }
    const handleReset=()=>{
      state.form={
        pageSize: 10,
        currentPage: 1,
      }
    }
    const handleExport = async () => {
      let res = await exportStatic(state.form)
      downloadXlsx(res, '通行人次列表.xlsx')
    }
    const handleSizeChange = (val) => {
      state.form.currentPage = 1
      state.form.pageSize = val
      getList()
    };
    const handleCurrentChange = (val) => {
      state.form.currentPage = val
      getList()
    }
    onMounted(() => {
      getList();
      getDeviceTypeList()
    });
    return {
      state,
      getList,
      handleSearch,
      handleReset,
      handleExport,
      handleSizeChange,
      handleCurrentChange,
    }
  }
}
</script>

<style lang="scss" scoped>
.green :hover {
  text-decoration: underline;
}

:deep(.el-form) {
  .el-input {
    width: 198px
  }
}
</style>