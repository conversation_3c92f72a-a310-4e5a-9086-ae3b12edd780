<template>
  <div class="person-msg">
    <div class="now-person-num">
      <div class="title">入住人数</div>
      <div class="num">1490</div>
    </div>
    <div class="person-total">
      <div class="title">归寝率</div>
      <div class="total-box">
        <div class="total-left">
          <kade-label-box class="label-box">
            <div class="label">未归人数</div>
            <div class="value">1292</div>
          </kade-label-box>
          <kade-label-box class="label-box">
            <div class="label">已归人数</div>
            <div class="value">1292</div>
          </kade-label-box>
        </div>
        <kade-progress style="margin:0 50px" />
        <div class="total-right">
          <kade-label-box class="label-box">
            <div class="label">晚归人数</div>
            <div class="value">1292</div>
          </kade-label-box>
          <kade-label-box class="label-box">
            <div class="label">请假人数</div>
            <div class="value">1292</div>
          </kade-label-box>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import progress from "./progress.vue"
import labelBox from "../../components/labelBox.vue"

export default {
  components: {
    "kade-label-box": labelBox,
    "kade-progress": progress,

  }
}
</script>
<style lang="scss" scoped>
.person-msg {
  .now-person-num {
    text-align: center;

    .title {
      font-weight: 400;
      font-size: 20px;
      color: #0EE4F9;
      margin-bottom: 20px;
    }

    .num {
      font-weight: 700;
      font-size: 58px;
      color: #FFFFFF;
    }
  }

  .person-total {
    .title {
    text-align: center;

      font-weight: 400;
      font-size: 20px;
      color: #0EE4F9;
      margin:20px 0 30px;
    }

    .total-box {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .total-left,.total-right {
        height: 100%;
        flex: 1;

        :first-child {
          margin-bottom: 40px;
        }
      }

      .label-box {
        box-sizing: border-box;
        width: 100%;
        padding: 10px;

        .label {
          font-weight: 400;
          font-size: 14px;
          color: #0EE4F9;
          margin-bottom: 0;
        }

        .value {
          font-weight: 700;
          font-size: 30px;
          color: #FE7007;
          text-align: center;
        }
      }
    }
  }
}
</style>