<template>
  <el-dialog :model-value="isShow" :title="TabModule().title+'详情'" width="1200px" :before-close="beforeClose" :close-on-click-modal="false">
    <kade-tab-wrap :tabs="tabs" style="margin: 20px 0" v-model="state.tab">
      <template #jbxx>
        <kade-basic-params :data="data" :TabModule="TabModule" />
      </template>
      <template #ghjl>
        <kade-record :data="data" />
      </template>
    </kade-tab-wrap>
    <div style="width:100%;height:1px"></div>
  </el-dialog>
</template>
<script>
import { ElDialog } from "element-plus"
import basicParams from "./basicParams"

import record from "./record"
import { reactive } from '@vue/reactivity'
const tabs = [
  {
    name: "jbxx",
    label: "基本信息",
  },
  {
    name: "ghjl",
    label: "更换记录",
  },
];
export default {
  components: {
    ElDialog,
    "kade-basic-params": basicParams,
    "kade-record": record,
  },
  props: {
    isShow: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default: null
    },
    TabModule: {
      type: Function,
      default: () => { }
    }
  },
  setup(props, context) {
    const state = reactive({
      tab: "jbxx"
    })
    const beforeClose = () => {
      context.emit("close", false)
      state.tab = "jbxx"
    }
    return {
      tabs,
      state,
      beforeClose
    }
  }
}
</script>
<style lang="scss" scoped>
</style>