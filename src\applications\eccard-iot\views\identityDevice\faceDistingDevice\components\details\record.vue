<template>
  <el-table style="width: 100%" v-loading="state.loading" :data="state.dataList" border stripe>
    <el-table-column v-for="(item,index) in column" :key="index" :label="item.label" :prop="item.prop" align="center">
      <template v-if="item.render" #default="scope">
        {{item.render(scope.row[item.prop])}}
      </template>
    </el-table-column>
    <el-table-column align="center" label="操作">
      <template #default="scope">
        <el-button size="mini" class="green" type="text" @click="edit(scope.row)">编辑</el-button>
        <el-button size="mini" class="green" type="text" @click="del(scope.row)">删除</el-button>
      </template>
    </el-table-column>
  </el-table>
</template>
<script>
import { ElTable, ElTableColumn, ElButton, ElMessageBox, ElMessage } from "element-plus"
import { useStore } from "vuex"
import { deviceReplaceList, deviceReplaceDel } from "@/applications/eccard-iot/api";
import { onMounted, reactive } from '@vue/runtime-core';
import { timeStr } from "@/utils/date.js"
const column = [
  { label: "记录时间", prop: "replaceDate", render: (val) => val && timeStr(val) },
  { label: "更换原因", prop: "replaceReason" },
  { label: "更换人员", prop: "operatorName" },
  { label: "备注", prop: "replaceRemarks" },
]
export default {
  components: {
    ElTable,
    ElTableColumn,
    ElButton
  },
  props: {
    data: {
      type: Object,
      default: null
    }
  },
  setup(props) {
    const store = useStore()
    const state = reactive({
      loading: false,
      dataList: []
    })
    const getList = async () => {
      let params = {
        pageNum: 1,
        pageSize: 10000,
        deviceType: "FACE_DEVICE",
        deviceId: props.data.id
      }
      state.loading = true
      try {
        let { data: { list } } = await deviceReplaceList(params)
        state.dataList = list
        state.loading = false
      }
      catch {
        state.loading = false
      }
    }
    
    const edit = (row) => {
      store.commit("identityDevice/faceDistingDevice/updateState", {
        key: "isRecord",
        payload: {
          type: "edit",
          data: row,
          isShow: true
        },
      });
    }
    const del = row => {
      ElMessageBox.confirm("确认删除?", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        let { code, message } = await deviceReplaceDel(row.id);
        if (code === 0) {
          ElMessage.success(message);
          getList();
        }
      });
    }
    onMounted(() => {
      getList()
    })
    return {
      column,
      state,
      edit,
      del
    }
  }
}
</script>