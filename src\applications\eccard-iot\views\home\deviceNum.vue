<template>
  <kade-route-card>
    <template #header>
      <span class="header-title">区域设备数量统计</span>
    </template>
    <div id="deviceNumCharts" class="chartline"></div>
  </kade-route-card>
</template>
<script>
import * as echarts from "echarts";
import { onMounted } from "vue";
export default {
  setup() {
    const echartInit = () => {
      var chartDom = document.getElementById('deviceNumCharts');
      var myChart = echarts.init(chartDom);
      var option;

      option = {
        xAxis: {
          type: 'category',
          data: ['区域一', '区域二', '区域三', '区域四', '区域五', '区域六', '区域七', '区域八', '区域九', '区域十', '区域十一', '区域十二',]
        },
        grid: {
          top: 20,
          bottom: 20,
          left: 40,
          right: 0
        },
        yAxis: [
          {
            show: true,
            type: "value",
            splitLine: {
              show: true,
              lineStyle: {
                type: "solid",
              },
            },
            axisLabel: {
              margin: 20,

            },
            minInterval: 1
          }
        ],
        series: [
          {
            data: [120, 200, 150, 80, 103, 466, 432, 654, 113, 131, 345, 781],
            type: 'bar',
            barWidth: 12,
            label: {
              show: false,
              distance: 0,
              formatter: '{c}'
            },
            itemStyle: {
              color: "#00cc33d8",
              borderRadius: [6, 6, 0, 0]
            }
          }
        ],
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#6a7985',
              
            }
          }
        }
      };

      option && myChart.setOption(option);
    };
    onMounted(() => {
      echartInit();
    });
    return {
      echartInit,
    };
  }
}
</script>
<style scoped lang="scss">
.chartline {
  height: 260px;
  width: 100%;
}
</style>