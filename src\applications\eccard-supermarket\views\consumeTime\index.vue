<template>
  <div class="personnelInformation">
    <kade-route-card>
      <kade-table-wrap title="消费时段列表">
        <el-table style="width: 100%" :data="state.dataList" v-loading="state.loading"
          highlight-current-row border stripe @row-click="rowClick" @selection-change="selectionChange">
          <!-- <el-table-column type="selection" width="55" align="center" /> -->
          <el-table-column label="时段名称" prop="name" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column label="开始时间" prop="beginTime" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column label="结束时间" prop="endTime" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column label="操作" align="center" width="200" show-overflow-tooltip>
            <template #default="scope">
              <el-button size="mini" type="text" @click="handleEdit(scope.row)">编辑</el-button>
            </template>
          </el-table-column>
        </el-table>
      </kade-table-wrap>
    </kade-route-card>
    <kade-consume-time-deit v-model="state.isEdit" :rowData="state.rowData" @update:modelValue="close" />
  </div>
</template>
<script>
import {
  ElTable,
  ElTableColumn,
  ElButton,
} from "element-plus";
import { onMounted, reactive } from "vue";
import { mealPeriodList } from "@/applications/eccard-supermarket/api.js";
import edit from "./components/edit.vue"
export default {
  components: {
    "el-table": ElTable,
    "el-table-column": ElTableColumn,
    "el-button": ElButton,
    "kade-consume-time-deit": edit
  },
  setup() {
    const state = reactive({
      isEdit: false,
      loading: false,
      dataList: [],
      total: 0,
      rowData: {},
    });

    const getList = async () => {
      state.loading = true
      try {
        let { data } = await mealPeriodList()
        state.dataList = data
        state.loading = false
      }
      catch {
        state.loading = false
      }
    }

    const handleEdit = (row) => {
      state.rowData = row
      state.isEdit = true
    }
    const close = val => {
      if (val) {
        getList()
      }
      state.isEdit = false
    }
    onMounted(() => {
      getList()
    })

    return {
      state,
      handleEdit,
      close
    };
  },
};
</script>
<style lang="scss" scoped>

</style>
