<template>
  <kade-route-card style="height: auto">
    <kade-table-wrap title="待办事项设置">
      <template #extra>
        <el-button @click="state.isTodoType=true" icon="el-icon-plus" size="mini" type="success">待办类型</el-button>
        <el-button @click="state.isTodoProject=true" icon="el-icon-plus" size="mini" type="primary">待办项目</el-button>
      </template>
      <el-table 
        style="width: 100%" 
        row-key="customId" 
        :data="state.dataList" 
        ref="multipleTable" 
        v-loading="state.loading" 
        height="70vh" 
        lazy
        :load="load"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        border 
        stripe>
        <el-table-column label="待办类型" prop="typeName" show-overflow-tooltip></el-table-column>
        <el-table-column label="待办项目" prop="name" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column label="待办编码" prop="code" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column label="处理方式" prop="mode" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column label="启用状态" prop="status" align="center" show-overflow-tooltip>
          <template #default="scope">
            <el-switch disabled v-model="scope.row.status" active-value="TRUE" inactive-value="FALSE"></el-switch>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" prop="createTime" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column  align="center" label="操作" >
          <template #default="scope">
            <el-button size="mini" type="text" @click="edit(scope.row)">编辑</el-button>
            <el-button size="mini" type="text" @click="handleClick(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination
          background
          :current-page="state.form.pageNum"
          :page-size="state.form.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :page-sizes="[6, 10, 20, 50, 100]"
          :total="state.total"
          @current-change="handlePageChange"
          @size-change="handleSizeChange"
        >
        </el-pagination>
      </div>
    </kade-table-wrap>
    <kade-edit-todo-type :isShow="state.isTodoType"  :data="state.rowData" :type="state.type" @close="closeType"/>
    <kade-edit-todo-project :todoTypeList="state.todoTypeList" :data="state.rowData" :isShow="state.isTodoProject" :type="state.type" @close="closeProject" />
  </kade-route-card>
</template>
<script>
import { reactive,onMounted } from "vue";
import {timeStr} from "@/utils/date"
import {ElTable,ElTableColumn,ElPagination,ElButton,ElSwitch} from "element-plus"
import { getTodoTypeListPage,getTodoTypeList,getTodoProjectList } from "@/applications/eccard-basic-data/api";

import editTodoType from "./components/editTodoType.vue"
import editTodoProject from "./components/editTodoProject.vue"
const column=[
  {label:"状态",prop:"",width:"",render:()=>{
    return "确认"
  }},
  {label:"待办类型",prop:"",width:""},
  {label:"待办项目",prop:"",width:""},
  {label:"接收时间",prop:"",width:""},
]
export default {
  components: {
    ElTable,ElTableColumn,ElPagination,ElButton,ElSwitch,
    "kade-edit-todo-type":editTodoType,
    "kade-edit-todo-project":editTodoProject,
  },
  setup() {
    const state = reactive({
      loading: false,
      isTodoType:false,
      isTodoProject:false,
      type:"add",
      form:{
        pageNum:1,
        pageSize:10
      },
      defaultTime:[],
      dataList:[],
      total:0,
      todoTypeList:[],
      getProjectList:[],
      rowData:"",
    });

    //获取待办类型列表
    const queryTodoTypeList=async ()=>{
      let {data,code}=await getTodoTypeList()
      if(code===0){
        state.todoTypeList=data
      }
    }

    const getList=async ()=>{
      state.loading=true
      state.dataList=[]
      let {data:{list,total}}=await getTodoTypeListPage(state.form)
      state.dataList=list.map((item,index)=>{
          return {...item,customId:index+1,type:"type",hasChildren: true }
        })
      state.total=total
      state.loading=false
    }
    
    const edit=(row)=>{
      if(row.type=='type'){
        state.isTodoType=true
      }else{
        state.isTodoProject=true
      }
      state.rowData=row
    }

    const expandChange=(row,expandedRows )=>{
      console.log(row,expandedRows );
    }

    const changeDate=(val)=>{
      if (val && val.length) {
        state.form.startDate = timeStr(val[0]);
        state.form.endDate = timeStr(val[1]);
      } else {
        delete state.form.startDate;
        delete state.form.endDate;
      }
    }

    const load = async (tree ,treeNode, resolve) => {
      let {data}=await getTodoProjectList({typeId:tree.id})
      resolve(data.map((item,index)=>({...item,type:"project",customId:index*100000,hasChildren: false })))
    };
    const onlyKey=(val)=>{
      console.log(val.id + 'only');
      return val.id + 'only'
    }

    const handlePageChange=(val)=>{
      state.form.pageNum=val
      getList()
    }
    const handleSizeChange=(val)=>{
      state.form.pageSize=val
      getList()
    }

    const closeProject=(val)=>{
      if(val){
        getList()
      }
      state.isTodoProject=false
      state.rowData=""
    }
    const closeType=(val)=>{
      if(val){
        getList()
      }
      state.isTodoType=false
      state.rowData=""
    }
    onMounted(()=>{
      queryTodoTypeList()
      getList()
    })
    return {
      column,
      state,
      expandChange,
      changeDate,
      edit,
      load,
      onlyKey,
      handlePageChange,
      handleSizeChange,
      closeProject,
      closeType
    };
  },
};
</script>
<style lang="scss" scoped>
  :deep(.el-divider--horizontal) {
    margin: 0;
  }
  :deep(.el-dialog__header) {
    border-bottom: 1px solid #efefef;
  }

  :deep(.el-overlay) {
    position: absolute;
    background-color: rgba(0, 0, 0, 0.3);
  }

  :deep(.el-dialog__footer) {
    text-align: center;
  }
</style>