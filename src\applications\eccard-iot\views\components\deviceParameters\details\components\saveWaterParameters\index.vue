<template>
  <kade-tab-wrap :tabs="tabs" style="margin-top: 20px" v-model="state.tab">
    <template #jbcs>
      <kade-basic-params />
    </template>
    <template #jfcs>
      <kade-charging-params />
    </template>
    <template #klcs>
      <kade-card-params />
    </template>
    <template #gjcs>
      <kade-senior-params />
    </template>
    <template #wdcs>
      <kade-temperature-params />
    </template>
  </kade-tab-wrap>
</template>
<script>
import { reactive } from "@vue/reactivity";
import { useStore } from "vuex";
import { onMounted } from "@vue/runtime-core";
import basicParams from "./basicParams"
import chargingParams from "./chargingParams"
import cardParams from "./cardParams"
import seniorParams from "./seniorParams"
import temperatureParams from "./temperatureParams"
const tabs = [
  { name: "jbcs", label: "基本参数" },
  { name: "jfcs", label: "计费参数" },
  { name: "klcs", label: "卡类参数" },
  { name: "gjcs", label: "高级参数" },
  { name: "wdcs", label: "温度参数" },
];
export default {
  components: {
    "kade-basic-params": basicParams,
    "kade-charging-params": chargingParams,
    "kade-card-params": cardParams,
    "kade-senior-params": seniorParams,
    "kade-temperature-params": temperatureParams,
  },
  setup() {
    const store = useStore();
    const state = reactive({
      tab: "jbcs",
      form: {},
    });
    onMounted(async () => {
      await store.dispatch("deviceParameters/getParams", store.state.deviceParameters[store.state.app.activeTab].selectRow.id);
      state.tab = "jbcs"
    });
    return {
      tabs,
      state,
    };
  },
};
</script>
<style lang="scss" scoped>
</style>