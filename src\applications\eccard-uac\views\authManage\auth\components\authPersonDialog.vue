<template>
  <el-dialog :model-value="modelValue" title="设置用户权限" width="1700px" :before-close="handleClose"
    :close-on-click-modal="false">
    <div style="padding:20px 0 0">
      <kade-table-wrap title="用户信息" icon="none">
        <el-divider></el-divider>
        <div class="padding-box">
          <el-table :data="[rowData]" border>
            <el-table-column label="组织机构" prop="deptName" align="center"></el-table-column>
            <el-table-column label="身份类别" prop="roleName" align="center"></el-table-column>
            <el-table-column label="用户编号" prop="userCode" align="center"></el-table-column>
            <el-table-column label="用户名称" prop="userName" align="center"></el-table-column>
            <el-table-column label="性别" prop="userSex" align="center">
              <template #default="scope">
                {{ dictionaryFilter(scope.row.userSex) }}
              </template>
            </el-table-column>
            <el-table-column label="卡类" prop="cardTypeName" align="center"></el-table-column>
            <el-table-column label="卡片类别" prop="cardCategoryName" align="center">
              <template #default="scope">
                {{ dictionaryFilter(scope.row.cardCategoryName) }}
              </template>
            </el-table-column>
          </el-table>
        </div>
      </kade-table-wrap>
      <kade-table-wrap title="待选列表" icon="none">
        <el-divider style="margin-bottom:20px"></el-divider>
        <kade-select-table :isShow="modelValue" :value='[]' :reqFnc="getAuthManageList"
          :selectCondition="selectCondition" :column="column" :isCurrentSelect="true" :params="state.params"
          @change="personChange" />
      </kade-table-wrap>
      <kade-table-wrap title="其它选项" icon="none">
        <el-divider></el-divider>
        <el-form style="margin-top:20px" label-width="120px" size="mini" ref="formRef" :rules="rules"
          :model="state.form" inline>
          <el-form-item label="时段：" prop="periodNo">
            <el-select v-model="state.form.periodNo">
              <el-option v-for="(item, index) in state.periodNoList" :key="index" :label="item.periodNo"
                :value="item.periodNo"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="一次刷卡限制：" prop="oneCard">
            <el-radio-group v-model="state.form.oneCard">
              <el-radio :label="item.value" v-for="(item, index) in boolList" :key="index">{{ item.label }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="授权时间：" prop="beginTime">
            <el-date-picker v-model="state.form.beginTime" type="datetime" placeholder="请选择时间" />
          </el-form-item>
          <el-form-item label="过期时间：" prop="endTime">
            <el-date-picker v-model="state.form.endTime" type="datetime" placeholder="请选择时间" />
          </el-form-item>
        </el-form>
        <div class="red" style="margin-left:20px">说明：时段和一次刷卡限制仅支持门禁控制器和通道控制器设备</div>
      </kade-table-wrap>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose" size="mini">取消</el-button>
        <el-button type="primary" v-if="type != 'details'" @click="submit" size="mini">保存</el-button>
        <el-button type="primary" v-else @click="$emit('edit')" size="mini">编辑</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script>
import { ElDialog, ElButton, ElDivider, ElTable, ElTableColumn, ElForm, ElFormItem, ElSelect, ElOption, ElRadioGroup, ElRadio, ElDatePicker, ElMessage } from "element-plus"
import { reactive, onMounted, ref } from 'vue'
import { useDict } from "@/hooks/useDict.js";
import { timeStr } from "@/utils/date.js"
import {
  getDevicePeriod, getAuthManageList, authManageAdd, acsGroupingInfoList
} from "@/applications/eccard-uac/api";
import selectTable from "@/components/table/selectTable.vue";
const column = [
  { label: "设备名称", prop: "deviceName", isDict: true, width: "" },
  { label: "设备类型", prop: "deviceTypeName", isDict: false, width: "" },
  { label: "门禁区域", prop: "areaName", isDict: false, width: "" },
  { label: "门禁分组", prop: "groupName", isDict: false, width: "" },
];
const rules = {
  periodNo: [
    {
      required: true,
      message: "请选择时段",
      trigger: "change",
    },
  ],
  oneCard: [
    {
      required: true,
      message: "请选择一次刷卡限制",
      trigger: "change",
    },
  ],
  beginTime: [
    {
      required: true,
      message: "请选择授权时间",
      trigger: "change",
    },
  ],
  endTime: [
    {
      required: true,
      message: "请选择过期时间",
      trigger: "change",
    },
  ],
}
export default {
  components: {
    ElDialog, ElButton, ElDivider, ElTable, ElTableColumn, ElForm, ElFormItem, ElSelect, ElOption, ElRadioGroup, ElRadio, ElDatePicker,
    "kade-select-table": selectTable,
  },
  emits: ["update:modelValue"],
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    rowData: {
      type: Object,
      default: null
    }
  },
  setup(props, context) {
    const formRef = ref(null)
    const boolList = useDict("SYS_BOOL_INT")
    const state = reactive({
      form: {},
      params: {
        currentPageKey: "currentPage",
        pageSizeKey: "pageSize",
        resListKey: "list",
        resTotalKey: "total",
        value: {

        },
        tagNameKey: "deviceName",
        valueKey: "id",
      },
      selectData: {}
    })
    const selectCondition = [
      {
        label: "门禁分组", valueKey: "groupId", placeholder: "请选择", isSelect: true,
        select: {
          list: [],
          option: {
            label: "groupName",
            value: "id",
          },
        },
      },
      { label: "门禁区域", valueKey: "areaPath", dataKey: "areaPath", placeholder: "请选择", isSelect: false, isTree: "area" },
    ]
    const getPeriodNoList = () => {
      getDevicePeriod().then((res) => {
        console.log(res)
        state.periodNoList = res.data
      })
    }
    const getGroupList = async () => {
      let { data } = await acsGroupingInfoList({ groupType: "deviceGroup" })
      selectCondition[0].select.list = data
    }
    const personChange = (val) => {
      state.selectData = { ...val }
    }
    const submit = () => {
      if (!state.selectData.isSelectAll && !state.selectData.list) {
        return ElMessage.error("请选择设备！")
      }
      formRef.value.validate(async valid => {
        if (valid) {
          state.loading = true
          try {
            let params = {
              userInfoList: [{
                cardCategory: props.rowData.cardCategoryName,
                familyId: props.rowData.familyUserId,
                userId: props.rowData.id
              }],
              deviceInfoList: state.selectData.list,
              ...state.form,
              beginTime: timeStr({ ...state.form }.beginTime),
              endTime: timeStr({ ...state.form }.endTime),
            }
            console.log(params)
            let { message, code } = await authManageAdd(params)
            if (code === 0) {
              ElMessage.success(message)
              context.emit("update:modelValue", true)
            }
            state.loading = false
          }
          catch {
            state.loading = false
          }
        }
      })
    }
    const handleClose = () => {
      context.emit("update:modelValue", false)
    }
    onMounted(() => {
      getGroupList()
      getPeriodNoList()
    })
    return {
      getAuthManageList,
      rules,
      column,
      formRef,
      boolList,
      state,
      selectCondition,
      personChange,
      submit,
      handleClose
    }
  }
}
</script>
<style lang="scss" scoped>
:deep(.kade-table-wrap) {
  margin-bottom: 20px;
}

.padding-box {
  padding: 10px 10px 0
}

.el-divider--horizontal {
  margin: 0
}

:deep(.box) {
  padding: 10px 10px 0;

  .el-form {
    .el-select {
      width: 150px;
    }

    .el-input {
      width: 150px
    }
  }
}

.el-table {
  border-left: 1px solid #EBEEF5;
  border-right: 1px solid #EBEEF5;
}
</style>