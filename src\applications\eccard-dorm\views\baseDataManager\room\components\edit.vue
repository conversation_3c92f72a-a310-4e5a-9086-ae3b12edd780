<template>
  <el-dialog v-loading="state.loading" :model-value="modelValue" :title="title(editType)" width="800px" :before-close="handleClose">
    <el-form inline label-width="150px" size="mini" ref="formRef" :model="state.form" :rules="editType!=='details'&&rules">
      <el-form-item label="房间号：" prop="roomNo">
        <el-input v-if="editType=='details'" :model-value="state.form.roomNo" readonly></el-input>
        <el-input v-else v-model="state.form.roomNo" placeholder="请输入房间号" maxlength="20"></el-input>
      </el-form-item>
      <el-form-item label="房间名称：" prop="roomName">
        <el-input v-if="editType=='details'" :model-value="state.form.roomName" readonly></el-input>
        <el-input v-else v-model="state.form.roomName" placeholder="请输入房间名称" maxlength="20"></el-input>
      </el-form-item>
      <kade-linkage-select v-if="editType!=='details'" :isEdit="modelValue?true:false" :value="state.form" :data="linkageData" @change="linkageChange" />
      <el-form-item label="所属区域：" v-if="editType=='details'">
        <el-input :model-value="state.form.areaName" readonly></el-input>
      </el-form-item>
      <el-form-item label="所属楼栋：" v-if="editType=='details'">
        <el-input :model-value="state.form.buildName" readonly></el-input>
      </el-form-item>
      <el-form-item label="所属单元：" v-if="editType=='details'">
        <el-input :model-value="state.form.unitNum" readonly></el-input>
      </el-form-item>
      <el-form-item label="所属楼层：" v-if="editType=='details'">
        <el-input :model-value="state.form.floorNum" readonly></el-input>
      </el-form-item>
      <el-form-item label="房间类型：" prop="roomTypeId">
        <el-input v-if="editType=='details'" :model-value="state.form.roomTypeName" readonly></el-input>
        <el-select v-model="state.form.roomTypeId" v-else clearable placeholder="全部">
          <el-option v-for="(item,index) in roomTypeList" :key="index" :label="item.roomTypeName" :value="item.roomTypeId"></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer v-if="editType!=='details'">
      <span class="dialog-footer">
        <el-button type="primary" @click="submit" size="mini">保存</el-button>
        <el-button @click="handleClose" size="mini">返回</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { ElDialog, ElButton, ElForm, ElFormItem, ElInput, ElSelect, ElOption, ElMessage } from "element-plus"
import { reactive, watch, nextTick, ref } from 'vue'
import { roomAdd, roomEdit } from "@/applications/eccard-dorm/api";
import linkageSelect from "@/applications/eccard-dorm/components/linkageSelect.vue"
const linkageData = {
  area: { label: "所属区域：", valueKey: "areaId", key: "id" },
  building: { label: "所属楼栋：", valueKey: "buildId" },
  unit: { label: "所属单元：", valueKey: "unitNum" },
  floor: { label: "所属楼层：", valueKey: "floorNum" },
}
export default {
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    rowData: {
      types: Object,
      default: null
    },
    editType: {
      types: String,
      default: ''
    },
    roomTypeList: {
      type: Array,
      default: null
    }
  },
  components: {
    ElDialog,
    ElButton,
    ElForm,
    ElFormItem,
    ElInput,
    ElSelect,
    ElOption,
    "kade-linkage-select": linkageSelect,
  },
  setup(props, context) {
    const formRef = ref(null)
    const state = reactive({
      loading: false,
      roomTypeList: [],
      form: {
        annexFront: ""
      }
    });
    watch(() => props.modelValue, val => {
      if (val) {
        if (props.editType == 'add') {
          state.form = {
            receiveMessage: 'TRUE'
          }
        } else {
          state.form = { ...props.rowData }
        }
        nextTick(() => {
          formRef.value.clearValidate()
        })
      }
    })
    const title = (val) => {
      if (val == 'add') {
        return '新建房间'
      } else if (val == 'edit') {
        return '编辑房间'
      } else if (val == 'details') {
        return '房间详情'
      }
    }
    const rules = {
      roomNo: [
        {
          required: true,
          message: "请输入房间号",
        },
        {
          pattern: /^[0-9]*[1-9][0-9]*$/,
          message: "请输入数字整数",
        },
      ],
      roomName: [
        {
          required: true,
          message: "请输入房间名称",
        },
        {
          pattern: /^\S.*\S$|(^\S{0,1}\S$)/,
          message: "房间名称首尾不能包含空格",
        },
      ],
      areaId: [
        {
          required: true,
          message: "请选择所属区域",
          trigger: "blur",
        },
      ],
      buildId: [
        {
          required: true,
          message: "请选择所属楼栋",
          trigger: "blur",
        },
      ],
      unitNum: [
        {
          required: true,
          message: "请选择所属单元",
          trigger: "blur",
        },
      ],
      floorNum: [
        {
          required: true,
          message: "请选择所属楼层",
          trigger: "blur",
        },
      ],
      roomTypeId: [
        {
          required: true,
          message: "请选择房间类型",
          trigger: "blur",
        },
      ],
    }
    const linkageChange = (val) => {
      state.form = { ...state.form, ...val }
    }
    const submit = () => {
      formRef.value.validate(async (valid) => {
        if (valid) {
          let params = state.form
          let fn = props.editType == 'add' ? roomAdd : roomEdit
          state.loading = true
          try {
            let { code, message } = await fn(params)
            if (code === 0) {
              ElMessage.success(message)
              context.emit("update:modelValue", true);
            }
            state.loading = false
          }
          catch {
            state.loading = false
          }

        } else {
          return false
        }
      })
    }
    const handleClose = () => {
      context.emit("update:modelValue", false)
    };
    return {
      linkageData,
      formRef,
      state,
      title,
      rules,
      linkageChange,
      submit,
      handleClose,
    }
  }
}
</script>

<style lang="scss" scoped>
.el-form {
  margin-top: 20px;
}
:deep(.el-input-number) {
  width: 192px;
}
:deep(.el-input) {
  width: 192px;
}
:deep(.single-image-uploader) {
  width: 100px;
  height: 100px;
  .el-upload {
    width: 100px;
    height: 100px;
  }
  .element-icons {
    font-size: 40px !important;
  }
}
</style>