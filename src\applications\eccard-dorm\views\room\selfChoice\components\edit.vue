<template>
  <el-dialog v-loading="state.loading" :model-value="isShow" :title="data.id ? '编辑设置' : '新增设置'" width="800px"
    :before-close="beforeClose" :close-on-click-modal="false">
    <el-form label-width="150px" size="mini" ref="formRef" style="margin-top:20px" :model="state.form" :rules="rules">
      <el-row>
        <el-col :span="24">
          <el-form-item label="组织机构：" prop="deptId">
            <kade-dept-select-tree style="width: 100%" :value="state.form.deptId" valueKey="id" :multiple="false"
              @valueChange="(val) => (state.form.deptId = val.id)" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="开始自助选宿时间：" prop="beginTime">
            <el-date-picker v-model="state.form.beginTime" type="datetime" placeholder="请选择" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="结束自助选宿时间：" prop="endTime">
            <el-date-picker v-model="state.form.endTime" type="datetime" placeholder="请选择" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="备注：">
            <el-input v-model="state.form.remarks" type="textarea" :autosize="{ minRows: 3, maxRows: 6 }"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="beforeClose()" size="mini">取&nbsp;&nbsp;消</el-button>
        <el-button @click="submit()" type="primary" size="mini">确&nbsp;&nbsp;认</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script>
import { ElDialog, ElButton, ElRow, ElCol, ElForm, ElFormItem, ElInput, ElDatePicker, ElMessage } from "element-plus";
import { reactive, ref, nextTick } from 'vue';
import { addSelfChoice, editSelfChoice } from "@/applications/eccard-dorm/api";
import { timeStr } from "@/utils/date.js"
import deptSelectTree from "@/components/tree/deptSelectTree.vue";
import { watch } from '@vue/runtime-core';
const rules = {
  deptId: [
    {
      required: true,
      message: "请选择组织机构",
      trigger: "change",
    },
  ],
  beginTime: [
    {
      required: true,
      message: "请选择开始自助选宿时间",
      trigger: "change",
    },
  ],
  endTime: [
    {
      required: true,
      message: "请选择结束自助选宿时间",
      trigger: "change",
    },
  ],
}
export default {
  components: {
    ElDialog,
    ElButton,
    ElRow,
    ElCol,
    ElForm,
    ElFormItem,
    ElInput,
    ElDatePicker,
    "kade-dept-select-tree": deptSelectTree,
  },
  props: {
    isShow: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Object,
      default: null
    }
  },
  setup(props, context) {
    const formRef = ref(null)
    const state = reactive({
      loading: false,
      form: {},
    })
    watch(() => props.isShow, val => {
      if (val) {
        if (props.data.id) {
          state.form = { ...props.data }
          console.log(props.data)
        }else{
          state.form={}
        }
      }
      nextTick(() => {
        formRef.value.clearValidate()
      })
    })
    const submit = () => {
      formRef.value.validate(async valid => {
        if (valid) {
          state.loading = true
          let fn = props.data.id ? editSelfChoice : addSelfChoice
          try {
            let params = { ...state.form }
            params.beginTime = timeStr(params.beginTime)
            params.endTime = timeStr(params.endTime)
            let { code, message } = await fn(params)
            if (code === 0) {
              ElMessage.success(message)
              context.emit("close", true)
              state.loading = false
            }
          }
          catch {
            state.loading = false
          }
        }
      })
    }
    const beforeClose = () => {
      context.emit("close", false)
    }
    return {
      formRef,
      rules,
      state,
      submit,
      beforeClose
    }
  }
};
</script>
<style lang="scss" scoped>
.export-header {
  padding: 10px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.template-upload {
  text-decoration: underline;
}

:deep(.el-input) {
  width: 100% !important;

  .el-input__inner {
    width: 100% !important;

  }
}

:deep(.el-textarea__inner) {
  // width:780px;
}

:deep(.el-select) {
  width: 100% !important;

}

:deep(.el-input__inner) {
  width: 100% !important;
}
</style>