<template>
  <el-dialog :model-value="isShow" title="商家结算" width="800px" :before-close="beforeClose" :append-to-body="true">
    <kade-table-wrap title="结算商家信息">
      <el-table style="width: 100%" :data="[selected]" border stripe>
        <el-table-column show-overflow-tooltip label="商户编号" prop="merchantNo" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="merchantName" label="商户名称" align="center"></el-table-column>
        <!-- <el-table-column show-overflow-tooltip label="所属学校" prop="areaName" align="center"></el-table-column> -->
        <el-table-column show-overflow-tooltip label="商户类型" prop="merchantType" align="center">
          <template #default="scope">
            {{ dictionaryFilter(scope.row.merchantType) }}
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip label="所属区域" prop="areaName" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip label="联系电话" prop="merchantTel" align="center"></el-table-column>
      </el-table>
    </kade-table-wrap>
    <kade-table-wrap title="结算信息" style="margin-top:20px">
      <el-divider style="margin:0"></el-divider>
      <el-form ref="formDom" label-width="120px" size="small" :model="state.form" :rules="rules" style="padding:20px">
        <el-row>
          <el-col :md="24" :lg="12">
            <el-form-item label="结算类型:" prop="settlementType">
              <el-select v-model="state.form.settlementType">
                <el-option v-for="(item,index) in merchantTypeList" :key="index" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="结算周期:" prop="requestDate">
              <el-date-picker v-model="state.form.requestDate" type="daterange" range-separator="~" start-placeholder="开始日期" end-placeholder="结束日期" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="当前结余金额:">
              <el-input disabled onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1')" :model-value="selected.merchantBalance">
                <template #prefix>￥</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="本次结算金额:" prop="currentSettlementAmount">
              <el-input onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1')" v-model="state.form.currentSettlementAmount">
                <template #prefix>￥</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <span style="line-height:40px">当前结算费率为 {{selected.merchantRate}}%，实际结算金额为 {{(Number(state.form.currentSettlementAmount)*Number(selected.merchantRate)/100).toFixed(2)}} 元</span>
          </el-col>
          <el-col :span="24">
            <el-form-item label="结算方式:" prop="settlementMode">
              <el-radio-group v-model="state.form.settlementMode">
                <el-radio :label="item.value" v-for="(item,index) in merchantModeList" :key="index">{{item.label}}</el-radio>
              </el-radio-group>
              <div v-if="state.form.settlementMode=='BANK_TRANSFER'">
                {{`账号信息：${state.details&&state.details.merchantBanks.accountBank}，${state.details&&state.details.merchantBanks.accountName}，${state.details&&state.details.merchantBanks.bankNo}`}}
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="结算人员">
          <el-select v-model="state.form.settlementUser">
            <el-option v-for="(item,index) in state.managerPersonList" :key="index" :label="item.userName" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="结算说明">
          <el-input :max="200" v-model="state.form.remark" type="textarea"></el-input>
        </el-form-item>
      </el-form>
    </kade-table-wrap>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="beforeClose" size="mini">取消</el-button>
        <el-button type="primary" @click="submit()" size="mini">提交</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script>
import {
  ElDialog,
  ElTable,
  ElTableColumn,
  ElRow,
  ElCol,
  ElDivider,
  ElForm,
  ElFormItem,
  ElDatePicker,
  ElInput,
  ElSelect,
  ElOption,
  ElRadioGroup,
  ElRadio,
  ElButton,
  ElMessage,
} from "element-plus";
import { reactive, ref } from "@vue/reactivity";
import { nextTick, watch } from "@vue/runtime-core";
import { getToken } from "@/utils";
import { useDict } from "@/hooks/useDict.js";
import { timeStr } from "@/utils/date.js";
import {
  addMerchantSettlementInfo,
  getMerchantInfoById,
  getMerchantOperatorList,
} from "@/applications/eccard-finance/api";
export default {
  components: {
    ElDialog,
    ElTable,
    ElTableColumn,
    ElRow,
    ElCol,
    ElDivider,
    ElForm,
    ElFormItem,
    ElDatePicker,
    ElInput,
    ElSelect,
    ElOption,
    ElRadioGroup,
    ElRadio,
    ElButton,
  },
  props: {
    isShow: {
      type: Boolean,
      default: false,
    },
    selected: {
      type: Object,
      default: null,
    },
  },
  setup(props, context) {
    const merchantTypeList = useDict("MERCHANT_SETTLEMENT_TYPE");
    const merchantModeList = useDict("MERCHANT_SETTLEMENT_MODE");
    const formDom = ref(null);
    const state = reactive({
      requestHeader: getToken(),
      form: {
        requestDate: [],
        currentSettlementAmount: "",
      },
      details: "",
      managerPersonList: [],
    });
    const validateAmount = (rule, value, callback) => {
      if (value > Number(props.selected.merchantBalance)) {
        callback(new Error("结算金额不能超过当前结余金额"));
      } else {
        callback();
      }
    };
    const rules = {
      currentSettlementBalance: [
        {
          required: true,
          message: "请输入当前结余金额",
          trigger: "blur",
        },
      ],
      currentSettlementAmount: [
        {
          required: true,
          message: "请输入本次结算金额",
          trigger: "blur",
        },
        { validator: validateAmount, trigger: "blur" },
      ],
      settlementType: [
        {
          required: true,
          message: "请选择结算类型",
          trigger: "change",
        },
      ],
      settlementMode: [
        {
          required: true,
          message: "请选择结算方式",
          trigger: "change",
        },
      ],
      requestDate: [
        {
          required: true,
          message: "请选择结算日期",
          trigger: "change",
        },
      ],
    };
    watch(
      () => props.isShow,
      async (val) => {
        if (val) {
          state.form = {
            requestDate: [],
            currentSettlementAmount: "",
          };
          let { data } = await getMerchantInfoById({
            merchantId: props.selected.merchantId,
          });
          state.details = data;
          let res = await getMerchantOperatorList({
            merchantId: props.selected.merchantId,
          });
          state.managerPersonList = res.data;
          nextTick(() => {
            formDom.value.clearValidate();
          });
        }
      }
    );

    const submit = () => {
      formDom.value.validate(async (valid) => {
        if (valid) {
          let params = {
            ...state.form,
            merchantId: props.selected.merchantId,
          };
          params.settlementBegindate = timeStr(params.requestDate[0]);
          params.settlementEnddate = timeStr(params.requestDate[1]);
          delete params.requestDate;
          params.actualSettlementAmount = (
            (Number(state.form.currentSettlementAmount) *
              Number(props.selected.merchantRate)) /
            100
          ).toFixed(2);
          params.previousBalanceAmount = props.selected.merchantBalance;
          params.proceduresAmount =
            state.form.currentSettlementAmount - params.actualSettlementAmount;
          params.currentSettlementBalance =
            params.previousBalanceAmount - state.form.currentSettlementAmount;
          console.log(params);
          let { code, message } = await addMerchantSettlementInfo(params);
          if (code === 0) {
            ElMessage.success(message);
            context.emit("close", true);
          }
        } else {
          return false;
        }
      });
    };
    const beforeClose = () => {
      context.emit("close", false);
    };
    return {
      merchantTypeList,
      merchantModeList,
      formDom,
      state,
      rules,
      submit,
      beforeClose,
    };
  },
};
</script>
<style lang="scss" scoped>
:deep(.el-form-item__content) {
  .el-select {
    width: 230px !important;
  }
  .el-input__inner {
    width: 230px !important;
  }
  .el-date-editor {
    width: 100% !important;
  }
  .el-date-editor.el-input {
    width: 230px;
  }
}
</style>