<template>
  <kade-route-card>
    <template #header>
      用户权限设置
    </template>
    <el-tabs v-model="state.activeName">
      <el-tab-pane label="区域权限" name="area">

      </el-tab-pane>
      <el-tab-pane label="组织权限" name="org">

      </el-tab-pane>
      <el-tab-pane label="角色权限" name="role">

      </el-tab-pane>
    </el-tabs>
    <div>
      <el-button icon="el-icon-circle-check" size="small" @click="handleSave">保存</el-button>
    </div>
  </kade-route-card>
</template>
<script>
import {
  ElTabPane,
  ElTabs,
  ElButton,
} from 'element-plus';
import { reactive, watchEffect } from 'vue';
import { useRoute } from 'vue-router';
import {
  getRoleByUser,
  getAreaByUser,
  getOrgByUser,
  setRoleToUser,
  setOrgToUser,
  setAreaToUser,
} from '@/applications/mocha_itom/api';
export default {
  components: {
    'el-tabs': ElTabs,
    'el-tab-pane': ElTabPane,
    'el-button': ElButton,
  },
  setup() {
    const route = useRoute();
    const state = reactive({
      loading: false,
      activeName: 'area',
      areaPermiss: [],
      orgPermiss: [],
      rolePermiss: [],
    });

    const loadData = async () => {
      const userId = route.query.id;
      let fn;
      switch (state.activeName) {
        case 'area':
          fn = getAreaByUser;
          break;
        case 'org':
          fn = getOrgByUser;
          break;
        case 'role':
          fn = getRoleByUser;
          break;
      }
      try {
        state.loading = true;
        const { data } = await fn({ userId });
        state[`${state.activeName}Permiss`] = data;
      } catch (e) {
        throw new Error(e.message);
      } finally {
        state.loading = false;
      }
    }

    const handleSave = async () => {
      const type = state.activeName;
      let fn;
      switch (state.activeName) {
        case 'area':
          fn = setAreaToUser;
          break;
        case 'org':
          fn = setOrgToUser;
          break;
        case 'role':
          fn = setRoleToUser;
          break;
      }
      console.log(fn, type)
    }

    watchEffect(() => {
      if (!state[`${state.activeName}Permiss`].length) {
        loadData();
      }
    });
    return {
      state,
      handleSave,
    }
  }
}
</script>