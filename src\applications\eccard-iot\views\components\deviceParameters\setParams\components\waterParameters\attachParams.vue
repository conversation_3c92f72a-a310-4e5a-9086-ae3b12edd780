<template>
  <div class="padding-form-box timeParam" v-loading="state.loading">
    <el-form inline size="small" label-width="120px">
      <el-form-item label="附加费类型:">
        <el-select v-model="state.form.paramContent.surchargeMode">
          <el-option v-for="(item,index) in dictListFnc().surchargeMode" :key="index" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="免收数量:">
        <el-input-number :min="0" v-model="state.form.paramContent.freeValue"></el-input-number>
      </el-form-item>
      <el-form-item label="污水处理:">
        <el-input-number :min="0" v-model="state.form.paramContent.sewageTreatment" :precision="2"></el-input-number>
      </el-form-item>
      <el-form-item label="水资源费:">
        <el-input-number :min="0" v-model="state.form.paramContent.WaterResourcesFee" :precision="2"></el-input-number>
      </el-form-item>
      <el-form-item label="价调基金:">
        <el-input-number :min="0" v-model="state.form.paramContent.priceAdjustmentFund" :precision="2"></el-input-number>
      </el-form-item>
      <el-form-item label="代收垃圾费类型:">
        <el-select v-model="state.form.paramContent.garbageCollectionFeeType">
          <el-option v-for="(item,index) in dictListFnc().garbageCollectionFeeType" :key="index" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="垃圾费:">
        <el-input-number :min="0" v-model="state.form.paramContent.garbageFee" :precision="2"></el-input-number>
      </el-form-item>
      <el-form-item label="免收数量:">
        <el-input-number :min="0" v-model="state.form.paramContent.freeValue"></el-input-number>
      </el-form-item>
    </el-form>
  </div>
  <div style="text-align: center">
    <el-button size="mini" @click="beforeClose()">取消</el-button>
    <el-button size="mini" type="primary" @click="saveClick()">保存</el-button>
  </div>
</template>

<script>
import {
  ElForm,
  ElFormItem,
  ElSelect,
  ElOption,
  ElInputNumber,
  ElButton,
  ElMessage,
} from "element-plus";
import { onMounted, reactive } from "vue";
import { useStore } from "vuex";
const defaultParamsFnc = () => {
  return {
    WaterResourcesFee: 0,	//水资源费	integer(int32)	
    freeValue: 0,	//免收数量	integer(int32)	
    garbageCollectionFeeType: "NO",	//代收垃圾费类型	string	
    garbageFee: 0,	//垃圾费	integer(int32)	
    garbageFeeFreeValue: 0,	//免收数量	integer(int32)	
    priceAdjustmentFund: 0,	//价调基金	integer(int32)	
    sewageTreatment: 0,	//污水处理	integer(int32)	
    surchargeMode: "NO",	//附加费类型	string	
  };
};

export default {
  components: {
    ElForm,
    ElFormItem,
    ElButton,
    ElSelect,
    ElOption,
    ElInputNumber,
  },
  setup() {
    const store = useStore();
    const state = reactive({
      loading: false,
      form: {
        paramContent: defaultParamsFnc(),
        id: "",
        paramId: "",
        paramType: "SURCHARGE",
      },
    });
    const dictListFnc = () => {
      return store.state.deviceParameters[store.state.app.activeTab].dict
    }
    const saveClick = async () => {
      state.loading = true;
      let storeData = store.state.deviceParameters[store.state.app.activeTab]
      state.form.paramId = storeData.selectRow.id;
      let fn = state.form.id ? storeData.TabModule.detailsEditFnc : storeData.TabModule.detailsAddFnc

      let params = { ...state.form }
      console.log(params.paramContent);
      params.paramContent = JSON.stringify(params.paramContent)
      let { message, code } = await fn(params);
      if (code === 0) {
        setTimeout(() => {
          ElMessage.success(message);
          store.dispatch("deviceParameters/getParams", store.state.deviceParameters[store.state.app.activeTab].selectRow.id);
          state.loading = false;
        }, 1500);
      }
    };

    const beforeClose = () => {
      store.commit("deviceParameters/updateState", {
        key: store.state.app.activeTab,
        childKey: "isSetParams",
        payload: false,
      });
    };
    onMounted(() => {
      console.log(store.state.app.activeTab, store.state.deviceParameters[store.state.app.activeTab].detailsParams);
      let val = store.state.deviceParameters[store.state.app.activeTab].detailsParams
      let arr = val.filter(item => item.paramType == 'SURCHARGE')
      if (arr && arr.length) {
        state.form = { ...arr[0] }
        state.form.paramContent = JSON.parse(state.form.paramContent)
      } else {
        //设置默认选项
        state.form.id = "";
        state.form.paramContent = defaultParamsFnc();
      }
    });

    onMounted(() => {

    });
    return {
      state,
      dictListFnc,
      saveClick,
      beforeClose,
    };
  },
  name: "basicParam",
};
</script>

<style lang="scss" scoped>
:deep(.el-form-item__content) {
  width: 200px;
  .el-input-number {
    width: 100%;
  }
  .el-select {
    width: 100% !important;
  }
}
</style>
