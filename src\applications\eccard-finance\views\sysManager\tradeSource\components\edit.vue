<template>
    <el-dialog
    :model-value="dialogVisible"
    :title="title"
    width="500px"
    :before-close="handleClose"
  >
  <el-form inline size="mini" label-width="140px">
      <el-form-item label="交易来源名称：">
          <el-input v-model="state.rowData.tradeName" :readonly="type==='detail'" ></el-input>
      </el-form-item>
      <el-form-item label="是否启用：">
          <el-switch v-model="state.rowData.useStatus" :disabled="type==='detail'" active-value="ENABLE_TRUE" inactive-value="ENABLE_FALSE"/>
      </el-form-item>
      <el-form-item label="属性值：" v-if="type==='add'">
          <el-input-number v-model="state.rowData.tradeCode" :min="0" />
      </el-form-item>
      <el-form-item label="是否系统预置：" v-if="type==='add'">
            <el-radio-group v-model="state.rowData.sysPreset">
                <el-radio label="TRUE">是</el-radio>
                <el-radio label="FALSE">否</el-radio>
            </el-radio-group>
      </el-form-item>
      <!-- <el-form-item label="排序：">
          <el-input v-model="state.rowData.tradeName"></el-input>
      </el-form-item> -->
  </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose" size="mini">关闭</el-button>
        <el-button type="primary" :disabled="state.rowData.sysPreset=='TRUE'" @click="editClick()" size="mini">{{type==="detail"?"编辑":"保存"}}</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { ElDialog,ElButton,ElForm,ElFormItem,ElInput,ElSwitch,ElInputNumber,ElRadioGroup,ElRadio, ElMessage} from "element-plus"
import { reactive } from '@vue/reactivity'
import { computed, watch } from '@vue/runtime-core';
import { saveTradeSource,updateTradeSource } from "@/applications/eccard-finance/api";
export default {
    props:{
        dialogVisible:{
            type:Boolean,
            default:false
        },
        type:{
            type:String,
            default:""
        },
        rowData:{
            type:[String,Object],
            default:''
        }
    },
    components:{
        ElDialog,
        ElButton,
        ElForm,
        ElFormItem,
        ElInput,
        ElSwitch,
        ElInputNumber,
        ElRadioGroup,
        ElRadio
    },
    setup(props,context){
        const state = reactive({
            rowData:{},
        });

        const title=computed(()=>{
            if(props.type==="add"){
                return "新增交易来源"
            }else if(props.type==="edit"){
                return "编辑交易来源"
            }else if(props.type==="detail"){
                return "交易来源详情"
            }
        })

        watch(()=>props.rowData,val=>{
            state.rowData={...val}
        })
  
        const editClick=async ()=>{
            if(props.type==="detail"){
                context.emit("editType")
            }else{
                const fn=props.type==="add"?saveTradeSource:updateTradeSource
                    let params
                if(props.type==="add"){
                    params={...state.rowData}
                }else{
                   let {sysPreset,tradeCode,tradeName,useStatus,id}={...state.rowData}
                   params={sysPreset,tradeCode,tradeName,useStatus,id}
                }
                let {code,message}=await fn(params)
                if(code===0){
                    ElMessage.success(message)
                    context.emit("close",true)
                }
            }
        }

        const handleClose =()=>{
            context.emit("close",false)
        }
        return{
            state,
            title,
            editClick,
            handleClose
        }
    }
}
</script>

<style lang="scss" scoped>
.el-form{
    margin-top: 20px;
}
</style>