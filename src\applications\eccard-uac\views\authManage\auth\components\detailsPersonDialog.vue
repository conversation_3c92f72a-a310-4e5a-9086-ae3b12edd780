<template>
  <el-dialog :model-value="modelValue" title="用户详情" width="1700px" :before-close="handleClose"
    :close-on-click-modal="false">
    <div style="padding:20px 0 20px">
      <kade-table-wrap title="人员信息">
        <el-divider></el-divider>
        <div class="padding-box box">
          <el-table :data="[rowData]" border>
            <el-table-column label="组织机构" prop="deptName" align="center"></el-table-column>
            <el-table-column label="身份类别" prop="roleName" align="center"></el-table-column>
            <el-table-column label="用户编号" prop="userCode" align="center"></el-table-column>
            <el-table-column label="用户名称" prop="userName" align="center"></el-table-column>
            <el-table-column label="性别" prop="userSex" align="center">
              <template #default="scope">
                {{ dictionaryFilter(scope.row.userSex) }}
              </template>
            </el-table-column>
            <el-table-column label="卡类" prop="cardTypeName" align="center"></el-table-column>
            <el-table-column label="卡片类别" prop="cardCategoryName" align="center">
              <template #default="scope">
                {{ dictionaryFilter(scope.row.cardCategoryName) }}
              </template>
            </el-table-column>
          </el-table>
        </div>
      </kade-table-wrap>
      <kade-table-filter @search="handleSearch" @reset="handleReset">
        <el-form inline label-width="100px" size="mini">
          <el-form-item label="门禁区域">
            <kade-area-select-tree style="width: 100%" :value="state.form.areaPath" valueKey="areaPath"
              :multiple="false" @valueChange="(val) => (state.form.areaPath = val.areaPath)" />
          </el-form-item>
          <!--           <el-form-item label="门禁分组">
            <el-select clearable v-model="state.form.groupId">
              <el-option v-for="(item, index) in state.groupTypeList" :key="index" :label="item.groupName"
                :value="item.id">
              </el-option>
            </el-select>
          </el-form-item> -->
          <el-form-item label="设备类型">
            <el-select clearable v-model="state.form.deviceType">
              <el-option v-for="(item, index) in state.deviceTypeList" :key="index" :label="item.cfgValue"
                :value="item.cfgKey"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </kade-table-filter>
      <kade-table-wrap title="已授权门禁">
        <template #extra>
          <el-button @click="delAuth" class="btn-purple" icon="el-icon-delete-solid" size="mini">删除权限</el-button>
        </template>
        <el-table :data="state.dataList" @selection-change="selectionChange" border>
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column show-overflow-tooltip label="门禁分组" prop="groupName" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip label="门禁区域" prop="areaName" align="center" width="400px">
          </el-table-column>
          <el-table-column show-overflow-tooltip label="设备类型" prop="deviceTypeName" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip label="设备名称" prop="deviceName" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip label="时段" prop="authPeriod" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip label="卡号" prop="cardNo" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip label="是否启用一次刷卡限制" prop="" align="center">
            <template #default="scope">
              {{ dictionaryFilter(scope.row.oneCard + '') }}
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip label="授权时间" prop="authBeginTime" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip label="过期时间" prop="authEndTime" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip label="权限状态" prop="downStatus" align="center">
            <template #default="scope">
              {{ dictionaryFilter(scope.row.downStatus) }}
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination">
          <el-pagination v-model:currentPage="state.form.pageNum" v-model:page-size="state.form.pageSize"
            :page-sizes="[10, 20, 30, 40]" background layout="total, sizes, prev, pager, next, jumper"
            :total="state.total" @size-change="handleSizeChange" @current-change="handleCurrentChange">
          </el-pagination>
        </div>
      </kade-table-wrap>
    </div>
  </el-dialog>
</template>
<script>
import { ElDialog, ElButton, ElDivider, ElTable, ElTableColumn, ElForm, ElFormItem, ElSelect, ElOption, ElPagination, ElMessage, ElMessageBox } from "element-plus"
import { reactive, watch, onMounted } from 'vue'
import { iotCfg, } from "@/applications/eccard-iot/api";
import { authManageUserDetails, authManageDel } from "@/applications/eccard-uac/api";
import areaSelectTree from "@/components/tree/areaSelectTree.vue";
export default {
  components: {
    ElDialog, ElButton, ElDivider, ElTable, ElTableColumn, ElForm, ElFormItem, ElSelect, ElOption, ElPagination,
    "kade-area-select-tree": areaSelectTree,
  },
  emits: ["update:modelValue"],
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    rowData: {
      type: Object,
      default: null
    }
  },
  setup(props, context) {
    const state = reactive({
      deviceTypeList: [],
      selectList: [],
      form: {},
      dataList: [],
      total: 0
    })
    watch(() => props.modelValue, val => {
      if (val) {
        state.dataList = []
        state.total = 0
        state.form = {
          currentPage: 1,
          pageSize: 10,
        }
        getList()
      }
    })
    /*     const getGroupList = async () => {
          let { data } = await acsGroupingInfoList({ groupType: "deviceGroup" })
          state.groupTypeList = data
        } */
    const getDeviceTypeList = async () => {
      let { data } = await iotCfg({ cfgType: 'ACCESS_DEVICE_TYPE' });
      state.deviceTypeList = data
    }
    const getList = async () => {
      state.loading = true
      let params = {
        ...state.form,
        userId: props.rowData.id
      }
      let { data: { list, total } } = await authManageUserDetails(params)
      state.dataList = list
      state.total = total
      state.loading = false
    }
    const handleSearch = () => {
      getList();
    }
    const handleReset = () => {
      state.form = {
        currentPage: 1,
        pageSize: 10,
      }
    }
    const selectionChange = val => {
      state.selectList = val
    }
    const delAuth = () => {
      if (!state.selectList.length) {
        return ElMessage.error("请选择设备！")
      }
      ElMessageBox.confirm("确认删除?", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        let { code, message } = await authManageDel({ authIds: state.selectList.map(item => item.id) });
        if (code === 0) {
          ElMessage.success(message);
          state.form.currentPage = 1
          getList();
        }
      });
    }
    const handleClose = () => {
      context.emit("update:modelValue", false)
    }
    onMounted(() => {
      getDeviceTypeList()
      // getGroupList()
    })
    return {
      state,
      selectionChange,
      delAuth,
      handleSearch,
      handleReset,
      handleClose
    }
  }
}
</script>
<style lang="scss" scoped>
:deep(.kade-table-wrap) {
  margin-bottom: 20px;
}

.padding-box {
  padding: 10px 10px 0;
}

.box {
  .el-table {
    border-left: 1px solid #EBEEF5;
    border-right: 1px solid #EBEEF5;
  }
}

.el-divider--horizontal {
  margin: 0
}

:deep(.box) {
  padding: 10px 10px 0;

  .el-form {
    .el-select {
      width: 150px;
    }

    .el-input {
      width: 150px
    }
  }
}
</style>