<template>
  <div class="attendance-device padding-box">
    <kade-table-filter @search="handleSearch" @reset="handleReset">
      <el-form inline label-width="80px" size="mini">
        <el-form-item label="类型名称">
          <el-input v-model="state.form.typeName" placeholder="请输入"></el-input>
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="工作站类型列表">
      <template #extra>
        <el-button icon="el-icon-plus" type="success" size="mini" @click="handleEdit({},'add')">新增</el-button>
      </template>
      <el-table border height="55vh" v-loading="state.loading" :data="state.dataList">
        <el-table-column show-overflow-tooltip prop="typeCode" label="类型编码" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="typeName" label="类型名称" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="remark" label="备注" align="center"></el-table-column>
        <el-table-column label="操作" align="center">
          <template #default="scope">
            <el-button type="text" @click="handleEdit(scope.row,'edit')" size="mini">编辑</el-button>
            <el-button type="text" @click="handleDel(scope.row)" size="mini">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination v-model:currentPage="state.form.pageNum" v-model:page-size="state.form.pageSize" :page-sizes="[10, 20, 30, 40]" background layout="total, sizes, prev, pager, next, jumper" :total="state.total" @size-change="handleSizeChange" @current-change="handleCurrentChange">
        </el-pagination>
      </div>
    </kade-table-wrap>
    <kade-factory-edit :dialogVisible="state.dialogVisible" :data="state.rowData" :type="state.type" @close="close"></kade-factory-edit>
  </div>
</template>

<script>
import {
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElTable,
  ElTableColumn,
  ElPagination,
  ElMessage,
  ElMessageBox,
} from "element-plus";
import { reactive } from "@vue/reactivity";
import { workStationTypeList,workStationTypeDel } from "@/applications/eccard-iot/api";
import edit from "./components/edit.vue";
import { onMounted } from '@vue/runtime-core';
export default {
  components: {
    ElButton,
    ElForm,
    ElFormItem,
    ElInput,
    ElTable,
    ElTableColumn,
    ElPagination,
    "kade-factory-edit": edit,
  },
  setup() {
    const state = reactive({
      dialogVisible: false,
      loading: false,
      form: {
        pageNum: 1,
        pageSize: 10
      },
      dataList: [],
      total: 0,
      rowData: {},
    });
    const getList = async () => {
      state.loading = true
      try {
        let { data:{list,total} } = await workStationTypeList(state.form)
        state.dataList=list
        state.total=total
        state.loading = false
      }
      catch {
        state.loading = false
      }
    }
    const handleEdit = (row, type) => {
      state.type = type
      state.rowData = row
      state.dialogVisible = true;
    };
    const handleDel = ({id}) => {
      ElMessageBox.confirm(`确认删除?`, {
        type: "warning",
        closeOnPressEscape: false,
        closeOnClickModal: false,
      }).then(async () => {
        try {
          const { message, code } = await workStationTypeDel( id );
          if (code === 0) {
            ElMessage.success(message);
            getList()
          }
        } catch (e) {
          throw new Error(e.message);
        }
      });
    }
    const handleSearch = () => {
      getList()
    }
    const handleReset = () => {
      state.form = {
        pageNum: 1,
        pageSize: 10
      }
    }
    const handleSizeChange=(val)=>{
      state.form.pageNum=1
      state.form.pageSize=val
      getList()
    }
    const handleCurrentChange=(val)=>{
      state.form.pageNum=val
      getList()
    }
    const close = (val) => {
      if(val){
        getList()
      }
      state.dialogVisible = false;
    };
    onMounted(() => {
      getList()
    })
    return {
      state,
      handleEdit,
      handleDel,
      handleSearch,
      handleReset,
      handleSizeChange,
      handleCurrentChange,
      close,
    };
  },
};
</script>

<style lang="scss" scoped>
:deep(.el-divider--horizontal) {
  margin: 0;
}
:deep(.el-dialog__footer) {
  text-align: center;
  border: 0;
}
:deep(.el-dialog__header) {
  border-bottom: 1px solid #eeeeee;
}
:deep(.el-dialog) {
  border-radius: 8px;
}
</style>