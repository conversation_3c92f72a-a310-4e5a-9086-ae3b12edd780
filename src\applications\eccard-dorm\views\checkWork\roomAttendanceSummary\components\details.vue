<template>
  <el-dialog :model-value="dialogVisible" :title="dialogTitle" width="900px" :before-close="handleClose">
    <el-table border :data="state.data" v-loading="state.loading">
      <el-table-column label="人员编号" prop="userCode" align="center"></el-table-column>
      <el-table-column label="人员姓名" prop="userName" align="center"></el-table-column>
      <el-table-column label="性别" prop="userSex" align="center">
        <template #default="scope">
          {{dictionaryFilter(scope.row.userSex)}}
        </template>
      </el-table-column>
      <el-table-column label="组织机构" prop="deptName" align="center"></el-table-column>
      <el-table-column label="考勤日期" prop="attendanceDate" align="center"></el-table-column>
      <el-table-column label="考勤时段" prop="attendancePeriodName" align="center"></el-table-column>
      <el-table-column label="考勤结果" prop="attendanceResult" align="center">
        <template #default="scope">
          {{dictionaryFilter(scope.row.attendanceResult)}}
        </template>
      </el-table-column>
    </el-table>
  </el-dialog>
</template>

<script>
import { ElDialog, ElTable, ElTableColumn, } from "element-plus"
import { reactive } from '@vue/reactivity'
import { dateStr } from "@/utils/date.js"
import { getNotReturnOrLate } from '@/applications/eccard-dorm/api.js'
import { watch } from '@vue/runtime-core'
export default {
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    dialogTitle: {
      type: String,
      default: ''
    },
    tableRow: {
      type: String,
      default: ''
    }
  },
  components: {
    ElDialog,
    ElTable,
    ElTableColumn,
  },
  setup(props, context) {
    const state = reactive({
      data: [],
      loading: false
    });
    watch(() => props.dialogVisible, val => {
      if (val) {
        detailList()
      }
    })
    const detailList = async () => {
      let { attendanceDate, attendancePeriodId, buildId, floorNum, roomId, unitNum, } = { ...props.tableRow }
      console.log(props.tableRow)
      let params = {
        attendanceDate: dateStr(attendanceDate), attendancePeriodId, buildId, floorNum, roomId, unitNum,
        attendanceResult: props.dialogTitle === "未归人员名单" ? "NOT_RETURN" : "LATE_RETURN"
      }
      console.log(params)
      let { data } = await getNotReturnOrLate(params)
      state.data = data
    }
    const handleClose = () => {
      context.emit("close", false)
    }
    return {
      state,
      handleClose,
    }
  }
}
</script>

<style lang="scss" scoped>
.el-table {
  margin-top: 15px;
}
</style>