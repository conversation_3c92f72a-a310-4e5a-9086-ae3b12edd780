<template>
    <kade-route-card style="height: auto">
      <kade-table-filter @search="search" @reset="reset">
          <el-form inline size="mini" label-width="100px">
             <el-form-item label="所属应用">
              <el-select
                clearable
                v-model="state.form.app"
                placeholder="请选择"
              >
                <el-option
                  v-for="(item, index) in state.appList"
                  :key="index"
                  :label="item.applyName"
                  :value="item.applyAppid"
                >
                </el-option>
              </el-select>
            </el-form-item>
             <el-form-item label="功能模块">
              <el-input
                placeholder="请输入页面名称"
                v-model="state.form.bizModule"
              ></el-input>
            </el-form-item>
             <el-form-item label="操作类型">
               <el-select clearable v-model="state.form.operateType" placeholder="请选择">
                <el-option :label="item.label" :value="item.value" v-for="(item,index) in type" :key="index">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="请求地址">
              <el-input
                placeholder="输入关键字搜索"
                v-model="state.form.uri"
              ></el-input>
            </el-form-item>
            <el-form-item label="请求参数">
              <el-input
                placeholder="输入关键字搜索"
                v-model="state.form.requestParam"
              ></el-input>
            </el-form-item>
            <el-form-item label="返回参数:">
              <el-input
                placeholder="输入关键字搜索"
                v-model="state.form.result"
              ></el-input>
            </el-form-item>
            <el-form-item label="操作结果">
              <el-select
                clearable
                v-model="state.form.operateResult"
                placeholder="请选择"
              >
                <el-option
                  v-for="(item, index) in state.loginResultList"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="操作员账号">
              <el-input
                placeholder="请输入页面名称"
                v-model="state.form.operateAccount"
              ></el-input>
            </el-form-item>
            <el-form-item label="操作时间">
              <el-date-picker
                style="width: 200px"
                v-model="state.requestDate"
                type="daterange"
                range-separator="~"
                unlink-panels
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item label="响应时间">
              <el-input
                placeholder="输入数值搜索"
                v-model="state.form.spendTime"
              ></el-input>
            </el-form-item>
          </el-form>
      </kade-table-filter>
      <kade-table-wrap title="操作日志列表">
        <template #extra>
          <el-button
            @click="exportClick()"
            icon="el-icon-daoru"
            size="mini"
            class="btn-blue"
            >导出</el-button
          >
        </template>
        <el-table
          style="width: 100%"
          :data="state.loginList"
          ref="multipleTable"
          v-loading="state.loading"
          border
          stripe
        >
          <el-table-column
            label="日志ID"
            prop="id"
            align="center"
          ></el-table-column>
          <el-table-column label="所属应用" prop="app" align="center">
          </el-table-column>

          <el-table-column
            label="功能模块"
            prop="bizModule"
            align="center"
          ></el-table-column>
          <el-table-column
            label="操作类型"
            prop="operateType"
            align="center"
          ></el-table-column>
          <el-table-column
            label="请求地址"
            prop="uri"
            align="center"
          >
          <template #default="scope">
            <div style="color:#3399FF;text-decoration: underline;" @click="jump(scope.row.uri)">{{ scope.row.uri }}</div>
          </template>
          </el-table-column>
          <el-table-column label="操作结果" prop="operateResult" align="center">
            <template #default="scope">
              <div :style="{color:scope.row.operateResult==1?'red':'green'}">{{scope.row.operateResult==1?"失败":"成功"}}</div>
            </template>
          </el-table-column>
          <el-table-column
            label="操作员账号"
            prop="operateAccount"
            align="center"
          ></el-table-column>
          <el-table-column
            label="操作员姓名"
            prop="operateAccount"
            align="center"
          ></el-table-column>
          <el-table-column
            label="操作时间"
            prop="operateTime"
            align="center"
          ></el-table-column>
          <el-table-column
            label="响应时间（毫秒）"
            prop="spendTime"
            align="center"
          ></el-table-column>
          <el-table-column label="操作" prop="endtermBalance" align="center">
            <template #default="scope">
              <el-button type="text" @click="detailClick(scope.row)">查看详情</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination">
          <el-pagination
          background
          v-model:currentPage="state.form.currentPage"
          v-model:page-size="state.form.pageSize"
          :page-sizes="[5, 10, 20, 30]"
          layout="total,sizes, prev, pager, next, jumper"
          :total="state.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
        </div>
      </kade-table-wrap>
    </kade-route-card>
    <kade-login-details
      :dialogVisible="state.dialogVisible"
      :selectRow="state.selectRow"
      @close="state.dialogVisible=false"
    />
</template>
<script>
import {
  ElButton,
  ElForm,
  ElFormItem,
  ElSelect,
  ElInput,
  ElOption,
  ElTable,
  ElTableColumn,
  ElPagination,
  ElDatePicker,
} from "element-plus";
const type = [
  {label:"其它",value:"0"},
  {label:"新增",value:"1"},
  {label:"修改",value:"2"},
  {label:"删除",value:"3"},
  {label:"查询",value:"4"},
  {label:"授权",value:"5"},
  {label:"导出",value:"6"},
  {label:"导入",value:"7"},
  {label:"强退",value:"8"},
  {label:"生成代码",value:"9"},
  {label:"清空数据",value:"10"},
]

// import { timeStr } from "@/utils/date.js";
import { reactive } from "@vue/reactivity";
import { getOperateLogList } from "@/applications/eccard-basic-data/api";
import { getUserApplyList } from "@/applications/unified_portal/api";
import { dateStr } from "@/utils/date";
import details from "./components/details";
import { onMounted } from "@vue/runtime-core";
export default {
  components: {
    "el-button": ElButton,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    "el-select": ElSelect,
    "el-option": ElOption,
    "el-table": ElTable,
    "el-table-column": ElTableColumn,
    "el-pagination": ElPagination,
    ElDatePicker,
    ElInput,
    "kade-login-details": details,
  },
  setup() {
    const state = reactive({
      dialogVisible: false,
      appList:[],
      form:{
        currentPage:1,
        pageSize:10,
      }, 
      dataList: [],
      total: 0,
      requestDate: [],
      loginResultList: [
        {
          label: "成功",
          value: 0,
        },
        {
          label: "失败",
          value: 1,
        },
      ],
      selectRow: "",
    });
    const getAppList=()=>{
      getUserApplyList().then((res)=>{
        console.log(res)
        state.appList = res.data
      })
    }
    const getList = function () {
      if (state.requestDate.length) {
        state.form.operateStartTime = dateStr(state.requestDate[0]);
        state.form.operateEndTime = dateStr(state.requestDate[1]);
      }
      getOperateLogList(state.form).then(({ code, message, data }) => {
        console.log(code, message, data);
        state.dataList = data.list;
        state.total = data.total;
      });
    };

    const detailClick = (row) => {
      console.log(1);
      state.selectRow = row;
      state.dialogVisible = true;
    };
    const jump = val =>{
      console.log(val)
      window.open(val)
    };
    const handlePageChange = (val) => {
      state.form.currentPage = val;
      getList();
    };
    const handleSizeChange = (val) => {
      state.form.currentPage = 1;
      state.form.pageSize = val;
      getList();
    };

    const reset = () => {
      state.form = {
        currentPage: 1,
        pageSize: 5,
      };
      state.requestDate = [];
    };
    onMounted(() => {
      getList();
      getAppList()
    });
    return {
      state,
      type,
      jump,
      getList,
      detailClick,
      handlePageChange,
      handleSizeChange,
      reset,
    };
  },
};
</script>
<style lang="scss">
:deep(.el-pagination__editor.el-input .el-input__inner){
  width: 46px;
}
:deep(.el-pagination__sizes .el-input .el-input__inner){
  width: 100px;
}
.el-input{
  width: 180px;
}
</style>
