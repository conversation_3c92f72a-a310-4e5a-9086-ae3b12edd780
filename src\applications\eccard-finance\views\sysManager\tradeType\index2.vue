<template>
  <div class="income-detail border-box">
    <kade-table-wrap title="交易类型">
      <el-table style="width: 100%" :data="state.dataList" v-loading="false" highlight-current-row border stripe>
        <el-table-column label="名称" prop="costName" align="center"></el-table-column>
        <el-table-column label="属性值" prop="costCode" align="center"></el-table-column>
        <el-table-column label="是否系统预置" prop="sysPreset" align="center">
          <template #default="scope">
            {{
              filterDictionary(scope.row.sysPreset, state.SYS_BOOL_STRING_LIST)
            }}
          </template>
        </el-table-column>
        <el-table-column label="收支类型" prop="inOut" align="center">
          <template #default="scope">
            {{
              filterDictionary(scope.row.inOut, state.WALLET_INOUT_TYPE_LIST)
            }}
          </template>
        </el-table-column>
        <el-table-column label="添加人" prop="createUserName" align="center"></el-table-column>
        <el-table-column label="创建时间" prop="createTime" align="center"></el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination
          background
          :current-page="state.form.currentPage"
          :page-size="state.form.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :page-sizes="[5, 10, 20, 50, 100]"
          :total="state.dataListTotal"
          @current-change="handlePageChange"
          @size-change="handleSizeChange"
        >
        </el-pagination>
      </div>
    </kade-table-wrap>
  </div>
</template>

<script>
import {onMounted, reactive} from "vue";
import {
  ElTable,
  ElTableColumn,
  ElPagination,
} from "element-plus";
import {
  getFinanceCostTypePage,
} from "@/applications/eccard-finance/api";

export default {
  components: {
    "el-table": ElTable,
    "el-table-column": ElTableColumn,
    "el-pagination": ElPagination,
  },
  setup() {
    const state = reactive({
      typeInfo: {},
      isShow: false,
      dataList: [],
      dataListTotal: 0,
      form: {
        currentPage: 1,
        pageSize: 10,
      },
      SYS_BOOL_STRING_LIST: JSON.parse(
        localStorage.getItem("kade_cache_dictionary")
      ).filter((item) => item.dictType == "SYS_BOOL_STRING"), //是否
      WALLET_INOUT_TYPE_LIST: JSON.parse(
        localStorage.getItem("kade_cache_dictionary")
      ).filter((item) => item.dictType == "WALLET_INOUT_TYPE"), //钱包收支类型
    });
    const getList = () => {
      getFinanceCostTypePage(state.form).then((res) => {
        state.dataList = res.data.list;
        state.dataListTotal = res.data.total;
      });
    };
    const filterDictionary = (val, dictionaryList) => {
      for (let i of dictionaryList) {
        if (val == i.dictCode) {
          return i.dictValue;
        }
      }
    };
    const off = () => {
      state.isShow = false;
    };
    const handlePageChange = (val) => {
      state.form.currentPage = val;
      getList();
    };
    const handleSizeChange = (val) => {
      state.form.currentPage = 1;
      state.form.pageSize = val;
      getList();
    };
    onMounted(() => {
      getList();
    });
    return {
      state,
      off,
      filterDictionary,
      handlePageChange,
      handleSizeChange,
    };
  },
};
</script>
<style lang="scss" scoped>
.income-detail {
  height: 680px;
}

.padding-form-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
