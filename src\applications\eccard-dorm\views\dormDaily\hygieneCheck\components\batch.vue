<template>
  <el-dialog :model-value="modelValue" title="批量新增卫生检查记录" width="1550px" :before-close="handleClose">
    <div v-loading="state.loading">
      <kade-shuttle-data :titleData="titleData" :listData="state.listData" :columnData="columnData" :delData="delData"
        @toAllRight="toAllRight" @toRight="toRight" @toLeft="toLeft" @toAllLeft="toAllLeft" @handleDel="handleDel"
        @oneCurrentChange="oneCurrentChange" @twoCurrentChange="twoCurrentChange">
        <template #firstSearch>
          <el-form label-width="50px" inline size="mini">
            <kade-linkage-select :isEdit="false" :data="linkageData1" :value="state.form1" @change="linkageChange1" />
            <el-button type="primary" size="mini" @click="handleSearch">搜索</el-button>
            <el-button type="primary" size="mini" @click="handleClear">清空</el-button>
          </el-form>
        </template>
        <template #secondSearch>
          <el-form label-width="50px" inline size="mini">
            <kade-linkage-select :isEdit="false" :data="linkageData2" :value="state.form2" @change="linkageChange2" />
            <el-button type="primary" size="mini" @click="search">搜索</el-button>
          </el-form>
        </template>
      </kade-shuttle-data>
      <el-row class="el-rows" :span="24">
        <div class="title">检查信息</div>
        <el-divider></el-divider>
        <el-form label-width="120px" inline size="mini" :model="state.form" ref="formRef" :rules="rules">
          <el-form-item label="检查日期：" prop="checkTime">
            <el-date-picker v-model="state.form.checkTime" type="date" placeholder="请选择检查日期" />
          </el-form-item>
          <el-form-item label="检查人：" prop="checkPerson">
            <el-input v-model="state.form.checkPerson" placeholder="请输入检查人"></el-input>
          </el-form-item>
          <el-form-item label="检查结果：" prop="checkResult">
            <el-select v-model="state.form.checkResult" placeholder="请选择">
              <el-option v-for="item in checkResultList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>

          <el-form-item label="得分：" prop="checkScore">
            <el-input v-model="state.form.checkScore" placeholder="请输入检查得分"></el-input>
          </el-form-item>
          <el-form-item label="是否通报：" prop="whetherNotify">
            <el-select v-model="state.form.whetherNotify" placeholder="请选择">
              <el-option v-for="item in whetherNotifyList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="检查情况：">
            <el-input type="textarea" :rows="2" v-model="state.form.checkRemark" placeholder="请输入检查情况"></el-input>
          </el-form-item>
        </el-form>
      </el-row>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose" size="mini">取消</el-button>
        <el-button type="primary" @click="submit" size="mini">确认</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { reactive } from "@vue/reactivity";
import { ref, watch } from 'vue'
import { useDict } from "@/hooks/useDict"
import { dateStr } from "@/utils/date.js"
import shuttleData from "@/components/shuttleData"
import {
  getRoomList,
  batchSelectInfo,
  batchSelectedInfo,
  batchSelectedOp,
  batchAddHygieneCheck,
} from "@/applications/eccard-dorm/api";
import linkageSelect from '@/applications/eccard-dorm/components/linkageSelect'
import {
  ElDialog,
  ElRow,
  ElButton,
  ElDivider,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElDatePicker,
  ElMessageBox,
  ElMessage
} from "element-plus";
const titleData = {
  oneTitle: "已选择房间",
  twoTitle: "未选择房间"
}
const columnData = {
  oneList: [
    { label: "区域", prop: "areaName" },
    { label: "房间", prop: "roomString" },
  ],
  twoList: [
    { label: "区域", prop: "areaName" },
    { label: "房间", prop: "roomString" },
  ],
}
const delData = {
  label: "移除",
  isLeft: true
}
const linkageData1 = {
  area: { label: '区域', valueKey: 'areaId', key: 'id' },
  building: { label: '楼栋', valueKey: 'buildId' },
  unit: { label: '单元', valueKey: 'unitNum' },
  room: { label: '房间', valueKey: 'roomId' }
}
const linkageData2 = {
  area: { label: '区域', valueKey: "areaId", key: "id" },
  building: { label: '楼栋', valueKey: "buildId" },
  unit: { label: '单元', valueKey: "unitNum" },
  room: { label: '房间', valueKey: "roomId" },
}
const rules = {
  checkTime: [{ required: true, message: "请选择检查日期", trigger: "blur" }],
  checkPerson: [{ required: true, message: "请输入检查人", trigger: 'blur' }],
  checkResult: [{ required: true, message: "请选择检查结果", trigger: "blur" }],
  checkScore: [{ required: true, message: "请输入检查得分", trigger: "blur" }, { pattern:  /^([0-9]|10)$/, message: "检查得分值范围为0~10" }],
  whetherNotify: [{ required: true, message: "请选择是否通报", trigger: "blur" }]
};
export default {
  props: {
    modelValue: {
      type: Boolean,
      default: false,
    },
  },
  components: {
    ElDialog,
    ElRow,
    ElButton,
    ElDivider,
    ElForm,
    ElFormItem,
    ElInput,
    ElSelect,
    ElOption,
    ElDatePicker,
    "kade-shuttle-data": shuttleData,
    "kade-linkage-select": linkageSelect,
  },
  setup(props, context) {
    const formRef = ref(null);
    const whetherNotifyList = useDict('SYS_BOOL_STRING')
    const checkResultList = useDict('DORM_DAILY_CHECK_RESULT')
    const state = reactive({
      loading: false,
      form: {},
      form1: {
        currentPage: 1,
        pageSize: 10
      },
      form2: {
        currentPage: 1,
        pageSize: 10
      },
      listData: {
        oneList: [],
        oneTotal: 0,
        twoList: [],
        twoTotal: 0,
      }
    });
    watch(() => props.modelValue, val => {
      if (val) {
        state.form1 = {
          currentPage: 1,
          pageSize: 10
        }
        state.form2 = {
          currentPage: 1,
          pageSize: 10
        }
        state.listData = {
          oneList: [],
          oneTotal: 0,
          twoList: [],
          twoTotal: 0,
        }
        getList1()
        getList2()
      }
    })
    const getList1 = async () => {
      state.loading = true
      try {
        let { data: { page: { dataList, totalCount } } } = await batchSelectedInfo(state.form1)
        state.listData.oneList = dataList
        state.listData.oneTotal = totalCount
        state.loading = false
      }
      catch {
        state.loading = false
      }
    }
    const getList2 = async () => {
      state.loading = true
      try {
        let { data: { page: { dataList, totalCount }, projectNo } } = await batchSelectInfo(state.form2)
        state.listData.twoList = dataList
        state.listData.twoTotal = totalCount
        state.form1.projectNo = projectNo
        state.form2.projectNo = projectNo
        state.loading = false
      }
      catch {
        state.loading = false
      }
    }
    const move = async (params) => {
      let { code } = await batchSelectedOp(params)
      if (code === 0) {
        getList1()
        getList2()
      }
    }
    const toAllRight = () => {
      let params = {
        allSelect: true,
        op: "remove",
        projectNo: state.form1.projectNo
      }
      move(params)
    }
    const toRight = (val) => {
      let params = {
        allSelect: false,
        infoList: val,
        op: "remove",
        projectNo: state.form1.projectNo
      }
      move(params)
    }
    const toLeft = (val) => {
      let params = {
        allSelect: false,
        infoList: val,
        op: "add",
        projectNo: state.form1.projectNo
      }
      move(params)
    }
    const toAllLeft = () => {
      let params = {
        allSelect: true,
        op: "add",
        projectNo: state.form1.projectNo
      }
      move(params)
    }
    const oneCurrentChange = (val) => {
      state.form1.currentPage = val
      getList1()
    }
    const twoCurrentChange = (val) => {
      state.form2.currentPage = val
      getList2()
    }
    const handleClose = () => {
      context.emit("update:modelValue", false);
    };
    const linkageChange1 = (val) => {
      state.form1 = { ...state.form1, ...val }
    }
    const linkageChange2 = (val) => {
      state.form2 = { ...state.form2, ...val }
    }
    const search = () => {
      getList2()
    }
    const handleSearch = () => {
      getList1()
    }
    const handleClear = () => {
      state.form1 = {
        currentPage: 1,
        pageSize: 10
      }
      // getList1()
    }
    const handleDel = (row) => {
      ElMessageBox.confirm("确认移除?", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        let params = {
          allSelect: false,
          infoList: [row],
          op: "remove",
          projectNo: state.form1.projectNo
        }
        move(params)
      });
    }
    const submit = () => {
      if (!state.listData.oneList.length) {
        return ElMessage.error("请先选择需要添加卫生检查的房间！")
      }
      formRef.value.validate(async valid => {
        if (valid) {
          state.loading = true
          let params = { ...state.form }
          params.checkTime = dateStr(params.checkTime)
          params.projectNo = state.form1.projectNo
          try {

            let { code, message } = await batchAddHygieneCheck(params)
            if (code === 0) {
              ElMessage.success(message)
              context.emit("update:modelValue", true);
            }
            state.loading = false
          }
          catch {
            state.loading = false
          }
        }
      })
    };
    return {
      titleData,
      columnData,
      delData,
      getRoomList,
      handleDel,
      rules,
      formRef,
      linkageData1,
      linkageData2,
      state,
      handleClose,
      search,
      handleSearch,
      handleClear,
      oneCurrentChange,
      twoCurrentChange,
      submit,
      linkageChange1,
      linkageChange2,
      whetherNotifyList,
      checkResultList,
      toAllRight,
      toRight,
      toLeft,
      toAllLeft
    };
  },
};
</script>

<style lang="scss" scoped>
.table {
  width: 100%;
  margin: 10px 15px 30px 10px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;

  .title {
    margin: 10px;
  }

  :deep(.selected-room) {
    border: 1px solid #eeeeee;
    border-radius: 5px;

    .el-form {
      .el-form-item__label {
        width: 60px !important;
      }

      .el-select,
      .el-input__inner {
        width: 140px !important;
      }

      .el-input,
      .el-input__inner {
        width: 140px !important;
      }
    }
  }

  .blue :hover {
    text-decoration: underline;
  }

  :deep(.unSelected-room) {
    width: 100%;
    border: 1px solid #eeeeee;
    border-radius: 5px;

    .el-form {
      .el-form-item__label {
        width: 60px !important;
      }

      .el-select,
      .el-input__inner {
        width: 140px !important;
      }

      .el-input,
      .el-input__inner {
        width: 140px !important;
      }
    }
  }
}

.el-rows {
  border: 1px solid #eeeeee;

  .title {
    margin: 10px 10px 0 10px;
  }
}

:deep(.el-pagination .el-select .el-input .el-input__inner) {
  width: 100px !important;
}

:deep(.el-pagination__editor.el-input .el-input__inner) {
  width: 46px !important;
}

:deep(.el-textarea__inner) {
  width: 240px;
}

:deep([data-v-38e88c0c] .el-date-editor.el-input) {
  width: 198px;
}
</style>