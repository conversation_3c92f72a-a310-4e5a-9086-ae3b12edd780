<template>
  <kade-tab-wrap :tabs="tabs1" v-model="state.tab1">
    <template #jbcs>
      <kade-tab-wrap :tabs="tabs2" v-model="state.tab2" v-if="state.isParams">
        <template #basicParam>
          <basic-param></basic-param>
        </template>
        <template #periodParam>
          <period-param></period-param>
        </template>
        <template #cardAuthParam>
          <card-auth-param></card-auth-param>
        </template>
        <template #ticketPrintParam>
          <ticket-print-param></ticket-print-param>
        </template>
        <template #styleSetting>
          <style-set-params></style-set-params>
        </template>
      </kade-tab-wrap>
      <el-empty v-else description="当前设备暂未绑定参数"></el-empty>
    </template>
    <template #zdycs>
      <el-tabs v-if="state.tabs.length" v-model="state.tab" class="demo-tabs" @tab-click="handleClick">
        <el-tab-pane v-for="(item, index) in state.tabs" :key="index" :label="item.paramsName" :name="item.id">
          <el-form label-width="120px" size="mini" inline>
            <el-form-item :label="v.paramsName" v-for="(v, i) in item.children" :key="i">
              <el-input readonly v-model="state.form[v.paramsCode]"></el-input>
            </el-form-item>
          </el-form>
        </el-tab-pane>
      </el-tabs>
      <el-empty v-else description="当前设备暂未绑定自定义参数"></el-empty>
    </template>
  </kade-tab-wrap>



</template>
<script>
import { ElEmpty, ElTabs, ElTabPane, ElForm, ElFormItem, ElInput } from "element-plus";
import basicParam from "./paramDetails/basicParam";
import cardAuthParam from "./paramDetails/cardAuthParam";
import periodParam from "./paramDetails/periodParam";
import ticketPrintParam from "./paramDetails/ticketPrintParam";
import styleSetParams from "./paramDetails/styleSetParams";
import { onMounted, reactive } from "vue";
import { useStore } from "vuex";
import { makeTree } from "@/utils"
import { getdeviceParamDetail } from "@/applications/eccard-iot/api";
const tabs2 = [
  {
    name: "basicParam",
    label: "基本参数",
  },
  {
    name: "periodParam",
    label: "时段参数",
  },
  {
    name: "cardAuthParam",
    label: "卡类权限参数",
  },

  {
    name: "ticketPrintParam",
    label: "票据打印参数",
  },
  {
    name: "styleSetting",
    label: "样式设置",
  },
];
const tabs1 = [
  {
    name: "jbcs",
    label: "基本参数",
  },
  {
    name: "zdycs",
    label: "自定义参数",
  },
];
export default {
  name: "paramInfo",
  components: {
    ElEmpty, ElTabs, ElTabPane, ElForm, ElFormItem, ElInput,
    basicParam: basicParam,
    periodParam: periodParam,
    cardAuthParam: cardAuthParam,
    ticketPrintParam: ticketPrintParam,
    styleSetParams: styleSetParams,
  },
  setup() {
    const store = useStore();
    const state = reactive({
      //参数设置弹出框
      tab2: "basicParam",
      tab1: "jbcs",
      tabs: [],
      tab: '',
      isParams: false,
    });
    const handleClick = (val) => {
      state.form = {}
      let a = state.tabs.find(item => item.id == val.props.name)
      state.tabData = a
      let list = JSON.parse(sessionStorage.getItem("customParamsList"))
      let listCutItem = list.find(item => item.deviceType == '智能消费终端')
      console.log(listCutItem, a);
      if (listCutItem && listCutItem.paramsData) {
        state.form = { ...listCutItem.paramsData[a.paramsCode] }
      } else {
        state.tabs = []
      }
    }
    const getParamsDetails = async () => {
      let { data } = await getdeviceParamDetail(store.state.deviceData.deviceDetail.paramId);
      store.commit("deviceData/updateState", {
        key: "detailsParams",
        payload: data
      });
    };
    onMounted(() => {
      if (store.state.deviceData.deviceDetail.paramId) {
        state.isParams = true
        getParamsDetails()
      } else {
        state.isParams = false
      }
      let data = JSON.parse(sessionStorage.getItem('templateList')).find(item => item.deviceType == '智能消费终端')
      console.log(data)
      if (data && data.paramsList) {
        state.tabs = makeTree(data.paramsList, "id", "parentId", "children");
      } else {
        state.tabs = []
      }
      if (state.tabs.length) {
        state.tab = state.tabs[0].id
      }
      handleClick({
        props: {
          name: state.tab
        }
      })


    })
    return {
      state,
      tabs1,
      tabs2,
      handleClick
    };
  },
};
</script>

<style scoped>
</style>
