import {

} from "@/applications/eccard-finance/api";
import { requestDate } from "@/utils/reqDefaultDate.js";
const state = {
  selectRow: '',
  isAdd: false,
  isEdit: false,
  isDetailsList: false,//人员清单
  isDetails: false,
  isImportReversal: false,
  roleList: [],
  departCheckList: [],
  cardTypeList: [],
  exportParam: {
    beginDate: requestDate()[0],
    endDate: requestDate()[1],
    currentPage: 1,
    pageSize: 6,
    auditStatus: null,
    projectName: null,
    walletCode: null,
    tenantId: null,
  },
};
const mutations = {
  updateState(state, { key, payload }) {
    state[key] = payload;
  },
};
const actions = {
};
export default {
  namespaced: true,
  state,
  mutations,
  actions
}