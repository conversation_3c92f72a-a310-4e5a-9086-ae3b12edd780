<template>
  <el-dialog :model-value="modelValue" :title="rowData.id ? '编辑标签' : '新增标签'" width="500px" :before-close="handleClose">
    <el-form label-width="100px" size="mini" ref="formRef" :model="state.form" :rules="rules">
      <!--       <el-form-item label="标签编号：" prop="buildTypeName">
        <el-input v-model="state.form.buildTypeName" placeholder="请输入标签编号" maxlength="20"></el-input>
      </el-form-item> -->
      <el-form-item label="标签名称：" prop="labelName">
        <el-input v-model="state.form.labelName" placeholder="请输入标签名称" maxlength="20"></el-input>
      </el-form-item>
      <!--       <el-form-item label="启用状态：" prop="useStatus">
        <el-select v-model="state.form.useStatus" clearable placeholder="全部">
          <el-option v-for="(item, index) in statusList" :key="index" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="备注：">
        <el-input v-model="state.form.remarks" type="textarea" maxlength="200" placeholder="请输入备注"></el-input>
      </el-form-item> -->
    </el-form>
    <div style="height:1px"></div>
    <template #footer>
      <span class="dialog-footer">
        <el-button type="primary" @click="handleSave" size="mini">保存</el-button>
        <el-button @click="handleClose" size="mini">返回</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { ElDialog, ElButton, ElForm, ElFormItem, ElInput, ElMessage,/*  ElSelect, ElOption  */ } from "element-plus"
import { reactive, ref, watch, nextTick } from 'vue'
import { useDict } from "@/hooks/useDict.js";
import { saveLabel, } from "@/applications/eccard-supermarket/api.js";

export default {
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    rowData: {
      type: Object,
      default: null
    },
  },
  components: {
    ElDialog,
    ElButton,
    ElForm,
    ElFormItem,
    ElInput,
    /*     ElSelect, ElOption */
  },
  setup(props, context) {
    const statusList = useDict("SYS_ENABLE")
    const formRef = ref(null)
    const state = reactive({
      form: {}
    });
    watch(() => props.modelValue, val => {
      if (val) {
        if (!props.rowData.id) {
          state.form = {}
        } else {
          state.form = { ...props.rowData }
        }
        nextTick(() => {
          formRef.value.clearValidate()
        })
      }
    })
    const rules = {
      labelName: [
        {
          required: true,
          message: "请输入标签名称",
          trigger: "blur",
        },
      ],
    }
    const handleSave = () => {
      formRef.value.validate(async valid => {
        if (valid) {
          try {
            state.loading = true
            let { code, message } = await saveLabel(state.form)
            if (code === 0) {
              ElMessage.success(message)
              context.emit("update:modelValue", true)
            }
            state.loading = false
          }
          catch {
            state.loading = false
          }
        } else {
          return false
        }
      })
    }
    const handleClose = () => {
      context.emit("update:modelValue", false)
    };
    return {
      statusList,
      formRef,
      state,
      rules,
      handleSave,
      handleClose,
    }
  }
}
</script>

<style lang="scss" scoped>
.el-form {
  margin-top: 20px;
}
</style>