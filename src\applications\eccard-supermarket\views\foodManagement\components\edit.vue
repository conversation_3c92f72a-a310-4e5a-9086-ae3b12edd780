<template>
  <el-dialog v-loading="state.loading" :model-value="modelValue" :title="title(editType)" width="1100px"
    :before-close="handleClose">
    <el-form inline label-width="100px" size="mini" ref="formRef" :model="state.form"
      :rules="editType !== 'details' && rules">
      <el-card header="菜品信息">
        <el-form-item label="菜品名称：" prop="dishName">
          <el-input v-if="editType == 'details'" :model-value="state.form.dishName" readonly></el-input>
          <el-input v-else v-model="state.form.dishName" placeholder="请输入菜品名称"></el-input>
        </el-form-item>
        <el-form-item label="菜品类型：" prop="dishType">
          <el-input v-if="editType == 'details'" :model-value="dictionaryFilter(state.form.dishType)" readonly>
          </el-input>
          <el-select v-else v-model="state.form.dishType" clearable placeholder="全部" :disabled="editType==='edit'">
            <el-option v-for="(item, index) in dishTypeList" :key="index" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="菜品标签：" prop="labelEntityList">
          <el-input v-if="editType == 'details'"
            :model-value="state.form.labelEntityList&&state.form.labelEntityList.map(item=>item.labelName).join('、')"
            readonly>
          </el-input>
          <el-select v-else v-model="state.form.labelEntityList" value-key="id" collapse-tags-tooltip collapse-tags
            multiple clearable placeholder="全部">
            <el-option v-for="(item, index) in labelList" :key="index" :label="item.labelName" :value="item">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="单位：" prop="dishUnit">
          <el-input v-if="editType == 'details'" :model-value="state.form.dishUnit" readonly></el-input>
          <el-input v-else v-model="state.form.dishUnit" placeholder="请输入单位"></el-input>
        </el-form-item>
        <el-form-item label="价格：" prop="dishPrice">
          <el-input v-if="editType == 'details'" :model-value="state.form.dishPrice" readonly></el-input>
          <el-input v-else v-model="state.form.dishPrice" placeholder="请输入价格"></el-input>
        </el-form-item>
        <el-form-item label="菜品图片：">
          <el-image v-if="editType == 'details'" style="width: 100px; height: 100px" :src="state.form.dishImageUrl"
            :preview-src-list="[state.form.dishImageUrl]" :initial-index="0" fit="cover"></el-image>
          <kade-single-image-upload v-else v-model="state.form.dishImageUrl" :action="uploadApplyLogo"
            icon="el-icon-plus" />
        </el-form-item>
      </el-card>
      <div style="height:1px"></div>
      <el-card header="套餐菜品选择列表" v-if="state.form.dishType==='DISH_PACKAGE'">
        <el-table v-if="editType == 'details'" style="width: 100%" :data="state.form.dishEntityList" v-loading="false"
          highlight-current-row border stripe @row-click="rowClick" @selection-change="selectionChange">
          <el-table-column label="菜品名称" prop="dishName" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column label="价格" prop="dishPrice" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column label="单位" prop="dishUnit" align="center" show-overflow-tooltip></el-table-column>
          
          <el-table-column label="菜品图片" prop="dishImageUrl" align="center" show-overflow-tooltip>
            <template #default="scope">
              <el-image style="width: 50px; height: 50px" :src="scope.row.dishImageUrl"
                :preview-src-list="[scope.row.dishImageUrl]" fit="cover" />
            </template>
          </el-table-column>
        </el-table>
        <kade-select-table v-else :isShow="state.isSelectFood" :value='state.form.dishEntityList' :reqFnc="dishListAPI"
          :selectCondition="selectCondition" :column="column" :params="params" :isCurrentSelect="true"
          @change="foodChange" />
      </el-card>
    </el-form>
    <template #footer v-if="editType !== 'details'">
      <span class="dialog-footer">
        <el-button type="primary" @click="handleSave" size="mini">保存</el-button>
        <el-button @click="handleClose" size="mini">返回</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { ElDialog, ElButton, ElCard, ElImage, ElForm, ElFormItem, ElInput, ElSelect, ElOption, ElTable, ElTableColumn, ElMessage } from "element-plus"
import { reactive, ref, nextTick } from 'vue'
import { useDict } from "@/hooks/useDict.js";
import { uploadApplyLogo } from "@/applications/unified_portal/api";
import { saveDish, dishList, labelList, dishDetails } from "@/applications/eccard-supermarket/api.js";

import SingleImageUpload from "@/components/singleImageUpload";
import selectTable from "@/components/table/selectTable.vue";

import { onMounted, watch } from '@vue/runtime-core';
const column = [
  { label: "菜品名称", prop: "dishName", isDict: false, width: "" },
  { label: "单位", prop: "dishUnit", isDict: false, width: "" },
  { label: "价格", prop: "dishPrice", isDict: false, width: "" },
];
const params = {
  currentPageKey: "pageNum",
  pageSizeKey: "pageSize",
  resListKey: "list",
  resTotalKey: "total",
  value: {
    dishType: "DISH_SINGLE"
  },
  tagNameKey: "dishName",
  valueKey: "id",
}
export default {
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    selectRow: {
      types: Object,
      default: null
    },
    editType: {
      types: String,
      default: ''
    },
    labelList: {
      type: Array,
      default: null
    }
  },
  components: {
    ElDialog,
    ElButton,
    ElCard,
    ElForm,
    ElFormItem,
    ElInput,
    ElSelect,
    ElOption,
    ElImage,
    ElTable, ElTableColumn,
    "kade-single-image-upload": SingleImageUpload,
    "kade-select-table": selectTable,
  },
  setup(props, context) {
    const dishTypeList = useDict("SYS_DISH_TYPE")
    const formRef = ref(null)
    const state = reactive({
      loading: false,
      isSelectFood: false,
      form: {}
    });
    watch(() => props.modelValue, async val => {
      if (val) {
        if (props.editType == 'add') {
          state.form = {
            dishEntityList: []
          }
        } else {
          let { data } = await dishDetails({ dishId: props.selectRow.id })
          state.form = JSON.parse(JSON.stringify(data))
          console.log(state.form.dishEntityList);
          if (props.editType == 'edit') {
            state.isSelectFood = true

          }
        }
        nextTick(() => {
          formRef.value.clearValidate()
        })
      }
    })
    const selectCondition = [
      {
        label: "菜品标签", valueKey: "labelEntityList", placeholder: "请选择", isSelect: true,
        select: {
          isMultiple: true,
          isValueObj: true,
          list: props.labelList,
          option: {
            label: "labelName",
            value: "id",
          },
        },
      },
    ]
    const title = (val) => {
      if (val == 'add') {
        return '新建菜品'
      } else if (val == 'edit') {
        return '编辑菜品'
      } else if (val == 'details') {
        return '菜品详情'
      }
    }
    const rules = {
      dishName: [
        {
          required: true,
          message: "请输入菜品名称",
        },
        {
          max: 20,
          message: "菜品名称长度不能超过20字符",
        },
        {
          pattern: /^\S.*\S$|(^\S{0,1}\S$)/,
          message: "菜品名称首尾不能包含空格",
        },
      ],
      dishType: [
        {
          required: true,
          message: "请选择菜品类型",
          trigger: "change",
        },
      ],
      dishUnit: [
        {
          required: true,
          message: "请输入菜品单位",
        },
      ],
      labelEntityList: [
        {
          required: true,
          message: "请选择菜品标签",
          trigger: "change",
        },
      ],
      dishPrice: [
        {
          required: true,
          message: "请输入菜品价格",
        },
        {
          pattern: /^0\.([1-9]|\d[1-9])$|^[1-9]\d{0,8}\.\d{0,2}$|^[1-9]\d{0,8}$/,
          message: '菜品价格在必须大于0且最多保存两位小数',
          trigger: 'blur'
        }
      ],
    }
    const getLabelList = async () => {
      let { data: { list } } = await labelList({})
      selectCondition[0].select.list = list
    }
    const foodChange = (val) => {
      console.log(123123);
      state.form.dishEntityList = val.list
    }
    const handleSave = () => {
      formRef.value.validate(async valid => {
        if (valid) {
          if (state.form.dishType === 'DISH_PACKAGE') {
            if (!(state.form.dishEntityList && state.form.dishEntityList.length)) {
              return ElMessage.error("请选择套餐菜品！")
            }
          } else {
            delete state.form.dishEntityList
          }
          state.loading = true
          console.log(state.form);
          try {
            let { code, message } = await saveDish(state.form)
            if (code === 0) {
              ElMessage.success(message)
              context.emit("update:modelValue", true)
              state.isSelectFood = false
            }
            state.loading = false
          }
          catch {
            state.loading = false
          }
        } else {
          return false
        }
      })
    }
    onMounted(() => {
      getLabelList()
    })
    const handleClose = () => {
      context.emit("update:modelValue", false)
      state.isSelectFood = false
    };
    return {
      dishListAPI: dishList,
      uploadApplyLogo,
      column,
      params,
      selectCondition,
      dishTypeList,
      formRef,
      state,
      title,
      rules,
      foodChange,
      handleSave,
      handleClose,
    }
  }
}
</script>

<style lang="scss" scoped>
.el-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.el-form {
  margin-top: 20px;
}

/* :deep(.el-input-number) {
  width: 192px;
}

:deep(.el-input) {
  width: 192px;
} */
:deep(.el-select) {
  width: 178px;
}


:deep(.single-image-uploader) {
  width: 100px;
  height: 100px;

  .el-upload {
    width: 100px;
    height: 100px;
  }

  .images {
    width: 100px;
    height: 100px;
  }

  .image-slot {
    width: 100px;
    height: 100px;
  }

  .element-icons {
    font-size: 40px !important;
  }
}
</style>