<template>
  <div>
    <el-dialog :model-value="dialogVisible" :title="state.isEdit ? '编辑补助发放' : '新增补助发放'" width="90%"
      :before-close="beforeClose" :close-on-click-modal="false">
      <div>
        <div class="padding-form-box">
          <el-form inline size="small" ref="ruleForm" :model="state.form" :rules="state.rules" label-width="120px">
            <el-form-item label="补助类型:" prop="subsidyType">
              <el-select clearable v-model="state.form.subsidyType" placeholder="请选择">
                <el-option v-for="(item, index) in subsidyTypeList" :key="index" :label="item.stName"
                  :value="item.stId">
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="项目名称:" prop="projectName">
              <el-input placeholder="请输入" v-model="state.form.projectName"></el-input>
            </el-form-item>

            <el-form-item label="交易类型:" prop="costType">
              <el-select disabled v-model="state.form.costType" placeholder="请选择">
                <el-option v-for="(item, index) in state.tradeTypeList" :key="index" :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="所属年月:" prop="subsidyMonth">
              <el-date-picker v-model="state.form.subsidyMonth" type="month" placeholder="请选择日期" @change="changeDate">
              </el-date-picker>
            </el-form-item>
          </el-form>
          <el-form size="small" label-width="120px">
            <el-form-item label="发放说明:">
              <el-input placeholder="请输入" type="textarea" v-model="state.form.grantRemark"></el-input>
            </el-form-item>
            <el-form-item label="发放方式:" prop="grantType">
              <el-radio-group v-model="state.grantType">
                <el-radio label="SYSTEM">发放清单</el-radio>
                <el-radio label="IMPORT">导入清单</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-form>

          <el-form inline v-show="state.grantType == 'SYSTEM'" ref="generateSubsidy" :model="state.generateSubsidyForm"
            :rules="state.generateSubsidyRule" size="small" label-width="120px">
            <el-form-item label=" 身份类别:" prop="userRole">
              <el-select v-model="state.generateSubsidyForm.userRole" placeholder="请选择">
                <el-option v-for="(item, index) in roleList" :key="index" :label="item.roleName" :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="生效时间:" prop="takeEffectTime">
              <el-date-picker v-model="state.generateSubsidyForm.takeEffectTime" type="date" placeholder="请选择日期"
                @change="changeDate">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="每人补助金额:" prop="subsidyAmount">
              <el-input placeholder="请输入" v-model="state.generateSubsidyForm.subsidyAmount"></el-input>
            </el-form-item>
            <el-form-item label="有效期至:" prop="invalidTime">
              <el-date-picker v-model="state.generateSubsidyForm.invalidTime" type="date" placeholder="请选择日期"
                @change="changeDate">
              </el-date-picker>
            </el-form-item>
          </el-form>
          <div class="select-input-lang" v-if="state.grantType == 'SYSTEM'">
            <el-form inline size="small" label-width="120px">
              <el-form-item label=" 组织机构:">
                <kade-dept-select-tree style="width: 100%" :value="state.generateSubsidyForm.deptPaths"
                  valueKey="deptPath" :multiple="true" @valueChange="
                    (val) => (state.generateSubsidyForm.deptPaths = val)
                  " />
              </el-form-item>

              <el-button size="small" type="primary" @click="defineGenerateListClick()">确认生成清单</el-button>
              <el-button size="small" @click="state.generateSubsidyForm = {}">重置</el-button>
            </el-form>
          </div>
          <el-form v-show="state.grantType == 'IMPORT'" inline size="small" label-width="100px">
            <el-form-item label=" 上传文件:">
              <el-upload class="upload-demo" :action="state.requestUrl"
                :headers="{ Authorization: `bearer ${state.requestHeader}` }" :on-success="handleSuccess"
                :on-remove="handleRemove" :limit="1" :file-list="fileList" ref="uploadDom">
                <el-button size="small" type="primary">上传充值清单</el-button>
              </el-upload>
            </el-form-item>
            <el-form-item>
              <div class="blue" @click="uploadSample()">下载样例</div>
            </el-form-item>
          </el-form>
          <div v-if="state.abnormalData > 0">
            <span class="red"> • </span>异常数据
            <span class="red"> {{ state.abnormalData }}</span> 条，请修改确认
          </div>
<!--           <el-table v-show="state.grantType == 'SYSTEM'" style="width: 100%" :data="state.WaitSubsidyList"
            v-loading="state.WaitSubsidyLoading" border stripe>
            <el-table-column label="校检结果" prop="verifyResult" align="center">
              <template #default="scope">
                <div :style="
                  scope.row.verifyResult != '成功' && { 'color': '#f00' }
                ">
                  {{ scope.row.verifyResult }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="用户编号" prop="userCode" align="center"></el-table-column>
            <el-table-column label="姓名" prop="userName" align="center"></el-table-column>
            <el-table-column label="组织机构" prop="deptName" align="center"></el-table-column>
            <el-table-column label="身份类别" prop="roleName" align="center"></el-table-column>
            <el-table-column label="操作" prop="userName" align="center">
              <template #default="scope">
                <div style="color: #1abc9c" @click="delClick(scope.row, scope.$index)">
                  删除
                </div>
              </template>
            </el-table-column>
          </el-table> -->
          <el-table style="width: 100%" :data="state.WaitSubsidyList"
            v-loading="state.WaitSubsidyLoading" border stripe>
            <el-table-column show-overflow-tooltip label="校检结果" prop="verifyResult" align="center">
              <template #default="scope">
                <div :style="
                  scope.row.verifyResult != '成功' && { 'color': '#f00' }
                ">
                  {{ scope.row.verifyResult }}
                </div>
              </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip label="用户编号" prop="userCode" align="center"></el-table-column>
            <el-table-column show-overflow-tooltip label="姓名" prop="userName" align="center"></el-table-column>
            <el-table-column show-overflow-tooltip label="组织机构" prop="deptName" align="center"></el-table-column>
            <el-table-column show-overflow-tooltip label="身份类别" prop="roleName" align="center"></el-table-column>
            <el-table-column show-overflow-tooltip label="生效时间" prop="takeEffectTime" align="center"></el-table-column>
            <el-table-column show-overflow-tooltip label="发放金额（元）" prop="subsidyAmount" align="center"></el-table-column>
            <el-table-column show-overflow-tooltip label="有效期至" prop="invalidTime" align="center"></el-table-column>
            <el-table-column show-overflow-tooltip label="操作" prop="userName" align="center">
              <template #default="scope">
                <!-- <span @click="delClick(scope.row)" style="margin-right: 10px">编辑</span> -->
                <el-button @click="delClick(scope.row)" type="text" size="mini">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <div class="pagination-box" v-if="state.grantType">
            <div>
              <span>合计：<span>{{ state.totalCount }}</span>人，<span>{{ state.totalAmount }}</span>元</span>
            </div>
            <div class="pagination">
              <el-pagination background :current-page="state.WaitSubsidyForm.currentPage"
                :page-size="state.WaitSubsidyForm.pageSize" layout="total, sizes, prev, pager, next, jumper"
                :page-sizes="[6, 10, 20, 50, 100]" :total="state.WaitSubsidyListTotal"
                @current-change="handlePageChange" @size-change="handleSizeChange">
              </el-pagination>
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="beforeClose()" size="mini">关&nbsp;&nbsp;闭</el-button>
          <el-button @click="submitForm()" size="mini" type="primary">确&nbsp;&nbsp;认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script>
import { computed, onMounted, reactive, ref, watch } from "vue";
import { useStore } from "vuex";
import { getToken } from "@/utils";
import { timeStr } from "@/utils/date.js";
import {
  generateSubsidyList,
  saveImportSubsidyList,
  getWaitSubsidyListByPage,
  deleteWaitSubsidy,
  exportSubsidyList,
} from "@/applications/eccard-finance/api";
import deptSelectTree from "@/components/tree/deptSelectTree.vue";

import {
  ElDialog,
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElTable,
  ElRadio,
  ElRadioGroup,
  ElTableColumn,
  ElPagination,
  ElUpload,
  ElDatePicker,
  ElMessage,
} from "element-plus";
export default {
  components: {
    "el-dialog": ElDialog,
    "el-button": ElButton,
    "el-input": ElInput,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    "el-select": ElSelect,
    "el-option": ElOption,
    "el-table": ElTable,
    "el-table-column": ElTableColumn,
    "el-pagination": ElPagination,
    ElRadio,
    ElRadioGroup,
    ElUpload,
    ElDatePicker,
    "kade-dept-select-tree": deptSelectTree,

  },
  props: {
    dialogVisible: {
      types: Boolean,
      default: false,
    },
  },
  setup(props, context) {
    const uploadDom = ref(null);
    const store = useStore();
    let generateSubsidy = ref(null);
    let ruleForm = ref(null);
    const state = reactive({
      isEdit: false,
      WaitSubsidyLoading: false,
      grantType: "SYSTEM",
      form: {
        costType: 101
      },
      generateSubsidyForm: {},
      rules: {
        subsidyType: [
          {
            required: true,
            message: "请选择补助类型",
            trigger: "change",
          },
        ],
        projectName: [
          {
            required: true,
            message: "请输入项目名称",
            trigger: "blur",
          },
        ],
        costType: [
          {
            required: true,
            message: "请选择交易类型",
            trigger: "change",
          },
        ],
        subsidyMonth: [
          {
            required: true,
            message: "请选择所属年月",
            trigger: "change",
          },
        ],
        grantType: [
          {
            required: true,
            message: "请选择发放方式",
            trigger: "change",
          },
        ],
      },
      generateSubsidyRule: {
        userRole: [
          {
            required: true,
            message: "请选择身份类别",
            trigger: "change",
          },
        ],
        takeEffectTime: [
          {
            required: true,
            message: "请选择生效时间",
            trigger: "change",
          },
        ],
        subsidyAmount: [
          {
            required: true,
            message: "请输入补助金额",
            trigger: "blur",
          },
        ],
        invalidTime: [
          {
            required: true,
            message: "请选择到期时间",
            trigger: "change",
          },
        ],
      },
      WaitSubsidyForm: {
        currentPage: 1,
        pageSize: 6,
      },
      totalAmount: 0,
      totalCount: 0,
      abnormalData: 0,
      WaitSubsidyList: [],
      WaitSubsidyListTotal: 0,
      requestHeader: getToken(),
      requestUrl: `${CONFIG.BASE_API_PATH}eccard-finance/subsidyRecord/importSubsidyList`,
      generateListForm: {
        deptPaths: [],
        rechargeAmount: 0,
        userRole: 0,
        cardTypes: [],
      },
      roleList: [],
      departCheckList: [],
      subsidyTypeList: [],
      tradeTypeList: [
        { label: "充值", value: 101 },
      ],
    });

    const subsidyTypeList = computed(() => {
      return store.state.subsidyData.subsidyTypeList;
    });
    const roleList = computed(() => {
      return store.state.subsidyData.roleList;
    });
    const departCheckList = computed(() => {
      return store.state.subsidyData.departCheckList;
    });
    watch(
      () => props.dialogVisible,
      (val) => {
        if (!val) {
          state.form = {};
          state.grantType = "SYSTEM";
          state.WaitSubsidyForm = {
            currentPage: 1,
            pageSize: 6,
          };
          state.WaitSubsidyList = [];
          console.log(ruleForm.value.clearValidate);
          ruleForm.value.resetFields();
          uploadDom.value.clearFiles();
          state.WaitSubsidyListTotal = 0;
          state.totalCount = 0;
          state.totalAmount = 0;
          state.abnormalData = 0;
        }
      }
    );

    watch(
      () => state.grantType,
      () => {
        state.WaitSubsidyList = [];
        state.generateSubsidyForm = {};
        state.WaitSubsidyForm = {
          currentPage: 1,
          pageSize: 6,
        };
        uploadDom.value.clearFiles();
        state.WaitSubsidyListTotal = 0;
        state.totalCount = 0;
        state.totalAmount = 0;
        state.abnormalData = 0;
      }
    );

    const cascaderChange = (val) => {
      console.log(val);
      console.log(state.generateSubsidyForm.deptPaths);
      if (val.length) {
        let arr = val.map(item => {
          return item.length && item[item.length - 1]
        })
        state.generateSubsidyForm.deptPaths = arr
      } else {
        state.generateSubsidyForm.deptPaths = []
      }
    };

    const handleSuccess = (res) => {
      let { totalCount, totalAmount, errorCount } = res.data;
      state.WaitSubsidyForm.projectNo = res.data.projectNo;
      state.form.projectNo = res.data.projectNo;
      state.totalCount = totalCount;
      state.totalAmount = totalAmount;
      state.abnormalData = errorCount;
      queryWaitSubsidyListByPage();
    };

    const handleRemove = () => {
      state.WaitSubsidyList = [];
      state.WaitSubsidyListTotal = 0;

      state.WaitSubsidyListTotal = 0;
      state.totalCount = 0;
      state.totalAmount = 0;
      state.abnormalData = 0;
    };

    //生成名单编号
    const defineGenerateListClick = async () => {
      generateSubsidy.value.validate((valid) => {
        if (valid) {
          let params = { ...state.generateSubsidyForm }
          params.takeEffectTime = timeStr(params.takeEffectTime);
          params.invalidTime = timeStr(params.invalidTime);
          params.subsidyAmount = Number(params.subsidyAmount);
          generateSubsidyList(params).then((res) => {
            if (res.code === 0) {
              state.WaitSubsidyForm.projectNo = res.data.projectNo;
              state.form.projectNo = res.data.projectNo;
              queryWaitSubsidyListByPage();
            } else {
              ElMessage.error(res.message);
            }
          });
        } else {
          return false;
        }
      });
    };

    //获取未入库名单
    const queryWaitSubsidyListByPage = () => {
      state.WaitSubsidyLoading = true;
      getWaitSubsidyListByPage(state.WaitSubsidyForm)
        .then(({ data }) => {
          let { generateSubsidy, pageInfo } = data;
          state.totalCount = generateSubsidy.totalCount;
          state.totalAmount = generateSubsidy.totalAmount;
          state.abnormalData = generateSubsidy.errorCount;
          state.WaitSubsidyList = pageInfo;
          state.WaitSubsidyListTotal = generateSubsidy.totalCount;
          state.WaitSubsidyLoading = false;
        })
        .catch(() => {
          state.WaitSubsidyLoading = false;
        });
    };

    const delClick = (val) => {
      console.log(val);
      let data = {
        projectNo: state.form.projectNo,
        id: parseInt(val.id),
      };
      deleteWaitSubsidy(data).then(({ code, message }) => {
        if (code === 0) {
          queryWaitSubsidyListByPage();
        } else {
          ElMessage.error(message);
        }
      });
    };
    const uploadSample = async () => {
      let res = await exportSubsidyList();
      let url = window.URL.createObjectURL(
        new Blob([res], { type: "application/vnd.ms-excel" })
      );
      let link = document.createElement("a");
      link.style.display = "none";
      link.href = url;
      link.setAttribute("download", "导入补助清单样例.xlsx");
      document.body.appendChild(link);
      link.click();
    };
    const submitForm = async () => {
      console.log(ruleForm);
      ruleForm.value.validate(async (valid) => {
        if (valid) {
          if (state.isEdit) {
            // let data = { ...state.form, ...state.generateSubsidyForm };
            console.log(state.form, state.generateSubsidyForm);
          } else {
            if (!state.form.projectNo) {
              return ElMessage.error("请先生成补助清单！");
            }
            state.form.subsidyMonth = timeStr(state.form.subsidyMonth);
            // state.form.subsidyMonth = 10;
            state.form.grantMode = state.grantType;
            let data={}
            if (state.form.grantMode == "SYSTEM") {
              let params = { ...state.generateSubsidyForm }
              params.takeEffectTime = timeStr(params.takeEffectTime);
              params.invalidTime = timeStr(params.invalidTime);
              params.subsidyAmount = Number(params.subsidyAmount);
              data = { ...state.form, ...params };
            } else {
              data = { ...state.form };
            }
              console.log(data);


            let { code, message } = await saveImportSubsidyList(data);
            if (code === 0) {
              ElMessage.success(message);
              context.emit("offAdd", true);
            } else {
              ElMessage.error(message);
            }
          }
        } else {
          return false;
        }
      });
    };

    const beforeClose = () => {
      store.commit("subsidyData/updateState", {
        key: "isEdit",
        payload: false,
      });
      context.emit("offAdd", false);
      state.isEdit = false;
    };

    const handlePageChange = (val) => {
      state.WaitSubsidyForm.currentPage = val;
      queryWaitSubsidyListByPage();
    };
    const handleSizeChange = (val) => {
      state.WaitSubsidyForm.currentPage = 1;
      state.WaitSubsidyForm.pageSize = val;
      queryWaitSubsidyListByPage();
    };

    onMounted(() => { });
    return {
      uploadDom,
      state,
      subsidyTypeList,
      cascaderChange,
      roleList,
      departCheckList,
      generateSubsidy,
      ruleForm,
      defineGenerateListClick,
      submitForm,
      beforeClose,
      handleRemove,
      handleSuccess,
      delClick,
      uploadSample,
      handlePageChange,
      handleSizeChange,
    };
  },
};
</script>
<style lang="scss" scoped>
:deep(.el-dialog__header) {
  border-bottom: 1px solid #efefef;
}

:deep(.el-overlay) {
  position: absolute;
  background-color: rgba(0, 0, 0, 0);
}

:deep(.el-dialog__footer) {
  text-align: center;
}

.select-input-lang {
  .el-select {
    width: 500px;
  }
}

.pagination-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
