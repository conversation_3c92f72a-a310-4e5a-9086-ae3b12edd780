<template>
  <div class="padding-form-box setBasicParam" v-loading="state.loading">
    <el-form inline size="mini" label-width="200px">
      <el-form-item label="标题名称：">
        <el-input v-model="state.form.printDetailEntity.title"></el-input>
      </el-form-item>
      <el-form-item
        :label="item.label"
        v-for="(item, index) in formList"
        :key="index"
      >
        <el-select v-model="state.form.printDetailEntity[item.valueKey]">
          <el-option
            v-for="(v, i) in option"
            :key="i"
            :label="v.label"
            :value="v.value"
          ></el-option>
        </el-select>
      </el-form-item>
    </el-form>
  </div>
  <div style="text-align: center">
    <el-button size="mini" @click="beforeClose()">取消</el-button>
    <el-button size="mini" type="primary" @click="saveClick()">保存</el-button>
  </div>
</template>

<script>
import {
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElOption,
  ElSelect,
  ElMessage,
} from "element-plus";
import { onMounted, reactive } from "vue";
import { useStore } from "vuex";
import { savePrint } from "@/applications/eccard-iot/api";

const option = [
  {
    value: 1,
    label: "是",
  },
  {
    value: 0,
    label: "否",
  },
];
const formList = [
  { label: "是否打印标题：", valueKey: "print_title" },
  { label: "是否打印商户编号：", valueKey: "print_num" },
  { label: "是否打印商户名称：", valueKey: "print_name" },
  { label: "是否打印操作员号：", valueKey: "print_oper_num" },
  { label: "是否打印机号：", valueKey: "print_dev_num" },
  { label: "是否打印交易类型：", valueKey: "print_deal_type" },
  { label: "是否打印交易金额：", valueKey: "print_deal_money" },
  { label: "是否打印余额：", valueKey: "print_balance" },
  { label: "是否打印补助余额：", valueKey: "print_bzye" },
  { label: "是否打印剩余次数：", valueKey: "print_sycs" },
  { label: "是否打印交易序号：", valueKey: "print_jyxh" },
  { label: "是否打印交易时间：", valueKey: "print_jysj" },
  { label: "是否打印菜单：", valueKey: "print_menu" },
  { label: "是否打印用户号：", valueKey: "print_usernum" },
  { label: "是否打印用户名：", valueKey: "print_username" },
];

const defaultParamsFnc = () => {
  return {
    title: "卡德智能",
    print_title: 0,
    print_num: 0,
    print_name: 0,
    print_oper_num: 0,
    print_dev_num: 0,
    print_deal_type: 0,
    print_deal_money: 0,
    print_balance: 0,
    print_bzye: 0,
    print_sycs: 0,
    print_jyxh: 0,
    print_jysj: 0,
    print_menu: 0,
    print_usernum: 0,
    print_username: 0,
  };
};

export default {
  components: {
    ElForm,
    ElFormItem,
    ElInput,
    ElSelect,
    ElOption,
    ElButton,
  },
  setup() {
    const store = useStore();
    const state = reactive({
      loading: false,
      form: {
        printDetailEntity: defaultParamsFnc(),
        id: "",
        paramId: "",
        paramType: "print",
      },
    });


    const saveClick = async () => {
      state.loading = true;
      state.form.paramId = store.state.deviceParameters[store.state.app.activeTab].selectRow.id;
      let { message, code } = await savePrint(state.form);
      if (code === 0) {
        setTimeout(() => {
          ElMessage.success(message);
          store.dispatch("deviceParameters/getParams",store.state.deviceParameters[store.state.app.activeTab].selectRow.id);
          state.loading = false;
        }, 1500);
      }
    };

    const beforeClose = () => {
      store.commit("deviceParameters/updateState", {
        key: store.state.app.activeTab,
        childKey:"isSetParams",
        payload: false,
      });
    };
    onMounted(() => {
      // store.dispatch("deviceParameters/getParams",store.state.deviceParameters[store.state.app.activeTab].selectRow.id);
      let val = store.state.deviceParameters[store.state.app.activeTab].detailsParams
      if (val && val.length) {
          let printList = val.filter((item) => item.paramType == "print");
          if (printList.length) {
            console.log(JSON.parse(printList[0].paramContent));
            state.form.id = printList[0].id;
            for (let key in state.form.printDetailEntity) {
              state.form.printDetailEntity[key] = JSON.parse(
                printList[0].paramContent
              )[key];
            }
            console.log(state.form.printDetailEntity);
          } else {
            state.form.id = "";
            state.form.printDetailEntity = defaultParamsFnc();
          }
        } else {
          //设置默认选项
          state.form.id = "";
          state.form.printDetailEntity = defaultParamsFnc();
        }
    });
    return {
      option,
      formList,
      state,
      saveClick,
      beforeClose,
    };
  },
  name: "basicParam",
};
</script>

<style lang="scss" scoped>
.setBasicParam {
  border: 1px solid #dcdfe6;
  border-radius: 5px;
  margin-bottom: 10px;
}
:deep(.el-form-item__content) {
  width: 215px;
  .el-select {
    width: 100% !important;
  }
}
</style>
