<template>
  <div class="navmenu-wrap">
    <div class="logo">
      <!-- <img class="logo1" v-if="toggle" :src="logo2" alt="" />
      <img class="logo2" v-else :src="logo" alt="" /> -->
      <img class="app-logo" :src="logoImg.logo" alt="">
      <div class="app-name"  v-if="!toggle">{{logoImg.title}}</div>
    </div>
    <el-menu
      :default-active="activeIndex"
      :default-openeds="openeds"
      class="navmenu"
      :background-color="panelBgColor"
      :active-text-color="menuActiveTextColor"
      :text-color="menuTextColor"
      :collapse-transition="false"
      popper-class="panel-menu-popup"
      :collapse="isWideDevice ? toggle : false"
      @open="(index) => handleExpand(index, true)"
      @close="(index) => handleExpand(index, false)"
      @select="handleSelect"
    >
      <template v-for="item in menuTree">
        <el-submenu v-if=" item.children" :key="item.id" :index="item.menuEnName">
          <template #title>
            <kade-icon :color="menuTextColor" :name="item.menuIcon" />
            <span class="menu-label">{{ item.menuName }}</span>
          </template>
          <el-menu-item v-for="sub in item.children" :key="sub.id" :index="sub.menuEnName">
            <kade-icon :color="iconColor(sub.menuEnName)" :name="sub.menuIcon" />
            <span class="menu-label">{{ sub.menuName }}</span>
          </el-menu-item>
          
        </el-submenu>
        <el-menu-item v-else :index="item.menuEnName" :key="item.id">
          <kade-icon :color="iconColor(item.routeName)" :name="item.menuIcon" />
          <template #title>
            <span class="menu-label">{{ item.menuName }}</span>            
          </template>
        </el-menu-item>        
      </template>
    </el-menu>    
  </div>
</template>
<script>
import { reactive , toRefs, computed } from 'vue';//computed, onMounted, onUnmounted
import { ElMenu, ElMenuItem, ElSubmenu } from 'element-plus';
import { useStore } from 'vuex';
import Logo from '@/assets/logo.png';
import Logo2 from '@/assets/logo2.png';
import Icon from '@/components/icon';
export default {
  components: {
    'el-menu': ElMenu,
    'el-menu-item': ElMenuItem,
    'el-submenu': ElSubmenu,
    'kade-icon': Icon,
  },
  props: {
    toggle: {
      type: Boolean,
      default: true,
    },
    isWideDevice: {
      type: Boolean,
      default: true,
    }
  },
  setup() {
    const state = reactive({
      logo: Logo,
      logo2: Logo2,
      openeds: [],
      ...THEMEVARS,
    });
    const store = useStore();
    const methods = {
      handleExpand() {
        
      },
      handleSelect(name) {
        const menus = store.getters['app/menus'];
        const target = menus.find(it => it.menuEnName === name);
        store.dispatch('app/addTab', {
          id: target.menuEnName,
          payload: target,
        });
      }
    };
    const iconColor = (name) => {
      if (name) {
        return state.menuActiveTextColor;
      }
      return state.menuTextColor;
    };
    const menuTree = computed(() => {
      console.log(store.getters['app/menuTree']);
        if(store.getters['app/menuTree'].length){
          let list=store.getters['app/menuTree'][0].children.map(item=>{
            let a={...item}
            if(!a.children.length){
              delete a.children
            }
            return a
          })
          return list
        }else{
          return []
        }
    });
    const activeIndex = computed(() => {
      return store.state.app.activeTab;
    });

    const logoImg=computed(()=>{
        return {
          logo:require(`../../assets/module_img/${CONFIG.APPID}.svg`),
          title:CONFIG.TITLE
        }
    })


    return {
      ...toRefs(state),
      ...methods,
      iconColor,
      appName: store.state.title,
      menuTree,
      activeIndex,
      logoImg
    }
  }
}
</script>
<style lang="scss" scoped>
.navmenu-wrap{
  width: 100%;
  height: 100%;
  .logo{
    padding-left: 20px;
    height: 70px;
    // width: 100%;
    display: flex;
    // justify-content: center;
    align-items: center;
    background-color: #002140;
    img{
      &.logo2{
        object-fit: fill;
        width: 80%;        
      }
      &.logo1{
        width: 30px;
        height: 30px;
      }
    }
    .logo-text{
      color: $panel-logo-text-color;
      margin-left: 15px;
      transition: all .2s linear;
      white-space: nowrap;
    }
    .app-logo{
      width: 32px;
      height: 32px;
      
    }
    .app-name{
      font: 500 18px arial;
      color: #fff;
      margin-left: 10px;
    }
  }
}
.menu-label{
  margin-left: 10px;
}
:deep(.el-menu){
  border-right: none;
  background-color: #001529!important;
  .el-menu-item.is-active{
    background-color: #000c17!important;
  }
  .el-submenu > .el-menu{
    & > .el-menu-item{
      background-color: #001529!important;
      &.is-active{
        background-color: #000c17!important;
      }
    }
  }
}
</style>