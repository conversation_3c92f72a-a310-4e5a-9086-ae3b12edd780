<template>
  <div class="padding-box" v-if="details">
    <div class="params-box" v-for="(item,index) in params" :key="index">
      <div class="params-label">{{item.label}}</div>
      <div class="params-value">{{details[item.filed]}}</div>
    </div>
  </div>
  <el-empty v-else description="当前设备参数未设定"></el-empty>
</template>
<script>
import { computed } from "@vue/runtime-core";
import { useStore } from "vuex";
import { ElEmpty } from "element-plus"
export default {
  components: {
    ElEmpty
  },
  setup() {
    const store = useStore();
    const details = computed(() => {
      let data = {}
      if (store.state['identityDevice/faceDistingDevice'].detailsParams.callback) {
        data = JSON.parse(store.state['identityDevice/faceDistingDevice'].detailsParams.callback.paramContent);
      } else {
        data = ''
      }
      return data;
    });
    const params = [
      { label: "识别回调Url", filed: "identifyCallbackUrl" },
      { label: "设备心跳url", filed: "deviceHeartbeatUrl" },
      { label: "拍照注册Url", filed: "photoRegisteredUrl" },
      { label: "测温版识别回调Url", filed: "temperatureIdentifyCallbackUrl" },
    ]
    return {
      params,
      details
    }
  }
}
</script>
<style lang="scss" scoped>
.params-box {
  display: flex;
  align-items: center;
  text-align: center;
  border: 1px solid #eeeeee;
  &:nth-last-child(n + 1) {
    border-top: 0;
  }
  &:first-child {
    border-top: 1px solid #eeeeee;
  }
  .params-label {
    width: 400px;
    line-height: 50px;
    background: #f6f6f6;
    border-right: 1px solid #eeeeee;
  }
  .params-value {
    flex: 1;
    min-width: 400px;
    line-height: 50px;
    height: 50px;
  }
}
</style>