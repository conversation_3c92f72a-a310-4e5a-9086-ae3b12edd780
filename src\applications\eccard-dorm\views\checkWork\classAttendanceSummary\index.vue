<template>
  <kade-route-card>
    <kade-table-filter @search="handleSearch" @reset="handleReset">
      <el-form inline label-width="90px" size="mini">
        <el-form-item label="组织机构">
          <kade-dept-select-tree style="width: 100%" :value="state.form.deptPath" valueKey="deptPath" :multiple="false"
            @valueChange="(val) => (state.form.deptId = val.deptPath)" />
        </el-form-item>
        <el-form-item label="考勤日期">
          <el-date-picker v-model="state.requestDate" type="daterange" range-separator="~" start-placeholder="请选择"
            end-placeholder="请选择" :size="size" />
        </el-form-item>
        <el-form-item label="考勤时段">
          <el-select clearable v-model="state.form.attendancePeriodId" placeholder="全部">
            <el-option v-for="(item, index) in state.periodIdList" :key="index" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否晚归">
          <el-select clearable v-model="state.form.lateFlag" placeholder="全部">
            <el-option v-for="(item, index) in lateFlagList" :key="index" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否未归">
          <el-select clearable v-model="state.form.notReturnFlag" placeholder="全部">
            <el-option v-for="(item, index) in notReturnList" :key="index" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="班级考勤汇总列表">
      <template #extra>
        <el-button class="btn-purple" icon="el-icon-bottom" type="" size="small" @click="exportClick">导出</el-button>
      </template>
      <el-table border :data="state.data" v-loading="state.loading">
        <el-table-column prop="fatherDeptName" label="院系" align="center"></el-table-column>
        <el-table-column prop="deptName" label="班级" align="center"></el-table-column>
        <el-table-column prop="totalStayPerson" label="住校人数" align="center"></el-table-column>
        <el-table-column prop="attendanceDate" label="考勤日期" align="center"></el-table-column>
        <el-table-column prop="attendancePeriodName" label="考勤时段" align="center"></el-table-column>
        <el-table-column prop="actuaNumber" label="实到人数" align="center">
          <template #default="scope">
            <div :style="{ 'backgroundColor': scope.row.actuaNumber == scope.row.dueNumber ? '#03c316' : '' }">
              {{ scope.row.actuaNumber }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="leaveNumber" label="请假人数" align="center">
          <template #default="scope">
            <div :style="{ 'backgroundColor': scope.row.leaveNumber > 0 ? '#aaaaaa' : '' }">{{ scope.row.leaveNumber }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="lateNumber" label="晚归人数" align="center">
          <template #default="scope">
            <div :style="{ 'backgroundColor': scope.row.lateNumber > 0 ? '#f59a23' : '' }">{{ scope.row.lateNumber }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="notReturnNumber" label="未归人数" align="center">
          <template #default="scope">
            <div :style="{ 'backgroundColor': scope.row.notReturnNumber > 0 ? '#d9001b' : '' }">{{ scope.row.notReturnNumber }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="lateFlag" label="是否晚归" align="center">
          <template #default="scope">
            {{ dictionaryFilter(scope.row.lateFlag) }}
          </template>
        </el-table-column>
        <el-table-column prop="notReturnFlag" label="是否未归" align="center">
          <template #default="scope">
            {{ dictionaryFilter(scope.row.notReturnFlag) }}
          </template>
        </el-table-column>
        <el-table-column label="详情" align="center" width="100px">
          <template #default="scope">
            <el-button type="text" @click="details('not', scope.row)" size="mini" class="green">未归</el-button>
            <el-button type="text" @click="details('late', scope.row)" size="mini" class="green">晚归</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination">
        <el-pagination :currentPage="state.form.currentPage" :page-size="state.form.pageSize"
          :page-sizes="[10, 20, 30, 40]" background layout="total, sizes, prev, pager, next, jumper"
          :total="state.total" @size-change="handleSizeChange" @current-change="handleCurrentChange">
        </el-pagination>
      </div>
    </kade-table-wrap>
    <kade-class-details :dialogTitle="state.dialogTitle" :dialogVisible="state.dialogVisible" :tableRow="state.tableRow"
      @close="() => state.dialogVisible = false"></kade-class-details>
  </kade-route-card>
</template>

<script>
import {
  ElButton,
  ElForm,
  ElFormItem,
  ElSelect,
  ElOption,
  ElDatePicker,
  ElTable,
  ElTableColumn,
  ElPagination,
} from "element-plus";
import { reactive } from "@vue/reactivity";
import details from "./components/details.vue";
import { dateStr } from "@/utils/date.js"
import { onMounted } from '@vue/runtime-core';
import { useDict } from "../../../../../hooks/useDict";
import deptSelectTree from "@/components/tree/deptSelectTree.vue";
import { attendanceClassSummaryList, exportAttendanceClassSummary, attendancePeriodList } from '@/applications/eccard-dorm/api.js'
export default {
  components: {
    ElButton,
    ElForm,
    ElFormItem,
    ElSelect,
    ElOption,
    ElDatePicker,
    ElTable,
    ElTableColumn,
    ElPagination,
    "kade-class-details": details,
    "kade-dept-select-tree": deptSelectTree,
  },
  setup() {
    const lateFlagList = useDict('SYS_BOOL_STRING')
    const notReturnList = useDict('SYS_BOOL_STRING')
    const state = reactive({
      form: {
        pageSize: 10,
        currentPage: 1
      },
      total: 0,
      data: [],
      loading: false,
      requestDate: [],
      dialogTitle: "",
      dialogVisible: false,
      periodIdList: '',
      tableRow:''
    });
    const getPeriod = async () => {
      let { data } = await attendancePeriodList()
      state.periodIdList = data
    }
    const getList = async () => {
      if (state.requestDate && state.requestDate.length) {
        state.form.beginDate = dateStr(state.requestDate[0])
        state.form.endDate = dateStr(state.requestDate[1])
      } else {
        delete state.form.beginDate
        delete state.form.endDate
      }
      state.loading = true
      try {
        let { data: { list, total } } = await attendanceClassSummaryList(state.form)
        state.data = list
        state.total = total
        state.loading = false
      }
      catch {
        state.loading = false
      }
    };
    const handleSizeChange = (val) => {
      state.form.currentPage = 1
      state.form.pageSize = val
      getList()
    };
    const handleCurrentChange = (val) => {
      state.form.currentPage = val
      getList()
    }
    const details = (type, row) => {
      console.log(row);
      if (type === "not") {
        state.dialogTitle = "未归人员名单";
      } else {
        state.dialogTitle = "晚归人员名单";
      }
      state.tableRow=row
      state.dialogVisible = true;
    };
    const handleSearch = () => {
      getList()
    };
    const handleReset = () => {
      state.form = {
        currentPage: 1,
        pageSize: 10
      }
      state.requestDate = []
      getList()
    };
    const exportClick = async () => {
      let res = await exportAttendanceClassSummary(state.form)
      let url = window.URL.createObjectURL(
        new Blob([res], { type: 'application/vnd.ms-excel' })
      )
      let link = document.createElement('a')
      link.style.display = 'none'
      link.href = url
      link.setAttribute('download', '班级考勤汇总表.xlsx')
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }
    onMounted(() => {
      getList()
      getPeriod()
    })
    return {
      state,
      details,
      lateFlagList,
      notReturnList,
      exportClick,
      handleSearch,
      handleReset,
      handleSizeChange,
      handleCurrentChange
    };
  },
};
</script>

<style lang="scss" scoped>
.green :hover {
  text-decoration: underline;
}

:deep(.el-dialog) {
  border-radius: 8px;
}

:deep(.el-dialog__header) {
  border-bottom: 1px solid #eeeeee;
}

:deep(.el-divider--horizontal) {
  margin: 0;
}

:deep(.el-icon-date:before) {
  display: none;
}

:deep(.el-dialog__body) {
  padding-bottom: 20px !important;
}

:deep(.el-table--border) {
  border-right: 1px solid #ebeef5;
}

:deep(.el-input--mini .el-input__inner) {
  width: 210px;
}

:deep(.el-pagination .el-select .el-input .el-input__inner) {
  width: 100px;
}

:deep(.el-pagination__editor.el-input .el-input__inner) {
  width: 46px;
}

:deep(.el-table td) {
  margin: 0;
  padding: 0;
  height: 50px;
}

:deep(.el-table .cell) {
  margin: 0;
  padding: 0;
}

:deep(.el-table td div) {
  box-sizing: border-box;
  height: 50px;
  line-height: 50px;
}
</style>