<template>
  <el-dialog :model-value="modelValue" title="绑定人员" width="90%" :before-close="beforeClose">
    <div style="margin-top:20px">
      <kade-select-table :isShow="modelValue" :value='[]' :reqFnc="getUnGroupUserListByPage"
        :selectCondition="state.selectCondition"  :column="column" :params="state.params" @change="personChange" />
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="beforeClose" size="mini">取消</el-button>
        <el-button type="primary" @click="submit()" size="mini">提交</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script>
import { ElDialog, ElButton, ElMessage } from "element-plus";
import { onMounted, reactive, watch } from "vue";
import { useDict } from "@/hooks/useDict.js";
import {
  acsUserGroupBatchAdd, getUnGroupUserListByPage, addAllUserListAdd
} from "@/applications/eccard-uac/api";
import {
  getRolelist
} from "@/applications/eccard-basic-data/api";
import selectTable from "@/components/table/selectTable.vue";
const column = [
  { label: "人员编号", prop: "userCode", isDict: false, width: "" },
  { label: "人员姓名", prop: "userName", isDict: false, width: "" },
  { label: "组织机构", prop: "deptName", isDict: false, width: "" },
  { label: "性别", prop: "userSex", isDict: true, width: "" },
  { label: "身份类别", prop: "roleName", isDict: false, width: "" },
];
export default {
  emits: ["update:modelValue"],
  components: {
    ElDialog,
    ElButton,
    "kade-select-table": selectTable,
  },
  props: {
    modelValue: {
      type: Boolean,
      default: false,
    },
    rowData: {
      type: Object,
      default: null,
    },
    TabModule: {
      type: Function,
      default: null,
    }
  },
  setup(props, context) {
    const state = reactive({
      loading: false,
      userListData:{},
      selectCondition: [
        { label: "组织机构", valueKey: "deptPath", dataKey: "deptPath", placeholder: "请选择", isSelect: false, isTree: "dept" },
        {
          label: "身份类别", valueKey: "userRole", placeholder: "请选择", isSelect: true,
          select: {
            list: [],
            option: {
              label: "roleName",
              value: "id",
            },
          },
        },
        {
          label: "性别", valueKey: "userSex", placeholder: "请输入", isSelect: true,
          select: {
            list: useDict("SYS_SEX"),
            option: {
              label: "label",
              value: "value",
            },
          },
        },
        { label: "编号/姓名", valueKey: "keyWord", placeholder: "请输入", isSelect: false, },
      ],
      params: {
        currentPageKey: "currentPage",
        pageSizeKey: "pageSize",
        resListKey: "list",
        resTotalKey: "total",
        value: {
          groupId: props.rowData.id
        },
        tagNameKey: "userName",
        valueKey: "userId",
      },
      deviceList: [],
    });
    watch(() => props.modelValue, val => {
      if (val) {
        state.params.value.groupId = props.rowData.id
      }
    })
    const queryRolelist = async () => {
      let { data } = await getRolelist();
      state.selectCondition[1].select.list = data;
    };
    const personChange = (val) => {
      console.log(val);
      state.userListData = val
    };
    const submit = async () => {
      state.loading = true
      let fn = state.userListData.isSelectAll ? addAllUserListAdd : acsUserGroupBatchAdd
      let params = {}
      if (state.userListData.isSelectAll) {
        params = {
          deptPath: state.userListData.params.deptPath,
          groupName: props.rowData.groupName,
          groupId: props.rowData.id,
          keyWord: state.userListData.params.keyWord,
          userRole: state.userListData.params.userRole,
          userSex: state.userListData.params.userSex,
        }
      } else {
        params = {
          groupId: props.rowData.id,
          groupName: props.rowData.groupName,
          userList: state.userListData.list
        }
      }
      try {
        let { code, message } = await fn(params)
        if (code === 0) {
          ElMessage.success(message)
          context.emit("update:modelValue", true);
        }
        state.loading = false
      }
      catch {
        state.loading = false
      }
    }
    const beforeClose = () => {
      context.emit("update:modelValue", false);
    };
    onMounted(async () => {
      queryRolelist()
    });

    return {
      getUnGroupUserListByPage,
      column,
      state,
      personChange,
      submit,
      beforeClose,
    };
  },
};
</script>

<style lang="scss" scoped>
</style>
