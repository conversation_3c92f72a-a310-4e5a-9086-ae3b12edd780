<template >
  <div class="screen-title">
    <div class="title">{{title}}</div>
    <div class="now-msg">
      <div class="now-date">
        <div class="now-time">10:8</div>
        <div class="now-date-week">
          <div class="now-week">星期一</div>
          <div class="now-date">2022年6月27日</div>
        </div>
      </div>
      <div class="now-user">
        <div class="now-user-msg">
          <div class="user-name">宿管员：张师傅</div>
          <div class="user-tel">联系电话：18982945597</div>
        </div>
        <div>
          <el-button size="mini" class="go-out" @click="screen">{{ state.fullscreen ? '退出' : '全屏' }}</el-button>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { reactive } from "vue"
import { ElButton } from "element-plus"

export default {
  components: {
    ElButton,
  },
  props:{
    title:{
      type:String,
      default:""
    }
  },
  setup() {
    const state = reactive({
      fullscreen: false,
    })
    const screen = () => {

      let element = document.documentElement;

      if (state.fullscreen) {

        if (document.exitFullscreen) {

          document.exitFullscreen();

        } else if (document.webkitCancelFullScreen) {

          document.webkitCancelFullScreen();

        } else if (document.mozCancelFullScreen) {

          document.mozCancelFullScreen();

        } else if (document.msExitFullscreen) {

          document.msExitFullscreen();

        }
      } else {

        if (element.requestFullscreen) {

          element.requestFullscreen();

        } else if (element.webkitRequestFullScreen) {

          element.webkitRequestFullScreen();

        } else if (element.mozRequestFullScreen) {

          element.mozRequestFullScreen();

        } else if (element.msRequestFullscreen) {

          // IE11

          element.msRequestFullscreen();

        }

      }
      state.fullscreen = !state.fullscreen;

    }
    return {
      state,
      screen
    }
  }
}
</script>
<style lang="scss" scoped>
.screen-title {
  color: #fff;
  width: 100%;
  position: relative;
  height: 102px;

  .title {
    text-align: center;
    padding: 15px 0 40px;
    font: 700 36px arial;
  }

  .now-msg {
    position: absolute;
    bottom: 20px;
    left: 0;
    width: 100%;
    padding: 0 20px;
    box-sizing: border-box;
    color: #fff;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .now-date {
      display: flex;
      align-items: center;

      .now-time {
        font: 700 28px arial;
        margin-right: 30px;
      }

      .now-date-week {
        font: 700 14px arial;
      }
    }

    .now-user {
      display: flex;
      align-items: center;

      .now-user-msg {
        font: 700 14px arial;
        margin-right: 20px;
      }

      .go-out {
        border: 0;
        color: #fff;
        background: linear-gradient(180deg, rgba(51, 255, 255, 1) 0%, rgba(51, 255, 255, 1) 0%, rgba(47, 28, 155, 1) 100%, rgba(47, 28, 155, 1) 100%);
      }

    }

  }
}
</style>