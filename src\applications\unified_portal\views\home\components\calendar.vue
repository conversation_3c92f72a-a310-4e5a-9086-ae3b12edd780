<template>
  <div class="calendar" v-loading="state.loading">
    <div class="left">
      <div class="l-head">
        <div class="l-head-l">待办({{ state.total }})</div>
        <el-button size="mini" type="primary" @click="showCreateModal = true">新增</el-button>
      </div>
      <div class="scroll-box">
        <div class="l-list" v-infinite-scroll="load">
            <template v-if="state.dataList.length">
              <div class="l-list-item" v-for="item in state.dataList" :key="item.id" @click="(e) => handleTodo(e, item)">
                <div class="title ellipsis">
                  <el-checkbox :disabled="!enabled(item)" :model-value="item.status === 'TS_DONE'" />
                  <el-tooltip
                    class="item" 
                    effect="light" 
                    :content="item.taskContent" 
                    placement="top-start"
                    popper-class="ellipse-poptip"
                    append-to-body
                  >
                    <span>{{ item.taskContent }}</span>
                  </el-tooltip>                  
                </div>
                <div class="desc ellipsis">
                  <el-tag size="small" :type="getTodoTagType(item)">{{ getTodoTagText(item) }}</el-tag>
                  <span class="time">{{ item.taskEndDay }}</span>
                </div>
              </div>   
            </template>                   
            <el-empty description="暂无待办事项" v-else />
        </div>        
      </div>
    </div>
    <div class="right custom-calendar">
      <el-calendar>
        <template #dateCell="{ data }">
          <div :class="['calendar-cell', { 'is-selected' : data.isSelected }]" @click="handleClick(data.day)">
            {{ data.day.split('-')[2] }}
          </div>
        </template>         
      </el-calendar>
    </div>
    <kade-create-todo title="新增待办" @change="handleAddchange" v-model="showCreateModal" />
  </div>
</template>
<script>
import { ElButton, ElCalendar, ElCheckbox, ElTag, ElMessageBox, ElMessage, ElTooltip } from 'element-plus';
import moment from 'moment';
import { ref } from 'vue';
import { ElEmpty } from 'element-plus';
import { getTaskList, updateTodoState } from '@/applications/unified_portal/api';
import { usePagination } from '@/hooks/usePagination';
import CreateTodo from './createTodo';
export default {
  components: {
    'el-button': ElButton,
    'el-calendar': ElCalendar,
    'el-checkbox': ElCheckbox,
    'el-empty': ElEmpty,
    'el-tag': ElTag,
    'el-tooltip': ElTooltip,
    'kade-create-todo': CreateTodo,
  },
  setup() {
    const showCreateModal = ref(false);
    const today = moment().format('YYYY-MM-DD');
    const { options, loadData, querys } = usePagination(getTaskList, { isNowDay: 1, endDay: today }, { pageSize: 6 });
    const load = () => {
      const { dataList, total } = options;
      if(dataList.length === total) {
        return false;
      }
      options.currentPage += 1;
      loadData(true);
    }
    const handleClick = (day) => {
      querys.isNowDay = day !== today ? 0 : 1;
      querys.endDay = day;
      options.currentPage = 1;
      loadData();
    }
    const getTodoTagType = (item) => {
      if(item.status === 'TS_DONE') {
        return 'primary';
      } else if(item.isOverdue === 1) {
        return 'danger';
      } else {
        return 'warning';
      }
    }
    const getTodoTagText = (item) => {
      if(item.status === 'TS_DONE') {
        return '已办';
      } else if(item.isOverdue === 1) {
        return '已逾期';
      } else {
        return '待办';
      }      
    }
    const enabled = (item) => {
      return item.isOverdue === 0 && item.status !== 'TS_DONE';      
    }
    const handleTodo = async (e, item) => {
      if(e.target.type === 'checkbox') {
        return;
      }
      if(enabled(item)) {
        ElMessageBox.confirm(`确认已办?`, {
            type: 'warning',
            closeOnPressEscape: false,
            closeOnClickModal: false,            
        }).then(async () => {
            try{
                const { message } = await updateTodoState({ taskId: item.id, status: 'TS_DONE' });
                item.status = 'TS_DONE';
                ElMessage.success(message);
            }catch(e) {
                throw new Error(e.message);
            }
        });
      }     
    }
    const handleAddchange = () => {
      options.currentPage = 1;
      loadData();
    }
    return {
      state: options,
      load,
      loadData,
      showCreateModal,
      handleClick,
      getTodoTagType,
      getTodoTagText,
      handleTodo,
      enabled,
      handleAddchange,
    }    
  },
}
</script>
<style lang="scss" scoped>
.calendar{
  display: flex;
  justify-content: space-between;
  .left,.right{
    width: 50%;
    flex: 1;
  }
  .left{
    border-right: 1px solid $border-color;
    .l-head{
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid $border-color;
      padding: 10px 20px;
      box-sizing: border-box;      
    }
    .scroll-box{
      height: 298px;
      height: calc(50vh - 170px);
      overflow-y: auto;
    }
    .l-list{
      padding: 0 20px;
      box-sizing: border-box; 
      .l-list-item{
        cursor: pointer;
        .title{
          .el-checkbox{
            margin-right: 10px;
          }
        }
        .desc{
          font-size: $font1;
          color: $font-sub-color;
          margin-top: 5px;
          .time{
            padding-left: 10px;
          }
        }
        padding: 10px 0;
      }
      .l-list-item + .l-list-item{
        border-top: 1px solid $border-color;
      }
    }
  }
}
::v-deep .custom-calendar{
  .el-button--mini{
    padding: 4px 5px;
    font-size: 12px;
  }
  .el-calendar-table td.is-selected{
    background-color: $primary-color;
    color: #fff;
    .el-calendar-day:hover{
      background-color: opacify($primary-color, 0.8);
    }
  }
}
</style>