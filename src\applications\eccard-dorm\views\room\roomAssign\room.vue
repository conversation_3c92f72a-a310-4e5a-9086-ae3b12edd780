<template>
  <div class="box" v-if="dataList.length">
    <kade-room v-for="(item) in dataList" :key="item.id" :data="item" />
  </div>
  <el-empty v-else description="暂无数据"></el-empty>
</template>

<script>
import { ElEmpty } from "element-plus"
import room from "@/applications/eccard-dorm/components/room.vue"
export default {
  components: {
    ElEmpty,
    "kade-room": room
  },
  props: {
    dataList: {
      type: Array,
      default: null
    }
  }
}
</script>

<style lang="scss" scoped>
.box {
  height: 55vh;
  margin: 20px;
  overflow-y: auto;
}
</style>