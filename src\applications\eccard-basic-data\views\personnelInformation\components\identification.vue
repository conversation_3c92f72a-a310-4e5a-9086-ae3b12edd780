<template>
  <el-form
    :label-width="labelWidth"
    style="margin-top: 20px"
    size="small"
  >
    <!-- <el-form-item label="人脸采集">

    </el-form-item> -->
    <el-form-item label="指纹采集">
      2021-04-06
    </el-form-item>
    <el-form-item label="卡类型">
      2021-04-06
    </el-form-item>
    <el-form-item label="卡状态">
      2021-04-06
    </el-form-item>
    <el-form-item label="物理卡号">
      2021-04-06
    </el-form-item>
    <el-form-item label="逻辑卡号">
      2021-04-06
    </el-form-item>
    <el-form-item label="2.4G卡号">
      2021-04-06
    </el-form-item>
    <el-form-item label="有效期">
      2021-04-06
    </el-form-item>    
    <!-- <el-form-item>
      <el-button type="primary" :loading="btnLoading" @click="handleSubmit">保存</el-button>
      <el-button @click="handleBack">返回</el-button>
    </el-form-item>                             -->
  </el-form>
</template>
<script>
import {
  ElForm,
  ElFormItem,
} from 'element-plus';
import { reactive } from 'vue';
export default {
  components: {
    'el-form': ElForm,
    'el-form-item': ElFormItem,
  },
  setup() {
    const state = reactive({
      btnLoading: false,
      data: {},
      loading: false,
    });
    const handleSubmit = () => {}
    const handleBack = () => {}
    return {
      state,
      handleSubmit,
      handleBack,
      labelWidth: THEMEVARS.formLabelWidth,      
    }
  }
}
</script>