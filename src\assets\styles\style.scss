﻿@import './reset.scss';
@import '../font/iconfont.css';

.panel-drawer .navmenu-wrap{
  background-color: $panel-left-bg;
}
.table-toolbox{
  margin-bottom: $space-normal;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  &.query{
    margin-bottom: 0;
  }
}
.ellipse-poptip{
  max-width: 400px;
}
.pagination{
  padding-top: 10px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.hoz-box{
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
}
.infoform{
  .el-form-item__label{
    color: #999;
  }
}
.link{
  color: $primary-color;
}
.ellipsis{
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
.el-button--text{
  &.success{
    color: $success-color;
  }
  &.danger{
    color: $danger-color;
  }
  &.info{
    color: $info-color;
  }
}
.table-filter{
  border-bottom: 1px solid $border-color;
  margin-bottom: 20px;
}
.el-button--default{
  &.btn-import{
    background-color: $--color-import;
    color: #fff;
    border: 1px solid $--color-import;
    &:hover{
      background-color: lighten($--color-import, 7%);
      border: 1px solid lighten($--color-import, 7%);
      outline: none;
      color: #fff;
    }
  }
}
.el-button [class*=el-icon-] + span{
  margin-left: 8px!important;
}
.el-button--small{
  padding: 7px 10px!important;
  font-size: 13px!important;
  i{
    font-size: 13px!important;
  }
}
.el-alert--info.is-dark{
  background-color: $primary-color!important;
}
.el-tree-node.is-current > .el-tree-node__content{
  & > .el-tree-node__expand-icon, & > .custom-tree-node{
    i,span{
      color: $primary-color!important;
    }
  }
}
.custom-tree-node.hasbtn{
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex: 1;
  .right{
    display: flex;
    justify-content: flex-end;
    flex-basis: 100px;
    padding-right: 10px;
    .a-btn{
      display: flex;
      justify-content: flex-start;
      align-items: center;
      color: $primary-color;
      cursor: pointer;
      i{
        margin-right: 5px;
      }
      .text,i{
        font-size: 13px;
      }
    }
    .a-btn + .a-btn{
      margin-left: 10px;
    }
  }
}
.border-box{
  border-radius: 5px;
  border: 1px solid $border-color;  
}
.padding-box{
  padding: 20px;
  box-sizing: border-box;
}
.padding-form-box{
  padding: 20px 20px 2px 20px;
  box-sizing: border-box;  
}