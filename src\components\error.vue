<template>
  <div class="error">
    <el-empty :description="message">
      <el-button @click="relogin" type="primary" size="small"
        >再次尝试</el-button
      >
    </el-empty>
  </div>
</template>
<script>
// 错误页
import { ElEmpty, ElButton } from "element-plus";
import { redirectLogin } from "@/utils";
import { removeToken, removeCacheUser, removeRefreshToken } from "@/utils";
import { loginOuted } from "@/service";
export default {
  props: {
    message: String,
  },
  components: {
    "el-empty": ElEmpty,
    "el-button": ElButton,
  },
  setup() {
    const relogin = async () => {
      sessionStorage.removeItem("kade-common-rediect-times");
      await loginOuted();
      removeToken();
      removeRefreshToken();
      removeCacheUser();
      localStorage.removeItem("kade_cache_dictionary");
      sessionStorage.clear();
      location.reload();
      redirectLogin();
    };
    return {
      relogin,
    };
  },
};
</script>
<style lang="scss" scoped>
.error {
  margin-top: 150px;
}
</style>