/* 出纳交款报表 */
<template>
  <div class="cashRecharge"  v-loading="state.loading">
    <kade-route-card style="height: auto">
      <kade-table-filter @search="search()" @reset="reset()">
        <el-form inline size="mini" label-width="100px">
          <el-form-item label="操作员:">
            <el-select clearable filterable multiple collapse-tags v-model="state.form.operatorId" placeholder="请选择">
              <el-option v-for="(item, index) in state.systemUserList" :key="index" :label="item.userName" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="统计时间:">
            <el-date-picker v-model="state.requestDate" type="datetimerange" unlink-panels :default-time="state.defaultTime" range-separator="~" start-placeholder="请选择日期" end-placeholder="请选择日期" @change="changeDate">
            </el-date-picker>
          </el-form-item>
          <!-- <el-form-item label="按入账时间:">
            <el-checkbox v-model="state.timeType"></el-checkbox>
          </el-form-item> -->
        </el-form>
      </kade-table-filter>
      <kade-table-wrap title="出纳交款报表">
        <template #extra>
          <el-button @click="exportClick()" icon="el-icon-daoru" size="mini" class="btn-blue">导出查询明细</el-button>
        </template>
        <el-table style="width: 100%" :data="state.detailList" ref="multipleTable" height="55vh" highlight-current-row border stripe>
          <el-table-column v-for="(item,index) in column" :key="index" :width="item.width" :label="item.label" :prop="item.prop" align="center" show-overflow-tooltip>
            <template v-if="item.render" #default="scope">
              {{item.render(scope.row[item.prop])}}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150" align="center">
            <template #default="scope">
              <div class="green" v-if="!(scope.row.type === 1)" @click="detailClick(scope.row)">
                查看交款明细
              </div>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination">
          <el-pagination background :current-page="state.form.currentPage" :page-size="state.form.pageSize" layout="total, sizes, prev, pager, next, jumper" :page-sizes="[6, 10, 20, 50, 100]" :total="state.total" @current-change="handlePageChange" @size-change="handleSizeChange">
          </el-pagination>
        </div>
      </kade-table-wrap>
    </kade-route-card>

    <el-dialog v-model="state.dialogVisible" title="交款明细" width="80%" :before-close="handleClose" :close-on-click-modal="false">
      <div class="search-box" style="margin-top: 20px">
        <el-form inline size="mini" label-width="100px">
          <el-form-item label="统计时间:">
            <el-date-picker v-model="state.requestDateDetail" type="datetimerange" unlink-panels :default-time="[new Date(), new Date()]" range-separator="~" start-placeholder="请选择日期" end-placeholder="请选择日期" @change="changeDateDetail">
            </el-date-picker>
          </el-form-item>
          <el-button @click="search('detail')" icon="el-icon-sousuo" size="mini" class="shop-upload" type="primary">搜索</el-button>
        </el-form>
      </div>
      <kade-table-wrap title="交款明细报表">
        <template #extra>
          <el-button @click="exportDetailClick()" icon="el-icon-daoru" size="mini" class="btn-blue">导出查询明细</el-button>
        </template>
        <el-table style="width: 100%" :data="state.detailPayList" ref="multipleTable" v-loading="state.loading" highlight-current-row border stripe>
          <el-table-column width="150" label="操作员" prop="operatorName" align="center"></el-table-column>
          <el-table-column label="统计日期" width="150" prop="settlementDate" align="center">
            <template #default="scope">
              {{
                  scope.row.settlementDate && timeStr(scope.row.settlementDate)
                }}
            </template>
          </el-table-column>

          <el-table-column label="交易钱包" prop="walletName" align="center"></el-table-column>
          <el-table-column label="交易类型" prop="costTypeName" align="center"></el-table-column>
          <el-table-column label="交易金额" prop="tradeAmount" align="center"></el-table-column>
          <el-table-column label="收支类型" prop="inoutTypeName" align="center"></el-table-column>
        </el-table>
        <div class="pagination">
          <el-pagination background :current-page="state.form.currentPage" :page-size="state.form.pageSize" layout="total, sizes, prev, pager, next, jumper" :page-sizes="[6, 10, 20, 50, 100]" :total="state.datailTotal" @current-change="handlePageChangeDetail" @size-change="handleSizeChangeDetail">
          </el-pagination>
        </div>
      </kade-table-wrap>
      <template #footer>
        <span class="dialog-footer">
          <el-button size="mini" @click="state.dialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script>
import {
  ElButton,
  ElForm,
  ElFormItem,
  ElSelect,
  ElOption,
  ElTable,
  ElTableColumn,
  ElPagination,
  ElDatePicker,
  // ElCheckbox,
  ElMessage,
  ElDialog,
} from "element-plus";
import { timeStr } from "@/utils/date.js";
import { reactive } from "@vue/reactivity";
import {
  getCashierPaymentList,
  getSystemUser,
  cashierPaymentExport,
  cashierPaymentDetailExport,
  getCashierPaymentDetailList,
} from "@/applications/eccard-finance/api";
import { onMounted, watch } from "@vue/runtime-core";
const column = [
  { label: "操作员", prop: "operatorName", width: "" },
  { label: "统计日期", prop: "settlementDate", width: "170", render: (val) => val && timeStr(val) },
  { label: "收入金额", prop: "incomeAmount", width: "" },
  { label: "支出金额", prop: "expendAmount", width: "" },
  { label: "应缴金额", prop: "payableAmount", width: "" },
]


export default {
  components: {
    "el-button": ElButton,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    "el-select": ElSelect,
    "el-option": ElOption,
    "el-table": ElTable,
    "el-table-column": ElTableColumn,
    "el-pagination": ElPagination,
    ElDatePicker,
    ElDialog,
    // ElCheckbox,
  },
  setup() {
    const state = reactive({
      loading: false,
      dialogVisible: false,
      timeType: true,
      selectName: "",
      form: {
        currentPage: 1,
        pageSize: 6,
      },
      detailForm: {
        currentPage: 1,
        pageSize: 6,
      },
      detailList: [],
      detailPayList: [],
      total: 0,
      datailTotal: 0,
      requestDate: "",
      requestDateDetail: "",
      systemUserList: [],
      defaultTime: [
        new Date(new Date(new Date().toLocaleDateString()).getTime()),
        new Date(
          new Date(new Date().toLocaleDateString()).getTime() +
          24 * 60 * 60 * 1000 -
          1
        ),
      ],
    });

    watch(
      () => state.requestDate,
      (val) => {
        if (!val) {
          delete state.form.startTime;
          delete state.form.endTime;
        }
      }
    );
    watch(
      () => state.requestDateDetail,
      (val) => {
        if (!val) {
          delete state.detailForm.startTime;
          delete state.detailForm.endTime;
        }
      }
    );
    //获取操作员
    const querySystemUser = () => {
      getSystemUser().then((res) => {
        state.systemUserList = res.data;
      });
    };
    const getList = async () => {
      state.loading = true;
      // state.form.timeType = state.timeType ? 2 : 1;
      let { code, data, message } = await getCashierPaymentList(state.form);
      if (code === 0) {
        let {
          page: { total, list },
          total: { expendAmount, incomeAmount, payableAmount, operatorName },
        } = data;
        state.detailList = list;
        if (state.detailList.length) {
          state.detailList.push({
            expendAmount,
            incomeAmount,
            payableAmount,
            operatorName,
            type: 1,
          });
        }
        state.total = total;
        state.loading = false;
      } else {
        ElMessage.error(message);
        state.loading = false;
      }
    };
    const getDetailList = async () => {
      let { data, code, message } = await getCashierPaymentDetailList(
        state.detailForm
      );
      if (code === 0) {
        let {
          page: { total, list },
          total: { tradeAmount, operatorName },
        } = data;
        state.detailPayList = list;

        if (state.detailPayList.length) {
          state.detailPayList.push({
            tradeAmount,
            operatorName,
          });
        }
        state.datailTotal = total;
        state.loading = false;
      } else {
        ElMessage.error(message);
        state.loading = false;
      }
    };
    const changeDate = (val) => {
      if (val && val.length) {
        state.form.startTime = timeStr(val[0]);
        state.form.endTime = timeStr(val[1]);
      } else {
        delete state.form.startTime;
        delete state.form.endTime;
      }
    };
    const changeDateDetail = (val) => {
      if (val && val.length) {
        state.detailForm.startTime = timeStr(val[0]);
        state.detailForm.endTime = timeStr(val[1]);
      } else {
        delete state.form.startTime;
        delete state.form.endTime;
      }
    };

    const search = (type) => {
      if (type) {
        state.detailForm.currentPage = 1;
        state.detailForm.pageSize = 6;
        getDetailList();
      } else {
        state.form.currentPage = 1;
        state.form.pageSize = 6;
        getList();
      }
    };

    const exportClick = async () => {
      state.loading = true;
      try {
        let res = await cashierPaymentExport(state.form);
        let url = window.URL.createObjectURL(
          new Blob([res], { type: "application/vnd.ms-excel" })
        );
        let link = document.createElement("a");
        link.style.display = "none";
        link.href = url;
        link.setAttribute("download", "出纳交款报表.xlsx");
        document.body.appendChild(link);
        link.click();
        state.loading = false
      }
      catch {
        state.loading = false;
      }
    };
    const exportDetailClick = async () => {

      let res = await cashierPaymentDetailExport(state.detailForm);
      let url = window.URL.createObjectURL(
        new Blob([res], { type: "application/vnd.ms-excel" })
      );
      let link = document.createElement("a");
      link.style.display = "none";
      link.href = url;
      link.setAttribute("download", state.selectName + "-交款明细报表.xlsx");
      document.body.appendChild(link);
      link.click();
    };

    const detailClick = (row) => {
      state.selectName = row.operatorName;
      state.detailForm.id = row.id;
      getDetailList();
      state.dialogVisible = true;
    };

    const handlePageChange = (val) => {
      state.form.currentPage = val;
      getList();
    };
    const handleSizeChange = (val) => {
      state.form.currentPage = 1;
      state.form.pageSize = val;
      getList();
    };
    const handlePageChangeDetail = (val) => {
      state.detailForm.currentPage = val;
      getDetailList();
    };
    const handleSizeChangeDetail = (val) => {
      state.detailForm.currentPage = 1;
      state.detailForm.pageSize = val;
      getDetailList();
    };
    const handleClose = () => {
      state.dialogVisible = false;
    };
    const reset = () => {
      state.form = {
        currentPage: 1,
        pageSize: 6,
      };
      state.requestDate = "";
    };
    onMounted(() => {
      getList();
      querySystemUser();
    });
    return {
      column,
      state,
      timeStr,
      handleClose,
      detailClick,
      exportClick,
      exportDetailClick,
      getList,
      search,
      getDetailList,
      changeDate,
      changeDateDetail,
      handlePageChange,
      handleSizeChange,
      handlePageChangeDetail,
      handleSizeChangeDetail,
      reset,
    };
  },
};
</script>
<style lang="scss" scoped>
:deep(.el-divider--horizontal) {
  margin: 0 0 10px 0;
}
.cashRecharge {
  min-height: 680px;
  :deep(.search-box) {
    .el-input__inner {
      width: 193px;
    }
    .el-date-editor {
      width: 400px;
    }
  }
}
:deep(.el-dialog__header) {
  border-bottom: 1px solid #efefef;
}
:deep(.el-dialog__headerbtn) {
  font-size: 20px;
  top: 10px;
  right: 10px;
}
:deep(.el-dialog__header) {
  padding: 10px 20px;
}
:deep(.el-overlay) {
  position: absolute;
  background-color: rgba(0, 0, 0, 0.2);
}
:deep(.el-dialog__footer) {
  text-align: center;
}
:deep(.el-dialog__title) {
  font-size: 14px;
}
</style>