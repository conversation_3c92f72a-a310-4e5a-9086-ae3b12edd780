<template>
  <div class="padding-box">
    <div class="basic-info-box" v-if="details">
      <div class="box-item" v-for="(item, index) in list" :key="index">
        <div class="item-label">{{ item.label }}</div>
        <div class="item-value" v-if="item.isDict">
          {{ dictListFnc()[item.valueKey].filter(val => val.value == details[item.valueKey])[0]?.label }}
        </div>
        <div class="item-value" v-else>{{ details[item.valueKey] }}</div>
      </div>
    </div>
    <el-empty v-else description="当前设备参数未设定"></el-empty>
  </div>
</template>
<script>
import { computed } from "vue";
import { useStore } from "vuex";
import { ElEmpty } from "element-plus"
const list = [
  { label: "计费模式", valueKey: "rateMode",isDict:true },
  { label: "免收数量", valueKey: "freeNum" },
  { label: "阶梯1数量:", valueKey: "step1Num" },
  { label: "阶梯1单价:", valueKey: "step1Price" },
  { label: "阶梯2数量:", valueKey: "step2Num" },
  { label: "阶梯2单价:", valueKey: "step2Price" },
  { label: "阶梯3数量:", valueKey: "step3Num" },
  { label: "阶梯3单价:", valueKey: "step3Price" },
  { label: "阶梯4数量:", valueKey: "step4Num" },
  { label: "阶梯4单价:", valueKey: "step4Price" },
  { label: "阶梯5数量:", valueKey: "step5Num" },
  { label: "阶梯5单价:", valueKey: "step5Price" },
  { label: "阶梯6数量:", valueKey: "step6Num" },
  { label: "阶梯6单价:", valueKey: "step6Price" },
  { label: "阶梯7数量:", valueKey: "step7Num" },
  { label: "阶梯7单价:", valueKey: "step7Price" },
  { label: "阶梯8数量:", valueKey: "step8Num" },
  { label: "阶梯8单价:", valueKey: "step8Price" },
  { label: "阶梯9数量:", valueKey: "step9Num" },
  { label: "阶梯9单价:", valueKey: "step9Price" },
  { label: "阶梯10数量:", valueKey: "step10Num" },
  { label: "阶梯10单价:", valueKey: "step10Price" },
];
export default {
  components: {
    ElEmpty

  },
  setup() {
    const store = useStore();
    const details = computed(() => {
      let data = "";
      for (let item of store.state.deviceParameters[store.state.app.activeTab].detailsParams) {
        if (item.paramType == "WATER") {
          data = JSON.parse(item.paramContent);
          break;
        }
      }
      if (!data) {
        data = ""
      }
      return data;
    });
    const dictListFnc = () => {
      return store.state.deviceParameters[store.state.app.activeTab].dict
    }
    return {
      list,
      details,
      dictListFnc
    };
  },
};
</script>
<style lang="scss" scoped>
.basic-info-box {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  border-left: 1px solid #ccc;
  border-top: 1px solid #ccc;
  min-width: 900px;
  .box-item {
    box-sizing: border-box;
    width: 33.333%;
    min-width: 300px;
    line-height: 50px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #ccc;
    .item-label {
      width: 50%;
      text-align: center;
      background: #f6f6f6;
      border-right: 1px solid #ccc;
    }
    .item-value {
      text-align: center;
      width: 50%;
      background: #fff;
      border-right: 1px solid #ccc;
    }
  }
}
</style>