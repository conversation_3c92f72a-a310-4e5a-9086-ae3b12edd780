<template>
  <div class="padding-box">
    <kade-table-filter @search="getList()" @reset="reset">
      <el-form inline size="mini" label-width="100px">
        <el-form-item label="消费时间">
          <el-date-picker v-model="state.requestDate" type="datetimerange" unlink-panels :default-time="defaultTime" range-separator="~" start-placeholder="请选择日期" end-placeholder="请选择日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="姓名">
          <el-input placeholder="请输入姓名" v-model="state.form.userName" maxlength="30" clearable></el-input>
        </el-form-item>
        <el-form-item label="编号">
          <el-input placeholder="请输入编号" v-model="state.form.userCode" maxlength="30" clearable></el-input>
        </el-form-item>
        <el-form-item label="组织机构:">
          <kade-dept-select-tree style="width: 100%" :value="state.form.deptPath" valueKey="deptPath" :multiple="false" @valueChange="(val) => (state.form.deptPath = val.deptPath)" />
        </el-form-item>
        <el-form-item label="区域">
          <kade-area-select-tree style="width: 100%" :value="state.form.areaPath" valueKey="areaPath" :multiple="false" @valueChange="(val) => state.form.areaPath = val.areaPath" />
        </el-form-item>
        <el-form-item label="设备编号">
          <el-input placeholder="请输入设备编号" v-model="state.form.deviceNo" maxlength="30" clearable></el-input>
        </el-form-item>
        <el-form-item label="设备类型">
          <el-select clearable placeholder="请选择设备类型" v-model="state.form.deviceType">
            <el-option v-for="item in state.DeviceType" :label="item.label" :value="item.value" :key="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="消费类型">
          <el-select clearable placeholder="请选择消费类型" v-model="state.form.consumeType">
            <el-option v-for="item in state.ConsumeType" :label="item.label" :value="item.value" :key="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="支付方式">
          <el-select clearable placeholder="请选择支付方式" v-model="state.form.payType">
            <el-option v-for="item in state.PayType" :label="item.label" :value="item.value" :key="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="订单状态">
          <el-select clearable placeholder="请选择订单状态" v-model="state.form.orderStatus">
            <el-option v-for="item in state.StatusType" :label="item.label" :value="item.value" :key="item.value"></el-option>
          </el-select>
        </el-form-item>

      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="水控交易明细报表" v-loading="state.loading">
      <template #extra>
        <span>
          {{ total() }}
        </span>
      </template>
      <el-table height="55vh" border :data="state.dataList">
        <el-table-column v-for="item in state.column" :key="item.prop" :prop="item.prop" :label="item.label" align="center" :width="item.width" show-overflow-tooltip>
          <template #default="scope">
            {{ content(scope.row, item) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center">
          <template #default="scope">
            <el-button type="text" v-if="isShowFnc(scope.row)" @click="handleRefund(scope.row)">退款</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination background v-model:currentPage="state.form.currentPage" v-model:page-size="state.form.pageSize" :page-sizes="[5, 10, 20, 30]" layout="total,sizes, prev, pager, next, jumper" :total="state.total" @size-change="handleSizeChange" @current-change="handlePageChange">
        </el-pagination>
      </div>
    </kade-table-wrap>
  </div>
</template>
<script>
import {
  ElForm,
  ElFormItem,
  ElInput,
  ElTable,
  ElTableColumn,
  ElDatePicker,
  ElSelect,
  ElOption,
  ElButton,
  ElPagination,
  ElMessage
} from "element-plus";
import { reactive } from '@vue/reactivity'
import { timeStr } from "@/utils/date.js";
import { requestDate, defaultTime } from "@/utils/reqDefaultDate";
import { onMounted } from "@vue/runtime-core";
import deptSelectTree from "@/components/tree/deptSelectTree.vue";
import areaSelectTree from "@/components/tree/areaSelectTree";
import {
  waterTradeDetail, waterTradeRefund
} from "@/applications/eccard-finance/api";
const column = [
  { prop: "userCode", label: "编号" },
  { prop: "userName", label: "姓名", width: '' },
  { prop: "deptName", label: "部门" },
  { prop: "areaName", label: "区域" },
  { prop: "deviceNo", label: "设备编号" },
  { prop: "deviceName", label: "设备名称" },
  { prop: "deviceTypeName", label: "设备类型" },
  { prop: "tradeModeName", label: "消费类型" },
  { prop: "payTypeName", label: "支付方式" },
  { prop: "tradePreBalance", label: "预扣金额" },
  { prop: "tradeAmount", label: "消费金额" },
  // { prop: "totalAmount", label: "优惠金额" },
  { prop: "createTime", label: "消费时间", width: '170px' },
  // { prop: "createTime", label: "上传时间", width: '100px' },
  { prop: "orderStatus", label: "订单状态" },
]
export default {
  components: {
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    ElInput,
    ElTable,
    ElTableColumn,
    ElDatePicker,
    ElSelect,
    ElOption,
    ElPagination,
    ElButton,
    "kade-dept-select-tree": deptSelectTree,
    "kade-area-select-tree": areaSelectTree,
  },
  setup() {
    const state = reactive({
      loading: false,
      column,
      form: {
        currentPage: 1,
        pageSize: 10,
      },
      Class: [],
      Region: [],
      total: 0,
      Generate: {},
      DeviceType: [
        { label: "智能节水控制器", value: "WATER_CONTROLLER" },
        { label: "无线智能节水控制器", value: "WIRELESS_WATER_CONTROLLER" },
        { label: "自助洗衣机", value: "WASHER" },
        { label: "自助洗鞋机", value: "WASHING_SHOES" },
        { label: "自助烘干机", value: "BAKE" },
      ],
      ConsumeType: [
        { value: 3, label: "刷卡" },
        { value: 1, label: "扫码" },
        { value: 5, label: "密码支付" },
      ],
      PayType: [
        { value: 1, label: "余额支付" },
        { value: 2, label: "即时消费" },
      ],
      StatusType: [
        { value: "RUNNING", label: "进行中" },
        { value: "FAIL", label: "失败" },
        { value: "END", label: "已完成" },
      ],
      requestDate: requestDate(),
      dataList: [],
    })

    const getParams = () => {
      let params = { ...state.form }
      if (state.requestDate && state.requestDate.length) {
        params.beginTime = timeStr(state.requestDate[0]);
        params.endTime = timeStr(state.requestDate[1]);
      } else {
        ElMessage.error("请选择消费时间")
        return false
      }
      return params
    }


    const getList = async () => {
      if (!getParams()) return
      let params = getParams()
      state.loading = true
      try {
        let { data: { generate, pageInfo: { total, list } } } = await waterTradeDetail(params)
        state.Generate = generate
        state.dataList = list
        state.total = total
        state.loading = false
      }
      catch {
        state.loading = false
      }
    };
    const content = (row, item) => {
      // scope.row[item.prop] === null ? '--' : scope.row[item.prop]
      if (row[item.prop] === null) {
        return '--'
      } else if (item.prop == 'orderStatus') {
        if (row[item.prop] == 'RUNNING') {
          return '进行中'
        } else if (row[item.prop] == 'FAIL') {
          return '失败'
        } else {
          return '已完成'
        }
      } else {
        return row[item.prop]
      }
    }
    const reset = () => {
      state.form = {
        currentPage: 1,
        pageSize: 10,
      },
        state.requestDate = requestDate()
      getList()
    };
    const total = () => {
      const transactionsCount = state.Generate.transactionsCount ?? 0;
      // const discountAmountTotal = state.Generate.discountAmountTotal ?? 0;
      const balanceConsumptionTotal = state.Generate.balanceConsumptionTotal ?? 0;
      const instantConsumptionTotal = state.Generate.instantConsumptionTotal ?? 0;
      // return `消费笔数合计¥ ${transactionsCount}笔、优惠金额合计¥ ${discountAmountTotal}元、余额消费合计¥ ${balanceConsumptionTotal}元、即时消费合计¥ ${instantConsumptionTotal}元`
      return `消费笔数合计${transactionsCount}笔、余额消费合计¥ ${balanceConsumptionTotal}元、即时消费合计¥ ${instantConsumptionTotal}元`
    }
    const isShowFnc = row => {
      if (row.deviceType !== 'WIRELESS_WATER_CONTROLLER') return false

      if (row.orderStatus == 'RUNNING') {
        let num = 6 * 60 * 60 * 1000
        let nowTime = new Date().getTime()
        let createTime = new Date(row.createTime).getTime()

        return (nowTime - createTime) > num

      } else {
        return false
      }
    }
    const handleRefund = async (row) => {
      state.loading = true;
      try {
        let { code, message } = await waterTradeRefund(row.id);
        if (code === 0) {
          ElMessage.success(message);
          getList()
        }
        state.loading = false;
      } catch {
        state.loading = false;
      }
    }
    //分页 下一页
    const handlePageChange = (val) => {
      state.form.currentPage = val;
      getList();
    };
    //分页 当前多少页
    const handleSizeChange = (val) => {
      state.form.currentPage = 1;
      state.form.pageSize = val;
      getList();
    };
    onMounted(() => {
      // getList();
    });
    return {
      defaultTime,
      state,
      column,
      reset,
      getList,
      content,
      total,
      isShowFnc,
      handleRefund,
      handlePageChange,
      handleSizeChange
    }
  }
}
</script>

<style lang="scss" scoped></style>