<template>
  <kade-modal v-bind="attrs" :modelValue="modelValue" @update:modelValue="update">
    <el-form inline label-width="120px" size="small">
      <el-form-item label="所属区域">
        <kade-area-select-tree style="width: 100%" :value="state.form.areaId" valueKey="id" :multiple="false"
          @valueChange="(val) => (state.form.areaId = val.id)" />
      </el-form-item>
      <el-form-item label="机号">
        <el-input clearable v-model="state.form.deviceNo" placeholder="请输入" />
      </el-form-item>

      <el-form-item label="设备名称">
        <el-input clearable v-model="state.form.deviceName" placeholder="请输入" />
      </el-form-item>
      <el-form-item label="所属设备类型">
        <el-select v-model="state.form.deviceType">
          <el-option v-for="(item, index) in state.deviceTypeList" :key="index" :label="item.cfgValue"
            :value="item.cfgKey"></el-option>
        </el-select>
      </el-form-item>
      <el-button type="primary" size="mini" @click="handleSearch">搜索</el-button>
    </el-form>

    <el-table style="width: 100%" :data="state.dataList" v-loading="state.loading" border stripe
      @selection-change="handleCurrentChange">
      <el-table-column type="selection" width="55"></el-table-column>
      <el-table-column label="设备机号" prop="deviceNo" align="center"></el-table-column>
      <el-table-column label="设备名称" prop="deviceName" align="center"></el-table-column>
      <el-table-column label="所属区域" prop="deviceAddress" align="center"></el-table-column>
      <el-table-column label="设备类型" prop="deviceType" align="center"></el-table-column>
    </el-table>
    <template #footer>
      <el-button icon="el-icon-circle-close" @click="cancel" size="small">取消</el-button>
      <el-button icon="el-icon-circle-check" @click="submit" :loading="state.btnLoading"
        :disabled="!state.selected.length" size="small" type="primary">保存</el-button>
    </template>
  </kade-modal>
</template>
<script>
import { reactive, watch, computed, onMounted } from "vue";
import { ElButton, ElForm, ElFormItem, ElSelect, ElOption, ElInput, ElMessage, ElTable, ElTableColumn } from "element-plus";
import Modal from "@/components/modal";
import { iotCfg } from "@/applications/eccard-iot/api";
import {
  getNotBindDeviceList,
  addMerchantDevice,
} from "@/applications/eccard-finance/api";
import areaSelectTree from "@/components/tree/areaSelectTree.vue";
export default {
  emits: ["update:modelValue", "change"],
  components: {
    "kade-modal": Modal,
    "el-table": ElTable,
    "el-table-column": ElTableColumn,
    "el-button": ElButton,
    ElForm, ElFormItem, ElSelect, ElOption, ElInput,
    "kade-area-select-tree": areaSelectTree,
  },
  props: {
    modelValue: {
      type: Boolean,
      default: false,
    },
    id: {
      type: [String, Number],
      required: true,
    },
  },
  setup(props, context) {
    const state = reactive({
      dataList: [],
      loading: false,
      selected: [],
      deviceTypeList: [],
      btnLoading: false,
      form: {},
    });
    const cancel = () => {
      context.emit("change", false);
    };
    const queryDeviceType = async () => {
      let { data } = await iotCfg();
      state.deviceTypeList = data
    };
    const submit = async () => {
      try {
        state.btnLoading = false;
        const { message, code } = await addMerchantDevice({
          deviceIds: state.selected.map((it) => it.deviceId),
          merchantId: props.id,
        });
        if (code === 0) {
          ElMessage.success(message);
          context.emit("change", true);
        }
      } catch (e) {
        throw new Error(e);
      } finally {
        state.btnLoading = false;
      }
    };
    const update = () => {
      context.emit("change", false);
    };
    const getList = async () => {
      try {
        state.loading = true;
        let params = { ...state.form }
        params.merchantId = props.id
        const { data } = await getNotBindDeviceList(params);
        state.dataList = data;
      } catch (e) {
        throw new Error(e.message);
      } finally {
        state.loading = false;
      }
    }
    const loadData = async () => {
      getList()
    };
    const handleSearch = () => {
      getList()
    }
    const handleCurrentChange = (v) => {
      state.selected = v;
    };
    const attrs = computed(() => {
      // eslint-disable-next-line no-unused-vars
      const { id, ...attrs } = props;
      return { ...attrs, title: "未绑定设备列表" };
    });
    watch(
      () => props.modelValue,
      (v) => {
        if (v && props.id) {
          loadData();
        }
      }
    );
    onMounted(() => {
      queryDeviceType()
    })
    return {
      state,
      cancel,
      submit,
      update,
      attrs,
      handleSearch,
      handleCurrentChange,
    };
  },
};
</script>
<style lang="scss" scoped>
</style>