<template>
  <!-- 个人余额明细 -->
  <div class="cashRecharge" v-loading="state.loading">
    <kade-route-card style="height: auto">
      <kade-table-filter @search="getList()" @reset="reset()">
        <el-form inline size="mini" label-width="100px">
          <el-form-item label="姓名/编号:">
            <el-input placeholder="姓名/编号关键字搜索" v-model="state.form.userName"></el-input>
          </el-form-item>
          <el-form-item label="组织机构:">
            <kade-dept-select-tree style="width: 100%" :value="state.form.deptPath" valueKey="deptPath" :multiple="false" @valueChange="(val) => (state.form.deptPath = val.deptPath)" />
          </el-form-item>
        </el-form>
      </kade-table-filter>
      <kade-table-wrap title="个人账户余额">
        <template #extra>
          <el-button @click="exportClick()" icon="el-icon-daoru" size="mini" class="btn-blue">导出查询明细</el-button>
        </template>
        <el-table style="width: 100%" :data="state.detailList" ref="multipleTable" height="55vh" highlight-current-row border stripe>
          <el-table-column v-for="(item,index) in column" :key="index" :width="item.width" :label="item.label" :prop="item.prop" align="center" show-overflow-tooltip>
          </el-table-column>
        </el-table>
        <div class="pagination">
          <el-pagination background :current-page="state.form.currentPage" :page-size="state.form.pageSize" layout="total, sizes, prev, pager, next, jumper" :page-sizes="[6, 10, 20, 50, 100]" :total="state.total" @current-change="handlePageChange" @size-change="handleSizeChange">
          </el-pagination>
        </div>
      </kade-table-wrap>
    </kade-route-card>
  </div>
</template>
<script>
import {
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElTable,
  ElTableColumn,
  ElPagination,
} from "element-plus";
import { timeStr } from "@/utils/date.js";
import { reactive } from "@vue/reactivity";
import { requestDate } from "@/utils/reqDefaultDate.js";
import {
  getPersonalAccountBalancePage,
  personalAccountBalanceExport,
} from "@/applications/eccard-finance/api";
import { onMounted } from "@vue/runtime-core";
import deptSelectTree from "@/components/tree/deptSelectTree.vue";
const column = [
  { label: "用户编号", prop: "userCode", width: "" },
  { label: "用户姓名", prop: "userName", width: "" },
  { label: "组织机构", prop: "deptName", width: "" },
  { label: "现金余额", prop: "cashBalance", width: "" },
  { label: "补助余额", prop: "subsidyBalance", width: "" },
  { label: "次数余额", prop: "frequencyBalance", width: "" },
]
export default {
  components: {
    "el-button": ElButton,
    "el-input": ElInput,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    "el-table": ElTable,
    "el-table-column": ElTableColumn,
    "el-pagination": ElPagination,
    "kade-dept-select-tree": deptSelectTree,
  },
  setup() {
    const state = reactive({
      loading: false,
      form: {
        currentPage: 1,
        pageSize: 6,
      },
      detailList: [],
      total: 0,
      costTypeList: [],
      departCheckList: [],
      systemUserList: [],
      subsidyTypeList: [],
    });

    const getList = async () => {
      state.loading = true;
      try {
        let {
          code,
          data: {
            personalBalanceList,
            cashBalanceCount,
            frequencyBalanceCount,
            subsidyBalanceCount,
            total,
          },
        } = await getPersonalAccountBalancePage(state.form);
        if (code === 0) {
          state.detailList = personalBalanceList;
          if (state.detailList.length) {
            state.detailList.push({
              cashBalance: cashBalanceCount,
              frequencyBalance: frequencyBalanceCount,
              subsidyBalance: subsidyBalanceCount,
              userCode: "合计",
            });
          }
          state.total = total;
        }
        state.loading = false;
      }
      catch {
        state.loading = false;
      }
    };

    const exportClick = async () => {
      state.loading = true
      try {
        let res = await personalAccountBalanceExport(state.form);
        let url = window.URL.createObjectURL(
          new Blob([res], { type: "application/vnd.ms-excel" })
        );
        let link = document.createElement("a");
        link.style.display = "none";
        link.href = url;
        link.setAttribute("download", "个人余额明细报表.xlsx");
        document.body.appendChild(link);
        link.click();
        state.loading = false
      }
      catch {
        state.loading = false
      }
    };

    const handlePageChange = (val) => {
      state.form.currentPage = val;
      getList();
    };
    const handleSizeChange = (val) => {
      state.form.currentPage = 1;
      state.form.pageSize = val;
      getList();
    };

    const reset = () => {
      state.form = {
        currentPage: 1,
        pageSize: 6,
      };
      state.requestDate = requestDate;
    };
    onMounted(() => {
      getList();
    });
    return {
      column,
      state,
      timeStr,
      exportClick,
      getList,
      handlePageChange,
      handleSizeChange,
      reset,
    };
  },
};
</script>
<style lang="scss" scoped>
:deep(.el-divider--horizontal) {
  margin: 0 0 10px 0;
}
.cashRecharge {
  :deep(.search-box) {
    .el-input__inner {
      width: 193px;
    }
    .el-date-editor {
      width: 400px;
    }
  }
}
</style>
