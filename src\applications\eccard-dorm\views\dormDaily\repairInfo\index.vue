<template>
  <kade-route-card>
    <kade-table-filter @search="handleSearch" @reset="handleReset">
      <el-form :model="state.form" inline size="mini" label-width="90px">
        <el-form-item label="关键字">
          <el-input v-model="state.form.keyWord" placeholder="房间号或房间名称关键字搜索"></el-input>
        </el-form-item>
        <kade-linkage-select :data="linkageData" :value="state.form" @change="linkageChange" />
        <el-form-item label="报修项目">
          <el-select v-model="state.form.repairProjectId" placeholder="全部">
            <el-option v-for="(item,index) in state.repairList" :key="index" :label="item.repairProjectName" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="维修状态">
          <el-select v-model="state.form.repairStatus" placeholder="全部">
            <el-option v-for="(item,index) in statusList" :key="index" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="报修时间">
          <el-date-picker v-model="state.requestDate" type="daterange" range-separator="~" start-placeholder="开始时间" end-placeholder="结束时间"></el-date-picker>
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <kade-table-wrap title=" 报修单列表">
      <template #extra>
        <el-button type="success" icon="el-icon-plus" size="mini" @click="edit('add')">新增报修单</el-button>
        <el-button type="primary" icon="el-icon-delete-solid" size="mini" @click="batchDel">批量删除</el-button>
      </template>
      <el-table border :data="state.data" v-loading="state.loading" @selection-change="selectChange">
        <el-table-column type="selection" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip v-for="(item,index) in column" :key="index" :label="item.label" :prop="item.prop" :width="item.width" align="center">
          <template #default="scope">
            {{dictionaryFilter(scope.row[item.prop])}}
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="200px">
          <template #default="scope">
            <el-button type="text" class="blue" size="mini" @click="edit('edit',scope.row)">编辑</el-button>
            <el-button type="text" class="blue" size="mini" @click="handle(scope.row)">处理</el-button>
            <el-button type="text" class="blue" size="mini" @click="handleDel(scope.row)">删除</el-button>
            <el-button type="text" class="blue" size="mini" @click="edit('details',scope.row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination v-model:currentPage="state.form.currentPage" v-model:page-size="state.form.pageSize" :page-sizes="[10, 20, 30, 40]" background layout="total, sizes, prev, pager, next, jumper" :total="state.total" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      </div>
    </kade-table-wrap>
    <kade-guarantee-edit v-model:modelValue="state.isShow" :type="state.type" :rowData="state.rowData"  @update:modelValue="close"/>
    <kade-guarantee-handle v-model:modelValue="state.dialogVisible" :rowData="state.rowData" @update:modelValue="close" />
  </kade-route-card>
</template>

<script>
import { reactive, onMounted } from 'vue'
import { useDict } from "@/hooks/useDict"
import { dateStr } from "@/utils/date.js"
// import { filterDictionary } from "@/utils/index.js";
import { ElButton, ElTable, ElTableColumn, ElForm, ElFormItem, ElInput, ElSelect, ElOption, ElPagination,ElDatePicker, ElMessage,ElMessageBox } from "element-plus"
import edit from "./components/edit"
import handle from './components/handle'
import linkageSelect from '@/applications/eccard-dorm/components/linkageSelect'
import { getRepairList,batchRepair,delRepair,getRepairItemList } from "@/applications/eccard-dorm/api.js"

const column = [
  { label: '所属区域', prop: 'areaName' },
  { label: '房间', prop: 'roomString', width: '300px' },
  { label: '报修项目', prop: 'repairProjectName' },
  { label: '报修人', prop: 'repairPersonName' },
  { label: '报修人电话', prop: 'repairPersonTel' },
  { label: '报修时间', prop: 'createTime' },
  { label: '维修状态', prop: 'repairStatus', }
]
const linkageData={
  buildingType:{label:'楼栋类型',valueKey:'buildType'},
  area:{label:'所属区域',valueKey:'areaPath',key:'areaPath'},
  building:{label:'楼栋',valueKey:'buildId'},
  unit:{label:'单元',valueKey:'unitNum'},
  floor:{label:'楼层',valueKey:'floorNum'},
}
export default {
  components: {
    ElButton,
    ElTable,
    ElTableColumn,
    ElForm,
    ElFormItem,
    ElInput,
    ElSelect,
    ElOption,
    ElPagination,
    ElDatePicker,
    "kade-guarantee-edit": edit,
    'kade-guarantee-handle':handle,
    "kade-linkage-select": linkageSelect
  },
  setup() {
    const statusList = useDict('DORM_REPAIR_STATUS') //维修状态
    const state = reactive({
      form: {
        currentPage: 1,
        pageSize: 10
      },
      total: 0,
      data: [],
      loading: false,
      requestDate: [],
      rowData: '',
      isShow: false,
      dialogVisible:false,
      type: '',
      selectData:'',
      repairList:[]
    })

    //获取保修项目列表
    const queryRepair = ()=>{
      getRepairItemList().then((res)=>{
        console.log(res)
        state.repairList=res.data
      })
    }
    const handleReset = ()=>{
      state.form={
        currentPage:1,
        pageSize:10
      }
      state.requestDate=[]
      getList()
    }
    const linkageChange = (val)=>{
      state.form={...state.form,...val}
    }
    const handleDel=(row)=>{
      ElMessageBox.confirm(`确认删除信息`,`提示`,{
        type:'warning',
        confirmButtonText:'确定',
        cancelButtonText:'取消'
      }).then(async()=>{
        let {code,message} = await delRepair(row.id)
        if(code===0){
          ElMessage.success(message)
          getList()
        }
      })
    }
    const selectChange=(val)=>{
      state.selectData=val
    }
    const batchDel=()=>{
      if(!state.selectData.length){
        return ElMessage.error('请先选择需要删除的信息！')
      }
      ElMessageBox.confirm(`确认删除已选择信息`,`提示`,{
        type:'warning',
        confirmButtonText:'确定',
        cancelButtonText:'取消'
      }).then(async()=>{
        let param = state.selectData.map((item)=>item.id).join(',')
        let {code,message} = await batchRepair(param)
        if(code===0){
          ElMessage.success(message)
          getList()
        }
      })
    }
    const handleSearch=()=>{
      getList()
    }
    const handleCurrentChange=val=>{
      state.form.currentPage=val
      getList()
    }
    const handleSizeChange=val=>{
      state.form.currentPage=1
      state.form.pageSize=val
      getList()
    }
    const edit = (type, row) => {
      state.type = type
      state.rowData = row
      state.isShow = true
    }
    const handle = (row) => {
      state.rowData = row
      state.dialogVisible = true
    }
    const getList=async()=>{
      if(state.requestDate&&state.requestDate.length){
      state.form.repairBeginDate=dateStr(state.requestDate[0])
      state.form.repairEndDate=dateStr(state.requestDate[1])
      }else{
        delete state.form.repairBeginDate
        delete state.form.repairEndDate
      }
      state.loading=true
      try{
      let {data:{list,total}} = await getRepairList(state.form)
      state.data=list
      state.total=total
      state.loading=false
      }
      catch{
        state.loading=false
      }
    }
    const close = (val)=>{
      if(val){
        getList()
      }
      state.dialogVisible=false
      state.isShow=false
    }
    onMounted(()=>{
      getList(),
      queryRepair()
    })
    return {
      state,
      column,
      edit,
      handle,
      close,
      batchDel,
      handleDel,
      statusList,
      linkageData,
      handleCurrentChange,
      handleReset,
      handleSearch,
      linkageChange,
      selectChange,
      handleSizeChange,
    }
  }
}
</script>

<style lang="scss" scoped>
.blue :hover {
  text-decoration: underline;
}
:deep(.el-input--mini .el-input__inner) {
  width: 198px;
}
:deep(.el-pagination .el-select .el-input .el-input__inner){
  width: 100px;
}
:deep(.el-pagination__editor.el-input .el-input__inner){
  width: 46px;
}
:deep(.el-dialog){
  border-radius: 6px;
  padding-bottom: 40px;
}
:deep(.el-dialog__header){
  border-bottom: 1px solid #eeeeee;
  margin-bottom: 20px;
}
:deep(.el-dialog__footer){
  border: none;
  text-align: center;
  margin-top: 10px;
}
</style>