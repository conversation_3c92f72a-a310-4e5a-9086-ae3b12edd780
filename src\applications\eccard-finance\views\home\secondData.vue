<template>
    <div class="title-box">数据标题</div>
    <div id="chartline" class="chartline"></div>
</template>

<script>
import * as echarts from "echarts";
import { onMounted } from "vue";

export default {
    setup() {
        const echartInit = () => {
            var chartDom = document.getElementById("chartline");
            var myChart = echarts.init(chartDom);
            var option = {
                tooltip: {
                    axisPointer: {
                        type: "shadow",
                    },
                },
                legend: {
                    orient: "horizontal",
                    bottom: "0%",
                    left: "5%",
                    itemGap: 25,
                    itemWidth: 7,
                    itemHeight: 7,
                    icon: "circle",
                },
                grid: {
                    top: "15%",
                    left: "5%",
                    right: "10%",
                    bottom: "20%",
                    containLabel: true,
                },
                xAxis: {
                    splitLine: {
                        show: false,
                    },
                    axisLine: {
                        lineStyle: {
                            color: "#9498b0s",
                        },
                    },
                    axisLabel: {
                        margin: 10,
                        textStyle: {
                            fontSize: 12,
                        },
                    },
                },
                yAxis: {
                    axisLine: {
                        show: false,
                    },
                    axisTick: {
                        show: false,
                    },
                    splitLine: {
                        lineStyle: {
                            type: "dashed",
                        },
                    },
                    axisLabel: {
                        margin: 20,
                        textStyle: {
                            fontSize: 13,
                        },
                    },
                },
                series: [
                    {
                        name: "付款订单",
                        symbolSize: 20,
                        color: "#badeff",
                        data: [
                            [1.1, 2.2],
                            [2.2, 3.3],
                            [3.3, 4.4],
                            [4.4, 5.5],
                            [5.5, 4.4],
                            [6.6, 3.3],
                            [7.7, 2.2],
                        ],
                        type: "scatter",
                    },
                ],
            };
            myChart.setOption(option);
        };
        onMounted(() => {
            echartInit();
        });
        return {
            echartInit,
        };
    },
};
</script>

<style lang="scss" scoped>
.title-box {
    font-size: 16px;
    font-weight: 600;
    border-bottom: 1px solid #eeeeee;
    padding: 0 0 10px 10px;
    margin-right: 10px;
}
.chartline {
    height: 260px;
    width: 100%;
}
</style>