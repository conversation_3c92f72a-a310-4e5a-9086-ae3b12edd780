<template>
  <kade-route-card>
    <kade-table-filter @search="handleSearch" @reset="handleReset">
      <el-form label-width="100px" size="mini" inline>
        <kade-linkage-select :data="linkageData" :value="state.form" @change="linkageChange" />
        <el-form-item label="人员编号">
          <el-input v-model="state.form.userCode" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="人员姓名">
          <el-input v-model="state.form.userName" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="组织机构">
          <kade-dept-select-tree style="width: 100%" :value="state.form.deptPath" valueKey="deptPath" :multiple="false"
            @valueChange="(val) => (state.form.deptPath = val.deptPath)" />
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="人员入住列表">
      <template #extra>
        <el-button icon="el-icon-daochu" size="mini" class="btn-purple" @click="handleExport">导出</el-button>
      </template>
      <el-table border :data="state.data" v-loading="state.loading" height="55vh">
        <el-table-column show-overflow-tooltip v-for="(item, index) in column" :key="index" :label="item.label"
          :prop="item.prop" :width="item.width" align="center"></el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination v-model:currentPage="state.form.currentPage" v-model:page-size="state.form.pageSize"
          :page-sizes="[10, 20, 50, 100,200]" background layout="total, sizes, prev, pager, next, jumper" :total="state.total" @size-change="handleSizeChange"
          @current-change="handleCurrentChange" />
      </div>
    </kade-table-wrap>

  </kade-route-card>
</template>

<script>
import { onMounted, reactive } from "vue";
import linkageSelect from "../../../components/linkageSelect.vue";
import deptSelectTree from "@/components/tree/deptSelectTree.vue";
import { personStayInfoList,exportPersonStayInfo } from '@/applications/eccard-dorm/api.js'
import {
  ElForm,
  ElFormItem,
  ElInput,
  ElButton,
  ElTable,
  ElTableColumn,
  ElPagination
} from "element-plus";
const linkageData = {
  area: { label: "区域", valueKey: "areaPath", key: "areaPath" },
  buildingType: { label: "楼栋类型", valueKey: "buildType" },
  building: { label: "楼栋", valueKey: "buildId" },
  unit: { label: "单元", valueKey: "unitNum" },
  floor: { label: "楼层", valueKey: "floorNum" },
  room: { label: "房间", valueKey: "roomId" },
};
const column = [
  { label: "区域", prop: "areaName" },
  { label: "楼栋类型", prop: "buildName" },
  { label: "房间", prop: "roomString", width: "300px" },
  { label: "床位号", prop: "bedNum" },
  { label: "人员编号", prop: "userCode" },
  { label: "人员姓名", prop: "userName" },
  { label: "组织机构", prop: "deptName", width: "300px" },
  { label: "分配时间", prop: "createTime" },
];
export default {
  components: {
    ElForm,
    ElFormItem,
    ElInput,
    ElButton,
    ElTable,
    ElTableColumn,
    ElPagination,
    "kade-linkage-select": linkageSelect,
    "kade-dept-select-tree": deptSelectTree,
  },
  setup() {
    const state = reactive({
      form: {
        currentPage: 1,
        pageSize: 10,
      },
      data: [],
      total: 0,
      loading: false,
    });
    const getList = async () => {
      state.loading = true
      try {
        let { data: { list,total } } = await personStayInfoList(state.form)
        state.data = list
        state.total = total
        state.loading = false
      }
      catch {
        state.loading = false
      }
    }
    const linkageChange = (val) => {
      state.form = { ...state.form, ...val }
    }
    const handleSizeChange = (val) => {
      state.form.currentPage = 1,
        state.form.pageSize = val
      getList()
    }
    const handleCurrentChange = (val) => {
      state.form.currentPage = val
      getList()
    }
    const handleSearch = () => {
      getList()
    };
    const handleReset = () => {
      state.form = {
        currentPage: 1,
        pageSize: 10
      }
      getList()
    };
    const handleExport= async()=>{
      let res = await exportPersonStayInfo(state.form)
      let url = window.URL.createObjectURL(
        new Blob([res], { type: "application/vnd.ms-excel" })
      );
      let link = document.createElement("a");
      link.style.display = "none";
      link.href=url
      link.setAttribute("download",'人员入住列表.xlsx')
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }
    onMounted(() => {
      getList()
    })
    return {
      state,
      handleReset,
      handleSearch,
      linkageData,
      column,
      linkageChange,
      handleSizeChange,
      handleCurrentChange,
      handleExport
    };
  },
};
</script>

<style lang="scss" scoped>
:deep(.el-input--mini .el-input__inner) {
  width: 192px;
}

:deep(.el-pagination .el-select .el-input .el-input__inner) {
  width: 100px;
}

:deep(.el-pagination__editor.el-input .el-input__inner) {
  width: 46px;
}
</style>