<template>
  <kade-route-card>
    <kade-table-filter @search="handleSearch" @reset="handleReset">
      <el-form inline label-width="100px" size="small">
        <el-form-item label="终端型号">
          <el-select clearable v-model="state.form.deviceModel" placeholder="全部">
            <el-option v-for="(item, index) in state.listData.modelList" :key="index" :label="item.productMode"
              :value="item.productMode">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="所属区域">
          <kade-area-select-tree style="width: 100%" :value="state.form.areaId" valueKey="id" :multiple="false"
            @valueChange="(val) => (state.form.areaId = val.id)" />
        </el-form-item>
        <el-form-item label="机号">
          <el-input clearable v-model="state.form.deviceNo" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="所属商户" v-if="TabModule().isSelectMerchant">
          <el-select clearable v-model="state.form.userName">
            <el-option :label="item.merchantName" :value="item.merchantId"
              v-for="(item, index) in state.listData.merchantList" :key="index"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="设备状态">
          <el-select clearable v-model="state.form.deviceStatus" placeholder="全部">
            <el-option v-for="(item, index) in deviceStaticList" :key="index" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="设备名称">
          <el-input clearable v-model="state.form.deviceName" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="设备IP">
          <el-input clearable v-model="state.form.deviceIp" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="所属工作站">
          <el-select clearable v-model="state.form.workstationId">
            <el-option :label="item.name" :value="item.id" v-for="(item, index) in state.listData.workStationList"
              :key="index"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="连接类型">
          <el-select clearable v-model="state.form.deviceConnectType">
            <el-option :label="item.label" :value="item.value" v-for="(item, index) in deviceConnectTypeList"
              :key="index"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <kade-table-wrap :title="TabModule().title + '列表'">
      <template #extra>
        <el-button icon="el-icon-plus" @click="edit({})" size="small" class="btn-green">新增</el-button>
        <el-button icon="el-icon-daorutupian" v-if="TabModule().isBatchAdd" @click="handleBatchAdd" size="small"
          class="btn-purple">批量新增</el-button>
        <el-button icon="el-icon-daorutupian" @click="addRecord()" size="small" class="btn-yellow">更换设备</el-button>
        <el-button icon="el-icon-daochu" size="small" class="btn-deep-blue" @click="handleExport">导出</el-button>

      </template>
      <el-table style="width: 100%" height="55vh" v-loading="state.loading" :data="state.dataList" @rowClick="rowClick"
        highlight-current-row border stripe>
        <el-table-column v-for="(item, index) in column()" :key="index" :width="item.width" :prop="item.prop"
          :label="item.label" align="center" show-overflow-tooltip>
          <template #default="scope" v-if="item.isDict">
            {{ dictionaryFilter(scope.row[item.prop]) }}
          </template>

        </el-table-column>
        <el-table-column label="操作" align="center" width="180px">
          <template #default="scope">
            <el-button class="green" size="mini" @click="detail(scope.row)" type="text">详情</el-button>
            <el-button class="green" size="mini" @click="edit(scope.row)" type="text">编辑</el-button>
            <el-button class="green" size="mini" @click="del(scope.row)" type="text">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination background :current-page="state.form.currentPage"
          layout="total, sizes, prev, pager, next, jumper" :page-sizes="[10, 20, 50, 100, 200]" :total="state.total"
          :page-size="state.form.pageSize" @current-change="handlePageChange" @size-change="handleSizeChange">
        </el-pagination>
      </div>
    </kade-table-wrap>
    <kade-device-edit :isShow="state.isEdit" :data="state.selectRow" :TabModule="TabModule" :listData="state.listData"
      @close="editClose" />
    <kade-batch-add :isShow="state.isBatchAdd" :TabModule="TabModule" :listData="state.listData"
      @close="BatchAddClose" />
    <kade-device-details :isShow="state.isDetails" :data="state.selectRow" :TabModule="TabModule"
      @close="state.isDetails = false" />
    <kade-replace-record :data="state.selectRow" :TabModule="TabModule" />
  </kade-route-card>
</template>
<script>
import { ElButton, ElForm, ElFormItem, ElInput, ElSelect, ElOption, ElTable, ElTableColumn, ElPagination, ElMessage, ElMessageBox } from "element-plus";
import { onMounted, reactive } from 'vue';
import { useStore } from 'vuex';
import { downloadXlsx } from "@/utils"
import { useDict } from "@/hooks/useDict.js";
import {
  getModel,
  getWorkStationList,
  deviceFactoryList,
  getMerchantList,
  identityGatewayListNoPage,
  hydropowerGatewayListNoPage,
  cameraDeviceList,
  cameraDeviceAdd,
  cameraDeviceEdit,
  cameraDeviceDel,
  cameraDeviceExport,
  doorLockDeviceList,
  doorLockDeviceAdd,
  doorLockDeviceEdit,
  doorLockDeviceDel,
  doorLockDeviceBatchAdd,
  electricControlDeviceList,
  electricControlDeviceAdd,
  electricControlDeviceBatchAdd,
  electricControlDeviceEdit,
  electricControlDeviceDel,
  electricControlDeviceExport,
  fingerprintList,
  fingerprintAdd,
  fingerprintEdit,
  fingerprintDel,
  fingerprintExport,
  fingerprintBatchAdd,
  visitorList,
  visitorDeviceAdd,
  visitorDeviceEdit,
  visitorDeviceDel,
  visitorExport,
  visitorBatchAdd,
  attendanceDeviceList,
  attendanceDeviceAdd,
  attendanceDeviceBatchAdd,
  attendanceDeviceEdit,
  attendanceDeviceDel,
  attendanceDeviceExport,
  analyseDeviceList,
  analyseDeviceAdd,
  analyseDeviceEdit,
  analyseDeviceDel,
  analyseDeviceExport
} from "@/applications/eccard-iot/api";
import areaSelectTree from "@/components/tree/areaSelectTree.vue";
import edit from "./components/edit.vue"
import batchAdd from "./components/batchAdd.vue"
import details from "./components/details"
import replaceRecord from "./components/replaceRecord"
export default {
  components: {
    ElButton,
    ElForm,
    ElFormItem,
    ElInput,
    ElSelect,
    ElOption,
    ElTable,
    ElTableColumn,
    ElPagination,
    "kade-area-select-tree": areaSelectTree,
    'kade-device-edit': edit,
    'kade-device-details': details,
    "kade-replace-record": replaceRecord,
    "kade-batch-add": batchAdd,

  },
  setup() {
    const store = useStore()
    const deviceStaticList = useDict("SYS_DEVICE_STATICE")
    const deviceConnectTypeList = useDict("SYS_DEVICE_CONNECT_TYPE")
    const camamerDirectionList = useDict("SYS_DEVICE_CAMAMER_DIRECTION")
    const powerControllerList = useDict("SYS_POWER_TYPE")
    const state = reactive({
      loading: false,
      isEdit: false,
      isDetails: false,
      isBind: false,
      isBatchAdd: false,
      form: {
        pageNum: 1,
        pageSize: 10
      },
      dataList: [],
      total: 0,
      selectRow: {},
      listData: {
        modelList: [],
        workStationList: [],
        deviceFactoryList: [],
        gatewayList: [],
        merchantList: [],
      },
    })
    /**
     * TabModule function
     * title 模块名称
     * isBatchAdd 是否含有批量新增
     * formList 新增设备时设备扩展信息
     * dataListFnc 设备列表请求函数
     * dataListAddFnc 新增设备
     * dataListEditFnc 修改设备
     * dataListBatchAddFnc 批量新增设备
     * dataListDelFnc 删除设备
     * dataListGatewayfnc 网关列表接口
     * exportFnc //导出接口
    */
    const TabModule = () => {
      let type = store.state.app.activeTab;
      if (type === "accessAIO") {
        return {
          deviceType: "",
          title: "指纹门禁一体机", type, isBatchAdd: true, formList: [
            { type: 'input', valueKey: "devicePassword", label: "通讯密码", placeholder: "请输入", batchIsShow: true, isValid: true, detailsKey: "devicePassword" },
            { type: 'input', valueKey: "devicePort", label: "端口号", placeholder: "请输入", batchIsShow: true, isValid: true, detailsKey: "devicePort" },
          ],
          dataListGatewayfnc: identityGatewayListNoPage,
          dataListFnc: fingerprintList,
          dataListAddFnc: fingerprintAdd,
          dataListEditFnc: fingerprintEdit,
          dataListBatchAddFnc: fingerprintBatchAdd,
          dataListDelFnc: fingerprintDel,
          exportFnc: fingerprintExport
        };
      } else if (type === "visitorDevice") {
        return {
          deviceType: "",
          title: "智能访客终端", type, isBatchAdd: true, formList: [
            { type: 'input', valueKey: "deviceCardCount", label: "临时卡数量", placeholder: "请输入", batchIsShow: true, isValid: true, detailsKey: 'deviceCardCount' },
            { type: 'input', valueKey: "verifyCode", label: "设备验证码", placeholder: "请输入", batchIsShow: true, isValid: true, detailsKey: 'verifyCode' },
          ],
          dataListGatewayfnc: identityGatewayListNoPage,
          dataListFnc: visitorList,
          dataListAddFnc: visitorDeviceAdd,
          dataListEditFnc: visitorDeviceEdit,
          dataListBatchAddFnc: visitorBatchAdd,
          dataListDelFnc: visitorDeviceDel,
          exportFnc: visitorExport
        };
      } else if (type === "camera") {
        return {
          deviceType: "",
          title: "摄像头", type, isBatchAdd: false, formList: [
            { type: 'input', valueKey: "devicePort", label: "端口号", placeholder: "请输入", batchIsShow: true, isValid: true, detailsKey: "devicePort" },
            { type: 'input', valueKey: "deviceAccount", label: "通讯账号", placeholder: "请输入", batchIsShow: true, isValid: true, detailsKey: "deviceAccount" },
            { type: 'input', valueKey: "devicePassword", label: "通讯密码", placeholder: "请输入", batchIsShow: true, isValid: true, detailsKey: "devicePassword" },
            {
              type: 'select', valueKey: "deviceDirection", label: "方向", placeholder: "请选择", batchIsShow: true, isValid: true, isDict: true, list: camamerDirectionList
            },
          ],
          dataListFnc: cameraDeviceList,
          dataListAddFnc: cameraDeviceAdd,
          dataListEditFnc: cameraDeviceEdit,
          dataListDelFnc: cameraDeviceDel,
          dataListGatewayfnc: identityGatewayListNoPage,
          exportFnc: cameraDeviceExport,
        };
      } else if (type === "doorLock") {
        return {
          deviceType: "",
          title: "智能门锁",
          type,
          isBatchAdd: true,
          dataListFnc: doorLockDeviceList,
          dataListAddFnc: doorLockDeviceAdd,
          dataListEditFnc: doorLockDeviceEdit,
          dataListBatchAddFnc: doorLockDeviceBatchAdd,
          dataListDelFnc: doorLockDeviceDel,
          dataListGatewayfnc: identityGatewayListNoPage,
        };
      } else if (type === "attendanceDevice") {
        return {
          deviceType: "",
          title: "人脸识别考勤终端", type, isBatchAdd: true, formList: [
            { type: 'input', valueKey: "devicePort", label: "端口号", placeholder: "请输入", batchIsShow: true, isValid: true, detailsKey: "devicePort" },
            { type: 'input', valueKey: "deviceAccount", label: "通讯账号", placeholder: "请输入", batchIsShow: true, isValid: true, detailsKey: "deviceAccount" },
            { type: 'input', valueKey: "devicePassword", label: "通讯密码", placeholder: "请输入", batchIsShow: true, isValid: true, detailsKey: "devicePassword" },
          ],
          dataListGatewayfnc: identityGatewayListNoPage,
          dataListFnc: attendanceDeviceList,
          dataListAddFnc: attendanceDeviceAdd,
          dataListEditFnc: attendanceDeviceEdit,
          dataListBatchAddFnc: attendanceDeviceBatchAdd,
          dataListDelFnc: attendanceDeviceDel,
          exportFnc: attendanceDeviceExport
        };
      } else if (type === "faceAnalysisDevice") {
        return {
          deviceType: "FACE_RECOGNITION_ANALYSIS_TERMINAL",
          title: "智能人脸分析盒", type, isBatchAdd: false, formList: [
            { type: 'input', valueKey: "devicePort", label: "端口号", placeholder: "请输入", batchIsShow: true, isValid: true, detailsKey: "devicePort" },
            { type: 'input', valueKey: "deviceAccount", label: "通讯账号", placeholder: "请输入", batchIsShow: true, isValid: true, detailsKey: "deviceAccount" },
            { type: 'input', valueKey: "devicePassword", label: "通讯密码", placeholder: "请输入", batchIsShow: true, isValid: true, detailsKey: "devicePassword" },
            { type: 'input', valueKey: "verifyCode", label: "验证码", placeholder: "请输入", batchIsShow: true, isValid: true, detailsKey: "verifyCode" },
          ],
          dataListGatewayfnc: identityGatewayListNoPage,
          dataListFnc: analyseDeviceList,
          dataListAddFnc: analyseDeviceAdd,
          dataListEditFnc: analyseDeviceEdit,
          dataListDelFnc: analyseDeviceDel,
          exportFnc: analyseDeviceExport
        };
      }/*  else if (type === "leaveDevice") {
        return {
          title: "人脸识别考勤终端", type, isBatchAdd: true,
          dataListGatewayfnc: identityGatewayListNoPage,
          dataListFnc: attendanceDeviceList,
          dataListAddFnc: attendanceDeviceAdd,
          dataListEditFnc: attendanceDeviceEdit,
          dataListBatchAddFnc: attendanceDeviceBatchAdd,
          dataListDelFnc: attendanceDeviceDel,
          exportFnc: attendanceDeviceExport
        };
      } */ else if (type === "bigScreenDevice") {
        return {
          deviceType: "",
          title: "智慧大屏终端", type, isSelectMerchant: true, formList: [
            {
              type: 'select', valueKey: "", label: "所属商户", placeholder: "请选择", batchIsShow: true, list: state.listData.merchantList.map(item => {
                return { label: item.merchantName, value: item.merchantId }
              })
            },
            {
              type: 'select', valueKey: "", label: "设备类型", placeholder: "请选择", batchIsShow: true, list: [
                { label: "监控类", value: "" },
                { label: "通知类", value: "" },
                { label: "展示类", value: "" },
              ]
            },
            {
              type: 'select', valueKey: "", label: "操作系统", placeholder: "请选择", batchIsShow: true, list: [
                { label: "Android", value: "" },
                { label: "Windows7", value: "" },
                { label: "Windows10", value: "" },
              ]
            },
            {
              type: 'select', valueKey: "", label: "屏幕尺寸", placeholder: "请选择", batchIsShow: true, list: [
                { label: "50寸", value: "" },
                { label: "55寸", value: "" },
                { label: "60寸", value: "" },
                { label: "65寸", value: "" },
                { label: "70寸", value: "" },
                { label: "75寸", value: "" },
              ]
            },
            {
              type: 'select', valueKey: "", label: "分辨率", placeholder: "请选择", batchIsShow: true, list: [
                { label: "1920*1080", value: "" },
                { label: "1920*1200", value: "" },
                { label: "3840*2160", value: "" },
              ]
            },
          ],
          dataListGatewayfnc: identityGatewayListNoPage,
        };
      } else if (type === "meetingSignInDevice") {
        return {
          deviceType: "CONFERENCE_TERMINAL",
          title: "会议签到终端", type, isBatchAdd: true, formList: [
            { type: 'input', valueKey: "", label: "端口号", placeholder: "请输入", batchIsShow: true },
            { type: 'input', valueKey: "", label: "MAC位址", placeholder: "请输入", batchIsShow: false },
          ],
          dataListGatewayfnc: identityGatewayListNoPage,
        };
      } else if (type === "electronicClassDevice") {
        return {
          deviceType: "ELECTRONIC_CARD_TERMINAL",
          title: "电子班牌终端", type, isBatchAdd: true,
          dataListGatewayfnc: identityGatewayListNoPage,
        };
      } else if (type === "powerController") {
        return {
          deviceType: "POWER_CONTROLLER",
          title: "智能电表", type, isSelectMerchant: true, isBatchAdd: true, formList: [
            {
              type: 'select', valueKey: "merchantId", label: "所属商户", placeholder: "请选择", batchIsShow: true, list: state.listData.merchantList.map(item => {
                return { label: item.merchantName, value: item.merchantId, }
              }), detailsKey: "merchantName"
            },
            {
              type: 'select', valueKey: "electricType", label: "电表类型", placeholder: "请选择", batchIsShow: true, list: powerControllerList,
              isValid: true, isDict: true, detailsKey: "electricType"
            },
            { type: 'input', valueKey: "electricNum", label: "电表集中器号", placeholder: "请输入", batchIsShow: true, isValid: true, detailsKey: "electricNum" },
            { type: 'input', valueKey: "channelNum", label: "通道号", placeholder: "请输入", batchIsShow: true, isValid: true, detailsKey: "channelNum" },
          ],
          dataListGatewayfnc: hydropowerGatewayListNoPage,
          dataListFnc: electricControlDeviceList,
          dataListAddFnc: electricControlDeviceAdd,
          dataListEditFnc: electricControlDeviceEdit,
          dataListBatchAddFnc: electricControlDeviceBatchAdd,
          dataListDelFnc: electricControlDeviceDel,
          exportFnc: electricControlDeviceExport,
        };
      } else if (type === "icCardDevice") {
        return {
          deviceType: "",
          title: "IC卡读写器", type, isBatchAdd: true,
          dataListGatewayfnc: identityGatewayListNoPage,
        };
      } else if (type === "beforeMachine") {
        return {
          deviceType: "",
          title: "前置服务网关", type, isBatchAdd: true,
          dataListGatewayfnc: identityGatewayListNoPage,
        };
      } else if (type === "passwordServer") {
        return {
          deviceType: "",
          title: "秘钥加密服务器", type, isBatchAdd: true,
          dataListGatewayfnc: identityGatewayListNoPage,
        };
      } else if (type === "financeEncryption") {
        return {
          deviceType: "",
          title: "金融加密机", type, isBatchAdd: true,
          dataListGatewayfnc: identityGatewayListNoPage,
        };
      } else {
        return {
          deviceType: "",
          title: "设备", type: "device", isBatchAdd: true,
          dataListGatewayfnc: identityGatewayListNoPage,
        };
      }
    };
    const column = () => {
      if (TabModule().isSelectMerchant) {
        return [
          { prop: "deviceModel", label: "终端型号", width: "" },
          { prop: "areaName", label: "所属区域", width: "" },
          { prop: "merchantName", label: "所属商户", width: "", },
          { prop: "deviceNo", label: "设备机号", width: "" },
          { prop: "deviceName", label: "设备名称", width: "" },
          { prop: "deviceStatus", label: "设备状态", width: "", isDict: true },
          { prop: "deviceConnectType", label: "连接类型", width: "", isDict: true },
          { prop: "deviceIp", label: "设备IP", width: "" },
        ]
      } else {
        return [
          { prop: "deviceModel", label: "终端型号", width: "" },
          { prop: "areaName", label: "所属区域", width: "" },
          { prop: "deviceNo", label: "设备机号", width: "" },
          { prop: "deviceName", label: "设备名称", width: "" },
          { prop: "deviceStatus", label: "设备状态", width: "", isDict: true },
          { prop: "deviceConnectType", label: "连接类型", width: "", isDict: true },
          { prop: "deviceIp", label: "设备IP", width: "" },
        ]
      }
    }
    //获取终端型号
    const queryModel = async () => {
      let { data } = await getModel({ productClass: TabModule().deviceType });
      state.listData.modelList = data;
    };
    //获取工作站
    const queryWorkStationList = async () => {
      let { data: { list } } = await getWorkStationList();
      state.listData.workStationList = list;
    };
    //获取网关
    const queryGatewayList = async () => {
      let { data } = await TabModule().dataListGatewayfnc();
      state.listData.gatewayList = data;
    };
    const getDeviceFactoryList = async () => {
      let { data: { list } } = await deviceFactoryList();
      state.listData.deviceFactoryList = list;
    };
    //获取商户列表
    const queryMerchantList = async () => {
      if (!TabModule().isSelectMerchant) return
      let { data } = await getMerchantList();
      state.listData.merchantList = data;
    };

    const getList = async () => {
      state.selectRow = {}
      for (let key in state.form) {
        if (!state.form[key]) {
          delete state.form[key]
        }
      }
      state.loading = true
      try {
        let { data: { list, total } } = await TabModule().dataListFnc(state.form)
        state.dataList = list
        state.total = total
        state.loading = false
      }
      catch {
        state.loading = false
      }
    }
    const rowClick = row => {
      state.selectRow = row
    }
    const edit = (row) => {
      state.selectRow = row
      state.isEdit = true
    }
    const handleBatchAdd = () => {

      state.isBatchAdd = true

    }
    const detail = (row) => {
      state.selectRow = row
      state.isDetails = true
    }
    const addRecord = () => {
      if (!state.selectRow.id) {
        return ElMessage.error("请选择设备！")
      }
      store.commit("device/updateState", {
        key: store.state.app.activeTab,
        payload: {
          rowData: {},
          deviceType: store.state['device'].isRecord[store.state.app.activeTab].deviceType,
          isShow: true
        },
      });
    }
    const del = (row) => {
      ElMessageBox.confirm("确认删除?", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        let { code, message } = await TabModule().dataListDelFnc(row.id);
        if (code === 0) {
          ElMessage.success(message);
          getList();
        }
      });
    }
    const handleExport = async () => {
      if (!TabModule().exportFnc) return
      state.loading = true
      try {
        let res = await TabModule().exportFnc(state.form)
        downloadXlsx(res, TabModule().title + '列表.xlsx')
        state.loading = false
      }
      catch {
        state.loading = false
      }
    }
    const handleSearch = () => {
      state.form.pageNum = 1
      getList()
    }
    const handleReset = () => {
      state.form = {
        pageNum: 1,
        pageSize: 10
      }
    }
    const handlePageChange = (val) => {
      state.form.pageNum = val
      getList()
    }
    const handleSizeChange = (val) => {
      state.form.pageNum = 1
      state.form.pageSize = val
      getList()
    }
    const editClose = (val) => {
      if (val) {
        getList()
      }
      state.isEdit = false
    }
    const BatchAddClose = (val) => {
      if (val) {
        getList()
      }
      state.isBatchAdd = false
    }
    onMounted(() => {
      getList()
      queryModel()
      queryWorkStationList()
      getDeviceFactoryList()
      queryGatewayList()
      queryMerchantList()
    })
    return {
      column,
      deviceStaticList,
      deviceConnectTypeList,
      state,
      TabModule,
      rowClick,
      edit,
      del,
      handleBatchAdd,
      detail,
      addRecord,
      handleExport,
      handleSearch,
      handleReset,
      handlePageChange,
      handleSizeChange,
      editClose,
      BatchAddClose
    }
  }
};
</script>
<style lang="scss" scoped>
:deep(.el-form-item__content) {
  .el-select {
    width: 200px;
  }

  .el-input__inner {
    width: 200px;
  }
}

:deep(.el-dialog__header) {
  border-bottom: 1px solid #efefef;
}

:deep(.el-overlay) {
  position: absolute;
  background-color: rgba(0, 0, 0, 0.3);
}

:deep(.el-dialog__footer) {
  text-align: center;
}
</style>
