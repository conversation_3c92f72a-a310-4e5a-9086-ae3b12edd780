<template>
  <div class="padding-form-box setBasicParam" v-loading="state.loading">
    <el-form inline size="small" label-width="200px">
      <el-col>
        <el-row :span="24">
          <el-form-item label="计费模式:">
            <el-select v-model="state.form.paramContent.rateMode">
              <el-option v-for="(item,index) in dictListFnc().rateMode" :key="index" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="免收数量:">
            <el-input-number :min="0" :max="100" v-model="state.form.paramContent.freeNum"></el-input-number>
          </el-form-item>
        </el-row>
        <el-row :span="6">
          <el-form-item :label="item.label" v-for="(item, index) in formList" :key="index">
            <el-input-number :min="0" v-model="state.form.paramContent[item.valueKey]" :precision="index%2==1?2:0"></el-input-number>
          </el-form-item>
        </el-row>
      </el-col>
    </el-form>
    <div style="text-align: center">
      <el-button size="mini" @click="beforeClose()">取消</el-button>
      <el-button size="mini" type="primary" @click="saveClick()">保存</el-button>
    </div>
  </div>

</template>

<script>
import {
  ElButton,
  ElCol,
  ElRow,
  ElForm,
  ElFormItem,
  ElInputNumber,
  ElOption,
  ElSelect,
  ElMessage,
} from "element-plus";
import { onMounted, reactive } from "vue";
import { useStore } from "vuex";
const option = [
  {
    value: 1,
    label: "不计费",
  },
  {
    value: 0,
    label: "统一价格",
  },
  {
    value: 2,
    label: "阶梯价格",
  },
];
const formList = [
  { label: "阶梯1数量:", valueKey: "step1Num" },
  { label: "阶梯1单价:", valueKey: "step1Price" },
  { label: "阶梯2数量:", valueKey: "step2Num" },
  { label: "阶梯2单价:", valueKey: "step2Price" },
  { label: "阶梯3数量:", valueKey: "step3Num" },
  { label: "阶梯3单价:", valueKey: "step3Price" },
  { label: "阶梯4数量:", valueKey: "step4Num" },
  { label: "阶梯4单价:", valueKey: "step4Price" },
  { label: "阶梯5数量:", valueKey: "step5Num" },
  { label: "阶梯5单价:", valueKey: "step5Price" },
  { label: "阶梯6数量:", valueKey: "step6Num" },
  { label: "阶梯6单价:", valueKey: "step6Price" },
  { label: "阶梯7数量:", valueKey: "step7Num" },
  { label: "阶梯7单价:", valueKey: "step7Price" },
  { label: "阶梯8数量:", valueKey: "step8Num" },
  { label: "阶梯8单价:", valueKey: "step8Price" },
  { label: "阶梯9数量:", valueKey: "step9Num" },
  { label: "阶梯9单价:", valueKey: "step9Price" },
  { label: "阶梯10数量:", valueKey: "step10Num" },
  { label: "阶梯10单价:", valueKey: "step10Price" },
];

const defaultParamsFnc = () => {
  return {
    freeNum: 0,	//免收数量	integer(int32)	
    rateMode: "FREE",	//计费模式	string	
    step10Num: 0,	//阶梯10数量	integer(int32)	
    step10Price: 0,	//阶梯10单价	integer(int32)	
    step1Num: 0,//阶梯1数量	integer(int32)	
    step1Price: 0,//阶梯1单价	integer(int32)	
    step2Num: 0,//阶梯2数量	integer(int32)	
    step2Price: 0,//阶梯2单价	integer(int32)	
    step3Num: 0,//阶梯3数量	integer(int32)	
    step3Price: 0,//阶梯3单价	integer(int32)	
    step4Num: 0,//阶梯4数量	integer(int32)	
    step4Price: 0,//阶梯4单价	integer(int32)	
    step5Num: 0,//阶梯5数量	integer(int32)	
    step5Price: 0,//阶梯5单价	integer(int32)	
    step6Num: 0,//阶梯6数量	integer(int32)	
    step6Price: 0,//阶梯6单价	integer(int32)	
    step7Num: 0,	//阶梯7数量	integer(int32)	
    step7Price: 0,	//阶梯7单价	integer(int32)	
    step8Num: 0,//阶梯8数量	integer(int32)	
    step8Price: 0,	//阶梯8单价	integer(int32)	
    step9Num: 0,//阶梯9数量	integer(int32)	
    step9Price: 0,//阶梯9单价	integer(int32)
  };
};

export default {
  components: {
    ElCol,
    ElRow,
    ElForm,
    ElFormItem,
    ElInputNumber,
    ElSelect,
    ElOption,
    ElButton,
  },
  setup() {
    const store = useStore();
    const state = reactive({
      loading: false,
      form: {
        paramContent: defaultParamsFnc(),
        id: "",
        paramId: "",
        paramType: "WATER",
      },
    });
    const dictListFnc = () => {
      return store.state.deviceParameters[store.state.app.activeTab].dict
    }
    const saveClick = async () => {
      state.loading = true;
      let storeData = store.state.deviceParameters[store.state.app.activeTab]
      state.form.paramId = storeData.selectRow.id;
      let fn = state.form.id ? storeData.TabModule.detailsEditFnc : storeData.TabModule.detailsAddFnc

      let params = { ...state.form }
      console.log(params.paramContent);
      params.paramContent = JSON.stringify(params.paramContent)
      let { message, code } = await fn(params);
      if (code === 0) {
        setTimeout(() => {
          ElMessage.success(message);
          store.dispatch("deviceParameters/getParams", store.state.deviceParameters[store.state.app.activeTab].selectRow.id);
          state.loading = false;
        }, 1500);
      }
    };

    const beforeClose = () => {
      store.commit("deviceParameters/updateState", {
        key: store.state.app.activeTab,
        childKey: "isSetParams",
        payload: false,
      });
    };
    onMounted(() => {
      console.log(store.state.app.activeTab, store.state.deviceParameters[store.state.app.activeTab].detailsParams);
      let val = store.state.deviceParameters[store.state.app.activeTab].detailsParams
      let arr = val.filter(item => item.paramType == 'WATER')
      if (arr && arr.length) {
        state.form = { ...arr[0] }
        state.form.paramContent = JSON.parse(state.form.paramContent)
      } else {
        //设置默认选项
        state.form.id = "";
        state.form.paramContent = defaultParamsFnc();
      }
    });
    return {
      option,
      formList,
      state,
      dictListFnc,
      saveClick,
      beforeClose,
    };
  },
  name: "basicParam",
};
</script>

<style lang="scss" scoped>
// .setBasicParam {
//   border: 1px solid #dcdfe6;
//   border-radius: 5px;
//   margin-bottom: 10px;
// }
:deep(.el-form-item__content) {
  width: 200px;
  .el-input-number {
    width: 100%;
  }
  .el-select {
    width: 100% !important;
  }
}
</style>
