<template>
  <el-dialog :model-value="dialogVisible" title="交款明细" width="80%" :before-close="handleClose" :close-on-click-modal="false">
    <!-- 用户编号、用户姓名、组织机构、充值钱包、充值单号、交易来源、交易方式、对账结果， -->
    <div class="search-box" style="margin-top: 20px">
      <el-form inline size="mini" label-width="100px">
        <el-form-item label="用户编号">
          <el-input placeholder="编号关键字搜索" v-model="state.form.userCode"></el-input>
        </el-form-item>
        <el-form-item label="用户姓名">
          <el-input placeholder="姓名关键字搜索" v-model="state.form.userName"></el-input>
        </el-form-item>
        <el-form-item label="组织机构">
          <kade-dept-select-tree style="width: 100%" :value="state.form.deptPath" valueKey="deptPath" :multiple="false" @valueChange="(val) => (state.form.deptPath = val.deptPath)" />
        </el-form-item>
        <el-form-item label="充值钱包:">
          <el-select clearable v-model="state.form.walletCode" placeholder="请选择">
            <el-option v-for="(item, index) in state.allWalletList" :key="index" :label="item.walletName" :value="item.walletCode">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="充值单号:">
          <el-input placeholder="请输入充值单号" v-model="state.form.rechargeNo"></el-input>
        </el-form-item>
        <el-form-item label="交易来源:">
          <el-select clearable v-model="state.form.tradeSource" placeholder="请选择" @change="tradeSourceChange">
            <el-option v-for="(item, index) in state.tradeSourceList" :key="index" :label="item.tradeName" :value="item.tradeCode">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="交易方式:">
          <el-select clearable v-model="state.form.tradeMode" placeholder="请选择">
            <el-option v-for="(item, index) in state.tradeModeList" :key="index" :label="item.name" :value="item.code">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="对账结果:">
          <el-select clearable v-model="state.form.billResult" placeholder="请选择">
            <el-option v-for="(item, index) in state.rechargeReconciliationList" :key="index" :label="item.dictValue" :value="item.dictCode">
            </el-option>
          </el-select>
        </el-form-item>
        <el-button @click="getList()" icon="el-icon-sousuo" size="mini" class="shop-upload" type="primary">搜索</el-button>
      </el-form>
    </div>
    <kade-table-wrap title="订单明细表" v-loading="state.loading">
      <template #extra>
        <el-button @click="exportClick()" icon="el-icon-daoru" size="mini" class="btn-blue">导出查询明细</el-button>
      </template>
      <el-table style="width: 100%" :data="state.detailList" ref="multipleTable" highlight-current-row border stripe>
        <el-table-column v-for="(item,index) in column" :key="index" :width="item.width" :label="item.label" :prop="item.prop" align="center" show-overflow-tooltip>
          <template v-if="item.render" #default="scope">
            {{item.render(scope.row[item.prop])}}
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination background :current-page="state.form.currentPage" :page-size="state.form.pageSize" layout="total, sizes, prev, pager, next, jumper" :page-sizes="[6, 10, 20, 50, 100]" :total="state.datailTotal" @current-change="handlePageChange" @size-change="handleSizeChange">
        </el-pagination>
      </div>
    </kade-table-wrap>
    <template #footer>
      <span class="dialog-footer">
        <el-button size="mini" @click="handleClose">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script>
import { onMounted, reactive, watch } from "vue";
import {
  ElDialog,
  ElButton,
  ElForm,
  ElFormItem,
  ElSelect,
  ElOption,
  ElInput,
  ElTable,
  ElTableColumn,
  ElPagination,
} from "element-plus";
import { timeStr } from "@/utils/date.js";
import {
  tradeSource,
  tradeMode,
  getWalletActiveList,
  getOnlinePayList,
  onlinePayExport,
} from "@/applications/eccard-finance/api";
import deptSelectTree from "@/components/tree/deptSelectTree.vue";
const column = [
  { label: "用户编号", prop: "userCode", width: "" },
  { label: "用户姓名", prop: "userName", width: "" },
  { label: "组织机构", prop: "deptName", width: "" },
  { label: "充值钱包", prop: "walletName", width: "" },
  { label: "充值单号", prop: "rechargeNo", width: "" },
  { label: "账单金额", prop: "tradeAmount", width: "" },
  { label: "充值时间", prop: "rechargeTime", width: "170", render: (val) => val && timeStr(val) },
  { label: "入账时间", prop: "reconciliationTime", width: "170", render: (val) => val && timeStr(val) },
  { label: "交易来源", prop: "tradeSourceName", width: "" },
  { label: "交易方式", prop: "tradeModeName", width: "" },
  { label: "操作员", prop: "operatorName", width: "" },
  { label: "对账结果", prop: "reconciliationStatus", width: "" },
  { label: "结果描述", prop: "reconciliationRemark", width: "" },
]
export default {
  components: {
    ElDialog,
    ElButton,
    ElForm,
    ElFormItem,
    ElSelect,
    ElOption,
    ElInput,
    ElTable,
    ElTableColumn,
    ElPagination,
    "kade-dept-select-tree": deptSelectTree,
  },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    rowData: {
      type: [String, Object],
      default: "",
    },
  },
  setup(props, context) {
    const state = reactive({
      loading: false,
      form: {
        currentPage: 1,
        pageSize: 6,
      },
      detailList: [],
      datailTotal: 0,
      tradeSourceList: [],
      tradeModeList: [],
      rechargeReconciliationList: JSON.parse(
        localStorage.getItem("kade_cache_dictionary")
      ).filter((item) => item.dictType == "RECHARGE_RECONCILIATION_STATUS"), //对账结果
    });

    watch(
      () => props.dialogVisible,
      (val) => {
        if (val) {
          getList();
        }
      }
    );

    //获取钱包列表
    const queryWalletActiveList = () => {
      getWalletActiveList().then((res) => {
        state.allWalletList = res.data.filter(
          (item) => item.walletCode != 1 && item.walletCode != 2
        );
      });
    };
    //获取交易来源
    const getTradeSource = () => {
      tradeSource().then((res) => {
        state.tradeSourceList = res.data.filter((item) => item.tradeCode != 4);
      });
    };
    //获取交易方式
    const getTradeModeList = () => {
      tradeMode({ costType: 101 }).then((res) => {
        state.tradeModeList = res.data.filter(
          (item) => item.code != 7 && item.code != 8
        );
      });
    };

    const getList = async () => {
      console.log(props.rowData);
      state.form.startTime = timeStr(props.rowData.billDate)
      state.form.endTime = timeStr(
        new Date(props.rowData.billDate).getTime() + 1000 * 60 * 60 * 24
      )
      state.loading = true
      try {
        let { code, data } = await getOnlinePayList(state.form);
        if (code === 0) {
          let {
            page: { total, list },
            totalAmount,
          } = data;

          state.detailList = list;

          if (state.detailList.length) {
            state.detailList.push({
              tradeAmount: totalAmount,
              userCode: "合计金额",
            });
          }
          state.datailTotal = total;
        }
        state.loading = false;
      }
      catch {
        state.loading = false;
      }

    };
    const exportClick = async () => {
      state.loading = true
      try {
        let res = await onlinePayExport(state.form);
        let url = window.URL.createObjectURL(
          new Blob([res], { type: "application/vnd.ms-excel" })
        );
        let link = document.createElement("a");
        link.style.display = "none";
        link.href = url;
        link.setAttribute("download", "订单明细报表.xlsx");
        document.body.appendChild(link);
        link.click();
        state.loading = false;
      }
      catch {
        state.loading = false;
      }
    };
    const handlePageChange = (val) => {
      state.form.currentPage = val;
      getList();
    };
    const handleSizeChange = (val) => {
      state.form.currentPage = 1;
      state.form.pageSize = val;
      getList();
    };

    const handleClose = () => {
      context.emit("close");
      state.form.currentPage = 1;
      state.form.pageSize = 6;
    };
    onMounted(() => {
      getTradeSource();
      getTradeModeList();
      queryWalletActiveList();
    });
    return {
      timeStr,
      column,
      state,
      getList,
      exportClick,
      handlePageChange,
      handleSizeChange,
      handleClose,
    };
  },
};
</script>
<style lang="scss" scoped>
</style>