<template>
  <div class="news">
    <kade-route-card>
      <kade-table-filter @search="handleSearch" @reset="handleReset">
        <el-form inline label-width="100px" size="small">
          <el-form-item label="标题">
            <el-input v-model="state.form.msgTitle" clearable placeholder="请输入" />
          </el-form-item>
          <el-form-item label="消息类型">
            <el-select v-model="state.form.msgType" clearable placeholder="请选择">
              <el-option v-for="item in msgType" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="发布时间">
            <el-date-picker v-model="state.date" type="daterange" range-separator="~" clearable unlink-panels start-placeholder="开始时间" end-placeholder="结束时间" @change="dateChange">
            </el-date-picker>
          </el-form-item>
        </el-form>
      </kade-table-filter>
      <kade-table-wrap>
        <template #extra>
          <el-button icon="el-icon-plus" @click="handleAdd" size="small" type="success">新增</el-button>
          <el-button icon="el-icon-delete-solid" @click="batchDel" size="small" class="btn-blue">批量删除</el-button>
        </template>
        <el-table style="width: 100%" v-loading="state.loading" :data="state.msgList" @selection-change="handleSelectChange" border stripe>
          <el-table-column prop="" show-overflow-tooltip type="selection" align="center"></el-table-column>
          <el-table-column v-for="(item,index) in column" :key="index" :label="item.label" align="center" :prop="item.prop" :width="item.width" show-overflow-tooltip>
            <template #default="scope" v-if="item.type=='text'">
              <div class="table-text" v-html="scope.row[item.prop]"></div>
            </template>
            <template #default="scope" v-else-if="item.isDict">
              {{dictionaryFilter(scope.row[item.prop])}}
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" fixed="right" width="200">
            <template #default="scope">
              <el-button @click="handleInfo(scope.row)" class="green" type="text" size="mini">详情</el-button>
              <el-button @click="handleEdit(scope.row)" class="green" type="text" size="mini">编辑</el-button>
              <el-button @click="handleDel(scope.row)" class="green" type="text" size="mini">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination">
          <el-pagination background :current-page="state.form.currentPage" layout="total, sizes, prev, pager, next, jumper" :page-sizes="[10, 20, 50, 100, 200]" :total="state.total" :page-size="state.form.pageSize" @current-change="currentChange" @size-change="sizeChange">
          </el-pagination>
        </div>
      </kade-table-wrap>
    </kade-route-card>
    <kade-msg-edit @success="handleSuccess" />
    <kade-msg-add @success="handleSuccess" />
    <kade-msg-details />
  </div>
</template>
<script>
import { onMounted, reactive } from "vue";
import { useStore } from "vuex";
import {
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElTable,
  ElTableColumn,
  ElButton,
  ElDatePicker,
  ElPagination,
  ElMessage,
  ElMessageBox,
} from "element-plus";
import { useDict } from "@/hooks/useDict";
import { timeStr } from "@/utils/date.js";
// import { filterDictionary } from "@/utils/index.js";
import add from "./add.vue";
import edit from "./edit.vue";
import details from "./details.vue";

import { getMsgList, delMsg, delMsgs } from "@/applications/eccard-dorm/api";

export default {
  components: {
    ElForm,
    ElFormItem,
    ElInput,
    ElSelect,
    ElOption,
    ElTable,
    ElTableColumn,
    ElButton,
    ElDatePicker,
    ElPagination,
    "kade-msg-edit": edit,
    "kade-msg-add": add,
    "kade-msg-details": details,
  },
  setup() {
    const store = useStore();
    const msgType = useDict("SYS_MESSAGE_TYPE");
    console.log(msgType);
    const state = reactive({
      loading: false,
      form: {
        currentPage: 1,
        pageSize: 10,
      },
      date: [],
      msgList: [],
      total: 0,
      selectDataList: [],
    });
    const column = [
      { label: "标题", prop: "msgTitle", type: "text", width: "300px" },
      {
        label: "消息类型",
        prop: "msgType",
        width: "120px",
        isDict: true,
      },
      { label: "发布人", prop: "createUserName" },
      { label: "发布时间", prop: "publishTime" },
      { label: "接收区域", prop: "areaName" },
      { label: "接收楼栋", prop: "buildName", width: "200px" },
      { label: "接收单元", prop: "unitName", width: "200px" },
    ];
    const getList = async () => {
      if (state.date && state.date.length) {
        state.form.publishStartTime = timeStr(state.date[0]);
        state.form.publishEndTime = timeStr(state.date[1]);
      } else {
        delete state.form.publishStartTime;
        delete state.form.publishEndTime;
      }
      state.loading = true;
      try {
        let {
          data: { list, total },
        } = await getMsgList(state.form);
        state.msgList = list;
        state.total = total;
        console.log(state.msgList);
        state.loading = false;
      } catch {
        state.loading = false;
      }
    };
    const handleSearch = () => {
      getList();
    };

    const handleReset = () => {
      state.form = {
        currentPage: 1,
        pageSize: 10,
      };
      state.date = [];
    };
    const currentChange = (val) => {
      state.form.currentPage = val;
      getList();
    };
    const sizeChange = (val) => {
      state.form.currentPage = 1;
      state.form.pageSize = val;
      getList();
    };
    const handleEdit = (row) => {
      store.commit("msg/updateState", {
        key: "selectRow",
        payload: row,
      });
      store.commit("msg/updateState", {
        key: "isEdit",
        payload: true,
      });
    };
    const handleAdd = () => {
      store.commit("msg/updateState", {
        key: "selectRow",
        payload: {},
      });
      store.commit("msg/updateState", {
        key: "isAdd",
        payload: true,
      });
    };
    const handleInfo = (row) => {
      store.commit("msg/updateState", {
        key: "selectRow",
        payload: row,
      });
      store.commit("msg/updateState", {
        key: "isDetails",
        payload: true,
      });
    };
    const handleDel = (row) => {
      ElMessageBox.confirm("确认删除？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        let { code, message } = await delMsg(row.id);
        if (code === 0) {
          ElMessage.success(message);
          getList();
        }
      });
    };
    const handleSelectChange = (val) => {
      console.log(val);
      state.selectDataList = val;
    };
    const batchDel = () => {
      if (!state.selectDataList.length) {
        return ElMessage.error("请先选择需要删除的公告！");
      }
      ElMessageBox.confirm("确认删除已选择公告?", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        let param = state.selectDataList.map((item) => item.id).join(",")
        let { code, message } = await delMsgs(param);
        if (code === 0) {
          ElMessage.success(message);
          getList();
        }
      });
    };
    const handleSuccess = () => {
      getList();
    };
    onMounted(() => {
      getList();
    });
    return {
      column,
      state,
      msgType,
      handleSearch,
      handleReset,
      currentChange,
      sizeChange,
      handleEdit,
      handleAdd,
      handleInfo,
      handleDel,
      batchDel,
      handleSuccess,
      handleSelectChange,
    };
  },
};
</script>
<style lang="scss" scoped>
.table-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.green :hover {
  text-decoration: underline;
}
:deep(.el-divider--horizontal) {
  margin: 0;
}
:deep(.el-dialog__header) {
  border-bottom: 1px solid #efefef;
}

:deep(.el-overlay) {
  position: absolute;
  background-color: rgba(0, 0, 0, 0.3);
}

:deep(.el-dialog__footer) {
  text-align: center;
}
</style>