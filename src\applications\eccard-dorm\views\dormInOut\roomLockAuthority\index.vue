<template>
<kade-route-card>
  <kade-table-filter @search="handleSearch" @reset="handleReset">
    <el-form inline size="mini" label-width="100px">
    <kade-linkage-select :data="linkageData" :value="state.form" @change="linkageChange"/>
    <el-form-item label="组织机构">
            <kade-dept-select-tree style="width: 100%" :value="state.form.deptPath" valueKey="deptPath" :multiple="false" @valueChange="(val) => (state.form.deptPath = val.deptPath)" />
          </el-form-item>
    <el-form-item label="性别">
          <el-select v-model="state.form.userSex" placeholder="请选择">
            <el-option v-for="(item,index) in sexList" :key="index" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
      <el-form-item label="同步状态">
          <el-select v-model="state.form.synStatus" placeholder="请选择">
            <el-option v-for="(item,index) in synStatusList" :key="index" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
      <el-form-item label="姓名/编号">
      <el-input v-model="state.form.userKeyword" placeholder="请输入"></el-input>
      </el-form-item>
    </el-form>
  </kade-table-filter>
  <kade-table-wrap title="权限列表">
    <template #extra>
      <el-button type="success" icon="el-icon-plus" size="mini" @click="add">新增授权</el-button>
        <el-button type="primary" icon="el-icon-delete-solid" size="mini" @click="handleBatch">批量删除</el-button>
    </template>
    <el-table :data="state.data" border v-loading="state.loading" @selection-change="selectChange" height="55vh">
      <el-table-column type="selection" align="center"></el-table-column>
      <el-table-column show-overflow-tooltip  v-for="(item,index) in column" :key="index" :label="item.label" :prop="item.prop" :width="item.width" align="center">
       <template #default="scope">
          {{dictionaryFilter(scope.row[item.prop])}}
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template #default="scope">
          <el-button type="text" class="green" size="mini" @click="handleDel(scope.row)">删除授权</el-button>
        </template>
      </el-table-column>
    </el-table>
    <dev class="pagination">
        <el-pagination v-model:currentPage="state.currentPage" v-model:page-size="state.pageSize" :page-sizes="[10, 20, 30, 40]" background layout="total, sizes, prev, pager, next, jumper" :total="state.total" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </dev>
  </kade-table-wrap>
  <kade-access-add v-model:modelValue="state.isShow" @update:modelValue="close"/>
</kade-route-card>
</template>

<script>
import { reactive, onMounted } from 'vue'
import add from "./components/add.vue"
import { useDict } from "@/hooks/useDict.js"
import deptSelectTree from '@/components/tree/deptSelectTree'
import linkageSelect from '@/applications/eccard-dorm/components/linkageSelect'
import { getRoomLockAuthority,delRoomLockAuthority,batchRoomLockAuthority } from "@/applications/eccard-dorm/api.js"
import { ElForm,ElFormItem,ElInput,ElButton,ElTable,ElTableColumn,ElPagination,ElMessageBox,ElMessage,ElSelect,ElOption } from "element-plus"
const linkageData={
  area:{label:'区域',valueKey:'areaId',key:'id'}
}
const column = [
  {label:'人员编号',prop:'userCode'},
  {label:'人员姓名',prop:'userName'},
  {label:'性别',prop:'userSex'},
  {label:'组织机构',prop:'deptName'},
  {label:'区域',prop:'roomString',width:'300px'},
  {label:'设备机号',prop:'deviceNo'},
  {label:'设备名称',prop:'deviceName'},
  {label:'同步状态',prop:'synStatus'},
  {label:'同步时间',prop:'synTime'},
  {label:'添加时间',prop:'createTime'}
]
export default{
  components:{
    ElForm,
    ElFormItem,
    ElInput,
    ElButton,
    ElTable,
    ElTableColumn,
    ElPagination,
    ElSelect,
    ElOption,
    "kade-access-add":add,
    "kade-linkage-select":linkageSelect,
    "kade-dept-select-tree":deptSelectTree   
  },
  setup(){
    const synStatusList = useDict('SYS_BOOL_STRING')
    const sexList = useDict('SYS_SEX')
    const state = reactive({
      form:{
        currentPage:1,
        pageSize:10
      },
      data:[],
      total:0,
      loading:false,
      isShow:false,
      selectData:[]
    })
    const handleSearch=()=>{
      getList()
    }
    const handleReset=()=>{
      state.form={
        currentPage:1,
        pageSize:10
      }
      getList()
    }
    const handleSizeChange=(val)=>{
      state.form.currentPage=1
      state.form.pageSize=val
    }
    const handleCurrentChange=(val)=>{
      state.form.currentPage=val
    }
    const add=()=>{
      state.isShow=true
    }
    const close=(val)=>{
      if(val){
        getList()
      }
      state.isShow=false
    }
    const linkageChange=(val)=>{
      state.form={...state.form,...val}
    }
    const getList=async()=>{
      state.loading=true
      try{
        let {data:{list,total}} = await getRoomLockAuthority(state.form)
        state.data=list
        state.total=total
        state.loading=false
      }
      catch{
        state.loading=false
      }
    }
    const selectChange=(val)=>{
      state.selectData=val
    }
    const handleBatch=()=>{
      if(!state.selectData.length){
        return ElMessage.error("请先选择需要删除的信息！")
      }
      ElMessageBox.confirm(`确认删除已选择信息？`,`提示`,{
        type:'warning',
        confirmButtonText:'确定',
        cancelButtonText:'取消'
      }).then(async()=>{
        let param = state.selectData.map((item)=>item.id).join(',')
        console.log(param)
        let {code,message} = await batchRoomLockAuthority(param)
        if(code===0){
          ElMessage.success(message)
          getList()
        }
      })
    }
    const handleDel=(row)=>{
      ElMessageBox.confirm(`确认删除？`,`提示`,{
        type:'warning',
        confirmButtonText:'确认',
        cancelButtonText:'取消'
      }).then(async()=>{
      let { code,message } = await delRoomLockAuthority(row.id)
      if(code===0){
        ElMessage.success(message)
        getList()
      }
      })
    }
    onMounted(()=>{
      getList()
    })
    return{
      state,
      add,
      close,
      column,
      linkageData,
      handleDel,
      sexList,
      handleBatch,
      selectChange,
      handleSearch,
      handleReset,
      synStatusList,
      linkageChange,
      handleSizeChange,
      handleCurrentChange,
    }
  }
}
</script>

<style lang="scss" scoped>
.green :hover{
  text-decoration: underline;
}
:deep(.el-input--mini .el-input__inner){
  width: 198px;
}
:deep(.el-pagination .el-select .el-input .el-input__inner){
  width: 100px;
}
:deep(.el-pagination__editor.el-input .el-input__inner){
  width: 46px;
}
:deep(.el-dialog){
  border-radius: 6px;
  padding-bottom: 15px;
}
:deep(.el-dialog__header){
  border-bottom: 1px solid #eeeeee;
}
:deep(.el-dialog__footer){
  border-top: none;
  text-align: center;
}
:deep(.el-divider--horizontal){
  margin: 0 0 20px 0;
}
</style>