<template>
  <div id="mychart2" class="echartDiv"></div>

</template>
<script>
import * as echarts from "echarts";
import { onMounted, ref, watch } from "vue";
import { incomeTrend } from "../../api/home.js";
export default {
  props: {
    queryDate: {
      type: Object,
      default: () => ({
        startTime: '',
        endTime: '',
        type: '1' // 按天
      })
    }
  },
  setup(props) {
    const chartData = ref({
      xAxisData: [],
      waterData: [],
      electricData: []
    });

    // 获取运营变化趋势数据
    const fetchOperationalData = async () => {
      if (!props.queryDate || !props.queryDate.startTime || !props.queryDate.endTime) {
        return;
      }

      try {
        console.log('运营变化趋势请求参数:', props.queryDate);
        const response = await incomeTrend(props.queryDate);
        console.log('运营变化趋势数据:', response);

        if (response && response.code === 0 && response.data) {
          // 处理接口返回的数据结构
          const waterRes = response.data?.waterRes || [];
          const elecRes = response.data?.elecRes || [];

          // 提取日期作为X轴数据
          const xAxisData = waterRes.map(item => item.date);
          const waterData = waterRes.map(item => parseFloat(item.num) || 0);
          const electricData = elecRes.map(item => parseFloat(item.num) || 0);

          chartData.value = {
            xAxisData: xAxisData,
            waterData: waterData,
            electricData: electricData
          };

          // 重新初始化图表
          echartInit();
        }
      } catch (error) {
        console.error('获取运营变化趋势数据失败:', error);
      }
    };
    const echartInit = () => {
      var chartDom = document.getElementById("mychart2");
      if (!chartDom) return;

      var myChart = echarts.init(chartDom);
      var option = {
        title: {
          text: '平台运营变化趋势'
        },
        tooltip: {
          trigger: "axis",
        },
        grid: {
          top: "20%",
          left: "0%",
          right: "3%",
          bottom: "0%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          boundaryGap: false,
          data: chartData.value.xAxisData.length > 0 ? chartData.value.xAxisData : ["暂无数据"],
          axisLine: {
            lineStyle: {
              color: "#9498b0s",
            },
          },
          axisLabel: {
            margin: 18,
            textStyle: {
              fontSize: 12,
            },
          },
        },
        yAxis: {
          type: "value",
          splitLine: {
            show: true,
            lineStyle: {
              type: "dashed",
            },
          },
          axisLabel: {
            margin: 20,
            textStyle: {
              fontSize: 12,
            },
          },
        },
        series: [
          {
            smooth: true,
            symbol: "circle",
            name: "水费:",
            data: chartData.value.waterData,
            type: "line",
            color: "#1890ff",
          },
          // {
          //   smooth: true,
          //   symbol: "circle",
          //   name: "电费:",
          //   data: chartData.value.electricData,
          //   type: "line",
          //   color: "#2fc25b",
          // },
        ],
      };
      myChart.setOption(option);
    };


    // 监听 queryDate 变化
    watch(() => props.queryDate, (newQueryDate) => {
      if (newQueryDate && newQueryDate.startTime && newQueryDate.endTime) {
        fetchOperationalData();
      }
    }, { deep: true, immediate: true });

    //挂载
    onMounted(() => {
      echartInit();
    });

    return {
      echartInit,
      fetchOperationalData,
      chartData
    };
  },
};
</script>
<style lang="scss" scoped>
.echartDiv {
  height: 340px;
}
</style>
