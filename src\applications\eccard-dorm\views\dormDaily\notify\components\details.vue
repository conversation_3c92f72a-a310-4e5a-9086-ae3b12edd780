<template xmlns:el-form-item="http://www.w3.org/1999/html">
  <el-dialog
    :model-value="dialogDetailsVisible"
    title="通知公告详情"
    width="40%"
    :before-close="handleClose"
  >
    <el-form style="margin-top:20px" inline label-width="100px" size="mini">
      <el-form-item :label="item.label" v-for="(item,index) in state.dialogList" :key="index">
        <el-input readonly :model-value="data[item.filed]"></el-input>
      </el-form-item>
      <el-form-item :label="state.leavePeriod.label">
        <el-input readonly :model-value="state.leavePeriod.filed"></el-input>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script>
import {ElDialog, ElForm, ElFormItem, ElInput} from "element-plus";
import {reactive} from "@vue/reactivity";

export default {
  components: {
    ElDialog,
    ElForm,
    ElFormItem,
    ElInput,
  },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    dialogDetailsVisible: {
      type: Boolean,
      default: false,
    },
    data: {
      types: Object,
      default: {}
    }
  },
  setup(prop, context) {
    const state = reactive({
      dialogList: [
        {label: "所属区域：", filed: "userId",},
        {label: "楼栋：", filed: "userName"},
        {label: "单元：", filed: "operateAccount"},
        {label: "房间：", filed: "reason"},
        {label: "检查日期：", filed: "ip"},
        {label: "检查人：", filed: "operateTime"},
        {label: "检查结果：", filed: "requestParam"},
        {label: "是否通报：", filed: "roomId"},
        {label: "检查情况：", filed: "roomId"},
        {label: "得分：", filed: "roomId"}
      ],
      leavePeriod: {label: "请假时间：", filed: "leavePeriod"},
    });
    const handleClose = () => {
      context.emit("close", false);
    };
    return {
      state,
      handleClose,
    };
  },
};
</script>

<style lang="scss" scoped>

</style>
