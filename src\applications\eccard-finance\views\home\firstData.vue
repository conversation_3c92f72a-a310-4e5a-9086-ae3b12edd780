<template>
    <div class="title-box">数据标题</div>
    <div id="echartline" class="echartline"></div>
</template>

<script>
import * as echarts from "echarts";
import { onMounted } from "vue";

export default {
    setup() {
        const echartInit = () => {
            var chartDom = document.getElementById("echartline");
            var myChart = echarts.init(chartDom);
            var option = {
                tooltip: {
                    axisPointer: {
                        type: "shadow",
                    },
                },
                legend: {
                    orient: "horizontal",
                    bottom: "0%",
                    itemGap: 25,
                    itemWidth: 7,
                    itemHeight: 7,
                    icon: "circle",
                },
                grid: {
                    top: "15%",
                    left: "5%",
                    right: "10%",
                    bottom: "20%",
                    containLabel: true,
                },
                xAxis: [
                    {
                        axisLine: {
                            lineStyle: {
                                color: "#9498b0s",
                            },
                        },
                        axisLabel: {
                            margin: 10,
                            textStyle: {
                                fontSize: 12,
                            },
                        },
                        type: "category",
                        data: [
                            "2013",
                            "2014",
                            "2015",
                            "2016",
                            "2017",
                            "2018",
                            "2019",
                            "2020",
                        ],
                    },
                ],
                yAxis: [
                    {
                        type: "value",
                        splitLine: {
                            show: true,
                            lineStyle: {
                                type: "dashed",
                            },
                        },
                        axisLabel: {
                            margin: 20,
                            textStyle: {
                                fontSize: 13,
                            },
                        },
                    },
                ],
                series: [
                    {
                        name: "项目一",
                        color: "#3ba1ff",
                        type: "bar",
                        stack: "Ad",
                        emphasis: {
                            focus: "series",
                        },
                        data: [27, 32, 21, 54, 90, 30, 41, 80],
                    },
                    {
                        name: "项目二",
                        color: "#4fcb74",
                        type: "bar",
                        stack: "Ad",
                        emphasis: {
                            focus: "series",
                        },
                        data: [22, 32, 91, 34, 60, 30, 90, 24],
                    },
                    {
                        name: "项目三",
                        color: "#ffc927",
                        type: "bar",
                        barWidth: 18,
                        stack: "Ad",
                        emphasis: {
                            focus: "series",
                        },
                        data: [27, 32, 41, 60, 70, 84, 92, 33],
                    },
                ],
            };
            myChart.setOption(option);
            window.onresize = myChart.resize
        };
        onMounted(() => {
            echartInit();
        });
        return {
            echartInit,
        };
    },
};
</script>

<style lang="scss" scoped>
.title-box {
    font-size: 16px;
    font-weight: 600;
    border-bottom: 1px solid #eeeeee;
    padding: 0 0 10px 10px;
    margin-right: 10px;
}
.echartline {
    height: 260px;
    width: 100%;
}
</style>