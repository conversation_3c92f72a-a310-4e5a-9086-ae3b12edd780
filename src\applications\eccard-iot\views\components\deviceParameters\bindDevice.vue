<template>
  <el-dialog :model-value="modelValue" title="绑定设备" v-loading="state.loading" width="90%" :before-close="beforeClose">
    <kade-table-wrap title="选择设备" style="margin-top:20px">
      <el-divider></el-divider>
      <kade-select-table :isShow="modelValue" :value="[]" :reqFnc="TabModule().onBindDeviceListFnc" :selectCondition="state.selectCondition" :column="column" :params="params" @change="deviceChange" />
    </kade-table-wrap>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="beforeClose" size="mini">取消</el-button>
        <el-button type="primary" @click="submit()" size="mini">提交</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script>
import {
  ElDialog,
  ElButton,
  ElMessage,
} from "element-plus";
import { reactive, ref } from "@vue/reactivity";
import { useStore } from "vuex";
import { onMounted } from "@vue/runtime-core";
import { useDict } from "@/hooks/useDict";
import selectTable from "@/components/table/selectTable.vue"
import {
  getModel,
  getWorkStationList,
  
} from "@/applications/eccard-iot/api";
const column = [
  { label: "所属区域", prop: "areaName", isDict: false, width: "" },
  { label: "设备机号", prop: "deviceNo", isDict: false, width: "" },
  { label: "设备名称", prop: "deviceName", isDict: false, width: "" },
  { label: "终端型号", prop: "deviceModel", isDict: false, width: "" },
  { label: "设备状态", prop: "deviceStatus", isDict: true, width: "" },
  { label: "连接类型", prop: "deviceConnectType", isDict: true, width: "" },
  { label: "设备IP", prop: "deviceIp", isDict: false, width: "" },
];
export default {
  components: {
    ElDialog,
    ElButton,
    "kade-select-table": selectTable,
  },
  props: {
    modelValue: {
      type: Boolean,
      default: false,
    },
    listResquest: {
      type: Function,
      default: null,
    },
    TabModule: {
      type: Function,
      default: null,
    }
  },
  setup(props, context) {
    const store = useStore();
    const multipleTable = ref(null)
    const state = reactive({
      loading: false,
      selectCondition: [
        {
          label: "终端型号", valueKey: "deviceModel", placeholder: "请选择", isSelect: true, select: {
            list: [],
            option: {
              label: "productMode",
              value: "productMode",
            },
          },
        },
        { label: "机号", valueKey: "deviceNo", placeholder: "请输入", isSelect: false },
        { label: "所属区域", valueKey: "areaId",dataKey: "id", placeholder: "请选择", isSelect: false, isTree: "area" },
        {
          label: "设备状态", valueKey: "deviceStatus", placeholder: "请选择", isSelect: true, select: {
            list: useDict("SYS_DEVICE_STATICE"),
            option: {
              label: "label",
              value: "value",
            },
          },
        },
        { label: "设备名称", valueKey: "deviceName", placeholder: "请输入", isSelect: false },
        {
          label: "所属工作站", valueKey: "workstationId", placeholder: "请选择", isSelect: true, select: {
            list: [],
            option: {
              label: "name",
              value: "id",
            },
          },
        },
        {
          label: "连接类型", valueKey: "deviceConnectType", placeholder: "请选择", isSelect: true, select: {
            list: useDict("SYS_DEVICE_CONNECT_TYPE"),
            option: {
              label: "label",
              value: "value",
            },
          },
        },
        { label: "设备IP", valueKey: "deviceIp", placeholder: "请输入", isSelect: false },
      ],
      deviceIds: []
    });
    const params = {
      currentPageKey: "pageNum",
      pageSizeKey: "pageSize",
      resListKey: "list",
      resTotalKey: "total",
      value: {
        deviceType: props.TabModule().title,
        bind: 'false',
        bindFlag:"0"
      },
      tagNameKey: "deviceName",
      valueKey: "id"
    }
    //获取终端型号
    const queryModel = async () => {
      let { data } = await getModel({ productClass: props.TabModule().deviceType });
      state.selectCondition[0].select.list = data
    };
    //获取工作站
    const queryWorkStationList = async () => {
      let { data:{list} } = await getWorkStationList();
      state.selectCondition[5].select.list = list
    };
    const deviceChange = (val) => {
      state.deviceIds = val.list.map(item => item.id)
    }
    const submit = async () => {
      if (!state.deviceIds.length) {
        return ElMessage.error("请选择设备！")
      }
      state.loading = true
      let params = {
        deviceIds: state.deviceIds,
        paramId: store.state.deviceParameters[store.state.app.activeTab].selectRow.id
      }
      console.log(props.TabModule());
      try {
        let { code, message } = await props.TabModule().bindDeviceFnc(params)
        if (code === 0) {
          ElMessage.success(message)
          beforeClose()
        }
        state.loading = false
      }
      catch {
        state.loading = false
      }

    }
    const beforeClose = () => {
      context.emit("update:modelValue", false)
      state.deviceIds = []
    };
    onMounted(() => {
      queryModel()
      queryWorkStationList()
    })
    return {
      column,
      params,
      multipleTable,
      state,
      deviceChange,
      submit,
      beforeClose,
    };
  },
};
</script>
<style lang="scss" scoped>
</style>