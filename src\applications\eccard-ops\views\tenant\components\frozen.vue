<template>
  <kade-modal v-bind="attrs" :modelValue="modelValue" @update:modelValue="update">
    <el-form ref="formRef" :label-width="labelWidth" :rules="rules" :model="state.model" size="small">
      <el-form-item :label="type === 'frozen' ? '冻结原因' : '备注'" prop="operationReason">
        <el-input show-word-limit maxlength="140" type="textarea" rows="4" placeholder="请输入" v-model="state.model.operationReason" />
      </el-form-item>                          
    </el-form>
    <template #footer>
      <el-button size="small" @click="cancel">取消</el-button>
      <el-button size="small" :loading="loading" @click="submit" type="primary">提交</el-button>
    </template>
  </kade-modal>
</template>
<script>
import Modal from '@/components/modal';
import { computed, reactive, ref, watch } from 'vue';
import { 
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElMessage,
} from 'element-plus';
import { updateStatus } from '@/applications/eccard-ops/api';
export default {
  emits: ['update:modelValue', 'change'],
  props: {
    title: {
      type: String,
      default: ''
    },
    modelValue: {
      type: Boolean,
      default: false,
    },
    id: {
      type: [String, Number],
      default: null
    },
    type: String,
  },
  components: {
    'kade-modal': Modal,
    'el-button': ElButton,
    'el-form': ElForm,
    'el-form-item': ElFormItem,
    'el-input': ElInput,
  },
  setup(props, context) {
    const formRef = ref(null);
    const loading = ref(false);
    const state = reactive({
      model: {
        operationReason: ''
      }
    });
    const rules = {
      operationReason: [
        { required: true, message: '请输入原因' },
      ],           
    };
    const cancel = () => {
      formRef.value?.resetFields();
      context.emit('update:modelValue', false);
    }
    const submit = () => {
      formRef.value?.validate(async (valid) => {
        if(valid) {
          try {
            loading.value = true;
            const { message } = await updateStatus({
              ...state.model,
              id: props.id,
              tenantStatus: props.type === 'frozen' ? 'FROZEN' : 'NORMAL',
            });
            ElMessage.success(message);
            context.emit('change');
            context.emit('update:modelValue', false);
          } catch(e) {
            throw new Error(e.message);
          } finally {
            loading.value = false;
          }
        }
      });      
    }
    const attrs = computed(() => {
      // eslint-disable-next-line no-unused-vars
      const { modelValue, id, type, ...attrs } = props;
      return attrs;
    });
    const update = (v) => {
      context.emit('update:modelValue', v);
    }
    watch(() => props.modelValue, (n) => {
      n && (state.model.operationReason = '');
    });
    return {
      attrs,
      update,
      formRef,
      cancel,
      submit,
      rules,
      labelWidth: THEMEVARS.formLabelWidth,
      state,
      loading,
    }
  }
}
</script>