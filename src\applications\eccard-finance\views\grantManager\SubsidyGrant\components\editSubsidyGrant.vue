<template>
  <div>
    <el-dialog :model-value="isEdit" :title="'编辑补助发放'" width="90%" :before-close="beforeClose"
      :close-on-click-modal="false">
      <div>
        <div class="padding-form-box">
          <el-form inline size="small" ref="ruleForm" :model="state.form" :rules="state.rules" label-width="120px">
            <el-form-item label="补助类型:" prop="subsidyType">
              <el-select clearable v-model="state.form.subsidyType" placeholder="请选择">
                <el-option v-for="(item, index) in subsidyTypeList" :key="index" :label="item.stName"
                  :value="item.stId">
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="项目名称:" prop="projectName">
              <el-input placeholder="请输入" v-model="state.form.projectName"></el-input>
            </el-form-item>

            <el-form-item label="交易类型:" prop="costType">
              <el-select clearable v-model="state.form.costType" placeholder="请选择">
                <el-option v-for="(item, index) in state.tradeTypeList" :key="index" :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="所属年月:" prop="subsidyMonth">
              <el-date-picker v-model="state.form.subsidyMonth" type="month" placeholder="请选择日期" @change="changeDate">
              </el-date-picker>
            </el-form-item>
          </el-form>
          <el-form size="small" label-width="120px">
            <el-form-item label="发放说明:">
              <el-input placeholder="请输入" type="textarea" v-model="state.form.grantRemark"></el-input>
            </el-form-item>
            <el-form-item label="发放方式:" prop="grantType">
              <el-radio-group v-model="state.grantType">
                <el-radio label="SYSTEM">发放清单</el-radio>
                <el-radio label="IMPORT">导入清单</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-form>

          <template v-if="state.grantType == 'SYSTEM'">
            <el-form inline ref="generateSubsidy" :model="state.generateSubsidyForm" :rules="state.generateSubsidyRule"
              size="small" label-width="120px">
              <el-form-item label=" 身份类别:" prop="userRole">
                <el-select v-model="state.generateSubsidyForm.userRole" placeholder="请选择">
                  <el-option v-for="(item, index) in roleList" :key="index" :label="item.roleName" :value="item.id">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="生效时间:" prop="takeEffectTime">
                <el-date-picker v-model="state.generateSubsidyForm.takeEffectTime" type="date" placeholder="请选择日期"
                  @change="changeDate">
                </el-date-picker>
              </el-form-item>
              <el-form-item label="每人补助金额:" prop="subsidyAmount">
                <el-input placeholder="请输入" v-model="state.generateSubsidyForm.subsidyAmount"></el-input>
              </el-form-item>
              <el-form-item label="有效期至:" prop="invalidTime">
                <el-date-picker v-model="state.generateSubsidyForm.invalidTime" type="date" placeholder="请选择日期"
                  @change="changeDate">
                </el-date-picker>
              </el-form-item>
            </el-form>
          </template>
          <div class="select-input-lang" v-if="state.grantType == 'SYSTEM'">
            <el-form inline size="small" label-width="120px">
              <el-form-item label=" 组织机构:">
                <kade-dept-select-tree style="width: 100%" :value="state.generateSubsidyForm.deptPaths"
                  valueKey="deptPath" :multiple="true" @valueChange="
                    (val) => (state.generateSubsidyForm.deptPaths = val)
                  " />
              </el-form-item>

              <el-button size="small" type="primary" @click="defineGenerateListClick()">确认生成清单</el-button>
              <el-button size="small" @click="state.generateSubsidyForm = {}">重置</el-button>
            </el-form>
          </div>
          <el-form v-show="state.grantType == 'IMPORT'" inline size="small" label-width="100px">
            <el-form-item label=" 上传文件:">
              <el-upload class="upload-demo" :action="state.requestUrl"
                :headers="{ Authorization: `bearer ${state.requestHeader}` }" :on-success="handleSuccess"
                :on-remove="handleRemove" :limit="3" :file-list="fileList" ref="uploadDom">
                <el-button size="small" type="primary">上传充值清单</el-button>
              </el-upload>
            </el-form-item>
            <el-form-item>
              <div class="blue" @click="uploadSample()">下载样例</div>
            </el-form-item>
          </el-form>
          <div v-if="state.abnormalData > 0">
            <span class="red">•</span> 异常数据
            {{ state.abnormalData }} 条，请修改确认
          </div>
<!--           <el-table v-if="state.grantType == 'SYSTEM'" style="width: 100%" :data="state.WaitSubsidyList"
            v-loading="state.WaitSubsidyLoading" border stripe>
            <el-table-column label="校检结果" prop="verifyResult" align="center">
              <template #default="scope">
                <div :style="
                  scope.row.verifyResult != '成功' && { 'color': '#f00' }
                ">
                  {{ scope.row.verifyResult }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="用户编号" prop="userCode" align="center"></el-table-column>
            <el-table-column label="姓名" prop="userName" align="center"></el-table-column>
            <el-table-column label="组织机构" prop="deptName" align="center"></el-table-column>
            <el-table-column label="身份类别" prop="roleName" align="center"></el-table-column>
            <el-table-column label="操作" prop="userName" align="center">
              <template #default="scope">
                <el-button type="text" @click="delClick(scope.row, scope.$index)" size="mini">删除</el-button>
              </template>
            </el-table-column>
          </el-table> -->
          <el-table style="width: 100%" :data="state.WaitSubsidyList"
            v-loading="state.WaitSubsidyLoading" border stripe>
            <el-table-column  show-overflow-tooltip label="校检结果" prop="verifyResult" align="center">
              <template #default="scope">
                <div :style="
                  scope.row.verifyResult != '成功' && { 'color': '#f00' }
                ">
                  {{ scope.row.verifyResult }}
                </div>
              </template>
            </el-table-column>
            <el-table-column  show-overflow-tooltip label="用户编号" prop="userCode" align="center"></el-table-column>
            <el-table-column  show-overflow-tooltip label="姓名" prop="userName" align="center"></el-table-column>
            <el-table-column  show-overflow-tooltip label="组织机构" prop="deptName" align="center"></el-table-column>
            <el-table-column  show-overflow-tooltip label="身份类别" prop="roleName" align="center"></el-table-column>
            <el-table-column  show-overflow-tooltip label="生效时间" prop="takeEffectTime" align="center"></el-table-column>
            <el-table-column  show-overflow-tooltip label="发放金额（元）" prop="subsidyAmount" align="center"></el-table-column>
            <el-table-column  show-overflow-tooltip label="有效期至" prop="invalidTime" align="center"></el-table-column>
            <el-table-column label="操作" prop="userName" align="center">
              <template #default="scope">
                <el-button type="text" @click="delClick(scope.row, scope.$index)" size="mini">编辑</el-button>
                <el-button type="text" @click="delClick(scope.row, scope.$index)" size="mini">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <div class="pagination-box" v-if="state.grantType">
            <div>
              <span>合计：<span>{{ state.totalCount }}</span>人，<span>{{ state.totalAmount }}</span>元</span>
            </div>
            <div class="pagination">
              <el-pagination background :current-page="state.WaitSubsidyForm.currentPage"
                :page-size="state.WaitSubsidyForm.pageSize" layout="total, sizes, prev, pager, next, jumper"
                :page-sizes="[6, 10, 20, 50, 100]" :total="state.WaitSubsidyListTotal"
                @current-change="handlePageChange" @size-change="handleSizeChange">
              </el-pagination>
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="beforeClose()" size="mini">关&nbsp;&nbsp;闭</el-button>
          <el-button @click="submitForm()" size="mini" type="primary">确&nbsp;&nbsp;认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script>
import { computed, onMounted, reactive, ref, watch } from "vue";
import { useStore } from "vuex";
import { getToken } from "@/utils";
import { timeStr } from "@/utils/date.js";
import {
  editSubsidyProject,
  delUpdateWaitSubsidy,
  getSubsidyListByPage,
  generateSubsidyList,
  getWaitSubsidyListByPage,
  deleteWaitSubsidy,
  exportSubsidyList,
} from "@/applications/eccard-finance/api";
import {
  ElDialog,
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElTable,
  ElRadio,
  ElRadioGroup,
  ElTableColumn,
  ElPagination,
  ElUpload,
  ElDatePicker,
  ElMessage,
} from "element-plus";
import deptSelectTree from "@/components/tree/deptSelectTree.vue";

export default {
  components: {
    "el-dialog": ElDialog,
    "el-button": ElButton,
    "el-input": ElInput,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    "el-select": ElSelect,
    "el-option": ElOption,
    "el-table": ElTable,
    "el-table-column": ElTableColumn,
    "el-pagination": ElPagination,
    ElRadio,
    ElRadioGroup,
    ElUpload,
    ElDatePicker,
    "kade-dept-select-tree": deptSelectTree,

  },
  props: {},
  setup(props, context) {
    const store = useStore();
    const uploadDom = ref(null);
    let generateSubsidy = ref(null);
    let ruleForm = ref(null);
    const state = reactive({
      isEdit: false,
      WaitSubsidyLoading: false,
      isNewUpload: 1, //1 未新发放清单  2生成清单  3上传清单
      grantType: "",
      form: {},
      generateSubsidyForm: {},
      rules: {
        subsidyType: [
          {
            required: true,
            message: "请选择补助类型",
            trigger: "change",
          },
        ],
        projectName: [
          {
            required: true,
            message: "请输入项目名称",
            trigger: "blur",
          },
        ],
        costType: [
          {
            required: true,
            message: "请选择交易类型",
            trigger: "change",
          },
        ],
        subsidyMonth: [
          {
            required: true,
            message: "请选择所属年月",
            trigger: "change",
          },
        ],
        grantType: [
          {
            required: true,
            message: "请选择发放方式",
            trigger: "change",
          },
        ],
      },
      generateSubsidyRule: {
        userRole: [
          {
            required: true,
            message: "请选择身份类别",
            trigger: "change",
          },
        ],
        takeEffectTime: [
          {
            required: true,
            message: "请选择生效时间",
            trigger: "change",
          },
        ],
        subsidyAmount: [
          {
            required: true,
            message: "请输入补助金额",
            trigger: "blur",
          },
        ],
        invalidTime: [
          {
            required: true,
            message: "请选择到期时间",
            trigger: "change",
          },
        ],
      },
      WaitSubsidyForm: {
        currentPage: 1,
        pageSize: 6,
      },
      totalAmount: 0,
      totalCount: 0,
      abnormalData: 0,
      WaitSubsidyList: [],
      WaitSubsidyListTotal: 0,
      requestHeader: getToken(),
      requestUrl: `${CONFIG.BASE_API_PATH}eccard-finance/subsidyRecord/importSubsidyList`,
      roleList: [],
      departCheckList: [],
      subsidyTypeList: [],
      tradeTypeList: [
        { label: "充值", value: 101 },
      ],
    });

    const isEdit = computed(() => {
      return store.state.subsidyData.isEdit;
    });

    const subsidyTypeList = computed(() => {
      return store.state.subsidyData.subsidyTypeList;
    });
    const roleList = computed(() => {
      return store.state.subsidyData.roleList;
    });
    const departCheckList = computed(() => {
      return store.state.subsidyData.departCheckList;
    });

    watch(
      () => state.grantType,
      () => {
        if (state.isNewUpload !== 1) {
          state.WaitSubsidyList = [];
          state.generateSubsidyForm = {};
          state.WaitSubsidyForm = {
            currentPage: 1,
            pageSize: 6,
          };
          uploadDom.value.clearFiles();
          state.WaitSubsidyListTotal = 0;
          state.totalCount = 0;
          state.totalAmount = 0;
          state.abnormalData = 0;
        }
      }
    );

    watch(
      () => store.state.subsidyData.isEdit,
      (val) => {
        if (val) {
          let data = store.state.subsidyData.selectRow;
          let {
            subsidyType,
            projectName,
            costType,
            subsidyMonth,
            grantRemark,
            grantMode,
            takeEffectTime,
            totalAmount,
            invalidTime,
            userRole,
            totalPerson,
            id,
          } = { ...data };

          state.grantType = grantMode;
          state.form = {
            subsidyType,
            projectName,
            costType,
            subsidyMonth,
            grantRemark,
            id
          };
          if (state.grantType == "IMPORT") {
            state.generateSubsidyForm = {};
          } else {
            state.generateSubsidyForm = {
              takeEffectTime: timeStr(takeEffectTime),
              subsidyAmount: totalAmount / totalPerson,
              invalidTime: timeStr(invalidTime),
              userRole,
              id,
            };
          }
          state.WaitSubsidyForm.projectId = id;

          state.totalCount = totalPerson;
          state.totalAmount = totalAmount;
          querySubsidyListByPage();
        }
      }
    );

    //上传成功
    const handleSuccess = (res) => {
      let { totalCount, totalAmount, errorCount } = res.data;
      state.isNewUpload = 3;
      state.WaitSubsidyForm.projectNo = res.data.projectNo;
      state.form.projectNo = res.data.projectNo;
      state.totalCount = totalCount;
      state.totalAmount = totalAmount;
      state.abnormalData = errorCount;
      queryWaitSubsidyListByPage();
    };

    //清除上传
    const handleRemove = () => {
      state.WaitSubsidyList = [];
      state.WaitSubsidyListTotal = 0;
    };
    const cascaderChange = (val) => {
      console.log(val);
      console.log(state.generateSubsidyForm.deptPaths);
      if (val.length) {
        let arr = val.map(item => {
          return item.length && item[item.length - 1]
        })
        state.generateSubsidyForm.deptPaths = arr
      } else {
        state.generateSubsidyForm.deptPaths = []
      }
    };
    //生成名单编号
    const defineGenerateListClick = () => {
      generateSubsidy.value.validate((valid) => {
        if (valid) {
          let params = { ...state.generateSubsidyForm }
          params.takeEffectTime = timeStr(params.takeEffectTime);
          params.invalidTime = timeStr(params.invalidTime);
          params.subsidyAmount = Number(params.subsidyAmount);

          generateSubsidyList(state.generateSubsidyForm).then((res) => {
            if (res.code === 0) {
              state.isNewUpload = 2;
              state.WaitSubsidyForm.projectNo = res.data.projectNo;
              state.form.projectNo = res.data.projectNo;
              state.totalAmount = res.data.totalAmount;
              state.totalCount = res.data.totalCount;
              state.abnormalData = res.data.errorCount;
              queryWaitSubsidyListByPage();
            } else {
              ElMessage.error(res.message);
            }
          });
        } else {
          return false;
        }
      });
    };

    //获取未入库名单
    const queryWaitSubsidyListByPage = () => {
      state.WaitSubsidyLoading = true;
      getWaitSubsidyListByPage(state.WaitSubsidyForm)
        .then(({ data }) => {
          let { generateSubsidy, pageInfo } = data;
          state.totalCount = generateSubsidy.totalCount;
          state.totalAmount = generateSubsidy.totalAmount;
          state.abnormalData = generateSubsidy.errorCount;
          state.WaitSubsidyList = pageInfo;
          state.WaitSubsidyListTotal = generateSubsidy.totalCount;
          state.WaitSubsidyLoading = false;
        })
        .catch(() => { });
      state.WaitSubsidyLoading = false;
    };

    //获取已入库名单
    const querySubsidyListByPage = () => {
      state.WaitSubsidyLoading = true;
      getSubsidyListByPage(state.WaitSubsidyForm)
        .then((res) => {
          state.WaitSubsidyList = res.data.pageInfo.list;
          state.WaitSubsidyListTotal = res.data.pageInfo.total;
          state.generateSubsidyForm.userRole = res.data.role ? Number(res.data.role) : "";
          state.generateSubsidyForm.deptPaths = res.data.dept;
          state.totalCount = res.data.generateSubsidy.totalCount;
          state.totalAmount = res.data.generateSubsidy.totalAmount;
          state.abnormalData = res.data.generateSubsidy.errorCount;
          state.WaitSubsidyLoading = false;
        })
        .catch(() => { });
      state.WaitSubsidyLoading = false;
    };

    const delClick = (val) => {
      if (state.isNewUpload === 1) {
        delUpdateWaitSubsidy(val.subSidyRecordId).then(({ code, message }) => {
          if (code === 0) {
            querySubsidyListByPage();
          } else {
            ElMessage.error(message);
          }
        });
      } else if (state.isNewUpload === 2 || state.isNewUpload === 3) {
        let data = {
          projectNo: state.form.projectNo,
          id: parseInt(val.id),
        };
        deleteWaitSubsidy(data).then(({ code, message }) => {
          if (code === 0) {
            queryWaitSubsidyListByPage();
          } else {
            ElMessage.error(message);
          }
        });
      }
    };
    const uploadSample = async () => {
      let res = await exportSubsidyList();
      let url = window.URL.createObjectURL(
        new Blob([res], { type: "application/vnd.ms-excel" })
      );
      let link = document.createElement("a");
      link.style.display = "none";
      link.href = url;
      link.setAttribute("download", "导入补助清单样例.xlsx");
      document.body.appendChild(link);
      link.click();
    };
    const submitForm = async () => {
      ruleForm.value.validate(async (valid) => {
        if (valid) {
          state.form.subsidyMonth = timeStr(state.form.subsidyMonth);
          /*           data.takeEffectTime = timeStr(data.takeEffectTime);
                    data.invalidTime = timeStr(data.invalidTime); */
          state.form.grantMode = state.grantType;
          let data = {}
          if (state.form.grantMode == "SYSTEM") {
            let params = { ...state.generateSubsidyForm }
            params.takeEffectTime = timeStr(params.takeEffectTime);
            params.invalidTime = timeStr(params.invalidTime);
            params.subsidyAmount = Number(params.subsidyAmount);
            data = { ...state.form, ...params };
          } else {
            data = { ...state.form };
          }
          if (state.isNewUpload === 1) {
            console.log(data);
          } else {
            if (!state.form.projectNo) {
              return ElMessage.error("请先生成或导入补助清单！");
            }
          }
          let { code, message } = await editSubsidyProject(data);
          if (code === 0) {
            ElMessage.success(message);
            context.emit("offEdit", true);
            state.isNewUpload = 1
          } else {
            ElMessage.error(message);
          }
        } else {
          return false;
        }
      });
    };

    const beforeClose = () => {
      store.commit("subsidyData/updateState", {
        key: "isEdit",
        payload: false,
      });
      context.emit("offEdit", false);
    };

    const handlePageChange = (val) => {
      state.WaitSubsidyForm.currentPage = val;
      querySubsidyListByPage();
    };
    const handleSizeChange = (val) => {
      state.WaitSubsidyForm.currentPage = 1;
      state.WaitSubsidyForm.pageSize = val;
      querySubsidyListByPage();
    };

    onMounted(() => { });
    return {
      state,
      uploadDom,
      isEdit,
      subsidyTypeList,
      roleList,
      departCheckList,
      generateSubsidy,
      defineGenerateListClick,
      ruleForm,
      submitForm,
      beforeClose,
      uploadSample,
      handleRemove,
      handleSuccess,
      delClick,
      cascaderChange,
      handlePageChange,
      handleSizeChange,
    };
  },
};
</script>
<style lang="scss" scoped>
:deep(.el-dialog__header) {
  border-bottom: 1px solid #efefef;
}

:deep(.el-overlay) {
  position: absolute;
  background-color: rgba(0, 0, 0, 0);
}

:deep(.el-dialog__footer) {
  text-align: center;
}

.select-input-lang {
  .el-select {
    width: 500px;
  }
}

.pagination-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
