<template>
  <div class="income-detail border-box">
    <div class="padding-form-box">
      <el-form inline size="small" label-width="100px">
        <el-form-item label="选择日期:">
          <el-col :span="24">
            <el-date-picker v-model="state.requestDate" type="datetimerange" range-separator="~" :default-time="state.defaultTime" start-placeholder="请选择日期" end-placeholder="请选择日期" unlink-panels @change="changeDate">
            </el-date-picker>
          </el-col>
          <el-col :span="3" class="date" v-for="(item, index) in state.defaultDateList" :key="index" @click="changeDefaultDate(item.value)">{{ item.label }}</el-col>
        </el-form-item>
        <div>
          <el-form-item label="金额范围:">
            <el-input class="tranBal-input" placeholder="交易金额" v-model="state.form.minAmount"></el-input>
            <span>&nbsp;&nbsp;- &nbsp;&nbsp;</span>
            <el-input class="tranBal-input" placeholder="交易金额" v-model="state.form.maxAmount"></el-input>
          </el-form-item>
          <el-form-item label="交易单号:">
            <el-input v-model="state.form.tradeNo" placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="收支类型:">
            <el-select clearable v-model="state.form.inoutType" placeholder="请选择">
              <el-option v-for="(item, index) in inoutTypeList" :key="index" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="交易类型:">
            <el-select clearable v-model="state.form.costType" placeholder="请选择">
              <el-option v-for="(item, index) in state.costTypeList" :key="index" :label="item.costName" :value="item.costCode"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="对方信息:">
            <el-input v-model="state.form.userName" placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="区域:">
            <kade-area-select-tree style="width: 100%" :value="state.form.areaPath" valueKey="areaPath" :multiple="false" @valueChange="(val) => (state.form.areaPath = val.areaPath)" />
          </el-form-item>
          <el-form-item label="&nbsp;">
            <el-button @click="loadData()" size="small" type="primary" icon="el-icon-search">搜索</el-button>
            <el-button @click="handleReset" icon="el-icon-refresh-right" size="small">重置</el-button>
          </el-form-item>
        </div>
      </el-form>
    </div>
    <el-divider></el-divider>
    <div class="padding-box transaction-info">
      <div class="transaction-item">
        <div class="label">交易总金额</div>
        <div class="value">
          <span class="num">{{state.totalAmount.incomeAmount}}</span>
          <span class="unit">元</span>
        </div>
      </div>
      <div class="transaction-item">
        <div class="label">成交订单数</div>
        <div class="value">
          <span class="num">{{state.totalAmount.numBalance}}</span>
          <span class="unit">笔</span>
        </div>
      </div>
    </div>
    <el-divider></el-divider>
    <kade-table-wrap title="明细列表">
      <template #extra>
        <el-button :disabled="!id||!state.total" @click="handleDownload" icon="el-icon-download" size="small" class="btn-deep-blue">下载查询明细</el-button>
        <el-button :disabled="!id||!state.total" @click="handlePrint" icon="el-icon-printer" size="small" class="btn-purple">打印</el-button>
      </template>
      <el-table style="width: 100%" :data="state.dataList" v-loading="false" highlight-current-row border stripe>
        <el-table-column show-overflow-tooltip label="交易编号" prop="tradeNo" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip label="交易时间" prop="tradeDate" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip label="对方信息" prop="userName" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip label="交易钱包" prop="walletName" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip label="收支类型" prop="inoutType" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip label="交易类型" prop="costTypeName" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip label="交易来源" prop="tradeSourceName" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip label="设备所在区域" prop="areaName" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip label="设备名称" prop="deviceName" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip label="收入金额(元)" prop="tradeAmount" align="center"></el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination background v-model:current-page="state.form.currentPage" layout="total, sizes, prev, pager, next, jumper" :page-sizes="[10, 20, 50, 100, 200]" :total="state.total" v-model:page-size="state.form.pageSize" @current-change="handlePageChange" @size-change="pageSizeChange">
        </el-pagination>
      </div>
    </kade-table-wrap>

  </div>
</template>
<script>
import {
  ElCol,
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElDatePicker,
  ElDivider,
  ElTable,
  ElTableColumn,
  ElPagination,
} from "element-plus";
import { reactive, watch, onMounted } from "vue";
import { formatColumns, downloadXlsx, print } from "@/utils";
import { timeStr } from "@/utils/date.js";
import {
  requestDefaultTime,
  requestDate,
  defaultDateList,
} from "@/utils/reqDefaultDate.js";
import { useDict } from "@/hooks/useDict.js";
import {
  costType,
  getMerchantIncomeListByPage,
  getMerchantIncomeListToExport,
  getMerchantIncomeListToPrint,
} from "@/applications/eccard-finance/api";
import areaSelectTree from "@/components/tree/areaSelectTree.vue";
export default {
  components: {
    ElCol,
    ElSelect,
    ElOption,
    ElDatePicker,
    "el-divider": ElDivider,
    "el-button": ElButton,
    "el-input": ElInput,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    ElTable,
    ElTableColumn,
    ElPagination,
    "kade-area-select-tree": areaSelectTree,
  },
  props: {
    id: {
      type: [String, Number],
    },
  },
  setup(props) {
    const inoutTypeList = useDict("WALLET_INOUT_TYPE");
    const state = reactive({
      requestDate: requestDate(),
      defaultTime: requestDefaultTime(),
      defaultDateList,
      selected: null,
      costTypeList: [],
      form: {
        currentPage: 1,
        pageSize: 10,
        beginDate: requestDate()[0],
        endDate: requestDate()[1],
      },
      dataList: [],
      total: 0,
      totalAmount:{
        expendAmount:0,
        incomeAmount:0,
        numBalance:0,
      },
      columns: formatColumns([
        {
          type: "selection",
          width: "55",
        },
        {
          prop: "jybh12",
          label: "交易编号",
        },
        {
          prop: "jybh11",
          label: "交易时间",
        },
        {
          prop: "jybh9",
          label: "对方信息",
        },
        {
          prop: "jybh8",
          label: "交易钱包",
        },
        {
          prop: "jybh7",
          label: "收支类型",
        },
        {
          prop: "jybh6",
          label: "交易类型",
        },
        {
          prop: "jybh5",
          label: "交易来源",
        },
        {
          prop: "jybh4",
          label: "设备所在区域",
        },
        {
          prop: "jybh3",
          label: "设备名称",
        },
        {
          prop: "jybh2",
          label: "收入金额(元)",
        },
      ]),
    });

    //获取交易类型
    const getCostTypeList = () => {
      costType().then((res) => {
        state.costTypeList = res.data;
      });
    };
    const loadData = async () => {
      if (!props.id) return;
      state.form.merchantId = props.id;
      let params={...state.form}
      for(let key in params){
        if(!params[key]) delete params[key]
      }
      let {
        data: { list, total },totalAmount
      } = await getMerchantIncomeListByPage(params);
      state.dataList = list;
      state.total = total;
      state.totalAmount=totalAmount
      for(let key in state.totalAmount){
        if(!state.totalAmount[key]){
          state.totalAmount[key]=0
        }
      }
    };

    const handleReset = () => {
      state.form = {
        currentPage: 1,
        pageSize: 10,
        beginDate: requestDate()[0],
        endDate: requestDate()[1],
      };
      state.requestDate = requestDate();
    };
    const changeDefaultDate = (val) => {
      state.requestDate = val();
      state.form.beginDate = state.requestDate[0];
      state.form.endDate = state.requestDate[1];
    };
    const changeDate = (val) => {
      state.form.beginDate = timeStr(val[0]);
      state.form.endDate = timeStr(val[1]);
    };
    const handleDownload = async () => {
      const res = await getMerchantIncomeListToExport(state.form);
      console.log(res);
      downloadXlsx(res, "收入明细表.xlsx");
    };
    const handlePrint = async () => {
      const { code, data } = await getMerchantIncomeListToPrint(state.form);
      if (code === 0) {
        print(data);
      }
    };
    const handlePageChange = (v) => {
      state.form.currentPage = v;
      loadData();
    };
    const pageSizeChange = (v) => {
      state.form.currentPage = 1;
      state.form.pageSize = v;
      loadData();
    };
    watch(
      () => props.id,
      (val) => {
        if (val) {
          loadData();
        }
      }
    );
    onMounted(() => {
      loadData();
      getCostTypeList();
    });
    return {
      inoutTypeList,
      state,
      loadData,
      handleReset,
      changeDefaultDate,
      changeDate,
      handleDownload,
      handlePrint,
      handlePageChange,
      pageSizeChange,
    };
  },
};
</script>
<style lang="scss" scoped>
.income-detail {
  .transaction-info {
    display: flex;
    .transaction-item {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      margin-right: 100px;
      .value {
        margin-top: 20px;
        .num {
          color: $font-dark;
          font-size: 30px;
        }
        .unit {
          color: $font-sub-color;
          margin-left: 10px;
        }
      }
    }
  }
  .el-divider--horizontal {
    margin: 0 !important;
  }
  .kade-table-wrap {
    border: none;
  }
}
.tranBal-input {
  width: 84px !important;
  .el-input__inner {
    width: 84px !important;
  }
}
.date {
  margin: 0 10px;
  color: rgba(0, 0, 0, 0.847058823529412);
}
:deep(.el-select){
  width: 193px;
}
</style>