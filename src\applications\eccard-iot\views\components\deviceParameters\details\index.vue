<template>
  <el-dialog :model-value="modelValue" :title="TabModule().title+'参数详情信息'" width="90%" :before-close="beforeClose">
    <kade-tab-wrap :tabs="tabs" style="margin-top: 20px" v-model="state.tab">
      <template #jbxx>
        <kade-basic-info :TabModule="TabModule" />
      </template> 
      <template #csxx>
        <kade-params-info :TabModule="TabModule" />
      </template>
      <template #bdsblb>
        <kade-bind-device-list :TabModule="TabModule" />
      </template>
    </kade-tab-wrap>
    <div style="margin-top:20px;height:1px"></div>
  </el-dialog>
</template>
<script>
import { ElDialog } from "element-plus";
import { reactive, ref } from "@vue/reactivity";
import basicInfo from "./basicInfo.vue";
import paramsInfo from "./paramsInfo.vue";
import bindDeviceList from "./bindDeviceList.vue";
const tabs = [
  { name: "jbxx", label: "基本信息" },
  { name: "csxx", label: "参数信息" },
  { name: "bdsblb", label: "绑定设备列表" },
];
export default {
  components: {
    ElDialog,
    "kade-basic-info": basicInfo,
    "kade-params-info": paramsInfo,
    "kade-bind-device-list": bindDeviceList,
  },
  props: {
    modelValue: {
      type: Boolean,
      default: false,
    },
    TabModule:{
      type:Function,
      default:null,
    }
  },
  setup(props,context) {
    const formDom = ref(null);
    const state = reactive({
      tab: "jbxx",
    });
    const beforeClose = () => {
      state.tab="jbxx"
      context.emit("update:modelValue",false)
    };
    return {
      tabs,
      formDom,
      state,
      beforeClose,
    };
  },
};
</script>
<style lang="scss" scoped>
</style>