<template>
  <kade-route-card style="height: auto">
    <kade-table-filter @search="getList()" @reset="reset()">
      <el-form inline size="mini" label-width="100px">
        <el-form-item label="待办类型">
          <el-select v-model="state.form.typeId">
            <el-option v-for="(item,index) in 10" :key="index" :label="item" :value="item"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="待办项目">
          <el-select v-model="state.form.projectId">
            <el-option v-for="(item,index) in 10" :key="index" :label="item" :value="item"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="state.form.status">
            <el-option v-for="(item,index) in 10" :key="index" :label="item" :value="item"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="接收时间">
          <el-date-picker
            v-model="state.requestDate"
            type="daterange"
            unlink-panels
            range-separator="~"
            start-placeholder="请选择日期"
            end-placeholder="请选择日期"
            @change="changeDate"
          >
          </el-date-picker>
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="待办事项列表">
      <el-table style="width: 100%" :data="state.dataList" ref="multipleTable" v-loading="state.loading" height="55vh" border stripe>
        <el-table-column v-for="(item,index) in column" :key="index" :width="item.width" :label="item.label" :prop="item.prop" align="center" show-overflow-tooltip>
          <template v-if="item.render" #default="scope">
            {{item.render(scope.row[item.prop])}}
          </template>  
        </el-table-column>
        <el-table-column label="操作" align="center">
          <template #default="scope">
            <el-button type="text" @click="handleClick(scope.row)">{{scope.row.status}}</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination
          background
          :current-page="state.form.pageNum"
          :page-size="state.form.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :page-sizes="[6, 10, 20, 50, 100]"
          :total="state.total"
          @current-change="handlePageChange"
          @size-change="handleSizeChange"
        >
        </el-pagination>
      </div>
    </kade-table-wrap>
  </kade-route-card>
</template>
<script>
import { reactive } from "@vue/reactivity";
import {timeStr} from "@/utils/date"
import {ElForm,ElFormItem,ElSelect,ElOption,ElDatePicker,ElTable,ElTableColumn,ElPagination,ElButton} from "element-plus"
import { getTodoInfoList } from "@/applications/eccard-basic-data/api";
import { onMounted } from '@vue/runtime-core';
const column=[
  {label:"状态",prop:"status"},
  {label:"待办类型",prop:"typeId",width:""},
  {label:"待办项目",prop:"projectId",width:""},
  {label:"接收时间",prop:"receiveTime",width:""},
]
export default {
  components: {
    ElForm,ElFormItem,ElSelect,ElOption,ElDatePicker,ElTable,ElTableColumn,ElPagination,ElButton
  },
  setup() {
    const state = reactive({
      loading: false,
      form:{
        pageNum:1,
        pageSize:10
      },
      requestDate:[],
      dataList:[],
      total:0
    });
    const getList=async ()=>{
      // state.loading=true
      let {data}=await getTodoInfoList(state.form)
      console.log(data);
      state.dataList=data.list
      state.total=data.total
      console.log(state.dataList,state.total);
      
      // state.loading=false
    }
    const changeDate=(val)=>{
      if (val && val.length) {
        state.form.startDate = timeStr(val[0]);
        state.form.endDate = timeStr(val[1]);
      } else {
        delete state.form.startDate;
        delete state.form.endDate;
      }
    }

    const handleClick=(row)=>{
      console.log(row);
      
    }

    const handlePageChange=(val)=>{
      state.form.pageNum=val
      getList()
    }
    const handleSizeChange=(val)=>{
      state.form.pageSize=val
      getList()
    }
    onMounted(()=>{
      getList()
    })
    return {
      column,
      state,
      changeDate,
      handleClick,
      handlePageChange,
      handleSizeChange
    };
  },
};
</script>
<style lang="scss" scoped>

</style>