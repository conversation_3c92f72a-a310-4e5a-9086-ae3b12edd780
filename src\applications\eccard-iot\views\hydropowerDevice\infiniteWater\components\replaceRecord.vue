<template>
  <el-dialog v-loading="state.loading" :model-value="isRecord.isShow" :title="(isRecord.rowData.id?'修改':'新增')+'设备更换记录'" width="640px" :before-close="beforeClose" :close-on-click-modal="false">
    <el-form ref="formRef" label-width="100px" size="mini" :model="state.form" :rules="rules" style="margin-top:20px">
      <el-form-item label="更换原因：" prop="replaceReason">
        <el-input v-model="state.form.replaceReason" placeholder="请输入"></el-input>
      </el-form-item>
      <el-form-item label="更换时间：" prop="replaceDate">
        <el-date-picker v-model="state.form.replaceDate" type="datetime" placeholder="请选择" />
      </el-form-item>
      <el-form-item label="更换人员：">
        <el-input v-model="state.form.operatorName" placeholder="请输入"></el-input>
      </el-form-item>
      <el-form-item label="备注：">
        <el-input v-model="state.form.replaceRemarks" placeholder="请输入"></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="submit()" type="primary" size="mini">保&nbsp;&nbsp;存</el-button>
        <el-button @click="beforeClose()" size="mini">返&nbsp;&nbsp;回</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script>
import { ElDialog, ElButton, ElForm, ElFormItem, ElInput, ElDatePicker, ElMessage } from "element-plus"
import { computed, reactive, watch, nextTick, ref } from 'vue'
import { useStore } from "vuex"
import { timeStr } from "@/utils/date"
import { deviceReplaceAdd, deviceReplaceEdit } from "@/applications/eccard-iot/api";
export default {
  components: {
    ElDialog,
    ElButton,
    ElForm,
    ElFormItem,
    ElInput,
    ElDatePicker
  },
  props: {
    data: {
      type: Object,
      default: null
    },
  },
  setup(props) {
    const formRef = ref(null)
    const store = useStore()
    const state = reactive({
      loading:false,
      form: {}
    })
    const isRecord = computed(() => {
      return store.state['hydropowerDevice/infiniteWater'].isRecord
    })
    watch(() => store.state['hydropowerDevice/infiniteWater'].isRecord.isShow, val => {
      if (val) {
        state.form = { ...store.state['hydropowerDevice/infiniteWater'].isRecord.rowData }
        nextTick(() => {
          formRef.value.clearValidate()
        })
      }
    })
    const rules = {
      replaceReason: [
        {
          required: true,
          message: "请输入更换原因",
        },
      ],
      replaceDate: [
        {
          required: true,
          message: "请选择更换时间",
          trigger: "change",
        },
      ],
    }
    const submit = () => {
      formRef.value.validate(async (valid) => {
        if (valid) {
          let { id, deviceModel, deviceName, deviceNo, deviceType } = props.data
          let params = { deviceId: id, deviceModel, deviceName, deviceNo, deviceType, ...state.form }
          params.replaceDate = timeStr(params.replaceDate)
          console.log(params);
          let fn = store.state['hydropowerDevice/infiniteWater'].isRecord.rowData.id ? deviceReplaceEdit : deviceReplaceAdd
          state.loading = true
          try {
            let { code, message } = await fn(params)
            if (code === 0) {
              ElMessage.success(message)
              beforeClose()
            }
            state.loading = false
          }
          catch {
            state.loading = false
          }
        } else {
          return false
        }
      })
    }
    const beforeClose = () => {
      store.commit("hydropowerDevice/infiniteWater/updateState", {
        key: "isRecord",
        payload: {
          type: store.state['hydropowerDevice/infiniteWater'].isRecord.type,
          isShow: false,
          rowData: {}
        },
      });
    }
    return {
      formRef,
      rules,
      state,
      isRecord,
      submit,
      beforeClose
    }
  }
}
</script>
<style lang="scss" scoped>
:deep(.el-input__inner) {
  width: 100% !important;
}
:deep(.el-date-editor.el-input) {
  width: 100%;
}
</style>