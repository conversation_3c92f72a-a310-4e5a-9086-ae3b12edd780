<template>
  <kade-route-card>
    <kade-table-filter @search="search" @reset="handleReset">
      <el-form inline size="small" label-width="100px">
        <el-form-item label="商户名称:">
          <el-input style="width: 215px" v-model="querys.keyWord" placeholder="请输入" clearable></el-input>
        </el-form-item>
        <el-form-item label="商家类型:">
          <el-select placeholder="请选择" v-model="querys.merchantType" style="width: 100%" clearable>
            <el-option v-for="item in types" :label="item.label" :value="item.value" :key="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="所属区域:">
          <kade-area-select-tree style="width: 100%" :value="querys.areaId" valueKey="id" :multiple="false"
            @valueChange="(val) => (querys.areaId = val.id)" />
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <!-- <kade-table
      title="商户列表"
      :columns="state.columns"
      :load-data="loadData"
      :table-options="options"
      @on-select-change="handleSelectChange"
      @on-page-change="handlePageChange"
      ref="tableRef"
    >
      <template #extra>
        <el-button @click="() => handleBtnClick()" icon="el-icon-plus" size="small" type="success">新建商户</el-button>
        <el-button class="btn-import" icon="el-icon-daoru" size="small">导入</el-button>
        <el-button type="danger" icon="el-icon-daochu" size="small">导出</el-button>               
      </template>
    </kade-table> -->

    <kade-shop-info-list :tableOptions="options" :params="querys" @on-select-change="handleSelectChange"
      @on-page-change="handlePageChange" :load-data="loadData" />

    <kade-tab-wrap :tabs="tabs" style="margin-top: 20px" v-model="state.tab">
      <template #srmx>
        <kade-income-details :id="state.selected?.merchantId || ''" />
      </template>
      <template #zhgl>
        <kade-account-manage :id="state.selected?.merchantId || ''" />
      </template>
    </kade-tab-wrap>
  </kade-route-card>
</template>
<script>
import { ElForm, ElFormItem, ElInput, ElOption, ElSelect } from "element-plus";
import { reactive, ref, getCurrentInstance } from "vue";
import { useStore } from "vuex";
import { usePagination } from "@/hooks/usePagination";
import { formatColumns } from "@/utils";
import { useDict } from "@/hooks/useDict";
import { useEvent } from "@/hooks/index.js"
import areaSelectTree from "@/components/tree/areaSelectTree.vue";

import IncomeDetails from "./components/incomeDetails";
import AccountManage from "./components/accountManage";
import shopInfoList from "./components/shopInfoList";
import { getMerchantListByPage } from "@/applications/eccard-finance/api";

const tabs = [
  {
    name: "srmx",
    label: "收入明细",
  },
  {
    name: "zhgl",
    label: "账号管理",
  },
];

export default {
  components: {
    "el-input": ElInput,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    "el-select": ElSelect,
    "el-option": ElOption,
    "kade-income-details": IncomeDetails,
    "kade-account-manage": AccountManage,
    "kade-shop-info-list": shopInfoList,
    "kade-area-select-tree": areaSelectTree,
  },
  setup() {
    const { options, search, querys, loadData } = usePagination(
      getMerchantListByPage,
      {
        keyWord: "",
        merchantType: "",
        areaId: "",
      },
      {},
      { currentPage: "currentPage", pageSize: "pageSize" }
    );

    let { $on } = useEvent()
    $on('success', ()=>{
      loadData()
    })

    const tableRef = ref(null);
    const store = useStore();
    const types = useDict("MERCHANT_TYPE");
    const { ctx } = getCurrentInstance();
    const handleBtnClick = (row, isInfo) => {
      let query = {};
      if (row) {
        query = { id: row.merchantId };
      }
      if (isInfo) {
        query.type = "info";
      }
      store.dispatch("app/addTab", {
        id: `ShopEdit${row?.merchantId || ""}`,
        payload: {
          menuName: "编辑商户",
          menuEnName: "ShopEdit",
          query,
        },
      });
    };
    const state = reactive({
      tab: "zhgl",
      areaName: "",
      selected: null,
      columns: formatColumns([
        {
          type: "selection",
          width: "55",
        },
        {
          label: "商户编号",
          prop: "merchantNo",
        },
        {
          label: "商户名称",
          prop: "merchantName",
        },
        {
          label: "商户类型",
          prop: "merchantType",
          render: (scope) => {
            return ctx.dictionaryFilter(scope.row.merchantType);
          },
        },
        {
          label: "所属区域",
          prop: "areaName",
        },
        {
          label: "终端总量",
          prop: "deviceTotalCount",
        },
        {
          label: "联系人",
          prop: "merchantContacts",
        },
        {
          label: "联系电话",
          prop: "merchantTel",
        },
        {
          label: "操作",
          prop: "oper",
          width: 160,
          render: ({ row }) => {
            return (
              <span>
                <ElButton
                  onClick={(e) => {
                    e.stopPropagation();
                    handleBtnClick(row, true);
                  }}
                  size="small"
                  type="text"
                >
                  查看详情
                </ElButton>
                <ElButton
                  onClick={(e) => {
                    e.stopPropagation();
                    handleBtnClick(row);
                  }}
                  size="small"
                  type="text"
                >
                  编辑
                </ElButton>
              </span>
            );
          },
        },
      ]),
    });

    const handleAreaChange = (data) => {
      console.log(123, data);
      querys.areaId = data ? data : "";
    };
    const handleReset = () => {
      Object.assign(querys, {
        areaId: "",
        keyWord: "",
        merchantType: "",
      });
      state.areaName = "";
      querys.areaId = "";
    };
    const handleSelectChange = (v) => {
      state.selected = v;
    };
    const handlePageChange = () => {
      if (
        state.selected &&
        options.dataList.some(
          (it) => it.merchantId === state.selected.merchantId
        )
      ) {
        tableRef.value?.tableRef?.setCurrentRow?.(state.selected);
      }
    };
    return {
      state,
      options,
      search,
      querys,
      loadData,
      tabs,
      types,
      tableRef,
      handleReset,
      handleSelectChange,
      handleAreaChange,
      handlePageChange,
      handleBtnClick,
    };
  },
};
</script>