<template>
  <div class="cashierdetails" v-loading="state.loading">
    <kade-route-card style="height: auto">
      <kade-table-filter @search="search()" @reset="reset()">
        <el-form inline label-width="100px" size="mini">
<!--           <el-form-item label="区域:">
            <kade-area-select-tree style="width: 100%" :value="state.form.areaPath" valueKey="areaPath"
              :multiple="false" @valueChange="(val) => (state.form.areaPath = val.areaPath)" />
          </el-form-item>
          <el-form-item label="操作员">
            <el-select v-model="state.form.operatorId" filterable placeholder="请选择" clearable>
              <el-option v-for="(item, index) in state.systemUserList" :key="index" :label="item.userName"
                :value="item.id" />
            </el-select>
          </el-form-item>

          <el-form-item label="组织机构:">
            <kade-dept-select-tree style="width: 100%" :value="state.form.deptPath" valueKey="deptPath"
              :multiple="false" @valueChange="(val) => (state.form.deptPath = val.deptPath)" />
          </el-form-item> -->
          <el-form-item label="交易方式">
            <el-select v-model="state.form.tradeMode" placeholder="请选择" size="small" clearable>
              <el-option v-for="(item, index) in state.costTypeList" :key="index" :label="item.name"
                :value="item.code">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="交易终端:">
            <el-select clearable v-model="state.form.deviceNo" placeholder="请选择">
              <el-option v-for="(item, index) in state.deviceByTerminalTypeList" :key="index" :label="item.label"
                :value="item.deviceNo">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="交易时间">
            <el-date-picker unlink-panels v-model="state.requestDate" type="datetimerange" :default-time="defaultTime"
              range-separator="~" start-placeholder="请选择日期" end-placeholder="请选择日期" />
          </el-form-item>
        </el-form>
      </kade-table-filter>
      <kade-table-wrap title="交易记录统计报表" >
        <template #extra>
          <el-button class="btn-blue" icon="el-icon-daochu" size="mini" @click="exportClick()">导出</el-button>
        </template>
        <el-table :data="state.dataList" border height="55vh">
          <!-- <el-table-column show-overflow-tooltip prop="areaName" label="区域名称" align="center"></el-table-column> -->
          <el-table-column show-overflow-tooltip prop="consumerCount" label="消费笔数" align="center"></el-table-column>
          <!-- <el-table-column show-overflow-tooltip prop="deptName" label="组织机构" align="center"></el-table-column> -->
          <!-- <el-table-column show-overflow-tooltip prop="deviceName" label="设备名称" align="center"></el-table-column> -->
          <el-table-column show-overflow-tooltip prop="deviceNo" label="设备机号" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip prop="tradeModeName" label="交易方式" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip prop="partnerAmount" label="搭伙费" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip prop="receivableAmount" label="应收金额" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip prop="reallyAmount" label="实收金额" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip prop="refundAmount" label="退款金额" align="center"></el-table-column>
        </el-table>

        <div class="pagination">
          <el-pagination :currentPage="state.form.pageNum" :page-size="state.form.pageSize"
            :page-sizes="[10, 20, 30, 40]" :small="small" background layout="total, sizes, prev, pager, next, jumper"
            :total="state.total" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
        </div>
      </kade-table-wrap>
    </kade-route-card>
  </div>
</template>

<script>
import { reactive } from "@vue/reactivity";
import {
  ElForm,
  ElFormItem,
  ElSelect,
  ElOption,
  ElDatePicker,
  ElButton,
  ElTable,
  ElTableColumn,
  ElPagination,
} from "element-plus";
/* import deptSelectTree from "@/components/tree/deptSelectTree";
import areaSelectTree from "@/components/tree/areaSelectTree"; */
import {
  getSystemUser,
  tradeMode,
  getTradeRecordStatistical,
  tradeRecordStatisticalExport
} from "@/applications/eccard-finance/api";
import { getDeviceStatus } from "@/applications/eccard-iot/api";
import { downloadXlsx } from "@/utils";
import { onMounted } from "@vue/runtime-core";
import { timeStr } from "@/utils/date.js";
import { requestDate } from "@/utils/reqDefaultDate";
const defaultTime = [
  new Date(new Date(new Date().toLocaleDateString()).getTime()),
  new Date(
    new Date(new Date().toLocaleDateString()).getTime() +
    24 * 60 * 60 * 1000 -
    1
  ),
];
export default {
  components: {
    ElForm,
    ElFormItem,
    ElSelect,
    ElOption,
    ElDatePicker,
    ElButton,
    ElTable,
    ElTableColumn,
    ElPagination,
/*     "kade-dept-select-tree": deptSelectTree,
    "kade-area-select-tree": areaSelectTree, */
  },
  setup() {
    const state = reactive({
      loading: false,
      requestDate: [requestDate()[0], requestDate()[1]],
      walletActiveList: [],
      form: {
        pageNum: 1,
        pageSize: 10,
      },
      dataList: [],
      total: 0,
      costTypeList: [],
      systemUserList: [],
    });

    const getList = () => {
      if (state.requestDate && state.requestDate.length) {
        state.form.beginDate = timeStr(state.requestDate[0]);
        state.form.endDate = timeStr(state.requestDate[1]);
      } else {
        delete state.form.beginDate;
        delete state.form.endDate;
      }
      let params = { ...state.form };
      state.loading = true
      getTradeRecordStatistical(params).then((res) => {
        let {
          data: { list, total },
        } = res;
        state.dataList = list;
        state.total = total;
        state.loading = false
      }).catch(() => {
        state.loading = false
      });
    };
    const search = () => {
      getList();
    };
    const reset = () => {
      state.form = {
        pageNum: 1,
        pageSize: 10,
      };
      state.requestDate = [requestDate()[0], requestDate()[1]];
    };
    const handleSizeChange = (val) => {
      state.form.pageNum = 1;
      state.form.pageSize = val;
      getList();
    };
    const handleCurrentChange = (val) => {
      state.form.pageNum = val;
      getList();
    };

    const querySystemUser = () => {
      getSystemUser().then((res) => {
        state.systemUserList = res.data;
      });
    };
    //获取交易方式
    const getTradeModeList = () => {
      tradeMode({costType:201}).then((res) => {
        state.costTypeList = res.data;
      });
    };
    //获取终端设备
    const queryDeviceByTerminalTypeList = () => {
      getDeviceStatus({}).then((res) => {
        state.deviceByTerminalTypeList = res.data.map((item) => {
          return {
            ...item,
            label: item.deviceNo + "-" + item.deviceName,
          };
        });
      });
    };
    const exportClick = async () => {
      if (state.requestDate && state.requestDate.length) {
        state.form.beginDate = timeStr(state.requestDate[0]);
        state.form.endDate = timeStr(state.requestDate[1]);
      } else {
        delete state.form.beginDate;
        delete state.form.endDate;
      }
      let params = { ...state.form };
      state.loading = true
      try {
        let res = await tradeRecordStatisticalExport(params);
        downloadXlsx(res, "交易记录统计报表.xlsx");
        state.loading = false
      }
      catch {
        state.loading = false
      }
    };
    onMounted(() => {
      getList();
      getTradeModeList()
      querySystemUser();
      queryDeviceByTerminalTypeList()
    });
    return {
      defaultTime,
      state,
      search,
      exportClick,
      reset,
      handleSizeChange,
      handleCurrentChange
    };
  },
};
</script>

<style lang="scss" scoped>
// :deep(.el-range-editor--mini.el-input__inner){
//     width: 205px;
// }
:deep(.el-select .el-input__inner) {
  height: 28px;
  width: 205px;
}

:deep(.el-input--mini .el-input__inner) {
  width: 205px;
}

:deep(.el-pagination .el-select .el-input .el-input__inner) {
  width: 100px;
}

:deep(.el-pagination__editor.el-input .el-input__inner) {
  width: 46px;
}
</style>