<template>
  <kade-route-card>
    <kade-table-filter @reset="handleReset" @search="handleSearch">
      <el-form inline label-width="90px" size="mini">
        <el-form-item label="关键字">
          <el-input v-model="state.form.keyWord" clearable placeholder="房间号或房间名称关键字搜索" size="mini"></el-input>
        </el-form-item>
        <kade-linkage-select :data="linkageData" :value="state.form" @change="linkageChange" />
        <el-form-item label="检查结果">
          <el-select clearable v-model="state.form.checkResult" placeholder="全部">
            <el-option v-for="(item, index) in checkResultList" :key="index" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否通报">
          <el-select clearable v-model="state.form.whetherNotify" placeholder="全部">
            <el-option v-for="(item, index) in whetherNotifyList" :key="index" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="检查日期">
          <el-date-picker style="width: 200px" v-model="state.requestDate" type="daterange" range-separator="~"
            @change="timeChange" unlink-panels>
          </el-date-picker>
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="卫生检查列表">
      <template #extra>
        <el-button type="success" icon="el-icon-plus" size="mini" @click="edit('add')">新增</el-button>
        <el-button class="btn-purple" icon="el-icon-edit" size="mini" @click="batchAdd">批量新增</el-button>
        <el-button class="btn-blue" icon="el-icon-top" size="mini" @click="handleExport">导入个人得分</el-button>
        <el-button class="btn-blue" icon="el-icon-delete-solid" size="mini" @click="batchDel">批量删除</el-button>
      </template>
      <el-table v-loading="state.loading" :data="state.dataList" border style="width: 100%"
        @selection-change="handleSelectChange">
        <el-table-column type="selection" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip v-for="(item, index) in column" :key="index" :prop="item.prop"
          :label="item.label" :width="item.width" align="center">
          <template #default="scope" v-if="item.isDict">
            {{ dictionaryFilter(scope.row[item.prop]) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="200px">
          <template #default="scope">
            <el-button type="text" size="mini" @click="edit('edit', scope.row)" class="green">编辑</el-button>
            <el-button type="text" size="mini" @click="handleDel(scope.row)" class="green">删除</el-button>
            <el-button type="text" size="mini" @click="edit('details', scope.row)" class="green">查看详情</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange"
          :current-page="currentPage" :page-size="10" layout="total, prev, pager, next, jumper" :total="state.total">
        </el-pagination>
      </div>
    </kade-table-wrap>
    <kade-hygiene-check-batch v-model:modelValue="state.isBatch" @update:modelValue="close" />
    <kade-hygiene-check-leading v-model:modelValue="state.isLeading" @update:modelValue="close" />
    <kade-hygiene-check-edit v-model:modelValue="state.isShow" :dialogType="state.dialogType"
      :selectRow="state.selectRow" @update:modelValue="close" />
  </kade-route-card>
</template>

<script>
import {
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElTable,
  ElTableColumn,
  ElPagination,
  ElMessage,
  ElMessageBox,
  ElSelect,
  ElOption,
  ElDatePicker,
} from "element-plus";
import linkageSelect from "@//applications/eccard-dorm/components/linkageSelect.vue";
import { reactive } from "@vue/reactivity";
import { timeStr } from "@/utils/date.js";
import edit from "./components/edit.vue";
import batch from "./components/batch.vue";
import leading from "./components/leading.vue";
import { useStore } from "vuex";
import {
  getHygieneCheckList,
  deleteHygieneCheckInfo,
  deleteHygieneCheckInfos,
} from "@/applications/eccard-dorm/api";
import { onMounted } from "vue";
import { useDict } from "@/hooks/useDict";

const column = [
  { label: "所属区域", prop: "areaName" },
  { label: "房间", prop: "roomString", width: "200px" },
  { label: "检查日期", prop: "checkTime" },
  { label: "检查人", prop: "checkPerson" },
  { label: "检查结果", prop: "checkResult", isDict: true },
  { label: "是否通报", prop: "whetherNotify", isDict: true },
];
const linkageData = {
  buildingType: { label: "楼栋类型", valueKey: "buildType" },
  area: { label: "所属区域", valueKey: "areaPath", key: "areaPath" },
  building: { label: "楼栋", valueKey: "buildId" },
  unit: { label: "单元", valueKey: "unitNum" },
  floor: { label: "楼层", valueKey: "floorNum" },
};

export default {
  components: {
    ElButton,
    ElForm,
    ElFormItem,
    ElInput,
    ElTable,
    ElTableColumn,
    ElPagination,
    ElSelect,
    ElOption,
    ElDatePicker,
    "kade-hygiene-check-edit": edit,
    "kade-hygiene-check-batch": batch,
    "kade-hygiene-check-leading": leading,
    "kade-linkage-select": linkageSelect,
  },
  setup() {
    const store = useStore();
    const checkResultList = useDict("DORM_DAILY_CHECK_RESULT"); //检查结果
    const whetherNotifyList = useDict("SYS_BOOL_STRING"); //是否通报
    const state = reactive({
      isShow: false,
      isBatch: false,
      isLeading: false,
      form: {
        dormType: "",
        currentPage: 1,
        pageSize: 10,
      },
      selectRow: "",
      loading: false,
      dialogType: "",
      requestDate: [],
      dataList: [],
      total: 0,
      selectList: [],
    });
    const edit = (type, row) => {
      console.log(row);
      state.dialogType = type;
      state.selectRow = row;
      state.isShow = true;
    };
    const handleExport = () => {
      state.isLeading = true;
    };
    const batchAdd = () => {
      state.isBatch = true;
    };
    const handleDel = (val) => {
      ElMessageBox.confirm(`确认删除?`, `提示`, {
        type: "warning",
        confirmButtonText: "确定",
        cancelButtonText: "取消",
      }).then(async () => {
        let { code, message } = await deleteHygieneCheckInfo(val.id);
        if (code === 0) {
          ElMessage.success(message);
          getList();
        }
      });
    };
    const close = (val) => {
      if (val) {
        getList();
      }
      state.isShow = false;
      state.isLeading = false;
      state.isBatch=false
    };
    const handleSelectChange = (val) => {
      state.selectList = val;
    };
    const batchDel = () => {
      if (!state.selectList.length) {
        return ElMessage.error("请先选择需要删除的信息！");
      }
      ElMessageBox.confirm("确认删除已选择信息?", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        // let param = state.selectList.map((item) => item.id).join(",")
        let param = state.selectList
          .map(function (item) {
            return item.id;
          })
          .join(",");
        let { code, message } = await deleteHygieneCheckInfos(param);
        if (code === 0) {
          ElMessage.success(message);
          getList();
        }
      });
    };
    const handleReset = () => {
      state.form = {
        pageSize: 10,
        currentPage: 1
      }
      state.requestDate = []
    };
    const handleSearch = () => {
      state.form.currentPage = 1
      state.form.pageSize = 10
      getList()
    };
    const linkageChange = (val) => {
      state.form = { ...state.form, ...val };
    };
    const getList = async () => {
      if (state.requestDate && state.requestDate.length) {
        state.form.checkBeginDate = timeStr(state.requestDate[0]);
        state.form.checkEndDate = timeStr(state.requestDate[1]);
      } else {
        delete state.form.checkBeginDate;
        delete state.form.checkEndDate;
      }
      for (let key in state.form) {
        if (!state.form[key]) {
          delete state.form[key];
        }
      }
      state.loading = true;
      try {
        let {
          data: { list, total },
        } = await getHygieneCheckList(state.form);
        state.dataList = list;
        state.total = total;
        console.log(state.dataList);
        state.loading = false;
      } catch {
        state.loading = false;
      }
    };

    const handleSizeChange = (val) => {
      state.form.currentPage = 1
      state.form.pageSize = val
      getList();
    };
    const handleCurrentChange = (val) => {
      state.form.currentPage = val;
      getList();
    };
    onMounted(() => {
      getList();
    });
    return {
      state,
      store,
      edit,
      handleExport,
      batchAdd,
      batchDel,
      close,
      handleSearch,
      handleReset,
      handleSizeChange,
      checkResultList,
      whetherNotifyList,
      column,
      linkageData,
      linkageChange,
      handleDel,
      handleSelectChange,
      handleCurrentChange,
    };
  },
};
</script>

<style lang="scss" scoped>
.green :hover {
  text-decoration: underline;
}

:deep(.el-pagination__editor.el-input .el-input__inner) {
  width: 46px;
}

:deep([data-v-38e88c0c] .el-range-editor--mini.el-input__inner) {
  width: 240px !important;
}

:deep(.el-input--mini .el-input__inner) {
  width: 198px;
}

:deep(.el-date-editor.el-input, .el-date-editor.el-input__inner) {
  width: 198px;
}

:deep(.el-textarea__inner) {
  width: 198px;
}

:deep(.el-dialog__footer) {
  border: 0;
  text-align: center;
}

:deep(.el-divider--horizontal) {
  margin: 10px 0;
}

:deep(.el-dialog__header) {
  border-bottom: 1px solid #eeeeee;
}

/* :deep(.el-dialog) {
  border-radius: 7px;
  padding-bottom: 40px;
} */

:deep(.table .person-grade[data-v-0dc0499d]) {
  height: 418px;
}
</style>
