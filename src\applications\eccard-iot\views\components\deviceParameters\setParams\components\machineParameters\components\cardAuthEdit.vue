<template>
  <el-dialog :model-value="isCardAuthEdit.isShow" :title="isCardAuthEdit.title" width="60%" :before-close="beforeClose" append-to-body>
    <div class="box-dialog">
      <kade-table-wrap title="卡类信息">
        <el-divider></el-divider>
        <div>
          <el-form inline size="mini" :model="state.form" :rules="state.rules" ref="form" label-width="150px" :disabled="isCardAuthEdit.disabled">
            <el-form-item label="卡类：" prop="card_num">
              <el-select clearable v-model="state.form.card_num" placeholder="请选择">
                <el-option v-for="(item, index) in cardTypeList" :key="index" :label="item.ctName" :value="item.ctCode">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="卡类别名：">
              <el-input placeholder="姓名或编号关键字搜索" v-model="state.form.card_name"></el-input>
            </el-form-item>
            <el-form-item label="卡类折扣率(%)：">
              <el-input-number v-model="state.form.card_discount" :min="0" :max="100" />
            </el-form-item>
            <el-form-item label="卡类单次限额(元)：">
              <el-input-number :precision="2" v-model="state.form.card_single_quota" :min="0" :max="9999.99" />
            </el-form-item>
            <el-form-item label="卡类日限额(元)：">
              <el-input-number :precision="2" v-model="state.form.card_day_quota" :min="0" :max="9999.99" />
            </el-form-item>
            <el-form-item label="卡类日限次：">
              <el-input-number v-model="state.form.card_day_xianci" :min="0" :max="255" />
            </el-form-item>
          </el-form>
        </div>
      </kade-table-wrap>
      <kade-table-wrap title="餐时段参数" style="margin-top: 10px">
        <el-divider></el-divider>
        <el-tabs type="card" @tab-click="handleClick">
          <el-tab-pane v-for="(item, index) in state.form.listTime" :key="index" :label="item.label">
            <el-form inline size="mini" label-width="150px" :disabled="isCardAuthEdit.disabled">
              <el-form-item label="是否允许交易：">
                <el-select v-model="item.card_is_trade" placeholder="请选择">
                  <el-option v-for="(item, index) in option" :key="index" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="次数定值：">
                <el-input-number v-model="item.card_dzcs" :min="0" :max="100" />
              </el-form-item>
              <el-form-item label="免费使用次数：">
                <el-input-number v-model="item.card_is_free" :min="0" :max="255" />
              </el-form-item>
              <el-form-item label="次数交易限次：">
                <el-input-number v-model="item.card_dzjycs" :min="0" :max="255" />
              </el-form-item>
              <el-form-item label="金额定值(第一次)：">
                <el-input-number :precision="2" v-model="item.card_jedz" :min="0" :max="999.99" />
              </el-form-item>
              <el-form-item label="金额定值(第二次)：">
                <el-input-number :precision="2" v-model="item.card_jedz1" :min="0" :max="999.99" />
              </el-form-item>
              <el-form-item label="金额定值(第三次)：">
                <el-input-number :precision="2" v-model="item.card_jedz2" :min="0" :max="999.99" />
              </el-form-item>
              <el-form-item label="交易限额(元)：">
                <el-input-number :precision="2" v-model="item.card_jyxe" :min="0" :max="9999.99" />
              </el-form-item>
              <el-form-item label="餐交易总限次：">
                <el-input-number v-model="item.card_jyxc" :min="0" :max="255" />
              </el-form-item>
            </el-form>
          </el-tab-pane>
        </el-tabs>
      </kade-table-wrap>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="beforeClose" size="mini">取&nbsp;&nbsp;消</el-button>
        <el-button @click="submitForm()" size="mini" type="primary">确&nbsp;&nbsp;认</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script>
import {
  ElDivider,
  ElButton,
  ElDialog,
  ElForm,
  ElFormItem,
  ElSelect,
  ElOption,
  ElInput,
  ElInputNumber,
  ElTabs,
  ElTabPane,
} from "element-plus";
import { computed, nextTick, reactive, ref, watch } from "vue";
import { useStore } from "vuex";

const formFnc = () => {
  return {
    card_num: "", //卡类
    card_name: "1类卡", //卡类别名
    card_discount: 100, //卡类折扣率(%)
    card_single_quota: 0, //卡类单次限额(元)
    card_day_quota: 0, //卡类日限额(元)
    card_day_xianci: 0, //卡类日限次
    listTime: [
      {
        card_is_trade: 1, //是否允许交易
        card_dzcs: 0, //次数定值
        card_is_free: 0, //免费使用次数
        card_dzjycs: 0, //次数交易限次
        card_jedz: 0, //金额定值(第一次)
        card_jedz1: 0, //金额定值(第二次)
        card_jedz2: 0, //金额定值(第三次)
        card_jyxe: 0, //交易限额(元)
        card_jyxc: 0, //餐交易总限次
        id: 0,
        label: "餐（时段）1",
      },
      {
        card_is_trade: 1, //是否允许交易
        card_dzcs: 0, //次数定值
        card_is_free: 0, //免费使用次数
        card_dzjycs: 0, //次数交易限次
        card_jedz: 0, //金额定值(第一次)
        card_jedz1: 0, //金额定值(第二次)
        card_jedz2: 0, //金额定值(第三次)
        card_jyxe: 0, //交易限额(元)
        card_jyxc: 0, //餐交易总限次
        id: 1,
        label: "餐（时段）2",
      },
      {
        card_is_trade: 1, //是否允许交易
        card_dzcs: 0, //次数定值
        card_is_free: 0, //免费使用次数
        card_dzjycs: 0, //次数交易限次
        card_jedz: 0, //金额定值(第一次)
        card_jedz1: 0, //金额定值(第二次)
        card_jedz2: 0, //金额定值(第三次)
        card_jyxe: 0, //交易限额(元)
        card_jyxc: 0, //餐交易总限次
        id: 2,
        label: "餐（时段）3",
      },
      {
        card_is_trade: 1, //是否允许交易
        card_dzcs: 0, //次数定值
        card_is_free: 0, //免费使用次数
        card_dzjycs: 0, //次数交易限次
        card_jedz: 0, //金额定值(第一次)
        card_jedz1: 0, //金额定值(第二次)
        card_jedz2: 0, //金额定值(第三次)
        card_jyxe: 0, //交易限额(元)
        card_jyxc: 0, //餐交易总限次
        id: 3,
        label: "餐（时段）4",
      },
      {
        card_is_trade: 1, //是否允许交易
        card_dzcs: 0, //次数定值
        card_is_free: 0, //免费使用次数
        card_dzjycs: 0, //次数交易限次
        card_jedz: 0, //金额定值(第一次)
        card_jedz1: 0, //金额定值(第二次)
        card_jedz2: 0, //金额定值(第三次)
        card_jyxe: 0, //交易限额(元)
        card_jyxc: 0, //餐交易总限次
        id: 4,
        label: "餐（时段）5",
      },
    ],
  };
};

const option = [
  {
    value: 1,
    label: "是",
  },
  {
    value: 0,
    label: "否",
  },
]

export default {
  components: {
    ElDivider,
    ElButton,
    ElDialog,
    ElForm,
    ElFormItem,
    ElSelect,
    ElOption,
    ElInput,
    ElInputNumber,
    ElTabs,
    ElTabPane,
  },
  props: {
    cardTypeList: {
      types: Array,
      default: [],
    },
  },
  setup() {
    const form = ref(null);
    const store = useStore();
    const state = reactive({
      isEdit: false,
      form: formFnc(),

      rules: {
        card_num: [
          {
            required: true,
            message: "请选择卡类",
            trigger: "change",
          },
        ],
      },
    });

    const isCardAuthEdit = computed(() => {
      return store.state.deviceParameters[store.state.app.activeTab].isCardAuthEdit;
    });

    watch(
      () => store.state.deviceParameters[store.state.app.activeTab].isCardAuthEdit.value,
      (val) => {
        if (val) {
          state.form = JSON.parse(JSON.stringify(val.row));
        } else {
          state.form = formFnc()
        }
      }
    );

    const beforeClose = () => {
      store.commit("deviceParameters/updateState", {
        key: store.state.app.activeTab,
        childKey: "isCardAuthEdit",
        payload: {
          title: "",
          value: "",
          isShow: false,
        },
      });
      nextTick(() => {
        form.value.clearValidate()
      })
    };
    const submitForm = () => {
      form.value.validate((valid) => {
        if (valid) {
          let scope = store.state.deviceParameters[store.state.app.activeTab].isCardAuthEdit.value
          if (scope.row) {
            let data = JSON.parse(
              JSON.stringify(store.state.deviceParameters[store.state.app.activeTab].cardAuthList)
            );
            data[scope.$index] = JSON.parse(
              JSON.stringify(state.form)
            );
            store.commit("deviceParameters/updateState", {
              key: store.state.app.activeTab,
              childKey: "cardAuthList",
              payload: data,
            });
          } else {
            let data = JSON.parse(
              JSON.stringify(store.state.deviceParameters[store.state.app.activeTab].cardAuthList)
            );
            data.push(JSON.parse(JSON.stringify(state.form)));
            store.commit("deviceParameters/updateState", {
              key: store.state.app.activeTab,
              childKey: "cardAuthList",
              payload: data,
            });
          }
          beforeClose()
          state.form = formFnc()
        } else {
          return false;
        }
      });
    };
    return {
      option,
      form,
      state,
      isCardAuthEdit,
      beforeClose,
      submitForm,
    };
  },
};
</script>
<style lang="scss" scoped>
.box-dialog {
  padding: 10px 0;
  .el-table__row {
    height: 30px !important;
  }
  .account-tips {
    text-align: center;
    margin-bottom: 10px;
    color: #f00;
  }
  .el-input-number--mini {
    width: 178px;
  }
}
.table-box {
  padding: 10px;
}
.submit-btn {
  text-align: center;
  margin: 10px auto;
}
.el-input__inner {
  border: 0;
}
.el-divider--horizontal {
  margin: 0 0 20px 0;
}

:deep(.el-dialog__header) {
  border-bottom: 1px solid #efefef !important;
}

:deep(.el-overlay) {
  position: absolute;
  background-color: rgba(0, 0, 0, 0.3);
}

:deep(.el-dialog__footer) {
  text-align: center;
}
:deep(.el-form-item__content) {
  width: 200px;
  .el-input-number {
    width: 100%;
  }
  .el-select {
    width: 100%;
  }
}
</style>