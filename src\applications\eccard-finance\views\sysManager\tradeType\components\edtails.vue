<template>
  <el-dialog
    :model-value="dialogVisible"
    title="统一收费类型详情"
    width="30%"
    :before-close="handleClose"
  >
    <el-form inline style="margin-top: 20px" size="mini">
      <el-form-item label="统一收费类型名称：">
        <el-input :model-value="state.form.costName"></el-input>
      </el-form-item>
    </el-form>
    <div>
      <span>是否启用：</span>
      <el-switch v-model="state.form.useStatus" active-value="ENABLE_TRUE" ENABLE_TRUE="ENABLE_FALSE" class="mb-2" inline-prompt active-text="是"> </el-switch>
    </div>
    <el-form inline style="margin-top: 20px" size="mini">
      <el-form-item label="排序：">
        <el-input-number v-model="state.form.num" :min="1" @change="handleChange" />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button type="primary" @click="handleClose" size="mini"
          >编辑</el-button
        >
        <el-button @click="handleClose" size="mini">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import {
  ElDialog,
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElSwitch,
  ElInputNumber,
} from "element-plus";
import { reactive } from "@vue/reactivity";
import { watch } from '@vue/runtime-core';
export default {
  components: {
    ElDialog,
    ElButton,
    ElForm,
    ElFormItem,
    ElInput,
    ElSwitch,
    ElInputNumber,
  },
  props: {
    dialogVisible: {
      types: Boolean,
      default: false,
    },
    dialogData:{
      type:[String,Object],
      default:""
    }
  },
  setup(props, contex) {
    const state = reactive({
      value:"",
      num: "1",
      form:{},
    });

   watch(()=>props.dialogData,val=>{
     if(val){
       state.form=val
     }else{
       state.form={}
     }
   })

    const handleClose = () => {
      contex.emit("close", false);
    };
    const handleChange = (val) => {
      console.log(val);
    };
    return {
      state,
      handleClose,
      handleChange,
    };
  },
};
</script>

<style lang="scss" scoped>
</style>