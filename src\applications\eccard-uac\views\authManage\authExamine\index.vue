<template>
  <div class="padding-box">
    <kade-table-filter @search="handleSearch" @reset="handleReset">
      <el-form inline label-width="100px" size="mini">
        <el-form-item label="组织机构">
          <kade-dept-select-tree style="width: 100%" :value="state.form.deptPath" valueKey="deptPath" :multiple="false"
            @valueChange="(val) => (state.form.deptPath = val.deptPath)" />
        </el-form-item>
        <el-form-item label="人员编号">
          <el-input clearable v-model="state.form.userCode" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="人员姓名">
          <el-input clearable v-model="state.form.userName" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="审核状态">
          <el-select clearable v-model="state.form.auditStatus">
            <el-option v-for="(item, index) in auditStatusList" :key="index" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="权限审核列表">
      <el-table border height="55vh" v-loading="state.loading" :data="state.dataList">
        <el-table-column show-overflow-tooltip prop="userCode" label="人员编号" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="userName" label="人员姓名" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="roleName" label="身份类别" align="center">
          <template #default="scope">
            {{ dictionaryFilter(scope.row.roleName) }}
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip prop="deptName" label="组织机构" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="beginTime" label="开始时间" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="endTime" label="结束时间" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="createTime" label="申请时间" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="auditStatus" label="审核状态" align="center">
          <template #default="scope">
          <div :style="{color:colorFnc(scope.row.auditStatus)}">{{ dictionaryFilter(scope.row.auditStatus) }}</div>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center">
          <template #default="scope">
            <el-button type="text" @click="handleEdit(scope.row,'details')" size="mini">详情</el-button>
            <el-button type="text"  v-if="scope.row.auditStatus== 'UNDER_REVIEW'" @click="handleEdit(scope.row,'edit')" size="mini">审核</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination v-model:currentPage="state.form.currentPage" v-model:page-size="state.form.pageSize"
          :page-sizes="[10, 20, 30, 40]" background layout="total, sizes, prev, pager, next, jumper"
          :total="state.total" @size-change="handleSizeChange" @current-change="handleCurrentChange">
        </el-pagination>
      </div>
    </kade-table-wrap>
    <kade-apply-edit-dialog v-model="state.isEdit" :dialogType="state.dialogType" :rowData="state.rowData" @update:modelValue="close" />
  </div>
</template>
<script>
import { onMounted, reactive } from "vue"
import { ElForm, ElFormItem, ElSelect, ElOption, ElInput, ElTable, ElTableColumn, ElPagination, ElButton } from "element-plus"
import edit from "./components/edit.vue"
import { getAuthCheckList,} from '@/applications/eccard-uac/api.js'
import deptSelectTree from "@/components/tree/deptSelectTree.vue";
const colorFnc =(val)=> {
  if(val=='AUDIT_PASSED'){
    return '#02D200'
  }else if(val=='AUDIT_FAIL'){
    return '#FF3F3F'
  }else{
    return '#3399FF'
  }
}
export default {
  components: {
    ElForm, ElFormItem, ElSelect, ElOption, ElInput, ElTable, ElTableColumn, ElPagination, ElButton,
    "kade-apply-edit-dialog":edit,
    "kade-dept-select-tree": deptSelectTree,
  },
  setup() {
    const state = reactive({
      loading: false,
      isEdit:false,
      form: {
        currentPage: 1,
        pageSize: 10
      },
      dataList: [],
      total: 0,
      rowData:'',
      dialogType:''
    })
    const handleEdit=(row,type)=>{
      state.rowData=row
      state.dialogType=type
      state.isEdit = true
    }
    const close=()=>{
      state.isEdit=false
      getList()
    }
    const handleCurrentChange = val => {
      state.form.currentPage = val
      getList()
    }
    const handleSizeChange = val => {
      state.form.currentPage = 1
      state.form.pageSize = val
      getList()
    }
    const handleSearch = ()=>{
      getList()
    }
    const handleReset=()=>{
      state.form={
        currentPage:1,
        pageSize:10
      }
      getList()
    }
    const getList=async()=>{
      state.loading=true
      try{
        let { data:{list,total}} = await getAuthCheckList(state.form)
        state.dataList=list
        state.total=total
        state.loading=false
      }catch{
        state.loading=false
      }
    }
    onMounted(()=>{
      getList()
    })
    return {
      state,
      colorFnc,
      close,
      handleEdit,
      handleCurrentChange,
      handleSizeChange,
      handleReset,
      handleSearch
    }
  }
}
</script>
<style scoped lang="scss">
:deep(.el-dialog__footer) {
  text-align: center;
}
:deep(.el-dialog){
  border-radius: 8px;
}
</style>
