<template>
  <div class="not-returned-msg">
    <div class="not-returned-title">未归人员</div>
    <el-carousel height="100%" direction="vertical" :autoplay="true" :interval="10000" indicator-position="none">
      <el-carousel-item v-for="item in 4" :key="item" style="color:#fff">
        <div class="person-box">
          <div class="person-item" v-for="(item, index) in 12" :key="index">
            <img src="" alt="">
            <div class="msg">
              <div class="name">西瓜</div>
              <div class="group">一年级三班</div>
              <div class="room">101</div>
            </div>
          </div>
        </div>
      </el-carousel-item>
    </el-carousel>
  </div>
</template>
<script>
import { ElCarousel, ElCarouselItem } from "element-plus"
export default {
  components: {
    ElCarousel, ElCarouselItem
  },
  setup() {

  }
}
</script>
<style lang="scss" scoped>
.not-returned-msg {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .not-returned-title {
    font-weight: 700;
    font-size: 18px;
    color: #0EE4F9;
    margin: 5px 0 10px;
  }

  .person-box {
    background: #001034;

    width: 100%;
    height: 100%;
    overflow-y: auto;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;

    .person-item {
      box-sizing: border-box;
      padding: 5px;
      width: 23%;
      background: #08266f;
      display: flex;
      align-items: center;
      margin-bottom: 10px;

      img {
        width: 82px;
        height: 98px;
      }

      .msg {
        flex: 1;
        text-align: center;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;

        .name {
          font-weight: 700;
          font-size: 20px;
          color: #fff;
        }

        .group {
          font-size: 13px;
          margin: 10px 0;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
        }

        .room {
          font-weight: 600;
          font-size: 18px;
          color: #fff;

        }
      }
    }
  }

}

.el-carousel {
  width: 100%;
  height: 100%;
}
</style>