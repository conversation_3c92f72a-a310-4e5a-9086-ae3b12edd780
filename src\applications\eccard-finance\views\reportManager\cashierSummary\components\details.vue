<template>
  <el-dialog :model-value="modelValue" title="交款明细" width="1000px" :before-close="handleClose" :close-on-click-modal="false">
    <div class="table-box">
      <div class="btn">
        <!-- <el-button size="mini" @click="handleExport" icon="el-icon-daoru" type="primary">导出</el-button> -->
        <el-button size="mini" @click="handlePrint" class="btn-purple" icon="el-icon-printer">打印</el-button>
      </div>
      <div class="title">出纳交款明细</div>
      <div class="mode-content">
        <div class="income-left">
          <div class="mode-title">收入</div>
          <div class="table-head">
            <div class="head-item">交易钱包</div>
            <div class="head-item">交易类型</div>
            <div class="head-item">交易笔数</div>
            <div class="head-item">交易金额</div>
          </div>
          <div :style="(data.cashierSummaryDetailIncomeListRes.length<data.cashierSummaryDetailExpendListRes.length)&&'border-bottom: 1px solid #ebeef5;'" class="table-tr" v-for="(item,index) in data.cashierSummaryDetailIncomeListRes" :key="index">
            <div class="prop">{{item.walletName}}</div>
            <div class="prop">{{item.costTypeName}}</div>
            <div class="prop">{{item.count}}</div>
            <div class="prop">{{item.amount.toFixed(2)}}</div>
          </div>
        </div>
        <div class="expend-right">
          <div class="mode-title">支出</div>
          <div class="table-head">
            <div class="head-item">交易钱包</div>
            <div class="head-item">交易类型</div>
            <div class="head-item">交易笔数</div>
            <div class="head-item">交易金额</div>
          </div>
          <div :style="(data.cashierSummaryDetailIncomeListRes.length>data.cashierSummaryDetailExpendListRes.length)&&'border-bottom: 1px solid #ebeef5;'" class="table-tr" v-for="(item,index) in data.cashierSummaryDetailExpendListRes" :key="index">
            <div class="prop">{{item.walletName}}</div>
            <div class="prop">{{item.costTypeName}}</div>
            <div class="prop">{{item.count}}</div>
            <div class="prop">{{item.amount.toFixed(2)}}</div>
          </div>
        </div>
      </div>
      <div class="mode-summary">
        <div class="summary-title">金额汇总</div>
        <div class="summary-content">
          <div class="summary-item" v-for="(item,index) in summaryList" :key="index">
            <div class="prop">{{item.label}}</div>
            <div class="prop">{{data[item.prop].toFixed(2)}}</div>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button size="mini" @click="handleClose">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script>
import { onMounted, reactive } from "vue";
import { ElDialog, ElButton } from "element-plus";
import { timeStr } from "@/utils/date.js";
import { downloadXlsx, print } from "@/utils";
import { cashierSummaryDetailExport, cashierSummaryDetailPrint } from "@/applications/eccard-finance/api";
const summaryList = [
  { label: "本期收入", prop: "incomeAmount" },
  { label: "本期支出", prop: "expendAmount" },
  { label: "本期应缴", prop: "payableAmount" },
]
export default {
  components: {
    ElDialog,
    ElButton,
  },
  emits: ["update:modelValue"],
  props: {
    modelValue: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Object,
      default: null,
    },
    params: {
      type: Object,
      default: null,
    }
  },
  setup(props, context) {
    const state = reactive({
      loading: false,
    });
    const handleExport = async () => {
      state.loading = true
      try {
        let res = await cashierSummaryDetailExport(props.params)
        downloadXlsx(res, "出纳交款明细.xlsx")
        state.loading = false
      }
      catch {
        state.loading = false
      }
    }
    const handlePrint = async () => {
      state.loading = true
      try {
        let { data, code } = await cashierSummaryDetailPrint(props.params)
        if (code === 0) {
          print(data)

        }
        state.loading = false
      }
      catch {
        state.loading = false
      }
    }
    const handleClose = () => {
      context.emit("update:modelValue", false);
    };
    onMounted(() => { });
    return {
      timeStr,
      summaryList,
      state,
      handleExport,
      handlePrint,
      handleClose,
    };
  },
};
</script>
<style lang="scss" scoped>
.table-box {
  box-sizing: border-box;
  padding: 20px;
  position: relative;
  .btn {
    position: absolute;
    right: 20px;
    top: 15px;
  }
  .title {
    text-align: center;
    font: bold 16px arial;
    margin-bottom: 10px;
  }
  .mode-content {
    display: flex;
    text-align: center;
    border: 1px solid #ebeef5;
    .income-left {
      width: 50%;
      border-right: 1px solid #ebeef5;
    }
    .expend-right {
      width: 50%;
    }
    .mode-title {
      line-height: 40px;
      height: 40px;
      border-bottom: 1px solid #ebeef5;
      font-weight: bold;
    }
    .table-head {
      display: flex;
      align-items: center;
      border-bottom: 1px solid #ebeef5;
      font-weight: bold;
      .head-item {
        width: 25%;
        min-width: 100px;
        line-height: 40px;
        height: 40px;
        border-right: 1px solid #ebeef5;
        &:last-child {
          border-right: 0;
        }
      }
    }
    .table-tr {
      display: flex;
      align-items: center;
      border-bottom: 1px solid #ebeef5;
      &:last-child {
        border-bottom: 0;
      }
      .prop {
        width: 25%;
        min-width: 100px;
        line-height: 40px;
        height: 40px;
        border-right: 1px solid #ebeef5;
        &:last-child {
          border-right: 0;
        }
      }
    }
  }
  .mode-summary {
    text-align: center;
    .summary-title {
      font-weight: bold;
      line-height: 40px;
      height: 40px;
      border: 1px solid #ebeef5;
      border-top: 0;
    }
    .summary-content {
      border-right: 1px solid #ebeef5;
      border-left: 1px solid #ebeef5;
      .summary-item {
        border-bottom: 1px solid #ebeef5;

        display: flex;
        .prop {
          width: 50%;
          line-height: 40px;
          height: 40px;
          border-right: 1px solid #ebeef5;
          &:last-child {
            border-right: 0;
          }
        }
      }
    }
  }
}
</style>