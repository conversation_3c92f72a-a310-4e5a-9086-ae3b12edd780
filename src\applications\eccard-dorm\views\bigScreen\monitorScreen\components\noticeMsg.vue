<template>
  <div class="notice-msg">
    <div class="notive-title">通知公告</div>
    <el-carousel height="100%" direction="vertical" :autoplay="true" :interval="5000" indicator-position="none">
      <el-carousel-item v-for="item in 4" :key="item" style="color:#fff">
        3月6日凌晨3时，学校接到兵团新冠肺炎疫情防控指挥部发来的协查函。{{ item > 1 ?'经查，该生已接种3针疫苗，3月4日抵校后核酸检测结果为阴性。':'' }}为确保师生健康，学校第一时间启动应急处置工作，对相关人员进行研判并暂时限定活动范围，未接到通知的师生正常工作学习生活。截至目前，被推送人员及相关人员核酸检测结果均为阴性（3月6日凌晨4时采集）。{{
            item > 2 ? "近期，国内疫情多点散发，学校疫情防控工作面临比较复杂的局面。":''
        }}学校温馨提示，为保护你和他人的健康，请认真做好个人防护（戴口罩、勤洗手、保持社交距离），支持学校疫情防控工作。
      </el-carousel-item>
    </el-carousel>
  </div>
</template>
<script>
import { ElCarousel, ElCarouselItem } from "element-plus"
export default {
  components: {
    ElCarousel, ElCarouselItem
  },
  setup() {

  }
}
</script>\
<style lang="scss" scoped>
.notice-msg {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .notive-title {
    font-weight: 700;
    font-size: 18px;
    color: #0EE4F9;
    margin: 5px 0 10px;
  }
}

.el-carousel {
  width: 100%;
  height: 100%;
}
</style>