<template>
    <div class="chart-box">
        <div class="title">实时交易趋势</div>
        <div class="total-box">
            <div class="total-item">
                <div class="title-text">今日交易总额</div>
                <div class="total-num">124,543,233元</div>
            </div>
            <div class="total-item">
                <div class="title-text">每秒交易总额</div>
                <div class="total-num">234元</div>
            </div>
        </div>
        <div id="mychart" class="echartDiv"></div>
    </div>
</template>

<script>
import * as echarts from "echarts";
import { onMounted } from "vue";

export default {
    setup() {
        const echartInit = () => {
            var chartDom = document.getElementById("mychart");
            var myChart = echarts.init(chartDom);
            var option = {
                tooltip: {
                    trigger: "axis",
                },
                grid: {
                    top: "12%",
                    left: "5%",
                    right: "8%",
                    bottom: "20%",
                    containLabel: true,
                },
                xAxis: {
                    type: "category",
                    boundaryGap: false,
                    data: [
                        "10:00",
                        "11:00",
                        "12:00",
                        "13:00",
                        "14:00",
                        "15:00",
                        "16:00",
                        "17:00",
                        "18:00",
                    ],
                    axisLine: {
                        lineStyle: {
                            color: "#9498b0s",
                        },
                    },
                    axisLabel: {
                        margin: 18,
                        textStyle: {
                            fontSize:12,
                        },
                    },
                },
                yAxis: {
                    type: "value",
                    splitLine: {
                        show: true,
                        lineStyle: {
                            type: "dashed",
                        },
                    },
                    axisLabel: {
                        margin: 20,
                        textStyle: {
                            fontSize: 12,
                        },
                    },
                },
                series: [
                    {
                        symbol: "none",
                        name: "线上:",
                        data: [150, 230, 224, 218, 135, 182, 260, 240, 270],
                        type: "line",
                        color: "#1890ff",
                    },
                    {
                        color: "#2fc25b",
                        symbol: "none",
                        name: "线下:",
                        data: [180, 200, 190, 240, 200, 140, 250, 270, 210],
                        type: "line",
                    },
                ],
            };
            myChart.setOption(option);
        };
        onMounted(() => {
            echartInit();
        });
        return {
            echartInit,
        };
    },
};
</script>

<style lang="scss" scoped>
.chart-box {
    padding-right: 10px;
    .title {
        padding: 15px 20px;
        font-size: 16px;
        font-weight: 800;
        border-bottom: 1px solid #eeeeee;
    }
    .total-box {
        display: flex;
        margin-left: 20px;
        margin-top: 20px;
        .total-item {
            margin-right: 40px;
            .title-text {
                margin-bottom: 5px;
                font-size: 14px;
                color: #0000006d;
            }
            .total-num {
                font-size: 24px;
            }
        }
    }
    .echartDiv {
        height: 250px;
    }
}
</style>
