<template>
  <el-dialog :model-value="modelValue" title="退款" width="500px" :before-close="handleClose">
    <el-form size="mini" label-width="100px" ref="formRef" :model="state.form" :rules="rules">
      <el-form-item label="交易单号：">
        <el-input v-model="state.form.out_trade_no" disabled></el-input>
      </el-form-item>
      <el-form-item label="交易金额：">
        <el-input :model-value="Math.abs(rowData.tradeAmount)" disabled></el-input>
      </el-form-item>
      <el-form-item label="退款金额：" prop="refund_amount">
        <el-input v-model="state.form.refund_amount" placeholder="请输入退款金额"></el-input>
      </el-form-item>
      <el-form-item label="退款原因：">
        <el-input v-model="state.form.refundReason" type="textarea" placeholder="请输入退款原因"></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose" size="mini">关闭</el-button>
        <el-button type="primary" @click="handleConfirm()" size="mini" :loading="state.loading">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { ElDialog, ElButton, ElForm, ElFormItem, ElInput, ElMessage } from "element-plus"
import { reactive, ref } from '@vue/reactivity'
import { watch, nextTick } from '@vue/runtime-core';
import { sicnuPaymentRefund, sicnuPaymentCodeRefund } from "@/applications/eccard-finance/api";
export default {
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    rowData: {
      type: [Object],
      default: null
    }
  },
  components: {
    ElDialog,
    ElButton,
    ElForm,
    ElFormItem,
    ElInput,
  },
  setup(props, context) {
    const formRef = ref(null)
    const state = reactive({
      loading: false,
      form: {},
    });

    watch(() => props.modelValue, val => {
      if (val) {
        let { tradeNo, tradeMode, deviceNo } = props.rowData
        state.form = {
          out_trade_no: tradeNo,
          tradeMode,
          deviceNo,

        }
        nextTick(() => {
          formRef.value.clearValidate()
        })
      }
    })
    const validatePass = (rule, value, callback) => {
      if (!isNaN(Number(value))) {
        if (value.split(".").length > 1 && value.split(".")[1].length > 2) {
          callback(new Error('金额必须保留小数点后两位'));
        }
        if (Number(value) > Number(Math.abs(props.rowData.tradeAmount))) {
          callback(new Error('退款金额不能大于交易金额'));
          return
        }
      } else {
        callback(new Error('金额必须输入数字'));
        return
      }
      return callback()
    };

    const rules = reactive({
      refund_amount: [
        { required: true, message: '请输入退款金额', trigger: 'blur' },
        { required: true, validator: validatePass, trigger: 'blur' },
      ],
    })
    const handleConfirm = () => {
      formRef.value.validate(async valid => {
        if (valid) {
          let fn = props.rowData.tradeMode == 401 ? sicnuPaymentCodeRefund : sicnuPaymentRefund
          let params
          if (props.rowData.tradeMode == 401) {
            params = {
              out_trade_no: state.form.out_trade_no,
              refund_amount: state.form.refund_amount,
              refund_desc: state.form.refundReason,
              tradeMode: state.form.tradeMode,
              deviceNo: state.form.deviceNo,
            }
          } else {
            params = { ...state.form }
          }
          state.loading = true
          try {
            let { Status, Message } = await fn(params)
            if (Status === 1) {
              ElMessage.success(Message)
              context.emit("update:modelValue", true)
            }
            state.loading = false
          }
          catch {
            state.loading = false
          }

        }
      })
    }
    const handleClose = () => {
      context.emit("update:modelValue", false)
    }
    return {
      formRef,
      state,
      rules,
      handleConfirm,
      handleClose
    }
  }
}
</script>

<style lang="scss" scoped>
.el-form {
  margin-top: 20px;
}
</style>