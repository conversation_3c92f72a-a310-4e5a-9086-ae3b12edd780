<template>
  <div class="flex-box" v-if="!state.edit">
    <div class="upload-photo">
      <div style="margin-bottom:20px">照片</div>
      <el-image class="img" :src="state.detailsData.userPhoto" @error="errorImg" fit="contain">
        <template #error>
          <div class="image-slot">
            <i class="el-icon-picture"></i>
          </div>
        </template>
      </el-image>
    </div>
    <el-form label-width="120px" style="flex: 1" size="small">
      <el-row>
        <el-col :span="6">
          <el-form-item label="人员编号：">
            <el-input :modelValue="state.detailsData.userCode" readonly></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="人员姓名：">
            <el-input :modelValue="state.detailsData.userName" readonly></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="性别：">
            <el-input :modelValue="dictionaryFilter(state.detailsData.userSex)" readonly></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="民族：">
            <el-input
              :modelValue="state.detailsData.userNation&&nationList&&nationList.filter(item=>item.code==state.detailsData.userNation)[0].name"
              readonly></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="籍贯：">
            <el-input :modelValue="state.detailsData.userNativePlace" readonly></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="出生地：">
            <el-input
              :modelValue="state.detailsData.userBirthPlace&&state.provinceList&&state.provinceList.filter(item=>item.code==state.detailsData.userBirthPlace)[0].name"
              readonly></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="出生日期：">
            <el-input :modelValue="state.detailsData.userBirthday" readonly></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="政治面貌：">
            <el-input :modelValue="dictionaryFilter(state.detailsData.userPoliticalFace)" readonly></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="证件类型：">
            <el-input :modelValue="dictionaryFilter(state.detailsData.userIdType)" readonly></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="证件号码：">
            <el-input :modelValue="state.detailsData.userIdNo" readonly></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="手机号码：">
            <el-input :modelValue="state.detailsData.userTel" readonly></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="户籍地址：" class="url-input">
            <el-input :modelValue="state.addressData.householdRegisterProvinceStr" readonly></el-input>
            <el-input :modelValue="state.addressData.householdRegisterCityStr" readonly></el-input>
            <el-input :modelValue="state.addressData.householdRegisterAreaStr" readonly></el-input>
            <el-input :modelValue="state.addressData.householdRegisterAddress" readonly></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="组织机构：">
            <el-input :modelValue="state.detailsData.deptName" readonly></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="岗位：">
            <el-input
              :modelValue="state.detailsData.userPostId&&state.stationList&&state.stationList.filter(item=>item.id==state.detailsData.userPostId)[0].postName"
              readonly></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="身份类别：">
            <el-input :modelValue="state.detailsData.roleName" readonly></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="是否住校：">
            <el-input :modelValue="dictionaryFilter(state.detailsData.userIsBoarders)" readonly></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="人员状态：">
            <el-input :modelValue="dictionaryFilter(state.detailsData.userState)" readonly></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="入校时间：">
            <el-input :modelValue="state.detailsData.userSchoolTime" readonly></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="离校时间：">
            <el-input :modelValue="state.detailsData.userLeaveTime" readonly></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="交易密码：">
            <el-input :modelValue="state.detailsData.userTransPwd" readonly></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="家庭住址：" class="url-input">
            <el-input :modelValue="state.addressData.familyProvinceStr" readonly></el-input>
            <el-input :modelValue="state.addressData.familyCityStr" readonly></el-input>
            <el-input :modelValue="state.addressData.familyAreaStr" readonly></el-input>
            <el-input :modelValue="state.addressData.familyAddress" readonly></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="备注：">
            <el-input :modelValue="state.detailsData.userRemark" type="textarea" readonly></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="是否启用：">
            <el-switch :modelValue="state.detailsData.userAccountState" active-value="ENABLE_TRUE"
              inactive-value="ENABLE_FALSE" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="24" style="text-align:center;marign-bottom:20px">
          <el-button @click="handleEdit" size="mini" type="primary">编辑</el-button>
        </el-col>
      </el-row>
    </el-form>
  </div>
  <div class="flex-box" v-else>
    <div class="upload-photo">
      <div style="margin-bottom:20px">照片</div>
      <kade-single-image-upload :action="uploadApplyLogo" icon="iconuser" v-model="state.form.userPhoto" />
      <el-alert style="margin-top: 10px" center show-icon type="warning" title="请上传一寸证件照片" :closable="false" />
    </div>
    <el-form ref="formRef" :model="state.form" :rules="rules" label-width="120px" style="flex:1" size="small">
      <el-row>
        <el-col :span="6">
          <el-form-item label="人员编号：" prop="userCode">
            <el-input v-model="state.form.userCode" placeholder="请输入" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="人员姓名：" prop="userName">
            <el-input v-model="state.form.userName" placeholder="请输入" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="性别：" prop="userSex">
            <el-select v-model="state.form.userSex" clearable>
              <el-option v-for="(item,index) in sexList" :key="index" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="民族：">
            <el-select v-model="state.form.userNation" clearable>
              <el-option v-for="(item,index) in nationList" :key="index" :label="item.name" :value="item.code">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="籍贯：">
            <el-input v-model="state.form.userNativePlace" placeholder="请输入" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="出生地：">
            <el-select v-model="state.form.userBirthPlace" clearable>
              <el-option v-for="(item,index) in state.provinceList" :key="index" :label="item.name" :value="item.code">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="出生日期：">
            <el-date-picker v-model="state.form.userBirthday" type="date" placeholder="请选择" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="政治面貌：">
            <el-select v-model="state.form.userPoliticalFace" clearable>
              <el-option v-for="(item,index) in faceList" :key="index" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="证件类型：">
            <el-select v-model="state.form.userIdType" clearable>
              <el-option v-for="(item,index) in cardTypes" :key="index" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="证件号码：">
            <el-input v-model="state.form.userIdNo" placeholder="请输入" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="手机号码：">
            <el-input v-model="state.form.userTel" placeholder="请输入" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="户籍地址：" class="url-input">
            <el-select v-model="state.addressForm.householdRegisterProvince" clearable
              @change="householdProvinceChange">
              <el-option v-for="(item,index) in state.provinceList" :key="index" :label="item.name" :value="item.code">
              </el-option>
            </el-select>
            <el-select v-model="state.addressForm.householdRegisterCity" clearable @change="householdCityChange">
              <el-option v-for="(item,index) in state.householdCityList" :key="index" :label="item.name"
                :value="item.code"></el-option>
            </el-select>
            <el-select v-model="state.addressForm.householdRegisterArea" clearable>
              <el-option v-for="(item,index) in state.householdAreaList" :key="index" :label="item.name"
                :value="item.code"></el-option>
            </el-select>
            <el-input v-model="state.addressForm.householdRegisterAddress" placeholder="请输入" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="组织机构：" prop="userDept">
            <kade-dept-select-tree style="width: 100%" :value="state.form.userDept" valueKey="id" :multiple="false"
              @valueChange="valueChange()" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="岗位：">
            <el-select v-model="state.form.userPostId" clearable>
              <el-option v-for="(item,index) in state.stationList" :key="index" :label="item.postName" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="身份类别：" prop="userRole">
            <el-select v-model="state.form.userRole" clearable>
              <el-option v-for="(item,index) in roleList" :key="index" :label="item.roleName" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="是否住校：">
            <el-select v-model="state.form.userIsBoarders" clearable disabled>
              <el-option v-for="(item,index) in isSchoolList" :key="index" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="人员状态：" prop="userState">
            <el-select v-model="state.form.userState">
              <el-option v-for="(item,index) in statusList" :key="index" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="入校时间：">
            <el-date-picker v-model="state.form.userSchoolTime" type="date" placeholder="请选择" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="离校时间：">
            <el-date-picker v-model="state.form.userLeaveTime" type="date" placeholder="请选择" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="交易密码：">
            <el-input :minlength="6" :maxlength="6" v-model="state.form.userTransPwd" placeholder="请输入"
              onkeyup="this.value=this.value.replace(/\D/g,'')" onafterpaste="this.value=this.value.replace(/\D/g,'')">
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="家庭住址：" class="url-input">
            <el-select v-model="state.addressForm.familyProvince" @change="familyProvinceChange" clearable>
              <el-option v-for="(item,index) in state.provinceList" :key="index" :label="item.name" :value="item.code">
              </el-option>
            </el-select>
            <el-select v-model="state.addressForm.familyCity" @change="familyCityChange" clearable>
              <el-option v-for="(item,index) in state.familyCityList" :key="index" :label="item.name"
                :value="item.code"></el-option>
            </el-select>
            <el-select v-model="state.addressForm.familyArea" clearable>
              <el-option v-for="(item,index) in state.familyAreaList" :key="index" :label="item.name"
                :value="item.code"></el-option>
            </el-select>
            <el-input v-model="state.addressForm.familyAddress" placeholder="请输入" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="备注：">
            <el-input v-model="state.form.userRemark" type="textarea" placeholder="请输入" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="是否启用：">
            <el-switch v-model="state.form.userAccountState" active-value="ENABLE_TRUE" inactive-value="ENABLE_FALSE" />
          </el-form-item>
        </el-col>
        <el-col :span="24" style="text-align:center;marign-bottom:20px">
          <el-button @click="state.edit=false" size="mini" :loading="state.loading">取消</el-button>
          <el-button @click="submit" size="mini" type="primary">保存</el-button>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>
<script>
import {
  ElRow,
  ElCol,
  ElImage,
  ElForm,
  ElFormItem,
  ElInput,
  ElSwitch,
  ElButton,
  ElSelect,
  ElOption,
  ElDatePicker,
  ElAlert,
  ElMessage,
} from "element-plus";
import { computed, onMounted, reactive, watch, ref } from "vue";
import { useStore } from "vuex";
import { useDict } from "@/hooks/useDict.js";
import { timeStr, dateStr } from "@/utils/date.js";
// import defaultImg from "@/assets/default-user.svg";
import {
  getBaseUserInfo,
  addUser,
  updateUserInfo,
  getUserAddressInfo,
  stationList,
  getAddressList,
  addAddress,
  editAddress,
} from "@/applications/eccard-basic-data/api";
import { uploadApplyLogo } from "@/applications/unified_portal/api";
import deptSelectTree from "@/components/tree/deptSelectTree.vue";
import SingleImageUpload from "@/components/singleImageUpload";
export default {
  components: {
    ElRow,
    ElCol,
    ElImage,
    ElForm,
    ElFormItem,
    ElInput,
    ElSwitch,
    ElButton,
    ElSelect,
    ElOption,
    ElDatePicker,
    ElAlert,
    "kade-dept-select-tree": deptSelectTree,
    "kade-single-image-upload": SingleImageUpload,
  },
  setup() {
    const statusList = useDict("BASE_USER_STATE");
    const sexList = useDict("SYS_SEX");
    const faceList = useDict("USER_POLITICAL_OUTLOOK");
    const isSchoolList = useDict("SYS_BOOL_STRING");
    const cardTypes = useDict("BASE_ID_TYPE");
    const formRef = ref(null);
    const store = useStore();
    const state = reactive({
      loading: false,
      edit: false,
      detailsData: {},
      addressData: {},
      form: {},
      addressForm: {},
    });
    const dataDetails = computed(() => {
      return store.state.userInfo.rowData;
    });
    const nationList = computed(() => {
      return store.state.userInfo.nationList;
    });
    const roleList = computed(() => {
      return store.state.userInfo.roleList;
    });
    watch(
      () => store.state.userInfo.isDetails,
      (val) => {
        if (store.state.userInfo.rowData.id && val) {
          getDetails();
          getUserAddress();
        } else {
          state.edit = false;
        }
      }
    );
    const rules = {
      userCode: [
        {
          required: true,
          message: "请输入用户编号",
        },
        {
          pattern: /^[0-9a-zA-Z]+$/,
          message: "请输入字母+数字",
        },
        {
          max: 20,
          message: "用户编号长度不能超过20字符",
        },
      ],
      userName: [
        {
          required: true,
          message: "请输入用户姓名",
        },
        {
          max: 20,
          message: "用户姓名长度不能超过20字符",
        },
        {
          pattern: /^\S.*\S$|(^\S{0,1}\S$)/,
          message: "用户姓名首尾不能包含空格",
        },
      ],
      userSex: [
        {
          required: true,
          message: "请选择性别",
          trigger: "change",
        },
      ],
      userDept: [
        {
          required: true,
          message: "请选择组织机构",
          trigger: "change",
        },
      ],
      userRole: [
        {
          required: true,
          message: "请选择身份类别",
          trigger: "change",
        },
      ],
      userState: [
        {
          required: true,
          message: "请选择人员状态",
          trigger: "change",
        },
      ],
    };
    const getDetails = async () => {
      let params = {
        userCode: store.state.userInfo.rowData.userCode,
        userId: store.state.userInfo.rowData.id,
      };
      let { data } = await getBaseUserInfo(params);
      state.detailsData = data;
      state.detailsData.userRole = Number(state.detailsData.userRole);
      state.detailsData.userBirthPlace = Number(state.detailsData.userBirthPlace);
    };
    const getUserAddress = async () => {
      let { data } = await getUserAddressInfo(store.state.userInfo.rowData.id);

      if (data) {
        state.addressData = data;
        data.householdRegisterProvince &&
          getCityList(data.householdRegisterProvince, "house");
        data.householdRegisterCity &&
          getAreaList(data.householdRegisterCity, "house");
        data.familyProvince && getCityList(data.familyProvince, "family");
        data.familyCity && getAreaList(data.familyCity, "family");
      } else {
        state.addressData = {};
      }
    };

    const getStationList = async () => {
      let { data } = await stationList();
      state.stationList = data;
    };
    const getProvinceList = async () => {
      let { data } = await getAddressList({ level: 1 });
      state.provinceList = data;
    };

    const getCityList = async (val, type) => {
      if (!val) return;
      let { data } = await getAddressList({ parentCode: val, level: 2 });
      if (type === "house") {
        state.householdCityList = data;
      } else {
        state.familyCityList = data;
      }
    };
    const getAreaList = async (val, type) => {
      if (!val) return;
      let { data } = await getAddressList({ parentCode: val, level: 3 });
      if (type === "house") {
        state.householdAreaList = data;
      } else {
        state.familyAreaList = data;
      }
    };

    const householdProvinceChange = (val) => {
      getCityList(val, "house");
      state.householdCityList = [];
      state.householdAreaList = [];
      state.addressForm.householdRegisterCity = "";
      state.addressForm.householdRegisterArea = "";
    };
    const householdCityChange = (val) => {
      getAreaList(val, "house");
      state.householdAreaList = [];
      state.addressForm.householdRegisterArea = "";
    };
    const familyProvinceChange = (val) => {
      getCityList(val, "family");
      state.familyCityList = [];
      state.familyAreaList = [];
      state.addressForm.familyCity = "";
      state.addressForm.familyArea = "";
    };
    const familyCityChange = (val) => {
      getAreaList(val, "family");
      state.familyAreaList = [];
      state.addressForm.familyArea = "";
    };

    const errorImg = () => {
      // state.detailsData.userPhoto = defaultImg;
    };
    const handleEdit = () => {
      state.form = { ...state.detailsData };
      state.addressForm = { ...state.addressData };
      state.edit = true;
    };
    const submit = () => {
      formRef.value.validate(async (valid) => {
        if (valid) {
          let params = { ...state.form };
          let fn = store.state.userInfo.rowData.id ? updateUserInfo : addUser;
          let fn1 = state.addressForm.id ? editAddress : addAddress;
          if (store.state.userInfo.rowData.id) {
            params.userId = store.state.userInfo.rowData.id;
          }
          params.userBirthday = params.userBirthday
            ? dateStr(params.userBirthday)
            : "";
          params.userSchoolTime = params.userSchoolTime
            ? timeStr(params.userSchoolTime)
            : "";
          params.userLeaveTime = params.userLeaveTime
            ? timeStr(params.userLeaveTime)
            : "";
          state.loading = true
          let { code, message } = await fn(params);
          if (code === 0) {
            let params1 = { ...state.addressForm };
            params1.userId = store.state.userInfo.rowData.id;
            for (let key in params1) {
              if (!params1[key]) {
                delete params1[key]
              }
            }
            let res = await fn1(params1);
            if (res.code === 0) {
              ElMessage.success(message);
              await getDetails();
              await getUserAddress();
              state.edit = false;
            }
          }
          state.loading = false
        } else {
          return false;
        }
      });
    };
    onMounted(() => {
      getDetails();
      getUserAddress();
      getProvinceList();
      getStationList();
    });
    return {
      uploadApplyLogo,
      formRef,
      state,
      rules,
      nationList,
      roleList,
      statusList,
      sexList,
      faceList,
      isSchoolList,
      cardTypes,
      dataDetails,
      errorImg,
      handleEdit,
      householdProvinceChange,
      householdCityChange,
      familyProvinceChange,
      familyCityChange,
      submit,
    };
  },
};
</script>
<style lang="scss" scoped>
.flex-box {
  margin-top: 20px;
  display: flex;

  .upload-photo {
    width: 160px;
    margin-right: 20px;

    .img {
      width: 100%;
      height: 224px;
      background: #fff;
    }
  }
}

.url-input {
  :deep(.el-form-item__content) {
    display: flex;

    .el-input {
      width: 130px !important;
      margin-right: 5px;

      .el-input__inner {
        width: 130px !important;
      }

      &:nth-child(n + 4) {
        margin-right: 5px;
        width: 100%;
      }

      &:last-child {
        width: 100% !important;

        .el-input__inner {
          width: 100% !important;
        }
      }
    }
  }
}

:deep(.el-date-editor.el-input) {
  width: 200px;
}

.url-input {
  :deep(.el-form-item__content) {
    display: flex;

    .el-select {
      margin-right: 5px;
    }

    .el-input {
      width: 100%;

      .el-input__inner {
        width: 100%;
      }
    }
  }
}

:deep(.single-image-uploader) {
  width: 160px;
  height: 224px;

  .el-upload {
    width: 160px;
    height: 224px;
  }

  .images {
    width: 160px;
    height: 224px;
  }

  .image-slot {
    width: 160px;
    height: 224px;
  }
}

.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: #f5f5f5;
  color: #8a8a8a;
  font-size: 30px;
}

.image-slot .el-icon {
  font-size: 30px;
}
</style>
