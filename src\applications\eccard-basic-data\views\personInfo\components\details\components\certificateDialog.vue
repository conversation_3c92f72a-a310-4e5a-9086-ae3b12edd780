<template>
  <el-dialog :model-value="isShow" :title="data.id?'编辑资格证书':'新增资格证书'" width="500px" :before-close="beforeClose"  :append-to-body="true">
    <div style="padding-top: 20px">
      <el-form size="mini" label-width="120px" ref="formDom" :rules="state.rules" :model="state.form">
        <el-form-item label="资格证书名称:" prop="certificateName">
          <el-input placeholder="请输入" v-model="state.form.certificateName" :maxlength="20"></el-input>
        </el-form-item>
        <el-form-item label="获取日期:" prop="gainTime">
          <el-date-picker v-model="state.form.gainTime" type="month" placeholder="请选择年月" />
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="beforeClose" size="mini">取消</el-button>
        <el-button type="primary" @click="submit()" size="mini">提交</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script>
import {
  ElDialog,
  ElForm,
  ElFormItem,
  ElInput,
  ElDatePicker,
  ElButton,
  ElMessage
} from "element-plus";
import { reactive, ref } from "@vue/reactivity";
import { nextTick ,watch} from "@vue/runtime-core";
import { useStore } from "vuex";
import { timeStr } from "@/utils/date.js";
import { addCertificate, editCertificate } from "@/applications/eccard-basic-data/api";
export default {
  components: {
    ElDialog,
    ElForm,
    ElFormItem,
    ElInput,
    ElDatePicker,
    ElButton,
  },
  props: {
    isShow: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Object,
      default: null,
    },
  },
  setup(props, context) {
    const store = useStore();
    const formDom = ref(null);
    const state = reactive({
      title: "",
      form: {},
      rules: {
        certificateName: [
          {
            required: true,
            message: "请输入证书名称",
            trigger: "blur",
          },
        ],
        gainTime: [
          {
            required: true,
            message: "请选择获取日期",
            trigger: "change",
          },
        ],
      },
    });
    watch(
      () => props.isShow,
      (val) => {
        if (val) {
          state.form = props.data.id ? { ...props.data } : {};
        }
        nextTick(() => {
          formDom.value.clearValidate();
        });
      }
    );
    const submit = () => {
      formDom.value.validate(async (valid) => {
        if (valid) {
          let fn = props.data.id ? editCertificate : addCertificate;
          let params={...state.form}
          params.gainTime=timeStr(params.gainTime)
          params.userId=store.state.userInfo.rowData.id
          let { code, message } = await fn(params);
          if (code === 0) {
            ElMessage.success(message);
            context.emit("close", true);
          }
        } else {
          return false;
        }
      });
    };

    const beforeClose = () => {
      context.emit("close", false);
      nextTick(() => {
        formDom.value.clearValidate();
      });
    };
    return {
      formDom,
      state,
      submit,
      beforeClose,
    };
  },
};
</script>
<style lang="scss" scoped>
:deep(.el-form-item__content) {
  .el-select {
    width: 100% !important;
  }
  .el-input__inner {
    width: 100% !important;
  }
  .el-date-editor.el-input {
    width: 100%;
  }
}
</style>