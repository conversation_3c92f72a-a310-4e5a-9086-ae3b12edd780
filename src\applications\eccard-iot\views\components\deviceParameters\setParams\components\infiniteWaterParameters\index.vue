<template>
  <kade-tab-wrap :tabs="tabs" style="margin-top: 20px" v-model="state.tab">
    <template #jccs>
      <kade-basic-params />
    </template>
    <template #gjcs>
      <kade-senior-params />
    </template>
    <template #klcs>
      <kade-card-params />
    </template>
  </kade-tab-wrap>
</template>
<script>
import { reactive, ref } from "@vue/reactivity";
import { useStore } from "vuex";
import { watch } from "@vue/runtime-core";
import basicParams from "./basicParams"
import seniorParams from "./seniorParams"
import cardParams from "./cardParams"
const tabs = [
  { name: "jccs", label: "基础参数" },
  { name: "klcs", label: "卡类参数" },
  { name: "gjcs", label: "高级参数" },
];
export default {
  components: {
    "kade-basic-params": basicParams,
    "kade-senior-params": seniorParams,
    "kade-card-params": cardParams,
  },
  setup() {
    const store = useStore();
    const formDom = ref(null);
    const state = reactive({
      tab: "jccs",
      form: {},
    });
    watch(() => store.state.deviceParameters[store.state.app.activeTab] && store.state.deviceParameters[store.state.app.activeTab].isSetParams, val => {
      if (val) {
        state.tab = "jccs"
      } else {
        state.tab = ""
      }
    })
    return {
      tabs,
      formDom,
      state,
    };
  },
};
</script>
<style lang="scss" scoped></style>