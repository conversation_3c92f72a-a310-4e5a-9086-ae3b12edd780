<template>
  <el-dialog :model-value="isShow" :title="(rowData.id?'编辑':'新增')+'标签'" width="500px" :before-close="beforeClose">
    <el-form ref="formRef" size="mini" label-width="120px" :model="state.form" :rules="rules" style="margin-top:20px">
      <el-form-item label="标签编号:" prop="labelNo">
        <el-input v-model="state.form.labelNo" placeholder="请输入"></el-input>
      </el-form-item>
      <el-form-item label="标签名称:" prop="labelName">
        <el-input v-model="state.form.labelName" placeholder="请输入"></el-input>
      </el-form-item>
      <el-form-item label="备注:" prop="remark">
        <el-input :max="200" v-model="state.form.remark" type="textarea" placeholder="请输入"></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="beforeClose()" size="mini">返&nbsp;&nbsp;回</el-button>
        <el-button @click="submit()" type="primary" size="mini">保&nbsp;&nbsp;存</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script>
import {
  ElDialog,
  ElForm,
  ElFormItem,
  ElInput,
  ElButton,
  ElMessage,
} from "element-plus";
import { reactive, ref } from "@vue/reactivity";
import { nextTick, watch } from "@vue/runtime-core";
import { addAreaLabel, editAreaLabel } from "@/applications/eccard-basic-data/api";
const rules = {
  labelNo: [
    {
      required: true,
      message: "请输入标签编号",
    },
    {
      pattern: /^[0-9a-zA-Z]+$/,
      message: "请输入字母+数字",
    },
    {
      max: 20,
      message: "标签编号长度不能超过20字符",
    },
  ],
  labelName: [
    {
      required: true,
      message: "请输入标签名称",
    },
    {
      max: 20,
      message: "标签名称长度不能超过20字符",
    },
    {
      pattern: /^\S.*\S$|(^\S{0,1}\S$)/,
      message: "标签名称首尾不能包含空格",
    },
  ],
};
export default {
  components: {
    ElDialog,
    ElForm,
    ElFormItem,
    ElButton,
    ElInput,
  },
  props: {
    isShow: {
      type: Boolean,
      default: false,
    },
    rowData: {
      type: Object,
      default: null,
    },
  },
  setup(props, context) {
    const formRef = ref(null);
    const state = reactive({
      form: {},
    });
    watch(
      () => props.isShow,
      (val) => {
        state.form = val&&props.rowData.id ? { ...props.rowData } : {};
        nextTick(()=>{
            formRef.value.clearValidate();
        })
      }
    );
    const submit = () => {
      formRef.value.validate(async (valid) => {
        if (valid) {
          let fn = props.rowData.id ? editAreaLabel : addAreaLabel;
          let { code, message } = await fn(state.form);
          if (code === 0) {
            ElMessage.success(message);
            context.emit("close", true);
          }
        } else {
          return false;
        }
      });
    };
    const beforeClose = () => {
      context.emit("close", false);
    };
    return {
      rules,
      formRef,
      state,
      submit,
      beforeClose,
    };
  },
};
</script>
<style lang="scss" scoped>
.el-divider--horizontal {
  margin: 0px 0px 10px;
}
.kade-table-wrap {
  padding-bottom: 0;
}
</style>