<template>
    <div class="box">
        <el-form inline size="small" label-width="100px">
            <el-form-item label="选择日期:">
                <el-col :span="24">
                    <el-date-picker v-model="state.requestDate" type="datetimerange" :default-time="state.defaultTime" range-separator="~" start-placeholder="请选择日期" end-placeholder="请选择日期" @change="changeDate">
                    </el-date-picker>
                </el-col>
                <el-col :span="3" class="date" v-for="(item, index) in state.defaultDateList" :key="index" @click="changeDefaultDate(item.value)">
                    {{ item.label }}
                </el-col>
            </el-form-item>
            <el-form-item label="&nbsp;">
                <el-button @click="search()" size="small" type="primary" icon="el-icon-search">搜索</el-button>
                <el-button icon="el-icon-refresh-right" @click="reset()" size="small">重置</el-button>
            </el-form-item>
        </el-form>
        <el-divider></el-divider>
        <div class="Statistics-box" v-for="(item, index) in personWalletTradeSum" :key="index">
            <div class="Statistics-item">
                <div class="Statistics-title">{{ item.walletName }}</div>
                <div class="color Statistics-num">{{ item.totalCount }}笔</div>
                <div class="Statistics-money">
                    <div>
                        <span class="font">{{ item.incomeTotalAmount }}</span><span class="color">{{item.walletName==="次数钱包"?"次":'元'}}</span>
                    </div>
                    <div class="color">
                        <span class="mg-right">{{item.walletName.substring(0,item.walletName.length-2)}}总收入</span><span>{{ item.incomeTotalCount }}笔</span>
                    </div>
                </div>
                <div class="Statistics-money">
                    <div class="line-height:40px">
                        <span class="font">{{ item.expendTotalAmount }}</span><span class="color">{{item.walletName==="次数钱包"?"次":'元'}}</span>
                    </div>
                    <div class="color">
                        <span class="mg-right">{{item.walletName.substring(0,item.walletName.length-2)}}总支出</span><span>{{ item.expendTotalCount }}笔</span>
                    </div>
                </div>
            </div>
            <el-divider></el-divider>
        </div>
        <div class="none" v-if="!personWalletTradeSum.length">暂无数据！</div>
    </div>
</template>
<script>
import {
    ElButton,
    ElForm,
    ElFormItem,
    ElDivider,
    ElDatePicker,
} from "element-plus";
import { timeStr } from "@/utils/date.js";
import { computed, onMounted, reactive } from "vue";
import { getPersonWalletTradeSum } from "@/applications/eccard-finance/api";
import { useStore } from "vuex";
import {
    requestDefaultTime,
    requestDate,
    defaultDateList,
} from "@/utils/reqDefaultDate.js";
export default {
    components: {
        ElButton,
        ElForm,
        ElFormItem,
        ElDivider,
        ElDatePicker,
    },
    setup() {
        const store = useStore();
        const state = reactive({
            form: {
                beginDate: requestDate()[0],
                endDate: requestDate()[1],
                userId: store.state.data.selectPerson.userId,
            },
            defaultDateList,
            requestDate: requestDate(),
            defaultTime: requestDefaultTime(),
        });
        //日期格式
        const timeStrDate = computed(() => {
            return timeStr;
        });
        const personWalletTradeSum = computed(() => {
            return store.state.data.personWalletTradeSum;
        });
        const changeDefaultDate = (val) => {
            state.requestDate = val();
            state.form.beginDate = state.requestDate[0];
            state.form.endDate = state.requestDate[1];
        };
        const changeDate = (val) => {
            state.form.beginDate = timeStr(val[0]);
            state.form.endDate = timeStr(val[1]);
        };
        const queryPersonWalletTradeSum = () => {
            state.form.userId = store.state.data.selectPerson.userId;
            getPersonWalletTradeSum(state.form).then((res) => {
                store.commit("data/updateState", {
                    key: "personWalletTradeSum",
                    payload: res.data,
                });
            });
        };
        const search = () => {
            queryPersonWalletTradeSum();
        };

        const reset = () => {
            state.form = {
                beginDate: requestDate()[0],
                endDate: requestDate()[1],
                userId: store.state.data.selectPerson.userId,
            };
            state.requestDate = requestDate();
        };
        onMounted(() => {
            state.form.userId = store.state.data.selectPerson.userId;
            store.dispatch("data/queryPersonWalletTradeSum", state.form);
        });
        return {
            state,
            timeStrDate,
            personWalletTradeSum,
            changeDefaultDate,
            changeDate,
            search,
            reset,
        };
    },
};
</script>
<style lang="scss" scoped>
.box {
    padding: 20px 0;
    border: 1px solid rgba(233, 233, 233, 1);
    border-radius: 5px;
    min-height: 650px;
    // overflow-y: scroll;
    .el-divider {
        margin: 0;
    }
}
.color {
    color: #999999;
}
.mg-right {
    margin-right: 10px;
}
.Statistics-item {
    display: flex;
    align-items: center;
    padding: 20px;
    .Statistics-title {
        font-weight: 650;
        font-size: 20px;
        margin-right: 20px;
    }
    .Statistics-num {
        margin-right: 200px;
    }
    .Statistics-money {
        margin: 0 80px;
    }
}
.font {
    font-family: "BebasNeue-Regular", "Bebas Neue", sans-serif;
    font-size: 32px;
    font-weight: 400;
}
.none {
    text-align: center;
    color: #999999;
    font-size: 20px;
    margin-top: 20px;
}
.date {
  margin: 0 10px;
  color: rgba(0, 0, 0, 0.847058823529412);
}
</style>