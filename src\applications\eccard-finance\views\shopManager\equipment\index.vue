<template>
  <kade-route-card>
    <kade-table-filter @search="search" @reset="handleReset">
      <el-form inline size="small" label-width="100px">
        <el-form-item label="商户名称:">
          <el-input style="width: 215px" v-model="querys.keyWord" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="商家类型:">
          <el-select clearable placeholder="请选择" v-model="querys.merchantType" style="width: 100%">
            <el-option v-for="item in types" :label="item.label" :value="item.value" :key="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="所属区域:">
          <kade-area-select-tree style="width: 100%" :value="querys.areaId" valueKey="id" :multiple="false"
            @valueChange="(val) => (querys.areaId = val.id)" />
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <kade-table title="商家信息列表" v-model:columns="state.columns" :load-data="loadData" :table-options="options"
      @on-select-change="handleSelectChange" @on-page-change="handlePageChange" ref="tableRef">
    </kade-table>
    <kade-tab-wrap extra :tabs="tabs" style="margin-top: 20px" v-model="state.tab">
      <template #sblb>
        <kade-equipment-list :id="state.selected?.merchantId" :isShow="state.showBindModal" @close="close" />
      </template>
      <template #extra>
        <el-button :disabled="!state.selected?.merchantId" @click="handleBind" type="primary" size="small"
          icon="el-icon-circle-plus-outline">添加绑定设备</el-button>
        <el-button :disabled="!state.selected?.merchantId" @click="handlePrint" size="small" type="danger"
          icon="el-icon-daochu">导出</el-button>
      </template>
    </kade-tab-wrap>

  </kade-route-card>
</template>
<script>
import {
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
} from "element-plus";
import { reactive, ref } from "vue";
import { formatColumns, downloadXlsx } from "@/utils";
import { useDict } from "@/hooks/useDict";
import { usePagination } from "@/hooks/usePagination";
import EquipmentList from "./components/equipmentList";
import areaSelectTree from "@/components/tree/areaSelectTree.vue";


import { getMerchantListByPage, getDeviceListToExport } from "@/applications/eccard-finance/api";

const tabs = [
  {
    name: "sblb",
    label: "设备列表",
  },
];

export default {
  components: {
    "el-button": ElButton,
    "el-input": ElInput,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    "el-select": ElSelect,
    "el-option": ElOption,
    "kade-equipment-list": EquipmentList,
    // "kade-area-select": AreaSelect,

    "kade-area-select-tree": areaSelectTree,
  },
  setup() {
    const { options, search, querys, loadData } = usePagination(
      getMerchantListByPage,
      {
        keyWord: "",
        merchantType: "",
        areaId: "",
      },
      {},
      { currentPage: "currentPage", pageSize: "pageSize" }
    );
    const tableRef = ref(null);
    const types = useDict("MERCHANT_TYPE");
    // const { ctx } = getCurrentInstance();
    const state = reactive({
      tab: "sblb",
      areaName: "",
      selected: null,
      showBindModal: false,
      columns: formatColumns([
        {
          type: "selection",
          width: "55",
        },
        {
          label: "商户编号",
          prop: "merchantNo",
        },
        {
          label: "商户名称",
          prop: "merchantName",
        },
        /*         {
          label: "商户类型",
          prop: "merchantType",
          render: (scope) => {
            return ctx.dictionaryFilter(scope.row.merchantType);
          },
        }, */
        {
          label: "所属区域",
          prop: "areaName",
        },
        {
          label: "终端总量",
          prop: "deviceTotalCount",
        },
        {
          label: "联系人",
          prop: "merchantContacts",
        },
        {
          label: "联系电话",
          prop: "merchantTel",
        },
        /*         {
          label: "操作",
          prop: "oper",
          width: 150,
          isDictionary: false,
          render: () => {
            return (
                <span>
                  <ElButton size="small" type="text">
                    查看详情
                  </ElButton>
                </span>

            );
          },
        }, */
      ]),
    });
    const handleAreaChange = (data) => {
      console.log(123, data);
      querys.areaId = data ? data : "";
    };
    const handleReset = () => {
      Object.assign(querys, {
        areaId: "",
        keyWord: "",
        merchantType: "",
      });
      state.areaName = "";
      querys.areaId = "";
    };
    const handleSelectChange = (v) => {
      state.selected = v;
    };
    const handlePageChange = () => {
      if (
        state.selected &&
        options.dataList.some(
          (it) => it.merchantId === state.selected.merchantId
        )
      ) {
        tableRef.value?.tableRef?.setCurrentRow?.(state.selected);
      }
    };

    const handleBind = () => {
      state.showBindModal = true;
    };
    const handlePrint = async () => {
      let res = await getDeviceListToExport({ merchantId: state.selected.merchantId })
      downloadXlsx(res, "绑定设备列表.xlsx")
    }
    const close = () => {
      state.showBindModal = false;
    }
    return {
      state,
      options,
      search,
      querys,
      loadData,
      tableRef,
      types,
      tabs,
      close,
      handleReset,
      handleAreaChange,
      handleSelectChange,
      handlePageChange,
      handleBind,
      handlePrint,
    };
  },
};
</script>