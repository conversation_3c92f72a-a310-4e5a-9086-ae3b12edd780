import Cookies from "js-cookie";
import NProgress from 'nprogress';
import 'nprogress/nprogress.css';
import { getMenuPurviewByUserId, getUserInfo } from '@/service';
import getLodop from '../print/LodopFuncs.js'
// import View from '@/components/view';
import { ElLoading } from 'element-plus';

NProgress.configure({
    showSpinner: false
});

// const formatPath = routePath => {
//     return /^\//.test(routePath) ? routePath : `/${routePath}`;
// }

// const getAuths = (arr, id) => {
//     const temp = arr.filter(it => it.pMenuId === id);
//     const target = {};
//     temp.forEach(it => {
//         target[it['menuEnName']] = it.menuName;
//     });
//     return target;
// }

// const getMenuTree = ({ pMenuId, menus, routeList }) => {
//     let tempArr = [];
//     menus.forEach(it => {
//         if(it.pMenuId === pMenuId && it.appId === CONFIG.APPID) {
//             const obj = {
//                 path: formatPath(it.menuUrl),
//                 name: it.menuEnName,
//                 meta: {
//                     label: it.menuName,
//                     icon: it.menuIcon,
//                     sort: it.menSort,
//                     auths: getAuths(menus, it.menuId),
//                 },
//                 component: routeList[it.menuEnName] || View,
//             };
//             const children = getMenuTree({ pMenuId: it.menuId, menus, routeList });
//             if(children.length) {
//                 obj.children = children;
//                 obj.redirect = { name: children[0]?.name };
//             }
//             tempArr.push(obj);
//         }
//     });
//     return tempArr;
// }

// 加载菜单
export const initAuth = async ({ store }) => {
    let cacheMenus = sessionStorage.getItem(`${CONFIG['CACHE_PREFIX']}auth_menus`);
    if (cacheMenus) {
        cacheMenus = JSON.parse(cacheMenus);
    } else {
        if (CONFIG.NAME === 'unified_portal') {
            cacheMenus = [];
        } else {
            const { data } = await getMenuPurviewByUserId(CONFIG.APPID);
            sessionStorage.setItem(`${CONFIG['CACHE_PREFIX']}auth_menus`, JSON.stringify(data));
            cacheMenus = data;
        }
    }
    cacheMenus = Array.isArray(cacheMenus) ? cacheMenus : [];
    store.commit('user/updateState', {
        key: 'isInitRoute',
        payload: true,
    }, { root: true });
    if (CONFIG.NAME !== 'unified_portal') {
        store.commit('app/updateState', {
            key: 'menus',
            payload: cacheMenus,
        }, { root: true });
        store.dispatch('app/initTab', {}, { root: true });
    }
}

// 登录重定向
// export function redirectLogin() {
//     let times = sessionStorage.getItem('kade-common-rediect-times') || 0;
//     times = parseInt(times);
//     if(times > 2) {
//         throw new Error('授权失败');
//     }
//     const arr = [
//         'response_type=code',
//         'scope=server',
//         `client_id=${CONFIG.APPID}`,
//         `rediect_url=${window.location.origin}/${CONFIG.NAME}`,
//         `state=${Math.round(Math.random()*1000)}`
//     ];
//     sessionStorage.setItem('kade-common-rediect-times', times + 1);
//     window.location.href = `${CONFIG.BASE_API_PATH}/auth/oauth/authorize?${arr.join('&')}`;
// }
// 登录重定向
export function redirectLogin() {
    let times = sessionStorage.getItem('kade-common-rediect-times') || 0;
    times = parseInt(times);
    if (times > 2) {
        throw new Error('授权失败');
    }

    //登录没有对应的模块时默认为unified_portal
    let data = "unified_portal"
    if (CONFIG.NAME) {
        data = CONFIG.NAME
    }
    const arr = [
        `rediectUrl=${window.location.origin}/${data}`
    ];
    sessionStorage.setItem('kade-common-rediect-times', times + 1);
    window.location.href = `${window.location.origin}/login?${arr.join('&')}`;
}

// 初始化，鉴权
export async function routeAuthentication({ router, store }) {
    const loading = ElLoading.service({ fullscreen: true });
    try {
        const token = getQueryString('token');

        if (getToken() || token) {
            if (token) {
                sessionStorage.removeItem('kade-common-rediect-times');
                setToken(token);
            }

            await initAuth({ store });
        } else {
            //const code = getQueryString('code');
            // if(!code) {
            redirectLogin();
            throw new Error('请登录');
            // }
            // const { data } = await getAuthToken({
            //     grant_type: 'authorization_code',
            //     code,
            //     redirect_url: `${window.location.origin}/${CONFIG.NAME}`,
            // });
            // sessionStorage.removeItem('kade-common-rediect-times');
            // setToken(data.token);
            // setRefreshToken(data.refreshToken);
        }
        if (!store.state.user.userInfo.id) {
            const { data } = await getUserInfo();
            setCacheUser(data);
            store.commit('user/updateState', {
                key: 'userInfo',
                payload: data,
            });
        }
        if (!store.state.app.dictionary.length) {
            await store.dispatch('app/loadDictionary');
        }
        router.beforeEach(async (to, from, next) => {
            NProgress.start();
            const title = to.meta?.title;
            const isNoAuth = to.meta.noauth;
            title && (document.title = `${title}-${CONFIG.TITLE}`);
            const token = getToken();
            // eslint-disable-next-line no-debugger
            if (token) {
                if (to.name === 'Login') {
                    next('/main');
                    NProgress.done();
                    return;
                }
                if (!store.state.user.isInitRoute) {
                    await initAuth({ store });
                    next({ ...to, replace: true });
                    return;
                } else if (to.name === 'Main') {
                    next({ name: 'Panel' });
                    return;
                }
                next();
            } else {
                if (!isNoAuth) {
                    next('/login');
                    NProgress.done();
                    return;
                }
                next();
            }
        });
        router.afterEach(() => {
            NProgress.done();
        });
    } catch (e) {
        throw new Error(e.message);
    } finally {
        loading.close();
    }
}

// 获取菜单
export function getMenus(routes) {
    return routes.filter(it => it.meta?.label).map(({ meta: { label, icon, sort }, children, name }) => {
        const target = { label, icon, sort: sort ?? 0 };
        if (children?.length) {
            target.expand = false;
            target.children = getMenus(children);
        }
        target.routeName = name;
        return target;
    }).sort((a, b) => a > b ? -1 : 1);
}

// 面包屑
export function getBreadcrumbs(menus, currentName) {
    const arr = JSON.parse(JSON.stringify(menus));
    return getDeepDataTree(currentName, 'routeName', arr);
}

// 部门树
export function getDeepDataTree(value, valueKey, data) {
    let arr = [];
    let returnArr = [];
    let depth = 0;

    function childrenEach(childrenData, depthN) {
        for (let j = 0; j < childrenData.length; j++) {
            depth = depthN;
            arr[depthN] = childrenData[j];
            if (childrenData[j][valueKey] === value) {
                returnArr = arr.slice(0, depthN + 1);
                break
            } else {
                if (childrenData[j].children) {
                    depth++;
                    childrenEach(childrenData[j].children, depth);
                }
            }
        }
        return returnArr;
    }
    return childrenEach(data, depth);
}



export function makeTree(data, key, pid, child) {
    let parents = data.filter(p => p[pid] == 0),
        children = data.filter(c => c[pid] != 0);
    dataToTree(parents, children);
    return parents;

    function dataToTree(parents, children) {
        parents.map(p => {
            children.map((c, i) => {
                if (c[pid] == p[key]) {
                    let _children = JSON.parse(JSON.stringify(children));
                    _children.splice(i, 1);
                    dataToTree([c], _children);
                    if (p[child]) {
                        p[child].push(c);
                    } else {
                        p[child] = [c];
                    }
                }
            })
        })
    }
}


// 深拷贝
export function deepClone(data) {
    if (!data || !(data instanceof Object) || (typeof data === "function")) {
        return data || undefined;
    }
    const constructor = data.constructor;
    const result = new constructor();
    for (let key in data) {
        if (Object.prototype.hasOwnProperty.call(data, key)) {
            result[key] = deepClone(data[key]);
        }
    }
    return result;
}

// 获取token
export function getToken() {
    return Cookies.get(`${CACHE_PREFIX}token`);
}

// 设置token
export function setToken(token) {
    Cookies.set(`${CACHE_PREFIX}token`, token);
}

// 删除token
export function removeToken() {
    Cookies.remove(`${CACHE_PREFIX}token`);
}

// 设置refresh_token
export function getRefreshToken() {
    return Cookies.get(`${CACHE_PREFIX}_refresh_token`);
}

// 设置缓存refresh_token
export function setRefreshToken(token) {
    Cookies.set(`${CACHE_PREFIX}_refresh_token`, token);
}

// 删除缓存refresh_token
export function removeRefreshToken() {
    Cookies.remove(`${CACHE_PREFIX}_refresh_token`);
}

// 获取缓存用户信息
export function getCacheUser() {
    const str = sessionStorage.getItem(`${CACHE_PREFIX}userinfo`);
    return str ? JSON.parse(str) : {};
}

// 设置缓存用户信息
export function setCacheUser(data) {
    sessionStorage.setItem(`${CACHE_PREFIX}userinfo`, JSON.stringify(data));
}

// 删除缓存用户信息
export function removeCacheUser() {
    sessionStorage.removeItem(`${CACHE_PREFIX}userinfo`);
}

// 是否数字
export function isNumber(value) {
    return typeof value === 'number' && !isNaN(value);
}

// 生成唯一id
export function createGuid() {
    function S4() {
        return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
    }
    return (S4() + S4() + "-" + S4() + "-" + S4() + "-" + S4() + "-" + S4() + S4() + S4());
}

// URL参数查询
export function getQueryString(name) {
    const reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i');
    const r = window.location.search.substr(1).match(reg);
    if (r != null) {
        return unescape(r[2]);
    }
    return null;
}

// 参数填充
export function fillData(target, source, ...addFields) {
    [...Object.keys(target), ...addFields].forEach(key => {
        target[key] = source[key];
    });
}

// 列表转树
export function flatArrayToTree(sourceArray, opts = {
    pidKey: 'pid',
    idKey: 'id',
    rootValue: 0,
}) {
    const {
        pidKey,
        idKey,
        rootValue,
    } = opts;
    const result = [];
    const map = {};
    sourceArray.forEach((el) => {
        const pid = el[pidKey];
        const id = el[idKey];
        map[id] = { ...el, children: map[id] ? map[id].children : [] };

        if (!map[pid]) {
            map[pid] = { children: [] };
        }
        if (pid === rootValue) {
            result.push(map[id]);
        } else {
            map[pid].children.push(map[id]);
        }
    });
    return result;
}


// kade-table组件columns转换
export function formatColumns(columns) {
    return columns.map(it => {
        return {
            align: "center",
            showOverflowTooltip: true,
            show: true,
            ...it,
        }
    });
}

// 初始化
export function initApp(app, store) {
    const dictionaryFilter = (key) => {
        const source = store.state.app.dictionary;
        const target = source.find(it => it.dictCode === key);
        return target?.dictValue || key;
    }
    app.config.globalProperties.dictionaryFilter = dictionaryFilter;
    app.mixin({
        methods: {
            dictionaryFilter,
            timeFilter: (value) => {
                if (value) {
                    return `${value}`.replace('T', ' ').replace(/\d\+.*$/, '');
                }
                return value;
            },
            getMenuComponent: (env) => {
                return store.state.app.componentMap[env] || null;
            }
        }
    });
}

// 导出下载文件流
export function downloadXlsx(blob, filename) {
    const link = document.createElement('a');
    link.download = filename;
    link.style.display = 'none';
    link.href = URL.createObjectURL(blob);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// 导入选择文件
export function importXlsx() {
    return new Promise((resolve, reject) => {
        const input = document.createElement('input');
        input.setAttribute('type', 'file');
        input.setAttribute('accept', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel');
        input.style.display = 'none';
        document.body.appendChild(input);
        input.addEventListener('change', (e) => {
            if (e.target.value) {
                resolve(e.target.files[0]);
            } else {
                reject(false);
            }
        });
        input.click();
        document.body.removeChild(input);
    })
}

//打印
export function print(data) {
    const LODOP = getLodop();
    if (LODOP) {
        LODOP.PRINT_INIT(""); //初始化
        LODOP.SET_PRINT_PAGESIZE('210mm', '297mm', 'A4打印'); //设置
        LODOP.ADD_PRINT_HTM(8, "2%", "95%", "95%", data);
        LODOP.SET_SHOW_MODE("LANDSCAPE_DEFROTATED", 1);
        LODOP.PREVIEW();
    }
}


// 关闭当前窗口
export function closeCurrentWindow() {
    if (window.opener) {
        if (navigator.userAgent.indexOf("Firefox") != -1 || navigator.userAgent.indexOf("Chrome") != -1) {
            window.location.href = "about:blank";
            window.close();
        } else {
            window.opener = null;
            window.open("", "_self");
            window.close();
        }
    } else {
        window.location.href = `${window.location.origin}/unified_portal`;
    }
}

// 获取当前窗口
export function getCurrentWindow() {
    return window.opener || window.parent;
}

//通过字典对应code获取对应value
export const filterDictionary = (val, dictionaryList) => {
    for (let i of dictionaryList) {
        if (val == i.value) {
            return i.label;
        }
    }
};

//键值对对象转数组
export function objToArray(obj) {
    let dictList = []
    for (let key in obj) {
        dictList.push({ label: obj[key], value: key })
    }
    return dictList
}

//数组转键值对对象
export function arrayToObj(array) {
    let dict = {}
    for (let i of array) {
        dict[i.value] = i.label
    }
    return dict
}

//封装promiseAll
export function promiseAll(promiseList) {
    return new Promise((resolve, reject) => {
        Promise.all(promiseList).then(res => {
            resolve(res)
        }).catch(err => {
            reject(err)
        })
    })
}

//文件转换为base64格式
export function fileToBase64(file) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = () => {
            resolve(reader.result);
        }
        reader.onerror = (error) => {
            reject(error);
        }
    })
}
