<template>
  <!-- 区域 -->
  <el-form-item v-if="data.area" :label="data.area.label" :prop="data.area.valueKey">
    <kade-area-select-tree style="width: 100%" :value="state.form[data.area.valueKey]" :valueKey="data.area.key"
      :multiple="false" @valueChange="areaChange" />
  </el-form-item>
  <!-- 楼栋 -->
  <el-form-item v-if="data.building" :label="data.building.label" :prop="data.building.valueKey">
    <el-select v-model="state.form[data.building.valueKey]" @change="buildingChange" clearable>
      <el-option v-for="(item, index) in state.buildingList" :key="index" :label="item.buildName" :value="item.buildId">
      </el-option>
    </el-select>
  </el-form-item>
  <!-- 单元 -->
  <el-form-item v-if="data.unit" :label="data.unit.label" :prop="data.unit.valueKey">
    <el-select v-model="state.form[data.unit.valueKey]" @change="unitChange" clearable>
      <el-option v-for="(item, index) in state.unitList" :key="index" :label="item.unitName" :value="item.unitId">
      </el-option>
    </el-select>
  </el-form-item>
  <!-- 楼层 -->
  <el-form-item v-if="data.floor" :label="data.floor.label" :prop="data.floor.valueKey">
    <el-select v-model="state.form[data.floor.valueKey]" @change="floorChange" clearable>
      <el-option v-for="(item, index) in state.floorList" :key="index" :label="item.floorName" :value="item.floorId">
      </el-option>
    </el-select>
  </el-form-item>
  <!-- 结束楼层 -->
  <el-form-item v-if="data.copyFloor" :label="data.copyFloor.label" :prop="data.copyFloor.valueKey">
    <el-select v-model="state.form[data.copyFloor.valueKey]" @change="copyFloorChange" clearable>
      <el-option v-for="(item, index) in state.floorList" :key="index" :label="item.floorName" :value="item.floorId">
      </el-option>
    </el-select>
  </el-form-item>
  <!-- 房间 -->
  <el-form-item v-if="data.room" :label="data.room.label" :prop="data.room.valueKey">
    <el-select v-model="state.form[data.room.valueKey]" @change="roomChange" clearable>
      <el-option v-for="(item, index) in state.roomList" :key="index" :label="item.roomName" :value="item.id">
      </el-option>
    </el-select>
  </el-form-item>
</template>
<script>
import { ElFormItem, ElSelect, ElOption } from "element-plus"
import { queryBuildingList, queryBuildingUnitList, queryBuildingFloorList, queryRoomList } from "@/applications/eccard-uac/api";
import { reactive } from '@vue/reactivity';
import { onMounted, watch } from '@vue/runtime-core';
import areaSelectTree from "@/components/tree/areaSelectTree.vue";
export default {
  components: {
    ElFormItem, ElSelect, ElOption,
    "kade-area-select-tree": areaSelectTree,
  },
  props: {
    /**
     * data {
     *  label 选择框label
     *  valueKey form返回值key
     * }
     */
    data: {
      type: Object,
      default: null
    },
    value: {
      type: Object,
      default: null
    },
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  setup(props, context) {
    const state = reactive({
      buildingList: [],
      unitList: [],
      floorList: [],
      roomList: [],
      form: {},
    })
    watch(() => props.isEdit, async isEdit => {
      if (isEdit) {
        let val = props.value
        if (val[props.data.area.valueKey]) {
          state.form[props.data.area.valueKey] = { ...val }[props.data.area.valueKey]
          getBuildingList({ areaId: { ...val }[props.data.area.valueKey] })
        }

        if (props.data.building && val[props.data.building.valueKey]) {
          state.form[props.data.building.valueKey] = { ...val }[props.data.building.valueKey]
          getUnitList({ ...val }[props.data.building.valueKey])
        }

        if (props.data.unit && val[props.data.unit.valueKey]) {
          state.form[props.data.unit.valueKey] = { ...val }[props.data.unit.valueKey]
          let params = {
            buildId: state.form[props.data.building.valueKey],
            unitId: state.form[props.data.unit.valueKey],
          }
          getFloorList(params)
        }
        if (props.data.floor && val[props.data.floor.valueKey]) {
          state.form[props.data.floor.valueKey] = { ...val }[props.data.floor.valueKey]
          let params = {
            buildId: state.form[props.data.building.valueKey],
            unitNum: state.form[props.data.unit.valueKey],
            floorNum: state.form[props.data.floor.valueKey],
          }
          getRoomList(params)
        }
        if (props.data.room && val[props.data.room.valueKey]) {
          state.form[props.data.room.valueKey] = { ...val }[props.data.room.valueKey]
        }
      }
    })
    watch(() => props.value, val => {
      for (let key in props.data) {
        state.form[props.data[key].valueKey] = val[props.data[key].valueKey]
      }
      if (props.data.area && !state.form[props.data.area.valueKey]) {
        state.buildingList = []
      }
      if (props.data.building && !state.form[props.data.building.valueKey]) {
        state.unitList = []
      }
      if (props.data.unit && !state.form[props.data.unit.valueKey]) {
        state.floorList = []
      }
    })
    const getBuildingList = async (params) => {
      if (!props.data.building) return
      let { data } = await queryBuildingList(params)
      state.buildingList = data
    }
    const getUnitList = async (params) => {
      if (!props.data.unit) return
      let { data } = await queryBuildingUnitList(params)
      state.unitList = data
    }
    const getFloorList = async (params) => {
      if (!props.data.floor) return
      let { data } = await queryBuildingFloorList(params)
      state.floorList = data
    }
    const getRoomList = async (params) => {
      if (!props.data.room) return
      let { data } = await queryRoomList(params)
      state.roomList = data
    }


    const buildingChange = (val) => {
      state.unitList = []
      state.floorList = []
      state.roomList = []
      if (props.data.unit) {
        state.form[props.data.unit.valueKey] = ""
      }
      if (props.data.floor) {
        state.form[props.data.floor.valueKey] = ""
      }
      if (props.data.copyFloor) {
        state.form[props.data.copyFloor.valueKey] = ""
      }
      if (props.data.room) {
        state.form[props.data.room.valueKey] = ""
      }
      if (val) {
        getUnitList(val)
      }
      context.emit("change", state.form)
    }
    const unitChange = (val) => {
      state.floorList = []
      state.roomList = []
      if (props.data.floor) {
        state.form[props.data.floor.valueKey] = ""
      }
      if (props.data.copyFloor) {
        state.form[props.data.copyFloor.valueKey] = ""
      }
      if (props.data.room) {
        state.form[props.data.room.valueKey] = ""
      }
      if (val) {
        let params = {
          buildId: state.form[props.data.building.valueKey],
          unitId: state.form[props.data.unit.valueKey],
        }
        getFloorList(params)
        let params1 = {
          buildId: state.form[props.data.building.valueKey],
          unitNum: state.form[props.data.unit.valueKey],
        }
        getRoomList(params1)
      }
      context.emit("change", state.form)
    }
    const floorChange = (val) => {
      state.roomList = []
      if (props.data.room) {
        state.form[props.data.room.valueKey] = ""
      }
      if (val) {
        let params = {
          buildId: state.form[props.data.building.valueKey],
          unitNum: state.form[props.data.unit.valueKey],
          floorNum: state.form[props.data.floor.valueKey],
        }
        getRoomList(params)
      }
      context.emit("change", state.form)
    }
    const copyFloorChange = () => {
      context.emit("change", state.form)
    }

    const roomChange = () => {
      context.emit("change", state.form)
    }


    const areaChange = (val) => {
      state.buildingList = []
      state.unitList = []
      state.floorList = []
      state.roomList = []
      if (props.data.building) {
        state.form[props.data.building.valueKey] = ""
      }
      if (props.data.unit) {
        state.form[props.data.unit.valueKey] = ""
      }
      if (props.data.floor) {
        state.form[props.data.floor.valueKey] = ""
      }
      if (props.data.room) {
        state.form[props.data.room.valueKey] = ""
      }
      state.form[props.data.area.valueKey] = val[props.data.area.key]

      console.log(val);
      if (val[props.data.area.key]) {
        let params = {}
        if (state.form[props.data.buildingType && props.data.buildingType.valueKey]) {
          params[props.data.buildingType.valueKey] = state.form[props.data.buildingType.valueKey]
        }
        params[props.data.area.valueKey] = val[props.data.area.key]
        getBuildingList(params)
      }
      context.emit("change", state.form)
    }

    onMounted(() => {
      console.log(props.isEdit);
      if (props.isEdit) {
        let val = props.value
        console.log(val);
        if (val[props.data.area.valueKey]) {
          state.form[props.data.area.valueKey] = { ...val }[props.data.area.valueKey]
          getBuildingList({ areaId: { ...val }[props.data.area.valueKey] })
        }
        if (props.data.building && val[props.data.building.valueKey]) {
          state.form[props.data.building.valueKey] = { ...val }[props.data.building.valueKey]
          getUnitList({ ...val }[props.data.building.valueKey])
        }
        if (props.data.unit && val[props.data.unit.valueKey]) {
          state.form[props.data.unit.valueKey] = { ...val }[props.data.unit.valueKey]
          let params = {
            buildId: state.form[props.data.building.valueKey],
            unitId: state.form[props.data.unit.valueKey],
          }
          getFloorList(params)
        }
        if (props.data.floor && val[props.data.floor.valueKey]) {
          state.form[props.data.floor.valueKey] = { ...val }[props.data.floor.valueKey]
          let params = {
            buildId: state.form[props.data.building.valueKey],
            unitNum: state.form[props.data.unit.valueKey],
            floorNum: state.form[props.data.floor.valueKey],
          }
          getRoomList(params)
        }
        if (props.data.room && val[props.data.room.valueKey]) {
          state.form[props.data.room.valueKey] = { ...val }[props.data.room.valueKey]
        }
      }
    })
    return {
      state,
      areaChange,
      buildingChange,
      unitChange,
      floorChange,
      copyFloorChange,
      roomChange
    }
  }
}
</script>