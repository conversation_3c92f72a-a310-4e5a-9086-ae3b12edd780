<template>
  <div class="padding-form-box timeParam" v-loading="state.loading">
    <el-form inline size="small" label-width="100px" v-for="(item, index) in state.form.timeDetailEntity.ListTime" :key="index">
      <el-form-item :label="item.label"> </el-form-item>
      <el-form-item label="开始时间：">
        <el-time-picker size="medium" v-model="item.start"></el-time-picker>
      </el-form-item>
      <el-form-item label="结束时间：">
        <el-time-picker size="medium" v-model="item.stop"></el-time-picker>
      </el-form-item>
    </el-form>
  </div>
  <div style="text-align: center">
    <el-button size="mini" @click="beforeClose()">取消</el-button>
    <el-button size="mini" type="primary" @click="saveClick()">保存</el-button>
  </div>
</template>

<script>
import {
  ElForm,
  ElFormItem,
  ElTimePicker,
  ElButton,
  ElMessage,
} from "element-plus";
import { onMounted, reactive } from "vue";
import { useStore } from "vuex";
import { time_to_sec, hourStr } from "@/utils/date";
import { saveTime } from "@/applications/eccard-iot/api";

const defaultParamsFnc = () => {
  return {
    ListTime: [
      {
        label: "餐时段1",
        start: new Date("1970/01/01"),
        stop: new Date(new Date("1770/01/02").getTime() - 1000),
        week: 0,
      },
      {
        label: "餐时段2",
        start: new Date("1970/01/01"),
        stop: new Date(new Date("1770/01/02").getTime() - 1000),
        week: 0,
      },
      {
        label: "餐时段3",
        start: new Date("1970/01/01"),
        stop: new Date(new Date("1770/01/02").getTime() - 1000),
        week: 0,
      },
      {
        label: "餐时段4",
        start: new Date("1970/01/01"),
        stop: new Date(new Date("1770/01/02").getTime() - 1000),
        week: 0,
      },
      {
        label: "餐时段5",
        start: new Date("1970/01/01"),
        stop: new Date(new Date("1770/01/02").getTime() - 1000),
        week: 0,
      },
    ],
  };
};

export default {
  components: {
    ElForm,
    ElFormItem,
    ElTimePicker,
    ElButton,
  },
  setup() {
    const store = useStore();
    const state = reactive({
      loading: false,
      form: {
        timeDetailEntity: defaultParamsFnc(),
        id: "",
        paramId: "",
        paramType: "time",
      },
    });

    const saveClick = async () => {
      state.loading = true;
      state.form.paramId = store.state.deviceParameters[store.state.app.activeTab].selectRow.id;
      let params = JSON.parse(JSON.stringify(state.form));
      params.timeDetailEntity.ListTime = params.timeDetailEntity.ListTime.map(
        (item) => {
          return {
            start: hourStr(item.start),
            stop: hourStr(item.stop),
            week: 0,
          };
        }
      );
      let { message, code } = await saveTime(params);
      if (code === 0) {
        setTimeout(() => {
          ElMessage.success(message);
          store.dispatch("deviceParameters/getParams", store.state.deviceParameters[store.state.app.activeTab].selectRow.id);
          state.loading = false;
        }, 1500);
      }
    };

    const beforeClose = () => {
      store.commit("deviceParameters/updateState", {
        key: store.state.app.activeTab,
        childKey: "isSetParams",
        payload: false,
      });
    };

    onMounted(() => {
      // store.dispatch("deviceParameters/getParams",store.state.deviceParameters[store.state.app.activeTab].selectRow.id);
      let val = store.state.deviceParameters[store.state.app.activeTab].detailsParams

      if (val && val.length) {
        let timeList = val.filter((item) => item.paramType == "time");
        if (timeList.length) {
          state.form.id = timeList[0].id;
          if (
            JSON.parse(timeList[0].paramContent).ListTime &&
            JSON.parse(timeList[0].paramContent).ListTime.length
          ) {
            state.form.timeDetailEntity.ListTime = JSON.parse(
              timeList[0].paramContent
            ).ListTime.map((item, index) => {
              return {
                start: new Date(
                  new Date("1770/01/01").getTime() + time_to_sec(item.start)
                ),
                stop: new Date(
                  new Date("1770/01/01").getTime() + time_to_sec(item.stop)
                ),
                label: "餐时段" + (index + 1),
              };
            });
            return;
          }
        } else {
          state.form.id = "";
          state.form.timeDetailEntity = defaultParamsFnc();
        }
      } else {
        //设置默认选项
        state.form.id = "";
        state.form.timeDetailEntity = defaultParamsFnc();
      }
    });
    return {
      state,
      saveClick,
      beforeClose,
    };
  },
  name: "basicParam",
};
</script>

<style lang="scss" scoped>
.timeParam {
  text-align: center;
  border: 1px solid #dcdfe6;
  border-radius: 5px;
  margin-bottom: 10px;
  width: 100%;
  .time-item {
    width: 100%;
    display: flex;
    align-items: center;
  }
}
</style>
