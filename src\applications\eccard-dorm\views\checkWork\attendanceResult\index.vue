<template>
  <kade-route-card>
    <kade-table-filter @search="handleSearch" @reset="handleReset">
      <el-form inline label-width="90px" size="mini">
        <kade-linkage-select :data="linkageData" :value="state.form" @change="linkageChange" />
        <el-form-item label="组织机构">
          <kade-dept-select-tree style="width: 100%" :value="state.form.deptPath" valueKey="deptPath" :multiple="false"
            @valueChange="(val) => (state.form.deptPath = val.deptPath)" />
        </el-form-item>
        <el-form-item label="人员编号">
          <el-input clearable v-model="state.form.userCode" placeholder="请输入">
          </el-input>
        </el-form-item>
        <el-form-item label="人员姓名">
          <el-input clearable v-model="state.form.userName" placeholder="请输入">
          </el-input>
        </el-form-item>
        <el-form-item label="记录来源">
          <el-select clearable v-model="state.form.recordSource" placeholder="全部">
            <el-option v-for="(item, index) in []" :key="index" :label="item" :value="index"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="考勤日期">
          <el-date-picker v-model="state.requestDate" type="daterange" range-separator="~" start-placeholder="请选择"
            end-placeholder="请选择">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="考勤时段">
          <el-select clearable v-model="state.form.attendancePeriodId" placeholder="全部">
            <el-option v-for="(item, index) in state.periodIdList" :key="index" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="考勤结果">
          <el-select clearable v-model="state.form.attendanceResult" placeholder="全部">
            <el-option v-for="(item, index) in resultList" :key="index" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="考勤结果列表">
      <template #extra>
        <el-button class="btn-purple" icon="el-icon-bottom" type="" size="mini" @click="exportClick">导出</el-button>
      </template>
      <el-table border :data="state.data" v-loading="state.loading">
        <el-table-column prop="userCode" label="人员编号" align="center"></el-table-column>
        <el-table-column prop="userName" label="人员姓名" align="center"></el-table-column>
        <el-table-column prop="deptName" label="组织机构" align="center"></el-table-column>
        <el-table-column prop="attendanceDate" label="考勤日期" align="center"></el-table-column>
        <el-table-column prop="attendancePeriodName" label="考勤时段" align="center"></el-table-column>
        <el-table-column prop="attendanceResult" label="考勤结果" align="center" show-overflow-tooltip>
          <template #default="scope">
            <div class="attendanceResult" :style="{ backgroundColor: colorFnc(scope.row.attendanceResult) }">
              {{ dictionaryFilter(scope.row.attendanceResult) }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="" label="记录来源" align="center"></el-table-column>
        <el-table-column prop="attendanceMode" label="考勤方式" align="center">
          <template #default="scope">{{ dictionaryFilter(scope.row.attendanceMode) }}</template>
        </el-table-column>
        <el-table-column prop="areaName" label="区域" align="center"></el-table-column>
        <el-table-column prop="roomString" label="房间" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column label="操作" align="center" width="90px">
          <template #default="scope">
            <el-button type="text" @click="details(scope.row)" size="mini" class="green">查看详情</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination v-model:currentPage="state.form.currentPage" v-model:page-size="state.form.pageSize"
          :page-sizes="[10, 20, 30, 40]" background layout="total, sizes, prev, pager, next, jumper"
          :total="state.total" @size-change="handleSizeChange" @current-change="handleCurrentChange">
        </el-pagination>
      </div>
    </kade-table-wrap>
    <kade-result-details :selectRow="state.selectRow" :dialogVisible="state.dialogVisible" @close="close()">
    </kade-result-details>
  </kade-route-card>
</template>

<script>
import {
  ElButton,
  ElForm,
  ElFormItem,
  ElSelect,
  ElOption,
  ElTable,
  ElTableColumn,
  ElPagination,
  ElDatePicker,
  ElInput,
} from "element-plus";
import { reactive } from "@vue/reactivity";
import details from "./components/details.vue"
import { dateStr } from "@/utils/date.js"
import {  onMounted } from '@vue/runtime-core';
import { useDict } from '@/hooks/useDict'
import linkageSelect from "../../../components/linkageSelect.vue";
import deptSelectTree from '@/components/tree/deptSelectTree';
import { attendancePeriodList, attendanceResultList, exportAttendanceResult } from '@/applications/eccard-dorm/api.js'
const linkageData = {
  area: { label: "区域", valueKey: "areaPath", key: "areaPath" },
  building: { label: "楼栋", valueKey: "buildId" },
  unit: { label: "单元", valueKey: "unitNum" },
  floor: { label: "楼层", valueKey: "floorNum" },
};
const colorFnc = (val) => {
  if (val === "NORMAL_RETURN") {
    return "#03c316"
  } else if (val === "LATE_RETURN") {
    return "#f59a23"
  } else if (val === "NOT_RETURN") {
    return "#d9001b"
  } else if (val === "LEAVE") {
    return "#aaaaaa"
  } else {
    return ""
  }
}
export default {
  components: {
    ElButton,
    ElForm,
    ElFormItem,
    ElSelect,
    ElOption,
    ElTable,
    ElTableColumn,
    ElPagination,
    ElDatePicker,
    ElInput,
    "kade-result-details": details,
    "kade-dept-select-tree": deptSelectTree,
    'kade-linkage-select': linkageSelect
  },
  setup() {
    const resultList = useDict('DORM_ATTENDANCE_RESULT')
    const state = reactive({
      form: {
        pageSize: 10,
        currentPage: 1
      },
      total: 0,
      data: [],
      requestDate: [],
      selectRow: "",
      dialogVisible: false,
      periodIdList: '',
      loading: false,
    });
    const linkageChange = (val) => {
      state.form = { ...state.form, ...val }
    }
    const getPeriodId = async () => {
      let { data } = await attendancePeriodList()
      state.periodIdList = data
    }
    const getList = async () => {
      if (state.requestDate && state.requestDate.length) {
        state.form.beginDate = dateStr(state.requestDate[0])
        state.form.endDate = dateStr(state.requestDate[1])
      } else {
        delete state.form.beginDate
        delete state.form.endDate
      }
      state.loading = true
      try {
        let { data: { list, total } } = await attendanceResultList(state.form)
        state.data = list
        state.total = total
        state.loading = false
      }
      catch {
        state.loading = false
      }
    }
    const details = (row) => {
      console.log(row)
      state.selectRow = row,
        state.dialogVisible = true
    };
    const close = () => {
      state.dialogVisible = false
    }
    const handleSizeChange = (val) => {
      state.form.currentPage = 1
      state.form.pageSize = val
      getList()
    };
    const handleCurrentChange = (val) => {
      state.form.currentPage = val
      getList()
    };
    const handleSearch = () => {
      getList()
    };
    const handleReset = () => {
      state.form = {
        pageSize: 10,
        currentPage: 1
      }
      state.requestDate = []
      getList()
    };
    const exportClick = async () => {
      let res = await exportAttendanceResult(state.form)
      let url = window.URL.createObjectURL(
        new Blob([res], { type: 'application/vnd.ms-excel' })
      )
      let link = document.createElement('a')
      link.style.display = 'none'
      link.href = url
      link.setAttribute('download', '考勤结果表.xlsx')
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    };
    onMounted(() => {
      getList()
      getPeriodId()
    })
    return {
      colorFnc,
      linkageData,
      state,
      resultList,
      details,
      exportClick,
      close,
      handleSizeChange,
      handleCurrentChange,
      handleSearch,
      handleReset,
      linkageChange
    };
  },
};
</script>

<style lang="scss" scoped>
.green :hover {
  text-decoration: underline;
}

:deep(.el-select .el-input__inner) {
  width: 193px;
}

:deep(.el-icon-time:before) {
  display: none;
}

:deep(.el-dialog__header) {
  border-bottom: 1px solid #eeeeee;
}

:deep(.el-dialog__footer) {
  text-align: center;
  border: 0;
}

:deep([data-v-48e7942d] .el-input--mini .el-input__inner) {
  width: 182px;
}

:deep(.el-pagination__editor.el-input .el-input__inner) {
  width: 46px;
}

:deep(.el-dialog) {
  border-radius: 8px;
  padding-bottom: 10px;
}

:deep(.el-pagination .el-select .el-input .el-input__inner) {
  width: 100px;
}

:deep(.el-table__row) {
  td {
    padding: 0;

  }
  .cell{
    padding:0
  }
}

.attendanceResult {
  width: 100%;
  height: 47px;
  line-height: 47px;
  color:#fff
}
</style>