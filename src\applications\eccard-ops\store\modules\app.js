import { markRaw, defineAsyncComponent } from 'vue';
import { getDictionary } from '@/applications/unified_portal/api';
import { getTenantList } from '@/applications/eccard-ops/api';
import { 
	actions as mixinActions,
	state as mixinState,
	getters as mixinGettgers,
} from '@/utils/tab.mixin';
const dictionary = localStorage.getItem(`kade_cache_dictionary`);
const state = {
  ...mixinState,
  dictionary: dictionary ? JSON.parse(dictionary) : [],
  applyTypes: [],
  topTenants: [],
	componentMap: {
		tenant: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "tenantinfo" */ '../../views/tenant'))),
		menu: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "tenantaudit" */ '../../views/menu'))),
    //日志
		exceptionLog: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "types" */ '../../views/log/exceptionLog'))),
		loginLog: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "types" */ '../../views/log/loginLog'))),
		operateLog: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "types" */ '../../views/log/operateLog'))),
	},
	customMenus: [],    
};
const mutations = {
	updateState(state, { key, payload }) { 
		state[key] = payload;
	},
};
const actions = {
  ...mixinActions,
  async loadDictionary({ commit }) {
    const { data } = await getDictionary();
    localStorage.setItem(`kade_cache_dictionary`, JSON.stringify(data));
    commit('updateState', { key: 'dictionary', payload: data });
  },
  async loadTopTenants({ commit }, isForce = false) {
    let cache = localStorage.getItem(`${CONFIG.CACHE_PREFIX}top_tenants`);
    if (cache && !isForce) {
      cache = JSON.parse(cache);
    } else {
      const { data } = await getTenantList();
      cache = data;
      localStorage.setItem(`${CONFIG.CACHE_PREFIX}top_tenants`, JSON.stringify(cache));
    }
    commit('updateState', {
      key: 'topTenants',
      payload: cache,
    });
  }
};

const getters = {
  ...mixinGettgers,
}

export default {
	namespaced: true,
	state,
	mutations,
	actions,
  getters,
}
