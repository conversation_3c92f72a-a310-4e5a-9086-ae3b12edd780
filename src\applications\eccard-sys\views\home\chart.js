import * as echarts from 'echarts';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
} from 'echarts/charts';
import {
  LegendComponent,
  TooltipComponent,
  GridComponent,
  AxisPointerComponent,

} from 'echarts/components';
import {
  CanvasRenderer
} from 'echarts/renderers';
import { onMounted, onUnmounted } from 'vue';
echarts.use(
  [ 
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON>vas<PERSON><PERSON>er,
    LegendComponent,
    TooltipComponent,
    GridComponent,
    AxisPointerComponent
  ]
);

const pieOptions = {
  tooltip: {
    trigger: 'item'
  },
  legend: {
    top: '5%',
    left: 'right'
  },
  color: ['#34b2e4', '#065380'],
  series: [
    {
      name: '项目概况',
      type: 'pie',
      radius: ['40%', '70%'],
      avoidLabelOverlap: false,
      label: {
          show: false,
          position: 'center'
      },
      emphasis: {
          label: {
              show: true,
              fontSize: '40',
              fontWeight: 'bold'
          }
      },
      labelLine: {
          show: false
      },
      data: [
          {value: 1048, name: '正常'},
          {value: 735, name: '异常'},
      ]
    }
  ]  
};

const barOptions = {
  legend: {
    top: 0,
    left: 'right',
  },
  grid: {
    left: 30,
    top: 40,
    bottom: 20,
    right: 0,
  }, 
  tooltip: {
    show: true,
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    },
  },  
  xAxis: [
    {
      type: 'category',
      axisTick: {show: false},
      data: ['2012', '2013', '2014', '2015', '2016'],
      axisLabel: {
        color: '#777'
      }
    }
  ],
  yAxis: [
    {
      type: 'value'
    }
  ],
  color: ['#365b7c', '#966480', '#d06f83'],
  series: [
    {
      name: 'CPU告警',
      type: 'bar',
      barGap: 0,
      barWidth: 20,
      data: [320, 332, 301, 334, 390]
    },
    {
      name: '内存告警',
      type: 'bar',
      barWidth: 20,
      data: [220, 182, 191, 234, 290]
    },
    {
      name: '响应告警',
      type: 'bar',
      barWidth: 20,
      data: [150, 232, 201, 154, 190]
    },
  ]  
};

const hozBarOptions = {
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    }
  },
  legend: {
    show: false,
  },
  grid: {
    top: 0,
    left: 40,
    right: 30,
    bottom: 20,
  },
  xAxis: {
    type: 'value',
    boundaryGap: [0, 0.01]
  },
  yAxis: {
    type: 'category',
    data: ['应用1', '应用2', '应用3', '应用4', '应用5', '应用6']
  },
  series: [
    {
      name: '2011年',
      type: 'bar',
      data: [18203, 23489, 29034, 104970, 131744, 630230],
      barWidth: 20,
      itemStyle: {
        color: new echarts.graphic.LinearGradient(
          0, 0, 1, 0,
          [
              {offset: 0, color: '#189df0'},
              {offset: 0.5, color: '#5776dc'},
              {offset: 1, color: '#52488e'}
          ]
        )
      },      
    },
  ]
};



export function useProjectInfo(el) {
  let chart = null;
  const onResize = () => {
    chart.resize();
  }
  onMounted(() => {
    chart = echarts.init(el.value);
    chart.setOption(pieOptions);
    window.addEventListener('resize', onResize);
  });
  onUnmounted(() => {
    window.removeEventListener('resize', onResize);
  });
  return chart;
}

export function useDistributionWarning(el) {
  let chart = null;
  const onResize = () => {
    chart.resize();
  }
  onMounted(() => {
    chart = echarts.init(el.value);
    chart.setOption(barOptions);
    window.addEventListener('resize', onResize);
  });
  onUnmounted(() => {
    window.removeEventListener('resize', onResize);
  });
  return chart;
}

export function useCpuChart(el) {
  let chart = null;
  const onResize = () => {
    chart.resize();
  }
  onMounted(() => {
    chart = echarts.init(el.value);
    chart.setOption(hozBarOptions);
    window.addEventListener('resize', onResize);
  });
  onUnmounted(() => {
    window.removeEventListener('resize', onResize);
  });
  return chart;
}

export function useMemoryChart(el) {
  let chart = null;
  const onResize = () => {
    chart.resize();
  }
  onMounted(() => {
    chart = echarts.init(el.value);
    chart.setOption(hozBarOptions);
    window.addEventListener('resize', onResize);
  });
  onUnmounted(() => {
    window.removeEventListener('resize', onResize);
  });
  return chart;
}