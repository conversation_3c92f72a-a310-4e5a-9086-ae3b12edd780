<template>
  <div class="padding-form-box setBasicParam" v-loading="state.loading">
    <el-form inline size="mini" label-width="200px">
      <el-form-item label="设备音量:">
        <el-input-number :min="0" :max="100" v-model="state.form.baseDetailEntity.dev_volume"></el-input-number>
      </el-form-item>
      <el-form-item label="管理员密码:">
        <el-input oninput="value=value.replace(/[^0-9.]/g,'')" :minlength="4" :maxlength="4" v-model="state.form.baseDetailEntity.dev_password" placeholder="请输入4位数字"></el-input>
      </el-form-item>
      <el-form-item label="背光延时:">
        <el-input-number :min="0" :max="100" v-model="state.form.baseDetailEntity.dev_backlit"></el-input-number>
      </el-form-item>
      <el-form-item label="消费模式:">
        <el-select v-model="state.form.baseDetailEntity.dev_qbjyms">
          <el-option v-for="item in consumeOption" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="是否启用打印:">
        <el-select v-model="state.form.baseDetailEntity.dev_print">
          <el-option v-for="item in option" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="是否启用用户密码:">
        <el-select v-model="state.form.baseDetailEntity.dev_user_password">
          <el-option v-for="item in option" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="是否统计日消费次:">
        <el-select v-model="state.form.baseDetailEntity.dev_rxfctj">
          <el-option v-for="item in option" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="是否统计餐消费额:">
        <el-select v-model="state.form.baseDetailEntity.dev_cxfetj">
          <el-option v-for="item in option" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="是否统计餐消费次:">
        <el-select v-model="state.form.baseDetailEntity.dev_cxfctj">
          <el-option v-for="item in option" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="是否统计日消费额:">
        <el-select v-model="state.form.baseDetailEntity.dev_rxfetj">
          <el-option v-for="item in option" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="允许使用主钱包交易:">
        <el-select v-model="state.form.baseDetailEntity.dev_zqbjyms">
          <el-option v-for="item in option2" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="允许使用补助交易:">
        <el-select v-model="state.form.baseDetailEntity.dev_sudsidy">
          <el-option v-for="item in option2" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="允许使用次数交易:">
        <el-select v-model="state.form.baseDetailEntity.dev_ci">
          <el-option v-for="item in option2" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="脱机交易限制(元):">
        <el-input-number v-model="state.form.baseDetailEntity.dev_uolCount" :min="0" :max="40000"></el-input-number>
      </el-form-item>
      <el-form-item label="同步时间间隔(秒):">
        <el-input-number :min="0" :max="100" v-model="state.form.baseDetailEntity.dev_scardtime"></el-input-number>
      </el-form-item>
      <el-form-item label="钱包最大余额(元):">
        <el-input-number :min="1" :max="160000" v-model="state.form.baseDetailEntity.dev_maxbal"></el-input-number>
      </el-form-item>
      <el-form-item label="补助现金交易比例(%):">
        <el-input-number :min="0" :max="99" v-model="state.form.baseDetailEntity.dev_xper"></el-input-number>
      </el-form-item>
      <el-form-item label="是否启动权限名单:">
        <el-select v-model="state.form.baseDetailEntity.dev_useauth">
          <el-option v-for="item in option" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="黑名单锁卡:">
        <el-select v-model="state.form.baseDetailEntity.dev_blacklock">
          <el-option v-for="item in option2" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
    </el-form>
  </div>
  <div style="text-align: center">
    <el-button size="mini" @click="beforeClose()">取消</el-button>
    <el-button size="mini" type="primary" @click="saveClick()">保存</el-button>
  </div>
</template>

<script>
import {
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElInputNumber,
  ElMessage,
  ElOption,
  ElSelect,
} from "element-plus";
import { onMounted, reactive } from "vue";
import { useStore } from "vuex";
import { saveBase } from "@/applications/eccard-iot/api";
const option = [
  {
    value: 1,
    label: "是",
  },
  {
    value: 0,
    label: "否",
  },
];
const option2 = [
  {
    value: 1,
    label: "允许",
  },
  {
    value: 0,
    label: "禁止",
  },
];
const consumeOption = [
  {
    value: 1,
    label: "单价模式",
  },
  {
    value: 2,
    label: "定值模式",
  },
  {
    value: 3,
    label: "编号模式",
  },
  {
    value: 4,
    label: "菜品模式",
  },
  {
    value: 5,
    label: "免费模式",
  },
  {
    value: 0,
    label: "禁用",
  },
];
const defaultParamsFnc = () => {
  return {
    dev_volume: 80, //设备音量
    dev_password: "0000", //管理员密码
    dev_backlit: 10, //背光延时
    dev_qbjyms: 1, //消费模式
    dev_print: 0, //是否启用打印
    dev_user_password: 0, //是否启用用户密码
    dev_rxfctj: 1, //是否统计日消费次
    dev_cxfetj: 1, //是否统计餐消费额
    dev_cxfctj: 1, //是否统计餐消费次
    dev_rxfetj: 1, //是否统计日消费额
    dev_zqbjyms: 1, //允许使用主钱包交易
    dev_sudsidy: 0, //允许使用补助交易
    dev_ci: 0, //允许使用次数交易
    dev_uolCount: 5000, //脱机交易限制
    dev_scardtime: 3, //同步时间间隔(秒)
    dev_maxbal: 20000, //钱包最大余额(元)
    dev_xper: 0, //补助现金交易比例(%)
    dev_useauth: 0, //是否启动权限名单
    dev_blacklock: 0, //黑名单锁卡
  };
};

export default {
  components: {
    ElInputNumber,
    ElForm,
    ElFormItem,
    ElInput,
    ElSelect,
    ElOption,
    ElButton,
  },
  setup() {
    const store = useStore();
    const state = reactive({
      loading: false,
      form: {
        baseDetailEntity: defaultParamsFnc(),
        id: "",
        paramId: "",
        paramType: "base",
      },
    });

    const saveClick = async () => {
      state.loading = true;
      state.form.paramId = store.state.deviceParameters[store.state.app.activeTab].selectRow.id;
      let { message, code } = await saveBase(state.form);
      if (code === 0) {
        setTimeout(() => {
          ElMessage.success(message);
          store.dispatch("deviceParameters/getParams", store.state.deviceParameters[store.state.app.activeTab].selectRow.id);
          state.loading = false;
        }, 1500);
      }
    };

    const beforeClose = () => {
      store.commit("deviceParameters/updateState", {
        key: store.state.app.activeTab,
        childKey: "isSetParams",
        payload: false,
      });
    };
    onMounted(() => {
      let val=store.state.deviceParameters[store.state.app.activeTab].detailsParams
      if (val && val.length) {
        let baseList = val.filter((item) => item.paramType == "base");
        if (baseList.length) {
          state.form.id = baseList[0].id;
          for (let key in state.form.baseDetailEntity) {
            state.form.baseDetailEntity[key] = JSON.parse(
              baseList[0].paramContent
            )[key];
          }
        } else {
          state.form.id = "";
          state.form.baseDetailEntity = defaultParamsFnc();
        }
      } else {
        //设置默认选项
        state.form.id = "";
        state.form.baseDetailEntity = defaultParamsFnc();
      }
    });
    return {
      option,
      option2,
      consumeOption,
      state,
      saveClick,
      beforeClose,
    };
  },
  name: "basicParam",
};
</script>

<style lang="scss" scoped>
.setBasicParam {
  border: 1px solid #dcdfe6;
  border-radius: 5px;
  margin-bottom: 10px;
}
:deep(.el-form-item__content) {
  width: 200px;
  .el-input-number {
    width: 100%;
  }
  .el-select {
    width: 100% !important;
  }
}
</style>
