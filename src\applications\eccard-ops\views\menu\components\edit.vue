<template>
  <kade-modal v-bind="attrs" :modelValue="modelValue" @update:modelValue="update">
    <el-form ref="formRef" label-width="120px" :rules="rules" :model="state.model" size="small">
      <el-form-item label="上级菜单" prop="parentId">
        <kade-select-tree :value="state.model.parentId" valueKey="id" :dataTreeList="menuTreeList" :props="treeProps"
          :multiple="false" @valueChange="val => state.model.parentId = val.id" />
      </el-form-item>
      <el-form-item label="菜单名称" prop="menuName">
        <el-input placeholder="请输入" v-model="state.model.menuName" />
      </el-form-item>
      <el-form-item label="菜单英文名称" prop="menuEnName">
        <el-input placeholder="请输入" v-model="state.model.menuEnName" />
      </el-form-item>
      <el-form-item label="图标" prop="menuIcon">
        <kade-select-icon v-model="state.model.menuIcon" />
      </el-form-item>
      <el-form-item label="排序" prop="menuSort">
        <el-input-number :step="1" :min="1" v-model="state.model.menuSort"></el-input-number>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="state.model.status">
          <el-radio v-for="item in status" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="组件路径" prop="component">
        <el-input :disabled="!!state.model.menuId" placeholder="请输入" v-model="state.model.component" />
      </el-form-item>
      <el-form-item label="路由路径" prop="routePath">
        <el-input :disabled="!!state.model.menuId" placeholder="请输入" v-model="state.model.routePath" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button icon="el-icon-circle-close" @click="handleCancel" size="small">取消</el-button>
      <el-button icon="el-icon-circle-check" :loading="state.loading" @click="handleSubmit" size="small" type="primary">
        保存</el-button>
    </template>
  </kade-modal>
</template>
<script>
import { reactive, computed, watch, ref, nextTick } from 'vue'
import { useDict } from "@/hooks/useDict";
import { addMenu, editMenu } from '@/applications/eccard-ops/api';

import { ElForm, ElFormItem, ElInput, ElRadio, ElRadioGroup, ElButton, ElInputNumber, ElMessage } from "element-plus"
import Modal from "@/components/modal";
import SelectIcon from "@/components/selectIcon";
import selectTree from "@/components/tree/selectTree";
const rules = {
  parentId: [
    {
      required: true,
      message: "请选择上级菜单",
      trigger: "change",
    },
  ],
  menuName: [
    {
      required: true,
      message: "请输入菜单名称",
    },
    {
      max: 30,
      message: "菜单名称长度不得超过30字符",
    },
  ],
  menuEnName: [
    {
      required: true,
      message: "请输入菜单英文名称",
    },
    {
      max: 50,
      message: "菜单英文名称长度不得超过50字符",
    },
  ],
  menuIcon: [
    {
      required: true,
      message: "请选择菜单图标",
      trigger: "change",
    },
  ],
  component: [
    {
      required: true,
      message: "请输入组件路径",
    },
  ],
  routePath: [
    {
      required: true,
      message: "请输入路由路径",
    },
  ],
};
const treeProps = {
  children: "children",
  label: "menuName",
}
export default {
  components: {
    "kade-modal": Modal,
    "kade-select-icon": SelectIcon,
    "kade-select-tree": selectTree,
    ElForm,
    ElFormItem,
    ElInput,
    ElRadio,
    ElRadioGroup,
    ElButton,
    ElInputNumber
  },
  emits: ["update:modelValue", "change"],
  props: {
    title: {
      type: String,
      default: "",
    },
    modelValue: {
      type: Boolean,
      default: false,
    },
    menu: {
      type: Object,
      default: null,
    },
    type: {
      type: String,
      default: "",
    },
    menuList: {
      type: Array,
      default: null
    }
  },
  setup(props, context) {
    const formRef = ref(null)
    const status = useDict("SYS_ENABLE");
    const state = reactive({
      loading: false,
      model: {}
    })
    const attrs = computed(() => {
      // eslint-disable-next-line no-unused-vars
      const { modelValue, user, ...attrs } = props;
      return attrs;
    });

    const menuTreeList = computed(() => {
      let list = [{
        menuName: '无',
        id: 0,
        children: []
      }]
      list[0].children = props.menuList
      console.log(list);
      return list
    })

    watch(() => props.menu, val => {
      console.log(props.menu);
      if (props.type == 'add') {
        nextTick(() => {
          state.model = { parentId: { ...val }.id, status: "ENABLE_TRUE" }
        })
      } else {
        nextTick(() => {
          let { id, parentId, menuName, menuEnName, menuIcon, menuSort, status, component, routePath } = { ...val }
          state.model = {
            id,
            parentId,
            menuName,
            menuEnName,
            menuIcon,
            menuSort,
            status,
            component,
            routePath
          }
        })
      }
    },
    )
    const handleSubmit = () => {
      formRef.value.validate(async valid => {
        if (valid) {
          state.loading = true
          let fn = props.type == 'add' ? addMenu : editMenu
          let { code, message } = await fn(state.model)
          if (code === 0) {
            ElMessage.success(message)
            context.emit("save")
            context.emit("update:modelValue", false);
          }
          state.loading = false
        } else {
          return false
        }
      })
    }
    const handleCancel = () => {
      formRef.value?.resetFields();
      context.emit("update:modelValue", false);
    }
    const update = (v) => {
      context.emit("update:modelValue", v);
    };
    return {
      rules,
      treeProps,
      status,
      formRef,
      state,
      menuTreeList,
      attrs,
      handleSubmit,
      handleCancel,
      update
    }
  }
}
</script>