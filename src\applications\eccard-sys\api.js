import request from '@/service';

/**
 * 获取系统角色
 */
export function getRoleForPage(params) {
  return request.get('/eccard-sys/sysRole/page', { params });
}
/**
 * 获取系统角色(不分页)
 */
export function getRole(params) {
  return request.get('/eccard-sys/sysRole/table', { params });
}
/**
 * 新增角色
 */
export function addRole(params) {
  return request.post('/eccard-sys/sysRole', params);
}


/**
 * 用户列表(分页)
 */
export function getSystemUserForPage(params) {
  return request.get('/eccard-sys/sysUser/page', { params });
}

/**
 * 编辑角色
 */
export function editRole(params) {
  return request.put('/eccard-sys/sysRole', params);
}

/**
 * 新增用户
 */
export function addSysUser(params) {
  return request.post('/eccard-sys/sysUser', params);
}
/**
 * 编辑用户
 */
export function editSysUser(params) {
  return request.put('/eccard-sys/sysUser', params);
}
/**
 * 删除用户
 */
export function delSysUser(params) {
  return request.delete(`/eccard-sys/sysUser/${params}`);
}
/**
 * 重置用户密码
 */
export function reSetPwd(params) {
  return request.patch('/eccard-sys/sysUser/reSetPwd', params);
}
/**
 * 修改用户密码
 */
export function updatePwd(params) {
  return request.patch('/eccard-sys/sysUser/updatePwd', params);
}

/**
 * 门户修改用户信息
 */
export function updateMe(params) {
  return request.put('/eccard-sys/sysUser/updateMe', params);
}

/**
 * 获取角色拥有的菜单id集合
 */
export function getRoleMenuList(params) {
  return request.get(`/eccard-sys/sysRole/${params}/roleMenuList`);
}
/**
 * 保存角色菜单权限
 */
export function saveRoleMenu(params) {
  return request.put(`/eccard-sys/sysRole/${params.roleId}/saveRoleMenu`, { ids: params.menuIds });
}

/**
 * 获取角色拥有的区域列表
 */
export function roleAreaList(params) {
  return request.get(`/eccard-sys/sysRole/${params}/roleAreaList`);
}
/**
 * 保存角色区域权限
 */
export function saveRoleArea(params) {
  return request.put(`/eccard-sys/sysRole/${params.roleId}/saveRoleArea`, { ids: params.areaIds });
}
/**
 * 获取角色拥有的机构列表(树)
 */
export function roleDeptList(params) {
  return request.get(`/eccard-sys/sysRole/${params}/roleDeptList`);
}

/**
 * 获取角色拥有的机构列表
 */
 export function getAllRoleDeptList(params) {
  return request.get(`/eccard-sys/sysRole/${params}/getAllRoleDeptList`);
}


/**
 * 保存角色组织机构权限
 */
export function saveRoleDept(params) {
  return request.put(`/eccard-sys/sysRole/${params.roleId}/saveRoleDept`, { ids: params.deptIds });
}

/**
 * 获取角色拥有的权限列表
 */
export function rolePermissionList(params) {
  return request.get(`/eccard-sys/sysRole/${params.roleId}/${params.menuId}/rolePermissionList`);
}
/**
 * 保存角色菜单按钮权限
 */
export function saveRolePermission(params) {
  return request.put(`/eccard-sys/sysRole/${params.roleId}/${params.menuId}/saveRolePermission`, { ids: params.ids });
}


/**
 * 获取角色拥有的机构-年级-班级列表
 */
export function userDeptGradeClassList(params) {
  return request.get(`/eccard-sys/sysPermission/userDeptGradeClassList/${params}`);
}
/**
 * 获取角色拥有的机构-年级-班级列表
 */
export function userDeptGradeList(params) {
  return request.get(`/eccard-sys/sysPermission/userDeptGradeList`, { params });
}

/**
 * 获取角色拥有的机构-年级树
 */
 export function userDeptGradeTree(params) {
  return request.get(`/eccard-sys/sysPermission/userDeptGradeTree`, { params });
}
