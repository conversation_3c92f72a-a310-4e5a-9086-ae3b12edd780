

const state = {
  isRecord: { //设备更换记录模态框显示隐藏数据
    accessAIO: { rowData: {}, isShow: false, deviceType: "FINGER_PRINT" },
    visitorDevice: { rowData: {}, isShow: false, deviceType: "INTELLIGENT_VISITOR_TERMINAL" },
    camera: { rowData: {}, isShow: false, deviceType: "CAMERA_DEVICE" },
    doorLock: { rowData: {}, isShow: false, deviceType: "DOORLOCK" },
    // multimediaAttendanceDevice: { rowData: {}, isShow: false },
    faceAnalysisDevice: { rowData: {}, isShow: false,deviceType: "FACE_RECOGNITION_ANALYSIS_TERMINAL" },
    attendanceDevice: { rowData: {}, isShow: false,deviceType: "MULTIMEDIA_ATTENDANCE_TERMINAL" },
    bigScreenDevice: { rowData: {}, isShow: false,deviceType: "INTELLIGENT_LARGESCREEN_TERMINAL" },
    meetingSignInDevice: { rowData: {}, isShow: false,deviceType: "CONFERENCE_TERMINAL" },
    electronicClassDevice: { rowData: {}, isShow: false,deviceType: "ELECTRONIC_CARD_TERMINAL" },
    powerController: { rowData: {}, isShow: false, deviceType: "POWER_CONTROLLER" },
    icCardDevice: { rowData: {}, isShow: false, deviceType: "ICCARD_DEVICE" },
    beforeMachine: { rowData: {}, isShow: false, deviceType: "BEFORE_MACHINE" },
    passwordServer: { rowData: {}, isShow: false, deviceType: "PASSWORD_SERVER" },
    financeEncryption: { rowData: {}, isShow: false, deviceType: "FINANCE_ENCRYPTION" },
    device: { rowData: {}, isShow: false, deviceType: "DEVICE" },
  },
  faceDict: {}
};
const mutations = {
  updateState(state, { key, payload }) {
    state.isRecord[key] = payload;
  },
  updateState1(state, { key, payload }) {
    state[key] = payload;
  },
};
const actions = {

};
export default {
  namespaced: true,
  state,
  mutations,
  actions
}
