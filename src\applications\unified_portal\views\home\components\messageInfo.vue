<template>
  <kade-modal v-bind="attrs" :modelValue="modelValue" @update:modelValue="update">
    <div class="message-body" v-loading="loading">
      {{ info.messageContent }}
    </div>
    <template #footer>
      <el-button @click="cancel" size="small">关闭</el-button>
    </template>
  </kade-modal>
</template>
<script>
import Modal from '@/components/modal';
import { computed, ref, watch } from 'vue';
import { ElButton } from 'element-plus';
import { getMessageDetailsByMsgId } from '@/applications/unified_portal/api';
export default {
  emits: ['update:modelValue', 'change'],
  props: {
    title: {
      type: String,
      default: ''
    },
    modelValue: {
      type: Boolean,
      default: false,
    },
    id: {
      type: [String, Number],
      default: ''
    }
  },
  components: {
    'kade-modal': Modal,
    'el-button': ElButton,
  },
  setup(props, context) {
    const loading = ref(false);
    const info = ref({});
    const cancel = () => {
      context.emit('update:modelValue', false);
    }
    const attrs = computed(() => {
      // eslint-disable-next-line no-unused-vars
      const { modelValue, id, ...attrs } = props;
      return attrs;
    });
    const update = (v) => {
      context.emit('update:modelValue', v);
    }
    const loadData = async () => {
      try {
        loading.value = true;
        const { data } = await getMessageDetailsByMsgId({ msgId: props.id });
        info.value = data;
      } catch(e) {
        throw new Error(e.message);
      } finally {
        loading.value = false;
      }
    }
    watch(() => props.modelValue, (n) => {
      if(n && props.id) {
        loadData();
      }
    });
    return {
      attrs,
      update,
      cancel,
      loading,
      info,
    }
  }
}
</script>
<style lang="scss" scoped>
.message-body{
  min-height: 200px;
}
</style>