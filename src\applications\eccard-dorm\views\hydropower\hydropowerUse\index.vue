<template>
        <kade-route-card>
            <kade-table-filter @search="handleSearch" @reset="handleRest">
                <el-form label-width="90px" inline size="mini">
                    <el-form-item label="区域">
                        <kade-area-select-tree placeholder="全部" style="width: 100%" :value="state.form.areaPath" valueKey="areaPath" :multiple="true" @valueChange="(val)=>state.form.areaPath=val"/>
                    </el-form-item>
                    <el-form-item label="楼栋类型">
                        <el-select v-model="state.form.b" placeholder="全部">
                            <el-option v-for="(item,index) in 5" :key="index" :label="item" :value="item" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="楼栋">
                        <el-select v-model="state.form.b" placeholder="全部">
                            <el-option v-for="(item,index) in 5" :key="index" :label="item" :value="item" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="单元">
                        <el-select v-model="state.form.b" placeholder="全部">
                            <el-option v-for="(item,index) in 5" :key="index" :label="item" :value="item" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="楼层">
                        <el-select v-model="state.form.b" placeholder="全部">
                            <el-option v-for="(item,index) in 5" :key="index" :label="item" :value="item" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="房间">
                        <el-input v-model="state.form.a" placeholder="请输入"></el-input>
                    </el-form-item>
                    <el-form-item label="汇总年月">
                        <el-date-picker v-model="state.requsetDate" type="month" placeholder="请选择" />
                    </el-form-item>
                </el-form>
            </kade-table-filter>
            <div class="total-box">
                <div class="total-item">
                    <span>用水总量：</span>
                    <div></div>
                </div>
                <div>
                    <span>用电总量：</span>
                    <div></div>
                </div>
            </div>
            <kade-table-wrap title="记录列表">
                <el-table border :data="state.dataList">
                    <el-table-column show-overflow-tooltip v-for="(item,index) in column" :key="index" :label="item.label" :prop="item.prop" :width="item.width" align="center"></el-table-column>
                </el-table>
                <div class="pagination">
                    <el-pagination 
                        v-model:currentPage="state.form.currentPage" 
                        v-model:page-size="state.form.pageSize" 
                        :page-sizes="[10, 20, 30, 40]" 
                        :small="small" 
                        background 
                        layout="total, sizes, prev, pager, next, jumper" 
                        :total="state.total" 
                        @size-change="handleSizeChange" 
                        @current-change="handleCurrentChange" />
                </div>
            </kade-table-wrap>
        </kade-route-card>
</template>

<script>
import { reactive } from "@vue/reactivity";
import { monthStr } from "@/utils/date.js";
import areaSelectTree from '@/components/tree/areaSelectTree';
import {
    ElForm,
    ElFormItem,
    ElInput,
    ElSelect,
    ElOption,
    ElDatePicker,
    ElTable,
    ElTableColumn,
    ElPagination,
} from "element-plus";
import { onMounted } from '@vue/runtime-core';

const column=[
    {label:'区域',prop:''},
    {label:'楼栋类型',prop:''},
    {label:'房间',prop:'a',width:'300px'},
    {label:'入住人数',prop:''},
    {label:'汇总年月',prop:''},
    {label:'用水量',prop:''},
    {label:'用电量',prop:''},
]

export default {
    components: {
        ElForm,
        ElFormItem,
        ElInput,
        ElSelect,
        ElOption,
        ElDatePicker,
        ElTable,
        ElTableColumn,
        ElPagination,
        "kade-area-select-tree":areaSelectTree
    },
    setup() {
        const state = reactive({
            form: {
                currentPage:1,
                pageSize:10
            },
            total:0,
            dataList: [{a:'1'}],
            requsetDate: "",
        });
        const getList=()=>{
            if(state.requsetDate){
                state.requsetDate=monthStr(state.requsetDate)
            }else{
                delete state.requsetDate
            }
        };
        const handleSearch = () => {
            getList()
        };
        const handleReset = () => {
            state.form={
                currentPage:1,
                pageSize:10
            }
            state.requsetDate=""
        };
        const handleCurrentChange=(val)=>{
            state.form.currentPage=val
            getList()
        };
        const handlePageChange=(val)=>{
            state.form.currentPage=1,
            state.form.pageSize=val
            getList()
        };
        onMounted(()=>{
            getList();
        })
        return {
            state,
            column,
            handleSearch,
            handleReset,
            handleCurrentChange,
            handlePageChange,
        };
    },
};
</script>

<style lang="scss" scoped>
:deep(.el-input--mini .el-input__inner) {
    width: 182px;
}
.total-box {
    display: flex;
    align-items: center;
    padding: 0 0 15px 10px;
    .total-item {
        margin-right: 60px;
    }
}
:deep(.el-pagination .el-select .el-input .el-input__inner){
    width: 100px;
}
:deep(.el-pagination__editor.el-input .el-input__inner){
    width: 46px;
}
</style>