<template>
  <div class="box">
    <div @mousedown.stop="drag" class="box-item" v-for="(item,index) in state.list" :key="index">{{item}}</div>
  </div>
</template>
<script>
import { reactive } from "@vue/reactivity";
export default {
  setup() {
    const state = reactive({
      list: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
    });

    const drag = (e) => {
      let boxDom = document.querySelector(".box");
      let boxWidth=Number(boxDom.style.width.substring(0, boxDom.style.width - 2))
      let boxHeight=Number(boxDom.style.height.substring(0, boxDom.style.height - 2))
      console.log(boxWidth,boxHeight);
      let itemDom = e.currentTarget;
      let itemDomLeft = Number(
        itemDom.style.left.substring(0, itemDom.style.left.length - 2)
      );
      let itemDomTop = Number(
        itemDom.style.top.substring(0, itemDom.style.top.length - 2)
      );
      let disX = e.clientX;
      let disY = e.clientY;
      document.onmousemove = (target) => {
        console.log(itemDomLeft, itemDomTop);
        itemDom.style.left = itemDomLeft + target.clientX - disX + "px";
        itemDom.style.top = itemDomTop + target.clientY - disY + "px";
       /*  if((itemDomLeft + target.clientX - disX)>boxWidth||(itemDomTop + target.clientY - disY)>boxHeight){
            itemDom.style.left =0
            itemDom.style.top=0
        } */
      };
      document.onmouseup = () => {
        // 鼠标松开后不再移动
        document.onmousemove = null;
        document.onmouseup = null;
      };
    };

    return {
      state,
      drag,
    };
  },
};
</script>
<style lang="scss" scoped>
.box {
  margin: 200px auto;
  width: 900px;
  padding: 50px;
  display: flex;
  justify-content: start;
  flex-wrap: wrap;
  overflow: hidden;
  border: 1px solid #ccc;
  .box-item {
    position: relative;
    text-align: center;
    width: 200px;
    line-height: 100px;
    background: #ccc;
    border-radius: 5px;
    margin: 5px 10px;
  }
}
</style>