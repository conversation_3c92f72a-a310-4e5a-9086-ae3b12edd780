<template>
  <div class="attendance-device padding-box">
    <kade-table-filter @search="handleSearch" @reset="handleReset">
      <el-form inline label-width="100px" size="mini">
        <el-form-item label="类型名称">
          <el-input clearable v-model="state.form.name" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="IP地址">
          <el-input clearable v-model="state.form.ip" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="工作站类型">
          <el-select clearable v-model="state.form.workstationType">
            <el-option v-for="(item,index) in state.workStationTypeList" :key="index" :label="item.typeName" :value="item.typeCode"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="工作站列表">
      <el-table border height="55vh" v-loading="state.loading" :data="state.dataList">
        <el-table-column show-overflow-tooltip prop="name" label="工作站名称" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="workstationTypeName" label="工作站类型" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="ip" label="IP地址" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="mac" label="MAC地址" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="registerTime" label="注册时间" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="status" label="工作站状态" align="center">
          <template #default="scope">

            <el-popover v-model="state.visible" placement="top" :width="160">
              <p style="margin:20px 0">确定{{scope.row.status=='ENABLE_TRUE'?'停用':'启用'}}该工作站？</p>
              <div style="text-align: right; margin: 0">
                <el-button size="small" type="text" @click="state.visible = false">取消</el-button>
                <el-button size="small" type="primary" @click="handleStatus(scope.row)">确定</el-button>
              </div>
              <template #reference>
                <el-switch @click="state.visible=true" disabled v-model="scope.row.status" active-color="#13ce66" inactive-color="#ff4949" active-value="ENABLE_TRUE" inactive-value="ENABLE_FALSE"></el-switch>
              </template>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip prop="updateDate" label="状态更新时间" align="center"></el-table-column>
        <el-table-column label="操作" align="center">
          <template #default="scope">
            <el-button type="text" @click="handleEdit(scope.row,'edit')" size="mini">编辑</el-button>
            <el-button type="text" @click="handleDel(scope.row)" size="mini">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination v-model:currentPage="state.form.pageNum" v-model:page-size="state.form.pageSize" :page-sizes="[10, 20, 30, 40]" background layout="total, sizes, prev, pager, next, jumper" :total="state.total" @size-change="handleSizeChange" @current-change="handleCurrentChange">
        </el-pagination>
      </div>
    </kade-table-wrap>
    <kade-factory-edit :dialogVisible="state.dialogVisible" :data="state.rowData" :type="state.type" @close="close"></kade-factory-edit>
  </div>
</template>

<script>
import {
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElPopover,
  ElSwitch,
  ElTable,
  ElTableColumn,
  ElPagination,
  ElMessage,
  ElMessageBox,
} from "element-plus";
import { reactive, onMounted } from "vue";
import { getWorkStationList, workStationTypeList, workStationEdit, workStationDel } from "@/applications/eccard-iot/api";

import edit from "./components/edit.vue";
export default {
  components: {
    ElButton,
    ElForm,
    ElFormItem,
    ElInput,
    ElSelect,
    ElOption,
    ElTable,
    ElTableColumn,
    ElPagination,
    ElPopover,
    ElSwitch,
    "kade-factory-edit": edit,
  },
  setup() {
    const state = reactive({
      loading: false,
      dialogVisible: false,
      visible: false,
      form: {
        pageNum: 1,
        pageSize: 10
      },
      dataList: [],
      total: 0,
      rowData: {},
      workStationTypeList: []
    });
    const getList = async () => {
      state.loading = true
      try {
        let { data: { list, total } } = await getWorkStationList(state.form)
        state.dataList = list
        state.total = total
        state.loading = false
      }
      catch {
        state.loading = false
      }
    }
    const getWorkStationTypeList = async () => {
      let { data: { list } } = await workStationTypeList()
      state.workStationTypeList = list
    }
    const handleEdit = (row, type) => {
      state.type = type
      state.rowData = row
      state.dialogVisible = true;
    };
    const handleStatus = async (val) => {
      let params={...val}
      if(params.status=="ENABLE_TRUE"){
        params.status="ENABLE_FALSE"
      }else{
        params.status="ENABLE_TRUE"
      }
      try {
        let { message, code } = await workStationEdit(params)
        if (code === 0) {
          ElMessage.success(message)
          getList()
        }
        state.visible = false
      }
      catch {
        state.visible = false
      }
    }
    const handleDel = ({ id }) => {
      ElMessageBox.confirm(`确认删除?`, {
        type: "warning",
        closeOnPressEscape: false,
        closeOnClickModal: false,
      }).then(async () => {
        try {
          const { message, code } = await workStationDel(id);
          if (code === 0) {
            ElMessage.success(message);
            getList()
          }
        } catch (e) {
          throw new Error(e.message);
        }
      });
    }
    const handleSearch = () => {
      getList()
    }
    const handleReset = () => {
      state.form = {
        pageNum: 1,
        pageSize: 10
      }
    }
    const handleSizeChange = (val) => {
      state.form.pageNum = 1
      state.form.pageSize = val
      getList()
    }
    const handleCurrentChange = (val) => {
      state.form.pageNum = val
      getList()
    }
    const close = (val) => {
      if (val) {
        getList()
      }
      state.dialogVisible = false;
    };
    onMounted(() => {
      getWorkStationTypeList()
      getList()
    })
    return {
      state,
      handleEdit,
      handleStatus,
      handleDel,
      handleSearch,
      handleReset,
      handleSizeChange,
      handleCurrentChange,
      close,
    };
  },
};
</script>

<style lang="scss" scoped>
:deep(.el-divider--horizontal) {
  margin: 0;
}
:deep(.el-dialog__footer) {
  text-align: center;
  border: 0;
}
:deep(.el-dialog__header) {
  border-bottom: 1px solid #eeeeee;
}
:deep(.el-dialog) {
  border-radius: 8px;
}
</style>