<template>
  <el-select clearable v-bind="options" :model-value="modelValue" @change="handleChange">
    <template v-for="item in list" :key="item.value">
      <el-option :label="item.label" :value="item.value"></el-option>
    </template>
  </el-select>
</template>
<script>
// 数据字典下拉组件
import { ElSelect, ElOption } from 'element-plus';
import { useDict } from '@/hooks/useDict';
export default {
  components: {
    'el-select': ElSelect,
    'el-option': ElOption,
  },
  emits: ['change', 'update:modelValue'],
  props: {
    type: {
      type: String,
      required: true,
    },
    modelValue: {
      type: [String, Number],
    },
    options: {
      type: Object,
      default: () => ({}),
    }
  },
  setup(props, context) {
    const list = useDict(props.type);
    const handleChange = (v) => {
      context.emit('update:modelValue', v);
      context.emit('change', v);
    }
    return {
      list,
      handleChange,
    }
  }
}
</script>