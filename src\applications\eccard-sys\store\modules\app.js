import { getDictionary, addOrEditApplyType, getAllApplyType } from '@/applications/unified_portal/api';
import { markRaw, defineAsyncComponent } from 'vue';
import { 
	actions as mixinActions,
	state as mixinState,
	getters as mixinGettgers,
} from '@/utils/tab.mixin';

const dictionary = localStorage.getItem(`kade_cache_dictionary`);
const state = {
  ...mixinState,
  dictionary: dictionary ? JSON.parse(dictionary) : [],
  applyTypes: [],
  topTenants: [],
  componentMap: {
    user: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "user" */ '../../views/user'))),
    role: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "role" */ '../../views/role'))),
  },
  customMenus: [],
};
const mutations = {
	updateState(state, { key, payload }) {
		state[key] = payload;
	},
};
const actions = {
  ...mixinActions,
  async loadDictionary({ commit }) {
    const { data } = await getDictionary();
    localStorage.setItem(`kade_cache_dictionary`, JSON.stringify(data));
    commit('updateState', { key: 'dictionary', payload: data });
  },
  async loadApplyType({ commit }) {
    const { data } = await getAllApplyType();
    commit('updateState', { key: 'applyTypes', payload: data });
  },
  async createApplyType({ dispatch }, payload) {
    await addOrEditApplyType(payload);
    dispatch('loadApplyType');
  }  
};

const getters = {
  ...mixinGettgers,
};

export default {
	namespaced: true,
	state,
	mutations,
	actions,
  getters,
}
