<template>
  <div class="attendance-device padding-box">
    <kade-table-filter @search="handleSearch" @reset="handleReset">
      <el-form inline label-width="80px" size="mini">
        <kade-linkage-select :data="linkageData" :value="state.form" @change="linkageChange" />
        <el-form-item label="区域">
          <el-select clearable v-model="state.form.area" placeholder="全部">
            <el-option v-for="(item,index) in 10" :key="index" :label="item" :value="index"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="所属楼栋">
          <el-select clearable v-model="state.form.building"  placeholder="全部">
            <el-option v-for="(item,index) in 10" :key="index" :label="item" :value="index"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="所属单元">
          <el-select clearable placeholder="全部" v-model="state.form.unit">
            <el-option v-for="(item,index) in 10" :key="index" :label="item" :value="index"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="设备类型">
           <el-select clearable placeholder="全部" v-model="state.form.type">
            <el-option v-for="(item,index) in 10" :key="index" :label="item" :value="index"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="设备机号">
          <el-input  v-model="state.form.no" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="设备名称">
          <el-input v-model="state.form.name" placeholder="请输入"></el-input>
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="考勤设备列表">
      <template #extra>
        <el-button
          icon="el-icon-plus"
          type="success"
          size="mini"
          @click="edit()"
          >新增</el-button
        >
      </template>
      <el-table border :data="[1]">
        <el-table-column prop="model" label="设备型号" align="center"></el-table-column>
        <el-table-column prop="area" label="所属区域" align="center"></el-table-column>
        <el-table-column prop="building" label="所属楼栋" align="center"></el-table-column>
        <el-table-column prop="unit" label="所属单元" align="center"></el-table-column>
        <el-table-column prop="no" label="设备机号" align="center"></el-table-column>
        <el-table-column prop="name" label="设备名称" align="center"></el-table-column>
        <el-table-column prop="type" label="设备类型" align="center"></el-table-column>
        <el-table-column prop="position" label="设备位置" align="center"></el-table-column>
        <el-table-column label="操作" align="center">
          <template #default="scope">
            <el-button type="text" @click="del(scope.row)" size="mini">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination
          v-model:currentPage="currentPage4"
          v-model:page-size="pageSize4"
          :page-sizes="[10, 20, 30, 40]"
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="100"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </div>
    </kade-table-wrap>
    <kade-device-edit
      :dialogVisible="state.dialogVisible"
      @close="close()"
    ></kade-device-edit>
  </div>
</template>

<script>
import {
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElTable,
  ElTableColumn,
  ElPagination,
} from "element-plus";
import { reactive } from "@vue/reactivity";
import edit from "./components/edit.vue";
import linkageSelect from '@/applications/eccard-dorm/components/linkageSelect'
const linkageData={
  area:{label:'区域',valueKey:'areaPath',key:'areaPath'},
  building:{label:'楼栋',valueKey:'buildId'},
  unit:{label:'单元',valueKey:'unitNum'}
}
export default {
  components: {
    ElButton,
    ElForm,
    ElFormItem,
    ElInput,
    ElSelect,
    ElOption,
    ElTable,
    ElTableColumn,
    ElPagination,
    "kade-device-edit": edit,
    "kade-linkage-select":linkageSelect
  },
  setup() {
    const state = reactive({
      dialogVisible: false,
      form:{},
    });
    const linkageChange=(val)=>{
      state.form={...state.form,...val}
    }
    const edit = (row) => {
      console.log(row);
      state.dialogVisible = true;
    };
    const close = () => {
      state.dialogVisible = false;
    };
    return {
      state,
      edit,
      close,
      linkageData,
      linkageChange,
    };
  },
};
</script>

<style lang="scss" scoped>
:deep(.el-divider--horizontal) {
  margin: 0;
}
:deep(.el-dialog__footer) {
  text-align: center;
  border: 0;
}
:deep(.el-dialog__header) {
  border-bottom: 1px solid #eeeeee;
}
:deep(.el-dialog) {
  border-radius: 8px;
}
:deep(.el-form-item__content .el-input__inner){
  width: 160px;
}
</style>