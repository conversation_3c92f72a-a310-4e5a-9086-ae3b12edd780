<template>
  <div>
    <el-dialog :model-value="isDetails" width="90%" :before-close="beforeClose" :close-on-click-modal="false">
      <template #title>
        <div class="dialog-title">
          <div class="detail-title">现金发放详情</div>
          <div class="detail-status" :style="{
            color:
              statusColor(row.auditStatus) &&
              statusColor(row.auditStatus).color,
            background:
              statusColor(row.auditStatus) &&
              statusColor(row.auditStatus).background,
          }">
            当前状态：{{
                statusColor(row.auditStatus) && statusColor(row.auditStatus).label
            }}
          </div>
        </div>
      </template>
      <div>
        <kade-table-wrap title="审核信息" v-if="
          row.auditStatus == 'SYS_AUDIT_PASSED' ||
          row.auditStatus == 'SYS_AUDIT_FAIL'
        ">
          <el-divider></el-divider>
          <div class="padding-form-box">
            <el-form inline size="mini" label-width="120px">
              <el-form-item label="审核人员：">
                <el-input :model-value="row.auditPerson" disabled></el-input>
              </el-form-item>
              <el-form-item label="审核时间：">
                <el-input :model-value="timeStr(row.auditTime)" disabled></el-input>
              </el-form-item>
            </el-form>
            <el-form size="mini" label-width="120px">
              <el-form-item label="备注：:">
                <el-input type="textarea" disabled :model-value="row.auditRemark"></el-input>
              </el-form-item>
            </el-form>
          </div>
        </kade-table-wrap>

        <kade-table-wrap title="充值信息">
          <el-divider></el-divider>
          <div class="padding-form-box">
            <el-form inline size="mini" label-width="120px">
              <el-form-item label="项目名称：">
                <el-input :model-value="row.projectName" readonly></el-input>
              </el-form-item>
              <el-form-item label="充值类型：">
                <el-input :model-value="row.walletName" readonly></el-input>
              </el-form-item>
              <el-form-item label="充值方式：">
                <el-input :model-value="row.tradeType" readonly></el-input>
              </el-form-item>
              <el-form-item label="发放方式：">
                <el-input :model-value="
                  row.grantModeName
                " readonly></el-input>
              </el-form-item>
              <el-form-item label="充值时间：">
                <el-input :model-value="timeStr(row.createTime)" readonly></el-input>
              </el-form-item>
              <el-form-item label="每人充值金额：">
                <el-input :model-value="row.totalAmount / row.totalPersonCount" readonly></el-input>
              </el-form-item>
              <el-form-item label="总充值人数：">
                <el-input :model-value="row.totalPersonCount" readonly></el-input>
              </el-form-item>
              <el-form-item label="总充值金额：">
                <el-input :model-value="row.totalAmount" readonly></el-input>
              </el-form-item>
              <el-form-item label="导入人员：">
                <el-input :model-value="row.createUserName" readonly></el-input>
              </el-form-item>
            </el-form>
            <el-form size="mini" label-width="120px">
              <el-form-item label="发放说明：">
                <el-input type="textarea" readonly :model-value="row.grantRemark"></el-input>
              </el-form-item>
            </el-form>
          </div>
        </kade-table-wrap>
        <kade-table-wrap title="人员清单">
          <el-table style="width: 100%" :data="state.personList" v-loading="false" highlight-current-row border stripe>
            <el-table-column label="用户编号" prop="userCode" align="center"></el-table-column>
            <el-table-column label="姓名" prop="userName" align="center"></el-table-column>
            <el-table-column label="组织结构" prop="deptName" align="center"></el-table-column>
            <el-table-column label="身份类别" prop="postName" align="center"></el-table-column>
            <el-table-column label="充值金额" prop="rechargeAmount" align="center"></el-table-column>
            <el-table-column label="到账状态" prop="" align="center"></el-table-column>
          </el-table>
          <div class="pagination">
            <el-pagination background :current-page="state.form.currentPage" :page-size="state.form.pageSize"
              layout="total, sizes, prev, pager, next, jumper" :page-sizes="[6, 10, 20, 50, 100]" :total="state.total"
              @current-change="handlePageChange" @size-change="handleSizeChange">
            </el-pagination>
          </div>
        </kade-table-wrap>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="beforeClose()" size="mini">关&nbsp;&nbsp;闭</el-button>
          <el-button @click="edit()" v-if="row.auditStatus !== 'SYS_AUDIT_PASSED'" type="primary" size="mini">
            编&nbsp;&nbsp;辑</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script>
import { useStore } from "vuex";
import { computed, reactive, watch } from "vue";
import { getCashGrantList } from "@/applications/eccard-finance/api";
import { statusColor } from "../styleData";
import { timeStr } from "@/utils/date.js";
import { filterDictionary } from "@/utils/index.js";
import { useDict } from "@/hooks/useDict";
import {
  ElDialog,
  ElDivider,
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElTable,
  ElTableColumn,
  ElPagination,
} from "element-plus";
export default {
  components: {
    "el-dialog": ElDialog,
    "el-button": ElButton,
    "el-input": ElInput,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    "el-table": ElTable,
    "el-table-column": ElTableColumn,
    "el-pagination": ElPagination,
    ElDivider,
  },
  setup() {
    const store = useStore();
    const state = reactive({
      form: {
        currentPage: 1,
        pageSize: 6,
        projectId: store.state.cashData.selectRow.projectId,
      },
      grantModeList: useDict("SYS_GRANT_MODE"), //清单生成方式
    });
    const isDetails = computed(() => {
      return store.state.cashData.isDetails;
    });
    const row = computed(() => {
      return store.state.cashData.selectRow;
    });

    //监听详情页打开
    watch(
      () => store.state.cashData.isDetails,
      (val) => {
        if (val) {
          state.form = {
            currentPage: 1,
            pageSize: 6,
            projectId: store.state.cashData.selectRow.projectId,
          };
          getDataList();
        }
      }
    );

    const getDataList = () => {
      getCashGrantList(state.form).then((res) => {
        state.personList = res.data.pageInfo.list;
        state.total = res.data.pageInfo.total;
      });
    };

    const edit = () => {
      beforeClose()
      store.commit("cashData/updateState", {
        key: "isEdit",
        payload: true,
      });
    };

    //关闭
    const beforeClose = () => {
      store.commit("cashData/updateState", {
        key: "isDetails",
        payload: false,
      });
    };

    const handlePageChange = (val) => {
      state.form.currentPage = val;
      getDataList();
    };
    const handleSizeChange = (val) => {
      state.form.currentPage = 1;
      state.form.pageSize = val;
      getDataList();
    };
    return {
      state,
      timeStr,
      filterDictionary,
      statusColor,
      isDetails,
      row,
      edit,
      getDataList,
      beforeClose,
      handlePageChange,
      handleSizeChange,
    };
  },
};
</script>
<style lang="scss" scoped>
.el-dialog__header {
  border-bottom: 1px solid #efefef;
}

/* .el-dialog__headerbtn {
  font-size: 30px;
  line-height: 30px;
} */
:deep(.el-dialog__header) {
  border-bottom: 1px solid #efefef;
}

:deep(.el-overlay) {
  position: absolute;
  background-color: rgba(0, 0, 0, 0);
}

:deep(.el-dialog__footer) {
  text-align: center;
}

.dialog-title {
  display: flex;
  align-items: center;

  .detail-status {
    border-radius: 4px;
    margin-left: 20px;
    padding: 10px;
    color: #f15a1f;
    background: #ffb89c;
  }
}

.el-divider--horizontal {
  margin: 5px 0;
}

.kade-table-wrap {
  margin: 10px 0;
}
</style>