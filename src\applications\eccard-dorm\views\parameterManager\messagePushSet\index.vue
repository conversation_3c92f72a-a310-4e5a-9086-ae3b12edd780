<template>
    <kade-route-card>
        <kade-table-wrap title="消息推送参数列表">
            <el-table border :data="state.data">
                <el-table-column show-overflow-tooltip prop="msgName" label="消息名称" align="center"></el-table-column>
                <el-table-column show-overflow-tooltip prop="pushMode" label="推送方式" align="center">
                    <template #default="scope">
                        {{ scope.row.pushMode.split(',').map(item=>dictionaryFilter(item)).join(',')}}
                    </template>
                </el-table-column>
                <el-table-column show-overflow-tooltip prop="msgType" label="消息类型" align="center">
                    <template #default="scope">
                        {{ dictionaryFilter(scope.row.msgType) }}
                    </template>
                </el-table-column>
                <el-table-column show-overflow-tooltip prop="pushCycle" label="推送周期" align="center">
                    <template #default="scope">
                        {{ scope.row.pushCycle?dictionaryFilter(scope.row.pushCycle):'--' }}
                    </template>
                </el-table-column>
                <el-table-column show-overflow-tooltip prop="pushTime" label="推送时间" align="center">
                    <template #default="scope" >
                        <div v-if="scope.row.pushCycle=='MONTHLY'">{{ scope.row.pushTime?scope.row.pushTime+'号':'--'}}</div>
                        <div v-else>{{scope.row.pushTime?scope.row.pushTime:'--'}}</div>
                    </template>
                </el-table-column>
                <el-table-column show-overflow-tooltip prop="status" label="启用状态" align="center">
                    <template #default="scope">
                        {{ dictionaryFilter(scope.row.status) }}
                    </template>
                </el-table-column>
                <el-table-column show-overflow-tooltip prop="forceReceive" label="是否强制接收" align="center">
                    <template #default="scope">
                        {{ dictionaryFilter(scope.row.forceReceive) }}
                    </template>
                </el-table-column>
                <el-table-column show-overflow-tooltip prop="receiveRole" label="接收人" align="center" width="300px">
                    <template #default="scope">
                        {{ scope.row.receiveRole.split(",").map(item=>dictionaryFilter(item)).join(",") }}
                    </template>
                </el-table-column>
                <el-table-column prop="" label="操作" align="center" width="80px">
                    <template #default="scope">
                        <el-button type="text" @click="edit(scope.row)" class="green" size="mini">编辑</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </kade-table-wrap>
        <kade-message-push-set-edit :dialogViseble="state.dialogViseble" :rowData="state.rowData"
            @close="close" />
    </kade-route-card>
</template>

<script>
import { ElTable, ElTableColumn, ElButton, } from "element-plus"
import { reactive } from '@vue/reactivity'
import edit from "./components/edit"
import { onMounted } from '@vue/runtime-core'
import { messagePushParamList } from '@/applications/eccard-dorm/api.js'
export default {
    components: {
        ElTable,
        ElTableColumn,
        ElButton,
        "kade-message-push-set-edit": edit
    },
    setup() {
        const state = reactive({
            form: {},
            data: [],
            dialogViseble: false,
            rowData: {},
            loading: false
        });
        const getList = async () => {
            state.loading = true
            try {
                let { data } = await messagePushParamList(state.form)
                state.data = data.map(item=>{
                    if(item.msgType=='REAL_TIME'){
                        return {
                            ...item,
                            pushCycle:null,
                            pushTime:null
                        }
                    }else{
                        return item
                    }
                })
                state.loading = false
            }
            catch {
                state.loading = false
            }
        };
        const edit = (row) => {
            console.log(row)
            state.dialogViseble = true
            state.rowData = row
        };
        const close=(val)=>{
            if(val){
                getList()
            }
            state.dialogViseble=false
        }
        onMounted(() => {
            getList();
        })
        return {
            state,
            edit,
            close
        }
    }
}
</script>

<style lang="scss" scoped>
.kade-table-wrap {
    padding: 0;
    border-bottom: none;
}

.green :hover {
    text-decoration: underline;
}

:deep(.el-dialog__header) {
    border-bottom: 1px solid #eeeeee;
    margin-bottom: 20px;
}

:deep(.el-dialog__footer) {
    text-align: center !important;
    border-top: none;
}

:deep(.el-textarea__inner) {
    width: 494px;
}

:deep(.el-date-editor.el-input, .el-date-editor.el-input__inner) {
    width: 182px;
}
</style>