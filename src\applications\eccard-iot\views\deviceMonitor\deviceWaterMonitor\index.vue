<template>
  <kade-route-card style="height: 100%">
    <div class="deviceRealTimeMonitor">
      <kade-table-wrap title="区域" style="width: 300px; height: 100%; margin-right: 20px">
        <el-divider></el-divider>
        <div class="search-box">
          <el-input v-model="state.areaKeyword" placeholder="请输入关键字搜索" size="small" clearable>
            <template #append>
              <el-button icon="el-icon-sousuo" @click="areaSearch()">查询</el-button>
            </template>
          </el-input>
          <el-tree class="area-tree" :data="state.areaCheckTreeList" :props="defaultProps" default-expand-all
            :expand-on-click-node="false" @node-click="handleNodeClick">
            <template #default="{ node }">
              <div class="custom-tree-node hasbtn">
                <span class="left">
                  <i class="el-icon-location-information"></i>
                  <span style="margin-left: 5px; color: #333">{{
                    node.label
                  }}</span>
                </span>
              </div>
            </template>
          </el-tree>
        </div>
      </kade-table-wrap>
      <kade-table-wrap title="设备监控列表" style="flex: 1; height: 100%;min-width: 800px">
        <el-divider></el-divider>
        <div class="padding-box">
          <el-form size="small" inline label-width="100px">
            <el-form-item label="设备型号：">
              <el-cascader v-model="state.form.deviceType" :options="state.deviceTypeList" :props="cascaderProps"
                :show-all-levels="false"></el-cascader>
            </el-form-item>
            <el-form-item>
              <el-button @click="handleSearch" type="primary">搜 索</el-button>
            </el-form-item>
          </el-form>
          <div class="census-box">
            <div :class="state.activeIndex === index && 'census-item-active'" class="census-item"
              @click="handleCensus(item, index)" v-for="(item, index) in state.censusList" :key="index">
              <div>{{ item.label }}</div>
              <div :style="{ color: state.activeIndex === index ? '#fff' : item.color, fontSize: '24px' }">{{
                state.totalData[item.filed]
              }}
              </div>
            </div>
          </div>
          <el-table style="width: 100%" height="55vh" v-loading="state.loading" :data="state.dataList" border stripe>
            <el-table-column prop="deviceModel" label="设备型号" align="center" show-overflow-tooltip></el-table-column>
            <el-table-column prop="merchantName" label="所属商户" align="center" show-overflow-tooltip></el-table-column>
            <el-table-column prop="deviceNo" label="设备机号" align="center" show-overflow-tooltip></el-table-column>
            <el-table-column prop="deviceName" label="设备名称" align="center" show-overflow-tooltip></el-table-column>
            <el-table-column prop="deviceOnlineName" label="在线状态" align="center" show-overflow-tooltip></el-table-column>
            <el-table-column prop="deviceStatus" label="设备状态" align="center" show-overflow-tooltip>
              <template #default="scope">
                {{ dictionaryFilter(scope.row.deviceStatus) }}
              </template>
            </el-table-column>
            <el-table-column prop="useStatus" label="使用状态" align="center" show-overflow-tooltip></el-table-column>
            <el-table-column prop="signalIntensity" label="信号强度" align="center" show-overflow-tooltip></el-table-column>
            <el-table-column prop="onlineTime" label="上线时间" align="center" show-overflow-tooltip></el-table-column>
            <!-- <el-table-column prop="a" label="离线时间" align="center" show-overflow-tooltip></el-table-column> -->
            <!-- <el-table-column prop="a" label="流量到期时间" align="center" show-overflow-tooltip></el-table-column> -->
          </el-table>
          <div class="pagination-box">
            <el-pagination :currentPage="state.form.currentPage" :pageSize="state.form.pageSize"
              :page-sizes="[5, 10, 20, 30, 40]" background layout="total, sizes, prev, pager, next, jumper"
              :total="state.total" @size-change="handleSizeChange" @current-change="handleCurrentChange">
            </el-pagination>
          </div>
        </div>

      </kade-table-wrap>
    </div>
  </kade-route-card>
</template>
<script>
import { onMounted, reactive } from "vue";
import {
  ElDivider,
  ElInput,
  ElTree,
  ElForm,
  ElFormItem,
  ElCascader,
  ElTable,
  ElTableColumn,
  ElButton,
  ElPagination,
} from "element-plus";
import { getUserAreaPurview } from "@/applications/eccard-basic-data/api";
import { iotCfg, getModel, wirelessWaterControllerMonitor, wirelessWaterControllerMonitorCount } from "@/applications/eccard-iot/api";
import { makeTree } from "@/utils/index.js";
const defaultProps = {
  children: "children",
  label: "areaName",
};
export default {
  components: {
    ElDivider,
    ElInput,
    ElTree,
    ElForm,
    ElFormItem,
    ElCascader,
    ElButton,
    ElTable,
    ElTableColumn,
    ElPagination
  },
  setup() {

    const state = reactive({
      activeIndex: 0,
      areaKeyword: "",
      areaCheckTreeList_copy: '',
      loading: false,
      checkedAll: false,
      deviceTypeList: [],
      rowArea: {},
      form: {
        currentPage: 1,
        pageSize: 10
      },
      censusList: [
        { color: "#409EFF", label: "全部设备", filed: "total", type: "" },
        { color: "#67C23A", label: "在线设备", filed: "online", type: "TRUE" },
        { color: "#f00", label: "异常设备", filed: "error", type: "ERROR" },
        { color: "#666", label: "离线设备", filed: "offline", type: "FALSE" },
      ],
      totalData: {},
      dataList: [],
      total: 0
    });
    const getDeviceTypeList = async () => {
      let { data } = await iotCfg({ cfgType: 'HARDWARE_WATER_ELECTRIC' });
      state.deviceTypeList = data.map(item => {
        return {
          label: item.cfgValue,
          value: item.cfgKey
        }
      })
    }

    const cascaderProps = {
      lazy: true,
      async lazyLoad(node, resolve) {
        console.log(node);
        const { level, value } = node;
        if (level === 1) {
          let { data } = await getModel({ productClass: value });
          resolve(data.map(item => ({ label: item.productMode, value: item.productMode, leaf: true })));

        }
      }
    }

    const getList = async () => {
      state.loading = true
      let params = {
        ...state.form,
        deviceModel: state.form.deviceType ? state.form.deviceType[1] : ""
      }
      delete params.deviceType
      try {
        let { data: { list, total } } = await wirelessWaterControllerMonitor(params)
        state.dataList = list.map(item => {
          return {
            ...item,
            checked: false
          }
        })
        state.total = total
        state.loading = false
      }
      catch {
        state.loading = false
      }
    }
    const getOnlineCountData = async () => {
      let params = {}
      if (state.rowArea.areaPath) {
        params.areaPath = state.rowArea.areaPath
      }
      if (state.form.deviceType) {
        params.deviceModel = state.form.deviceType[1]
      }
      let { data } = await wirelessWaterControllerMonitorCount(params)
      console.log(data);
      state.totalData = data
    }
    const handleNodeClick = (val) => {
      state.rowArea = val
      state.form.areaPath = state.rowArea.areaPath
      state.form.currentPage = 1
      state.form.pageSize = 10
      getList()
      getOnlineCountData()
    }


    const handleSearch = () => {
      getList()
    }

    const queryAreaCheckList = () => {
      getUserAreaPurview().then((res) => {
        let arr = makeTree(res.data, "id", "areaParentId", "children");
        state.areaCheckTreeList = [...arr];
        state.areaCheckTreeList_copy = [...arr];
      });
    };
    const mapTree = (value, arr) => {
      let newarr = [];
      arr.forEach((element) => {
        if (element.areaName.indexOf(value) > -1) {
          // 判断条件
          newarr.push(element);
        } else {
          if (element.children && element.children.length > 0) {
            let redata = mapTree(value, element.children);
            if (redata && redata.length > 0) {
              let obj = {
                ...element,
                children: redata,
              };
              newarr.push(obj);
            }
          }
        }
      });
      return newarr;
    };

    const areaSearch = () => {
      state.areaCheckTreeList = mapTree(state.areaKeyword, state.areaCheckTreeList_copy)
    }
    const handleCensus = (item, index) => {
      if (index === state.activeIndex) return
      state.activeIndex = index
      if (item.type) {
        state.form.deviceOnline = item.type

      } else {
        delete state.form.deviceOnline
      }
      getList()
    }
    const handleDevice = (item) => {
      item.checked = !item.checked
    }
    const handleSizeChange = (val) => {
      state.form.currentPage = 1
      state.form.pageSize = val
      getList()
    };
    const handleCurrentChange = (val) => {
      state.form.currentPage = val
      getList()

    };
    onMounted(() => {
      getList()
      getOnlineCountData()
      queryAreaCheckList();
      getDeviceTypeList()
    });
    return {
      defaultProps,
      state,
      cascaderProps,
      handleNodeClick,
      handleSearch,
      areaSearch,
      handleCensus,
      handleDevice,
      handleSizeChange,
      handleCurrentChange
    };
  },
};
</script>
<style lang="scss" scoped>
.deviceRealTimeMonitor {
  height: 100%;
  display: flex;
  justify-content: space-between;

  .search-box {
    padding: 10px;
    height: 70vh;
  }

  .area-tree {
    margin-top: 20px;
    height: calc(100%-112px);
    overflow-y: auto;
  }

  .census-box {
    display: flex;
    align-items: center;
    margin-bottom: 20px;

    .census-item {
      box-sizing: border-box;
      padding: 10px;
      margin-right: 20px;
      width: 200px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      border: 1px solid #f2f2f2;
      border-radius: 5px;
    }

    .census-item-active {
      border: none !important;
      background: #409EFF !important;
      color: #fff !important;

    }
  }

  .pagination-box {
    text-align: right;
    padding: 15px 0;
  }
}

:deep(.el-table) {
  margin: 0;
  border-left: 1px solid #EBEEF5 !important;
  border-right: 1px solid #EBEEF5 !important;
}

:deep(.el-divider--horizontal) {
  margin: 0;
}
</style>
