<template>
  <el-dialog :model-value="modelValue" :title="title" width="800px" :before-close="handleClose">
    <el-form v-loading="state.loading" inline label-width="130px" size="mini" :model="state.form"
      :rules="type !== 'details' && rules" ref="formRef">
      <!--       <el-form-item label="人员编号：" prop="userCode">
        <el-input v-if="type == 'details'" readonly :model-value="state.form.userCode"></el-input>
        <el-input v-else v-model="state.form.userCode" placeholder="请输入人员编号"></el-input>
      </el-form-item>
      <el-form-item label="人员姓名：" prop="userName">
        <el-input v-if="type == 'details'" readonly :model-value="state.form.userName"></el-input>
        <el-input v-else v-model="state.form.userName" placeholder="请输入人员姓名"></el-input>
      </el-form-item> -->
      <el-form-item v-if="type == 'details'" label="所属区域：">
        <el-input readonly :model-value="state.form.areaName"></el-input>
      </el-form-item>
      <el-form-item v-if="type == 'details'" label="楼栋：">
        <el-input :model-value="state.form.buildName" readonly></el-input>
      </el-form-item>
      <el-form-item v-if="type == 'details'" label="单元：">
        <el-input readonly :model-value="state.form.unitName"></el-input>
      </el-form-item>
      <el-form-item v-if="type == 'details'" label="楼层：">
        <el-input readonly :model-value="state.form.floorName"></el-input>
      </el-form-item>
      <el-form-item v-if="type == 'details'" label="房间号：">
        <el-input :model-value="state.form.roomName" readonly></el-input>
      </el-form-item>
      <kade-linkage-select v-if="type !== 'details'" :isEdit="modelValue ? true : false" :data="linkageData"
        :value="state.form" @change="linkageChange" />
      <el-form-item label="违规人员：" prop="userId">
        <el-input v-if="type == 'details'" readonly :model-value="state.form.userName"></el-input>
        <el-select v-else v-model="state.form.userCode" placeholder="请选择违规人员">
          <el-option v-for="(item, index) in state.personList" :key="index" :label="item.userName" :value="item.userCode" />
        </el-select>
      </el-form-item>
      <el-form-item label="违规时间：" prop="breachTime">
        <el-input v-if="type == 'details'" readonly :model-value="state.form.breachTime"></el-input>
        <el-date-picker v-else v-model="state.form.breachTime" type="date" placeholder="请选择日期" />
      </el-form-item>
      <el-form-item label="违规类型：" prop="breachType">
        <el-input v-if="type == 'details'" readonly :model-value="dictionaryFilter(state.form.breachType)"></el-input>
        <el-select v-else v-model="state.form.breachType" placeholder="请选择违规类型">
          <el-option v-for="(item, index) in breachTypeList" :key="index" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="违规详情：" prop="breachRemark">
        <el-input v-if="type == 'details'" type="textarea" :rows="3" :model-value="state.form.breachRemark" readonly>
        </el-input>
        <el-input v-else v-model="state.form.breachRemark" type="textarea" :rows="3" maxlength="200"
          placeholder="请输入内容">
        </el-input>
      </el-form-item>
      <el-form-item label="处理状态：" prop="processingStatus">
        <el-input v-if="type == 'details'" readonly :model-value="dictionaryFilter(state.form.processingStatus)">
        </el-input>
        <el-select v-else v-model="state.form.processingStatus" placeholder="请选择处理状态">
          <el-option v-for="(item, index) in processingList" :key="index" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="处理时间：">
        <el-input v-if="type == 'details'" readonly :model-value="state.form.processingTime"></el-input>
        <el-date-picker v-else v-model="state.form.processingTime" type="date" placeholder="请选择日期" />
      </el-form-item>
      <el-form-item label="处理结果：" prop="processingRemark">
        <el-input v-if="type == 'details'" type="textarea" :rows="3" :model-value="state.form.processingRemark"
          readonly></el-input>
        <el-input v-else v-model="state.form.processingRemark" type="textarea" :rows="3" maxlength="200"
          placeholder="请输入内容"></el-input>
      </el-form-item>
      <el-form-item label="是否通报：" prop="whetherNotify">
        <el-input v-if="type == 'details'" readonly :model-value="state.form.whetherNotify"></el-input>
        <el-select v-else v-model="state.form.whetherNotify" placeholder="请选择是否通报">
          <el-option v-for="(item, index) in whetherNotifyList" :key="index" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="检查人：" prop="checkPerson">
        <el-input v-if="type == 'details'" readonly :model-value="state.form.checkPerson"></el-input>
        <el-input v-else v-model="state.form.checkPerson" maxlength="20" placeholder="请输入检查人"></el-input>
      </el-form-item>
    </el-form>

    <template #footer>
      <span class="dialog-footer" v-if="type != 'details'">
        <el-button type="" @click="handleClose" size="mini">取消</el-button>
        <el-button type="primary" @click="submit" size="mini">确认</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import {
  ElDialog,
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElDatePicker,
  ElMessage,
} from "element-plus";
import { reactive } from "@vue/reactivity";
import { computed, watch, ref, nextTick } from "vue";
import { useDict } from "@/hooks/useDict";
import { dateStr } from "@/utils/date.js";
import linkageSelect from "@/applications/eccard-dorm/components/linkageSelect";
import {
  addBreachPrinciple,
  editBreachPrinciple,
  getRoomStayInfo
} from "@/applications/eccard-dorm/api.js";
const linkageData = {
  area: { label: "所属区域：", valueKey: "areaId", key: "id" },
  building: { label: "楼栋：", valueKey: "buildId" },
  unit: { label: "单元：", valueKey: "unitNum" },
  floor: { label: "楼层：", valueKey: "floorNum" },
  room: { label: "房间号：", valueKey: "roomId" },
};

const rules = {
  roomId: [{ required: true, message: "请选择房间", trigger: "change" }],
  breachTime: [{ required: true, message: "请选择违规时间", trigger: "blur" }],
  breachType: [
    { required: true, message: "请选择违规类型", trigger: "change" },
  ],
   userCode: [
    { required: true, message: "请选择违规人员", trigger: "change" },
  ],
  breachRemark: [
    { required: true, message: "请输入违规详情", trigger: "blur" },
    { max: 200, message: '不能超过200字符' }
  ],
  processingStatus: [
    { required: true, message: "请选择处理状态", trigger: "change" },
  ],
  processingRemark: [
    { required: true, message: "请输入处理结果", trigger: "blur" },
    { max: 200, message: '不能超过200字符' }
  ],
  whetherNotify: [
    { required: true, message: "请选择是否通报", trigger: "change" },
  ],
  checkPerson: [{ required: true, message: "请输入检查人", trigger: "blur" }],
};
export default {
  components: {
    ElDialog,
    ElButton,
    ElForm,
    ElFormItem,
    ElInput,
    ElSelect,
    ElOption,
    ElDatePicker,
    "kade-linkage-select": linkageSelect,
  },
  props: {
    modelValue: {
      type: Boolean,
      default: false,
    },
    type: {
      type: String,
      default: "",
    },
    selectRow: {
      types: Object,
      default: null,
    },
  },
  setup(props, context) {
    const formRef = ref(null);
    const processingList = useDict("DORM_DAILY_CHECK_RESULT"); //处理状态
    const whetherNotifyList = useDict("SYS_BOOL_STRING"); //是否通报
    const breachTypeList = useDict("DORM_ILLEGAL_TYPE"); //违规类型
    const state = reactive({
      form: {
        userId: 0,
      },
      loading: false,
      personList:[]
    });
    watch(
      () => props.modelValue,
      (val) => {
        if (val) {
          if (props.type === "add") {
            state.form = {};
          } else {
            let {
              id,
              userId,
              userCode,
              userName,
              unitName,
              areaId,
              buildId,
              unitNum,
              floorNum,
              roomId,
              breachTime,
              breachType,
              breachRemark,
              processingStatus,
              processingRemark,
              processingTime,
              whetherNotify,
              floorName,
              checkPerson,
              buildName,
              areaName,
              roomName,
            } = props.selectRow;
            state.form = {
              id,
              userId,
              userCode,
              userName,
              areaId,
              floorName,
              buildId,
              unitNum,
              unitName,
              floorNum,
              roomId,
              breachTime: dateStr(breachTime),
              breachType,
              breachRemark,
              processingStatus,
              processingRemark,
              processingTime: dateStr(processingTime),
              whetherNotify,
              checkPerson,
              buildName,
              areaName,
              roomName,
            };
            queryPerson(roomId)
          }
          nextTick(() => {
            formRef.value.clearValidate();
          });
        }
      }
    );
    const linkageChange = (val) => {
      state.form = { ...state.form, ...val };
      delete state.form.userId
      state.personList=[]
      if (val.roomId) {
        queryPerson(val.roomId)
      }
    };

    const title = computed(() => {
      if (props.type === "add") {
        return "新增违规违纪";
      } else if (props.type === "edit") {
        return "编辑违规违纪";
      } else {
        return "违规违纪详情";
      }
    });
    //获取房间人员
    const queryPerson = (roomId) => {
      getRoomStayInfo({ roomId }).then((res) => {
        state.personList = res.data
      })
    }
    const submit = () => {
      formRef.value.validate(async (valid) => {
        if (valid) {
          let param = { ...state.form };
          console.log(param);
          param.breachTime = dateStr(param.breachTime);
          param.processingTime = dateStr(param.processingTime);
          let fn =
            props.type == "add" ? addBreachPrinciple : editBreachPrinciple;
          state.loading = true;
          try {
            let { code, message } = await fn(param);
            if (code === 0) {
              ElMessage.success(message);
              context.emit("update:modelValue", true);
            }
            state.loading = false;
          } catch {
            state.loading = false;
          }
        }
      });
    };
    const handleClose = () => {
      context.emit("update:modelValue", false);
      formRef.value.clearValidate();
      state.form = {};
    };
    return {
      state,
      title,
      rules,
      formRef,
      submit,
      handleClose,
      linkageData,
      linkageChange,
      processingList,
      breachTypeList,
      whetherNotifyList,
    };
  },
};
</script>

<style lang="scss" scoped>
:deep(.el-date-editor.el-input, .el-date-editor.el-input__inner) {
  width: 200px !important;
}
</style>
