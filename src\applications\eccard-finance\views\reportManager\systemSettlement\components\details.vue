<template>
  <el-dialog v-loading="state.loading" :model-value="modelValue" title="系统结算详情" width="1000px" :before-close="handleClose" :close-on-click-modal="false">
    <div class="table-box">
      <div class="btn">
        <!-- <el-button size="mini" @click="handleExport" icon="el-icon-daoru" type="primary">导出</el-button> -->
        <el-button size="mini" @click="handlePrint" class="btn-purple" icon="el-icon-printer">打印</el-button>
      </div>
      <div class="title">系统结算{{typeFnc(rowData.pageInfo[0].settlementMethod)}}报表</div>
      <div class="mode-content">
        <div class="table-head">
          <div class="head-item">交易钱包</div>
          <div class="head-item">交易类型</div>
          <div class="head-item">交易笔数</div>
          <div class="head-item">交易金额</div>
          <div class="head-item">交易钱包</div>
          <div class="head-item">交易类型</div>
          <div class="head-item">交易笔数</div>
          <div class="head-item">交易金额</div>
        </div>
        <div class="type-item" v-for="(item,index) in settlementMethodList" :key="index">
          <div class="settlement-title">{{item.label}}</div>
          <div class="settlement-box">
            <div class="settlement-item" :class="border(rowData.pageInfo.filter(val=>val.settlementType==item.value).length,i)" v-for="(v,i) in rowData.pageInfo.filter(val=>val.settlementType==item.value)" :key="i">
              <div class="prop">{{v.walletName}}</div>
              <div class="prop">{{v.costTypeDesc}}</div>
              <div class="prop">{{v.totalCount}}</div>
              <div class="prop">{{v.totalAmount.toFixed(2)}}</div>
            </div>
          </div>
        </div>
        <div class="summary">
          <div class="summary-type" v-for="(item,index) in state.summaryList" :key="index">
            <div class="summary-title">{{item.title}}</div>
            <div class="summary-data" v-for="(v,i) in item.list" :key="i">
              <div class="summary-label">{{v.label}}</div>
              <div class="summary-value">{{typeof rowData.generate[v.prop]=='number'? rowData.generate[v.prop].toFixed(2):rowData.generate[v.prop]?rowData.generate[v.prop]:'--'}}</div>
            </div>
          </div>
        </div>
      </div>
      <div class="text">
        <div>金额汇总计算公式：</div>
        <div>1、不计收支=脱机水控钱包线上充值+脱机水控钱包线下充值+自助洗衣钱包线上充值+自助洗衣钱包线下充值+自助话机钱包线上充值+自助话机钱包线下充值+交押金+工本费+脱机水控钱包冲正+自助洗衣钱包冲正+自助话机钱包冲正+脱机水控钱包退款+自助洗衣钱包退款+自助话机钱包退款+退押金；</div>
        <div>2、本期收入=现金钱包线上充值+现金钱包线下充值+补助钱包线上充值+现金钱包纠错+补助钱包纠错；</div>
        <div>3、本期支出=现金钱包冲正+补助钱包冲正+现金钱包消费+补助钱包消费+现金钱包转出+现金钱包退款+补助钱包退款+补助钱包清零；</div>
        <div>4、本期结余=本期收入+本期支出。</div>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button size="mini" @click="handleClose">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script>
import { onMounted, reactive } from "vue";
import { ElDialog, ElButton } from "element-plus";
import { timeStr } from "@/utils/date.js";
import { print } from "@/utils";
import { useDict } from "@/hooks/useDict.js";
// import {useStore} from "vuex";
import { systemSettlementDetailPrint } from "@/applications/eccard-finance/api";
// import func from 'vue-editor-bridge';
export default {
  components: {
    ElDialog,
    ElButton,
  },
  emits: ["update:modelValue"],
  props: {
    modelValue: {
      type: Boolean,
      default: false,
    },
    rowData: {
      type: Object,
      default: null,
    },
  },
  setup(props, context) {
    const settlementMethodList = useDict("SYSTEM_SETTLEMENT_TYPE");
    
    // const store=useStore()
    // const dict = store.state.app.dictionary;
    // const settlementMethodList = dict.filter(function(item){
    //   return item.dictType === 'SYSTEM_SETTLEMENT_TYPE'
    // }).map(function(it){
    //   return {
    //     label:it.dictValue,
    //     value:it.dictCode
    //   }
    // });
    console.log(settlementMethodList)

    const state = reactive({
      loading: false,
      dataList: [
        {
          title: "一、存款类",
          list: [
            { wallet: "现金钱包", model: "充值", num: 10, amount: 18437 },
            { wallet: "现金钱包", model: "充值", num: 10, amount: 18437 },
            { wallet: "现金钱包", model: "充值", num: 10, amount: 18437 },
            { wallet: "现金钱包", model: "充值", num: 10, amount: 18437 },
            { wallet: "现金钱包", model: "充值", num: 10, amount: 18437 },
          ],
        },
        {
          title: "二、冲正纠错类",
          list: [
            { wallet: "现金钱包", model: "充值", num: 10, amount: 18437 },
            { wallet: "现金钱包", model: "充值", num: 10, amount: 18437 },
            { wallet: "现金钱包", model: "充值", num: 10, amount: 18437 },
            { wallet: "现金钱包", model: "充值", num: 10, amount: 18437 },
            { wallet: "现金钱包", model: "充值", num: 10, amount: 18437 },
            { wallet: "现金钱包", model: "充值", num: 10, amount: 18437 },
          ],
        },
        {
          title: "三、消费转账类",
          list: [
            { wallet: "现金钱包", model: "充值", num: 10, amount: 18437 },
            { wallet: "现金钱包", model: "充值", num: 10, amount: 18437 },
            { wallet: "现金钱包", model: "充值", num: 10, amount: 18437 },
          ],
        },
        {
          title: "四、退款类",
          list: [{ wallet: "现金钱包", model: "充值", num: 10, amount: 18437 }],
        },
        {
          title: "五、清零类",
          list: [
            { wallet: "现金钱包", model: "充值", num: 10, amount: 18437 },
            { wallet: "现金钱包", model: "充值", num: 10, amount: 18437 },
          ],
        },
      ],
      summaryList: [
        {
          title: "金额汇总",
          list: [
            { label: "不计收支", prop: "moneyNoStatis" },
            { label: "本期收入", prop: "moneyIn" },
            { label: "本期支出", prop: "moneyOut" },
            { label: "本期结余", prop: "moneyBalance" },
          ],
        },
        {
          title: "次数汇总",
          list: [
            { label: "不计收支", prop: "numberNoStatis" },
            { label: "本期收入", prop: "numberIn" },
            { label: "本期支出", prop: "numberOut" },
            { label: "本期结余", prop: "numberBalance" },
          ],
        },
      ],
    });
    const border = (length, index) => {
      if (length % 2 == 0) {
        return length - 3 < index && "none-boder-bottom";
      } else {
        return length - 2 < index && "none-boder-bottom";
      }
    };
    const typeFnc=(val)=>{
      if (val == "YEAR_SETTLEMENT") {
        return '年'
      } else if (val == "MONTH_SETTLEMENT") {
        return '月'
      } else {
        return '日'
      }
    }
    const handlePrint = async () => {
      state.loading = true
      try {
        let { data, code } = await systemSettlementDetailPrint({ settlementDate: timeStr(props.rowData.pageInfo[0].settlementDate) })
        if (code === 0) {
          print(data)
        }
        state.loading = false
      }
      catch {
        state.loading = false

      }

    }
    const handleClose = () => {
      context.emit("update:modelValue", false);
    };
    onMounted(() => { });
    return {
      timeStr,
      // store,
      // dict,
      settlementMethodList,
      state,
      border,
      typeFnc,
      handlePrint,
      handleClose,
    };
  },
};
</script>
<style lang="scss" scoped>
.table-box {
  box-sizing: border-box;
  padding: 20px;
  position: relative;
  .btn {
    position: absolute;
    right: 20px;
    top: 15px;
  }
  .title {
    text-align: center;
    font: bold 16px arial;
    margin-bottom: 10px;
  }
  .mode-content {
    border: 1px solid #ebeef5;
    .table-head {
      text-align: center;
      display: flex;
      justify-content: space-around;
      align-items: center;
      border-bottom: 1px solid #ebeef5;
      font-weight: bold;
      .head-item {
        width: 100%;
        min-width: 100px;
        height: 40px;
        line-height: 40px;
        border-right: 1px solid #ebeef5;
        &:last-child {
          border-right: 0;
        }
      }
    }
    .type-item {
      .settlement-title {
        padding-left: 10px;
        height: 40px;
        line-height: 40px;
        font-weight: bold;
        border-bottom: 1px solid #ebeef5;
      }
      .settlement-box {
        text-align: center;
        display: flex;
        flex-wrap: wrap;
        border-bottom: 1px solid #ebeef5;
        .settlement-item {
          width: 50%;
          display: flex;
          align-items: center;
          justify-content: space-around;
          border-bottom: 1px solid #ebeef5;

          .prop {
            width: 100%;
            height: 40px;
            line-height: 40px;
            border-right: 1px solid #ebeef5;
          }
          &:nth-child(2n) {
            .prop:last-child {
              border-right: 0;
            }
          }
        }
        .none-boder-bottom {
          border-bottom: none;
        }
      }
    }
    .summary {
      text-align: center;
      display: flex;
      align-items: center;
      .summary-type {
        box-sizing: border-box;
        width: 50%;
        &:first-child {
          border-right: 1px solid #ebeef5;
        }
        .summary-title {
          height: 40px;
          line-height: 40px;
          border-bottom: 1px solid #ebeef5;
          font-weight: bold;
        }
        .summary-data {
          display: flex;
          align-items: center;
          height: 40px;
          line-height: 40px;
          border-bottom: 1px solid #ebeef5;
          &:last-child {
            border-bottom: 0;
          }
          .summary-label {
            width: 100px;
            border-right: 1px solid #ebeef5;
          }
          .summary-value {
            flex: 1;
          }
        }
        &:first-child .summary-label {
          border-left: 0;
        }
      }
    }
  }
}
.text{
  font:12px/30px arial;
}
</style>