<template>
  <kade-route-card style="height: auto">
    <kade-table-wrap title="交易类型列表">
      <el-table border height="70vh" :data="state.data" >
        <el-table-column type="selection" align="center"></el-table-column>
        <el-table-column prop="costName" label="名称" align="center"></el-table-column>
        <el-table-column prop="costCode"  label="属性值"  align="center"></el-table-column>
        <el-table-column prop="sysPreset" label="是否系统预置" align="center" >
            <template #default="scope">
              {{filterDictionary(scope.row.sysPreset,bool)}}
            </template>
        </el-table-column>
        <el-table-column prop="createUserName" label="添加人" align="center"></el-table-column>
        <el-table-column prop="createTime" label="创建时间" align="center"></el-table-column>
        <!-- <el-table-column label="操作" align="center">
          <template #default="scope">
            <el-button type="text" @click="edtails(scope.row)">查看详情</el-button>
          </template>
        </el-table-column> -->
      </el-table>
      <div class="pagination">
        <el-pagination
          background
          v-model:currentPage="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[5,10, 20, 30]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="state.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </div>
      <kade-type-edtails :dialogVisible="state.dialogVisible" :dialogData="state.dialogData" @close="close"></kade-type-edtails>
    </kade-table-wrap>
  </kade-route-card>
</template>

<script>
import { getFinanceCostTypePage } from "@/applications/eccard-finance/api.js"
import {useDict} from "@/hooks/useDict"
import {filterDictionary} from "@/utils"
import { reactive,onMounted } from "vue";
import { ElTable, ElTableColumn,ElPagination } from "element-plus";
import edtails from "./components/edtails.vue"
export default {
  components: {
    ElTable,
    ElTableColumn,
    // ElDivider,
    ElPagination,
    // ElButton,
    "kade-type-edtails" :edtails,
  },
  setup() {
    const bool=useDict("SYS_BOOL_STRING")
    const state = reactive({
      dialogVisible:false,
      data:[],
      dialogData:'',
      total:0,
      form: {
        currentPage: 1,
        pageSize: 10,
      },
    });
    const getList =()=>{
      getFinanceCostTypePage(state.form).then((res)=>{
        console.log(res)
        state.data=res.data.list
        state.total=res.data.total
      })
    };

    const edtails =(row)=>{
      console.log(row)
      state.dialogData=row
      state.dialogVisible=true
    };
    const close = ()=>{
      state.dialogVisible=false
    };
    const handleSizeChange =(val)=>{
      console.log(val)
      state.form.pageSize=val
      getList()
    };
    const handleCurrentChange =(val)=>{
      console.log(val)
      state.form.currentPage=val
      getList()
    };
    onMounted(()=>{
      getList()
    })
    return {
      filterDictionary,
      bool,
      state,
      edtails,
      close,
      getList,
      handleSizeChange,
      handleCurrentChange
    };
  },
};
</script>

<style lang="scss" scoped>
.trade-type {
  padding: 20px;
  width: 100%;
  box-sizing: border-box;
  .table-box {
    border: 1px solid #eeeeee;
    border-radius: 4px;
    padding: 0;
    width: 100%;
    .title-box {
      margin: 15px;
      .el-icon-s-fold {
        margin-right: 10px;
      }
    }
    .pagination{
      margin: 20px 15px;
    }
  }
  :deep(.el-divider--horizontal) {
    margin: 0;
  }
  :deep(.el-dialog__footer){
    border: 0;
    text-align: center;
  }
  :deep(.el-dialog__header){
    border-bottom: 1px solid #eeeeee;
  }
}
</style>