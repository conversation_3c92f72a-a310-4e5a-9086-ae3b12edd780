<template>
    <div class="user">
        <kade-route-card>
            <kade-table-wrap title="用户列表">
                <template #extra>
                <el-button icon="el-icon-plus" size="small" type="primary" @click="addUser">新增</el-button>
                </template>
                <el-table style="width: 100%" :data="options.dataList" v-loading="options.loading" border stripe>
                    <el-table-column
                        label="用户账号"
                        prop="userAccount"
                        align="center"
                    ></el-table-column>
                    <el-table-column
                        label="邮箱"   
                        prop="userEmail"
                        align="center"
                    ></el-table-column> 
                    <el-table-column
                        label="用户名称"
                        align="center"
                        prop="userName"
                    ></el-table-column>
                    <el-table-column
                        label="性别"
                        align="center"
                    >
                    <template #default="scope">
                        {{ dictionaryFilter(scope.row.userSex) }}
                    </template>
                    </el-table-column>
                    <el-table-column
                        label="用户类型"
                        align="center"
                    >
                    <template #default="scope">
                        {{ dictionaryFilter(scope.row.userType) }}
                    </template>                
                    </el-table-column> 
                    <el-table-column
                        label="用户来源"
                        align="center"
                    >
                    <template #default="scope">
                        {{ dictionaryFilter(scope.row.userSource) }}
                    </template>                
                    </el-table-column>                                                                                                                                            
                    <el-table-column
                        label="操作"
                        align="center"
                        width="120"
                    >
                        <template #default="scope">
                            <el-button type="text" style="margin-right: 10px" @click="editUser(scope.row)" size="mini">编辑</el-button>
                            <el-dropdown trigger="click" @command="(c) => handleCommand(c, scope.row)">
                                <el-button size="mini" type="text">
                                    更多<i class="el-icon-arrow-down el-icon--right"></i>
                                </el-button>
                                <template #dropdown>
                                    <el-dropdown-menu>
                                        <el-dropdown-item command="operation">权限设置</el-dropdown-item>
                                        <el-dropdown-item command="reset">重置密码</el-dropdown-item>
                                        <el-dropdown-item command="appauth">应用授权</el-dropdown-item>
                                    </el-dropdown-menu>
                                </template>
                            </el-dropdown>
                        </template>
                    </el-table-column>                                    
                </el-table>
                <div class="pagination">
                    <el-pagination
                        background
                        v-model:current-page="options.currentPage"
                        layout="total, sizes, prev, pager, next, jumper"
                        :page-sizes="[10, 20, 50, 100, 200]"        
                        :total="options.total"
                        v-model:page-size="options.pageSize"
                        @current-change="(val) => pageChange(val)"
                        @size-change="(val) => sizeChange(val)"
                    >
                    </el-pagination>             
                </div>                
            </kade-table-wrap>
        </kade-route-card>
        <kade-user-edit @change="loadData" :user="state.user" :title="state.user.id ? '编辑' : '新增'" v-model="showCreateModal" />
        <kade-appauth-modal :id="state.user.id" title="应用授权" v-model="showAuthModal" />
    </div>
</template>
<script>
import { ref, reactive } from 'vue';
import { 
    ElTable,
    ElTableColumn,
    ElButton,
    ElPagination,
    ElMessageBox,
    ElMessage,
    ElDropdown,
    ElDropdownMenu,
    ElDropdownItem,
} from 'element-plus';
import { useStore } from 'vuex';
import { getSystemUserForPage, reSetPwd } from '@/applications/mocha_itom/api';
import EditModal from './components/edit';
import AppauthModal from './components/appauth';
import { usePagination } from '@/hooks/usePagination';
export default {
  components: {
    'el-table': ElTable,
    'el-button': ElButton,
    'el-table-column': ElTableColumn,
    'el-pagination': ElPagination,
    'el-dropdown': ElDropdown,
    'el-dropdown-menu': ElDropdownMenu,
    'el-dropdown-item': ElDropdownItem,
    'kade-user-edit': EditModal,
    'kade-appauth-modal': AppauthModal,
  },
  setup() {
    const showCreateModal = ref(false);
    const showAuthModal = ref(false);
    const store = useStore();
    const state = reactive({
        user: {},
    });
    const { options, loadData,pageChange,sizeChange } = usePagination(getSystemUserForPage, {}, {}, { currentPage: 'startPage', pageSize: 'rowNum' });
    const addUser = () => {
        state.user = {};
        showCreateModal.value = true;
    }
    const editUser = (user) => {
        state.user = user;
        showCreateModal.value = true;
    }
    const resetPass = ({ id }) => {
        ElMessageBox.prompt(`请输入密码`, '重置密码', {
            inputPattern: /^[^\u4E00-\u9FA5]{6,18}$/,
            inputErrorMessage: '密码格式错误，请输入6~18位非中文字符'
        }).then(async ({ value }) => {
            try{
                const { message } = await reSetPwd({ id, newPwd: value });
                ElMessage.success(message);
            }catch(e) {
                throw new Error(e.message);
            }
        });
    }
    const setPermiss = (row) => {
        store.dispatch('app/addTab', {
            id: `UserPermiss${row.id}`,
            payload: {
                menuName: `用户权限设置(${row.userName})`,
                menuEnName: 'UserPermiss',
                query: {
                    id: row.id,
                }
            }
        });
    }

    const handleCommand = (c, row) => {
        switch(c) {
            case 'operation':
                setPermiss(row);
                break;
            case 'reset':
                resetPass(row);
                break;
            case 'appauth':
                state.user = row;
                showAuthModal.value = true;
                break;
        }
    }
    return {
      options,
      loadData,
      pageChange,
      sizeChange,
      showCreateModal,
      showAuthModal,
      state,
      addUser,
      editUser,
      handleCommand,
    };
  }
}
</script>
<style lang="scss" scoped>
.user{
    width: 100%;
    height: 100%;
}
</style>