<template>
  <div class="tenant-register">
    <el-card class="tenant-card">
      <template #header>
        <div class="tenant-register-header">
          <img :src="logo" alt="" />
          <div class="text">
            100+企业客户口碑见证
            申请注册，立即体检            
          </div>
        </div>
      </template>
      <div class="tenant-register-body">
        <el-form ref="formRef" :rules="rules" :model="state.model" size="large" @keyup.enter="handleSubmit">
          <el-form-item prop="tenantName">
            <el-input v-model="state.model.tenantName" placeholder="请输入完整的公司/单位名称"></el-input>
          </el-form-item>
          <el-form-item prop="tenantEmail">
            <el-input v-model="state.model.tenantEmail" placeholder="请输入邮箱地址"></el-input>
          </el-form-item>
          <el-form-item prop="tenantMobile">
            <el-input v-model="state.model.tenantMobile" placeholder="请输入手机号"></el-input>
          </el-form-item>          
          <el-form-item prop="mobileCode">
            <div class="teantmobile">
              <el-input v-model="state.model.mobileCode" placeholder="请输入验证码"></el-input>
              <el-button
                :loading="state.sendLoading"
                :disabled="state.isSend && !!state.times"
                type="primary"
                @click="handleSend"
              >{{ mgText }}</el-button>
            </div>
          </el-form-item>
          <el-form-item prop="isAgree">
            <el-checkbox v-model="state.model.isAgree">同意<span class="link">《租户协议》</span>和<span class="link">《安全协议》</span></el-checkbox>
          </el-form-item>
          <el-form-item>
            <el-button
              :loading="state.loading"
              @click="handleSubmit"
              style="width: 100%"
              type="primary"
            >立即注册</el-button>
          </el-form-item>                 
        </el-form>
      </div>
    </el-card>
  </div>
</template>
<script>
import {
  ElCard,
  ElForm,
  ElFormItem,
  ElInput,
  ElButton,
  ElCheckbox,
  ElMessage,
} from 'element-plus';
import { ref, reactive, computed } from 'vue';
import { validateEmail, validateMobile } from '@/utils/validate';
import { addTenantSelf, sendSms } from '@/applications/eccard-ops/api';
import Logo from '@/assets/u18.png';

const getDefaultModel = () => ({
  tenantEmail: '',
  mobileCode: '',
  tenantMobile: '',
  tenantName: '',
  tenantOperName: '卡德智能',
  isAgree: false,
});
export default {
  components: {
    'el-card': ElCard,
    'el-form': ElForm,
    'el-form-item': ElFormItem,
    'el-input': ElInput,
    'el-button': ElButton,
    'el-checkbox': ElCheckbox,
  },
  setup() {
    const state = reactive({
      model: getDefaultModel(),
      loading: false,
      isSend: false,
      times: 0,
      sendLoading: false,
    });
    const formRef = ref(null);
    const timer = ref(null);
    const rules = {
      tenantEmail: [{
        required: true,
        message: '请输入邮箱'
      },{
        validator: validateEmail,
      }],
      tenantMobile: [{
        required: true,
        message: '请输入手机号'
      },{
        validator: validateMobile,
      }],
      mobileCode: [{
        required: true,
        message: '请输入验证码',
      }],
      tenantName: [{
        required: true,
        message: '请输入公司或单位名称',
      }],
      isAgree: [{
        validator: (rule, value, callback) => {
          if(!value) {
            callback(new Error('请阅读并同意租户协议及安全协议'));
          } else {
            callback();
          }
        }
      }]
    };
    const mgText = computed(() => {
      if (state.times) {
        return `${state.times}s`;
      } else {
        return '获取验证码';
      }
    });
    const handleSubmit = () => {
      formRef.value.validate(async (valid) => {
        if (valid) {
          try {
            state.loading = true;
            // eslint-disable-next-line no-unused-vars
            const { isAgree, ...fields } = state.model;
            const { message } = await addTenantSelf(fields);
            ElMessage.success(message);
            state.model = getDefaultModel();
            formRef.value.resetFields();
          } catch(e) {
            throw new Error(e.message);
          } finally {
            state.loading = false;
          }
        }
      });
    }
    const reduceTime = () => {
      if(timer.value) {
        clearTimeout(timer.value);
        timer.value = null;
      }
      timer.value = setTimeout(() => {
        state.times -= 1;
        if(state.times > 0) {
          reduceTime();
        }
      }, 1000);
    }
    const handleSend = () => {
      formRef.value.validateField('tenantMobile', async (msg) => {
        if (!msg) {
          try {
            state.sendLoading = true;
            await sendSms({ sendMobile: state.model.tenantMobile });
            state.isSend = true;
            state.times = 60;
            reduceTime();
          } catch(e) {
            throw new Error(e.message);
          } finally {
            state.sendLoading = false;
          }
        }
      });
    }
    return {
      state,
      rules,
      formRef,
      mgText,
      handleSubmit,
      handleSend,
      logo: Logo,
    }
  }
}
</script>
<style lang="scss" scoped>
  .tenant-register{
    width: 100%;
    height: 100%;
    background-image: url(../../../assets/login.jpg);
    background-size: contain;
    .tenant-register-header{
      display: flex;
      justify-content: center;
      flex-direction: column;
      align-items: center;
      font-size: 18px;
      color: #777;
      img{
        width: 300px;
        margin-top: 10px;
      }
      .text{
        margin-top: 15px;
        margin-bottom: 10px;
      }
    }
    .tenant-card{
      position: absolute;
      width: 700px;
      height: 580px;
      left: 0;
      top: 0;
      right: 0;
      bottom: 0;
      margin: auto;
    }
    .tenant-register-body{
      padding: 30px 150px;
      box-sizing: border-box;
    }
    .teantmobile{
      display: flex;
      justify-content: space-between;
      .el-button{
        margin-left: 15px;
      }
    }
    .link{
      color: $primary-color;
    }
  }
</style>