<template>
  <el-dialog
    :model-value="isShow"
    title="人员信息详情"
    width="1500px"
    :before-close="beforeClose"
    :close-on-click-modal="false"
  >
    <kade-tab-wrap :tabs="tabs" style="margin-top: 20px" v-model="state.tab">
      <template #grxx><kade-info /> </template>
      <template #yhk> <kade-bank-card /></template>
      <template #kzzl> <kade-extend-info /></template>
      <template #jt> <kade-family /></template>
      <template #rlzp> <kade-photo /></template>
      <template #fj> <kade-annex /></template>
    </kade-tab-wrap>
  </el-dialog>
</template>
<script>
import { ElDialog } from "element-plus"
import { reactive } from '@vue/reactivity'
import {useStore} from "vuex"
import info from "./info.vue"
import bankCard from "./bankCard.vue"
import extendInfo from "./extendInfo.vue"
import family from "./family.vue"
import photo from "./photo.vue"
import annex from "./annex.vue"
import { computed } from '@vue/runtime-core'
const tabs = [
  {
    name: "grxx",
    label: "个人资料",
  },
  {
    name: "yhk",
    label: "银行卡",
  }, 
  {
    name: "kzzl",
    label: "扩展资料",
  },
  {
    name: "jt",
    label: "家庭",
  }, 
  {
    name: "rlzp",
    label: "人脸照片",
  },
  {
    name: "fj",
    label: "附件",
  }, 
];
export default {
  components:{
    ElDialog,
    "kade-info":info,
    "kade-bank-card":bankCard,
    "kade-extend-info":extendInfo,
    "kade-family":family,
    "kade-photo":photo,
    "kade-annex":annex,
  },
  setup(){
    const store=useStore()
    const state=reactive({
      tab:"grxx"
    })
    const isShow=computed(()=>{
      return store.state.userInfo.isDetails
    })
    const beforeClose=()=>{
      store.commit("userInfo/updateState", {
        key: "isDetails",
        payload: false
      });
      state.tab="grxx"
    }
    return {
      tabs,
      state,
      isShow,
      beforeClose
    }
  }
}
</script>
<style lang="scss" scoped>

</style>