<template>
  <el-dialog
    :model-value="dialogVisible"
    title="登录日志详情"
    width="60%"
    :before-close="handleClose"
  >
    <el-form style="margin-top:20px" inline label-width="100px" size="mini" >
      <el-form-item :label="item.label" v-for="(item,index) in state.dialogList" :key="index">
        <el-input readonly :model-value="data[item.filed]"></el-input>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script>
import { ElDialog, ElForm, ElFormItem, ElInput } from "element-plus";
import { reactive } from "@vue/reactivity";
export default {
  components: {
    ElDialog,
    ElForm,
    ElFormItem,
    ElInput,
  },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    data:{
      types:Object,
      default:{}
    }
  },
  setup(prop, context) {
    const state = reactive({
      dialogList :[
        {label: "日志ID：",filed: "id",},
        {label: "登录结果：",filed: "operateResult"},
        {label: "登录账号：",filed: "operateAccount"},
        {label: "用户名称：",filed: "operateName"},
        {label: "登录地址：",filed: "ip"},
        {label: "登录时间",filed: "operateTime"},
        {label: "请求参数",filed: "requestParam"},
        {label: "返回参数",filed: "result"},
        {label: "客户端系统：",filed: "os"},
        {label: "浏览器名称：",filed: "browser"},
        {label: "响应时间：",filed: "spendTime"}
      ]
    });
    const handleClose = () => {
      context.emit("close", false);
    };
    return {
      state,
      handleClose,
    };
  },
};
</script>

<style lang="scss" scoped>

</style>