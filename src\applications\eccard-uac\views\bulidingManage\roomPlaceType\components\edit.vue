<template>
  <el-dialog :model-value="modelValue" :title="title(editType)" width="800px"
    :before-close="handleClose">
    <el-form inline label-width="150px" size="mini" ref="formRef" :model="state.form"
      :rules="editType !== 'details' && rules">
      <el-form-item label="类型名称：" prop="name">
        <el-input v-if="editType == 'details'" :model-value="state.form.name" readonly></el-input>
        <el-input v-else v-model="state.form.name" placeholder="请输入类型名称"></el-input>
      </el-form-item>
      <el-form-item label="容纳人数：" prop="capacityCount">
        <el-input v-if="editType == 'details'" :model-value="state.form.capacityCount" readonly></el-input>
        <el-input-number v-else v-model="state.form.capacityCount" :min="0" :max="100" />
      </el-form-item>
      <el-form-item label="房间面积：" prop="space">
        <el-input v-if="editType == 'details'" :model-value="state.form.space" readonly></el-input>
        <el-input v-else v-model="state.form.space" placeholder="请输入房间面积"></el-input>
      </el-form-item>
      <el-form-item label="启用状态：" prop="useStatus">
        <el-input v-if="editType == 'details'" :model-value="dictionaryFilter(state.form.useStatus)" readonly></el-input>
        <el-select v-else v-model="state.form.useStatus" clearable placeholder="全部">
          <el-option v-for="(item, index) in statusList" :key="index" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="房间图：">
        <el-image v-if="editType == 'details'" style="width: 100px; height: 100px" :src="state.form.resourceUrl"
          :preview-src-list="[state.form.resourceUrl]" :initial-index="0" fit="cover"></el-image>
        <kade-single-image-upload v-else v-model="state.form.resourceUrl" :action="uploadApplyLogo"
          icon="el-icon-plus" />
      </el-form-item>
    </el-form>
    <template #footer v-if="editType !== 'details'">
      <span class="dialog-footer">
        <el-button type="primary" @click="handleSave"  :loading="state.loading" size="mini">保存</el-button>
        <el-button @click="handleClose" size="mini">返回</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { ElDialog, ElButton, ElForm, ElFormItem, ElInput, ElInputNumber, ElSelect, ElOption, ElImage, ElMessage } from "element-plus"
import { reactive, ref, nextTick } from 'vue'
import { useDict } from "@/hooks/useDict.js";
import { uploadApplyLogo } from "@/applications/unified_portal/api";
import { roomTypeAdd, roomTypeEdit } from "@/applications/eccard-uac/api";
import SingleImageUpload from "@/components/singleImageUpload";
import { watch } from '@vue/runtime-core';
export default {
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    selectRow: {
      types: Object,
      default: null
    },
    editType: {
      types: String,
      default: ''
    }
  },
  components: {
    ElDialog,
    ElButton,
    ElForm,
    ElFormItem,
    ElInput,
    ElInputNumber,
    ElSelect,
    ElOption,
    ElImage,
    "kade-single-image-upload": SingleImageUpload,
  },
  setup(props, context) {
    const boolList = useDict("SYS_BOOL_STRING");
    const statusList = useDict("SYS_ENABLE");
    const formRef = ref(null)
    const state = reactive({
      loading: false,
      form: {
        resourceUrl: "",
        sysPreset:"FALSE"
      }
    });
    watch(() => props.modelValue, val => {
      if (val) {
        if (props.editType == 'add') {
          state.form = {
            resourceUrl: "",
            sysPreset:"FALSE"
          }
        } else {
          state.form = { ...props.selectRow }
        }
        nextTick(() => {
          formRef.value.clearValidate()
        })
      }
    })
    const title = (val) => {
      if (val == 'add') {
        return '新建房间场所类型'
      } else if (val == 'edit') {
        return '编辑房间场所类型'
      } else if (val == 'details') {
        return '房间场所类型详情'
      }
    }
    const rules = {
      name: [
        {
          required: true,
          message: "请输入类型名称",
        },
        {
          max: 20,
          message: "类型名称长度不能超过20字符",
        },
        {
          pattern: /^\S.*\S$|(^\S{0,1}\S$)/,
          message: "类型名称首尾不能包含空格",
        },
      ],
      capacityCount: [
        {
          required: true,
          message: "请输入容纳人数",
        },
      ],
      space: [
        {
          required: true,
          message: "请输入房间面积",
        },
        {
          pattern: /^([1-9][0-9]{0,2}|1000)$/,
          message: '房间面积在1-1000m²',
          trigger: 'blur'
        },
      ],
      useStatus: [
        {
          required: true,
          message: "请选择启用状态",
          trigger: "change",
        },
      ]
    }
    const handleSave = () => {
      formRef.value.validate(async valid => {
        if (valid) {
          let fn = props.editType == 'add' ? roomTypeAdd : roomTypeEdit
          state.loading = true
          try {
            let { code, message } = await fn(state.form)
            if (code === 0) {
              ElMessage.success(message)
              context.emit("update:modelValue", true)
            }
            state.loading = false
          }
          catch {
            state.loading = false
          }
        } else {
          return false
        }
      })
    }
    const handleClose = () => {
      context.emit("update:modelValue", false)
    };
    return {
      uploadApplyLogo,
      boolList,
      statusList,
      formRef,
      state,
      title,
      rules,
      handleSave,
      handleClose,
    }
  }
}
</script>

<style lang="scss" scoped>
.el-form {
  margin-top: 20px;
}

:deep(.el-input-number) {
  width: 192px;
}

:deep(.el-input) {
  width: 192px;
}

:deep(.single-image-uploader) {
  width: 100px;
  height: 100px;

  .el-upload {
    width: 100px;
    height: 100px;
  }

  .images {
    width: 100px;
    height: 100px;
  }

  .image-slot {
    width: 100px;
    height: 100px;
  }

  .element-icons {
    font-size: 40px !important;
  }
}
</style>
