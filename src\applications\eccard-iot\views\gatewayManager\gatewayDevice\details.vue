<template>
  <el-dialog :model-value="modelValue" :title="TabModule().title+'详情'" width="90%" :before-close="beforeClose">
    <kade-tab-wrap :tabs="tabs" v-model="state.tab" class="tab-warp">
      <template #wgxx>
        <kade-gateway-info :rowData="rowData" />
      </template>
      <template #bdsblb>
        <kade-gateway-device-list :rowData="rowData" />
      </template>
    </kade-tab-wrap>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="beforeClose()" size="mini">返&nbsp;&nbsp;回</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script>
import { ElDialog, ElButton } from "element-plus";
import info from "./components/info";
import bindDeviceList from "./components/bindDeviceList";
import { onMounted, reactive,watch } from "vue";
const tabs = [
  {
    name: "wgxx",
    label: "网关信息",
  },
  {
    name: "bdsblb",
    label: "绑定设备列表",
  },
];

export default {
  name: "paramInfo",
  emits:["update:modelValue"],
  components: {
    ElDialog,
    ElButton,
    "kade-gateway-info": info,
    "kade-gateway-device-list": bindDeviceList,
  },
  props: {
    modelValue:{
      type:Boolean,
      default:false
    },
    TabModule: {
      type: Function,
      default: () => {},
    },
    rowData:{
      type:Object,
      default:null
    }
  },
  setup(props, context) {
    const state = reactive({
      //参数设置弹出框
      tab: "wgxx",
      isParams: false,
    });
    watch(
      () => props.modelValue,
      (val) => {
        if (!val) {
          state.tab = "wgxx";
        }
      }
    );
    const beforeClose = () => {
      context.emit("update:modelValue", false);
    };
    onMounted(() => {});
    return {
      state,
      tabs,
      beforeClose,
    };
  },
};
</script>

<style lang="scss" scoped>
.tab-warp{
    margin: 20px 0;
}
</style>
