<template>
  <div>
    <el-table style="width: 100%" :data="walletTradeList" border stripe>
      <el-table-column
        v-for="(item, index) in state.columns"
        :key="index"
        :label="item.label"
        :prop="item.prop"
        align="center"
      >
        <template v-if="item.filter || item.isEdit" #default="scope">
          <div v-if="item.filter">
            {{ filterDictionary(scope.row.walletType, state.walletTypeList) }}
          </div>
          <el-switch
            @click="switchChange(scope.row)"
            v-if="item.isEdit"
            :model-value="scope.row.useStatus"
            active-value="ENABLE_TRUE"
            inactive-value="ENABLE_FALSE"
          />
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<script>
import { ElTable, ElTableColumn, ElSwitch, ElMessage } from "element-plus";
import { computed, reactive, watch } from "vue";
import { useStore } from "vuex";
import { updateWalletTradeModeStatus } from "@/applications/eccard-finance/api";

export default {
  components: {
    ElTable,
    ElTableColumn,
    ElSwitch,
  },
  setup() {
    const store = useStore();
    const state = reactive({
      columns: [
        {
          label: "充值方式",
          prop: "tradeModeName",
        },
        {
          label: "是否启用",
          prop: "useStatus",
          isEdit: true,
        },
      ],
    });

    const walletTradeList = computed(() => {
      return store.state.walletManagerData.walletTradeList;
    });

    watch(
      () => store.state.walletManagerData.selectRow,
      (val) => {
        if (val) {
          store.dispatch("walletManagerData/getWalletTrade", {
            tradeType: 101,
          });
        }
      }
    );

    const switchChange =async (row) => {
      console.log(row);
      let params = {
        tradeMode: row.tradeMode,
        tradeType: 101,
        useStatus: row.useStatus=="ENABLE_TRUE"?"ENABLE_FALSE":"ENABLE_TRUE",
        walletId:store.state.walletManagerData.selectRow.walletId,
      };
      let {message,code}=await updateWalletTradeModeStatus(params);
      if(code===0){
        ElMessage.success(message)
        store.dispatch("walletManagerData/getWalletTrade",{tradeType:101});
      }
    };

    return {
      state,
      walletTradeList,
      switchChange,
    };
  },
};
</script>