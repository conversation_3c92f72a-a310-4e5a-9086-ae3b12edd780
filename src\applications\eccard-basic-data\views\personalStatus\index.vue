<template>
    <div class="personalStatus">
        <el-card>
            <div class="table-toolbox">
              <el-button size="small" type="primary" @click="state.showCreateModal = true">新增</el-button>
            </div>          
            <el-table style="width: 100%" :data="state.data" v-loading="state.loading" border stripe>
                <el-table-column
                    label="人员状态"
                    prop="applyName"
                    width="150"
                ></el-table-column>
                <el-table-column
                    label="备注"
                    prop="status"
                    width="150"
                ></el-table-column>
                <el-table-column
                    label="人数"
                    prop="applyLogo"
                ></el-table-column>                                                                                            
                <el-table-column
                    label="操作"
                    align="center"
                    width="100"
                >
                    <template #default>
                        <el-button type="info" size="mini">编辑</el-button>
                        <el-button type="danger" size="mini">删除</el-button>
                    </template>
                </el-table-column>                                    
            </el-table>
            <div class="pagination">
              <el-pagination
                background
                layout="prev, pager, next"
                :total="1000">
              </el-pagination>              
            </div>           
        </el-card>
        <kade-role-edit title="状态编辑" v-model="state.showCreateModal" />
    </div>
</template>
<script>
import { reactive, onMounted } from 'vue';
import { 
    ElTable,
    ElTableColumn,
    ElButton,
    ElCard,
    ElPagination,
} from 'element-plus';
import { getRolelist } from '@/applications/eccard-basic-data/api';
import EditModal from './components/edit';
export default {
  components: {
    'el-table': ElTable,
    'el-button': ElButton,
    'el-table-column': ElTableColumn,
    'el-card': ElCard,
    'el-pagination': ElPagination,
    'kade-role-edit': EditModal,
  },
  setup() {
    const state = reactive({
        data: [],
        loading: false,
        showCreateModal: false,
        query: {
          title: '',
          type: '',
          time: ''
        }
    });
    const loadData = async () => {
        state.loading = true;
        try{
            const { retData: { retData }} = await getRolelist();
            state.data = retData;                
        } catch(e) {
            throw new Error(e);
        } finally {
            state.loading = false;
        }
    };

    onMounted(() => {
        loadData();
    });
    return {
      state,
    };
  }
}
</script>
<style lang="scss" scoped>
.personalStatus{
    width: 100%;
    height: 100%;
}
</style>