<template>
  <div class="padding-box" v-if="details">
    <div class="basic-info-box">
      <div class="box-item" v-for="(item, index) in list" :key="index">
        <div class="item-label">{{ item.label }}</div>
        <div class="item-value" v-if="item.render">{{item.render(details[item.valueKey])}}</div>
        <div class="item-value" v-else>{{details[item.valueKey]}}</div>
      </div>
    </div>
  </div>
  <el-empty v-else description="当前设备参数未设定"></el-empty>

</template>
<script>
import { computed } from "@vue/runtime-core";
import { useStore } from "vuex";
import { ElEmpty } from "element-plus"

export default {
  components: {
    ElEmpty
  },
  setup() {
    const store = useStore();
    const details = computed(() => {
      let data = {}
      if (store.state['identityDevice/faceDistingDevice'].detailsParams.temperature) {
        data = JSON.parse(store.state['identityDevice/faceDistingDevice'].detailsParams.temperature.paramContent);
      } else {
        data = ''
      }
      console.log(data);
      return data;
    });
    const dictListFnc = () => {
      return store.state['identityDevice/faceDistingDevice'].faceDict
    }
    const list = [
      { label: "测温模式开关", valueKey: "temperatureSwith", render: val => dictListFnc().temperatureSwith.filter(item => item.value == val)[0]?.label },
      { label: "异常温度语音播报开关", valueKey: "nomalTemperatureSwith", render: val => dictListFnc().nomalTemperatureSwith.filter(item => item.value == val)[0]?.label },
      { label: "语音自定义", valueKey: "voiceCustomization" },
      { label: "异常温度判断值", valueKey: "abnormalTemperatureValue" },
      { label: "体温异常通行开关", valueKey: "abnormalTemperatureSwith", render: val => dictListFnc().abnormalTemperatureSwith.filter(item => item.value == val)[0]?.label },
      { label: "测温位置", valueKey: "temperaturePosition", render: val => dictListFnc().temperaturePosition.filter(item => item.value == val)[0]?.label },
      { label: "有效温度最低值", valueKey: "temperatureLowValue", render: val => val == 1 ? '是' : '否' },
      { label: "有效温度最高值", valueKey: "strangerSwitch", render: val => val == 1 ? '是' : '否' },
      { label: "环境温度补偿开关", valueKey: "temperatureCompensationSwitch", render: val => dictListFnc().temperatureCompensationSwitch.filter(item => item.value == val)[0]?.label },
      { label: "设备模式", valueKey: "deviceMode", render: val => dictListFnc().deviceMode.filter(item => item.value == val)[0]?.label },
      { label: "测温模块类型", valueKey: "temperatureModuleType", render: val => dictListFnc().temperatureType.filter(item => item.value == val)[0]?.label },
    ];
    return {
      list,
      details,
    };
  },
};
</script>
<style lang="scss" scoped>
.basic-info-box {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  border-left: 1px solid #ccc;
  border-top: 1px solid #ccc;
  min-width: 900px;
  .box-item {
    box-sizing: border-box;
    width: 33.333%;
    min-width: 300px;
    line-height: 50px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #ccc;
    .item-label {
      width: 50%;
      text-align: center;
      background: #f6f6f6;
      border-right: 1px solid #ccc;
    }
    .item-value {
      text-align: center;
      width: 50%;
      height: 50px;
      background: #fff;
      border-right: 1px solid #ccc;
    }
  }
}
</style>