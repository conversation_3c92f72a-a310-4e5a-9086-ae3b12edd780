<template>
  <el-dialog :model-value="isShow" :title="(rowData.id?'编辑':'新增')+'岗位'" width="500px" :before-close="beforeClose">
    <el-form ref="formRef" size="mini" label-width="120px" :model="state.form" :rules="rules" style="margin-top:20px">
      <el-form-item label="岗位编号:" prop="postCode">
        <el-input v-model="state.form.postCode" placeholder="请输入"></el-input>
      </el-form-item>
      <el-form-item label="岗位名称:" prop="postName">
        <el-input v-model="state.form.postName" placeholder="请输入"></el-input>
      </el-form-item>
      <el-form-item label="备注:" prop="postRemark">
        <el-input v-model="state.form.postRemark" type="textarea" placeholder="请输入"></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="beforeClose()" size="mini">返&nbsp;&nbsp;回</el-button>
        <el-button @click="submit()" type="primary" size="mini">保&nbsp;&nbsp;存</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script>
import {
  ElDialog,
  ElForm,
  ElFormItem,
  ElInput,
  ElButton,
  ElMessage,
} from "element-plus";
import { reactive, ref } from "@vue/reactivity";
import { nextTick, watch } from "@vue/runtime-core";
import { addStation, editStation } from "@/applications/eccard-basic-data/api";
const rules = {
  postCode: [
    {
      required: true,
      message: "请输入岗位编号",
    },
    {
      pattern: /^[0-9a-zA-Z]+$/,
      message: "请输入字母+数字",
    },
    {
      max: 20,
      message: "岗位编号长度不能超过20字符",
    },
  ],
  postName: [
    {
      required: true,
      message: "请输入岗位名称",
    },
    {
      max: 20,
      message: "岗位名称长度不能超过20字符",
    },
    {
      pattern: /^\S.*\S$|(^\S{0,1}\S$)/,
      message: "岗位名称首尾不能包含空格",
    },
  ],
};
export default {
  components: {
    ElDialog,
    ElForm,
    ElFormItem,
    ElButton,
    ElInput,
  },
  props: {
    isShow: {
      type: Boolean,
      default: false,
    },
    rowData: {
      type: Object,
      default: null,
    },
  },
  setup(props, context) {
    const formRef = ref(null);
    const state = reactive({
      form: {},
    });
    watch(
      () => props.isShow,
      (val) => {
        state.form = val&&props.rowData.id ? { ...props.rowData } : {};
        nextTick(()=>{
            formRef.value.clearValidate();
        })
      }
    );
    const submit = () => {
      formRef.value.validate(async (valid) => {
        if (valid) {
          let fn = props.rowData.id ? editStation : addStation;
          let { code, message } = await fn(state.form);
          if (code === 0) {
            ElMessage.success(message);
            context.emit("close", true);
          }
        } else {
          return false;
        }
      });
    };
    const beforeClose = () => {
      context.emit("close", false);
    };
    return {
      rules,
      formRef,
      state,
      submit,
      beforeClose,
    };
  },
};
</script>
<style lang="scss" scoped>
.el-divider--horizontal {
  margin: 0px 0px 10px;
}
.kade-table-wrap {
  padding-bottom: 0;
}
</style>