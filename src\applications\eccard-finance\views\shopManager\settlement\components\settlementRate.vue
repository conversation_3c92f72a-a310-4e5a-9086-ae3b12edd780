<template>
  <div class="padding-form-box">
    <el-form inline size="small" label-width="100px">
      <el-form-item label="当前结算费率:">
        <el-input v-model="state.rate" placeholder="请输入">
          <template #suffix>%</template>
        </el-input>
      </el-form-item>
      <el-form-item>
        <el-button @click="editRate" :disabled="!id" type="primary">修改</el-button>
      </el-form-item>
    </el-form>
  </div>
  <el-table style="width: 100%" :data="state.dataList" v-loading="state.loading" border stripe>
    <el-table-column label="操作时间" prop="lastModifyTime" align="center" show-overflow-tooltip></el-table-column>
    <el-table-column label="操作人" align="center" prop="userName" show-overflow-tooltip>
    </el-table-column>
    <el-table-column label="调整前费率(%)" align="center" show-overflow-tooltip prop="beforeRate">
    </el-table-column>
    <el-table-column label="调整后费率(%)" align="center" show-overflow-tooltip prop="afterRate">
    </el-table-column>
  </el-table>
  <div class="pagination">
    <el-pagination background v-model:current-page="state.form.currentPage" layout="total, sizes, prev, pager, next, jumper" :page-sizes="[10, 20, 50, 100, 200]" :total="state.total" v-model:page-size="state.form.pageSize" @current-change="handlePageChange" @size-change="handleSizeChange">
    </el-pagination>
  </div>
</template>
<script>
import {
  ElTable,
  ElTableColumn,
  ElButton,
  ElInput,
  ElForm,
  ElFormItem,
  ElPagination,
  ElMessage,
} from "element-plus";
import { onMounted, reactive, watch } from "vue";
import {
  getMerchantRateLogListByPage,
  addMerchantRateLog,
} from "@/applications/eccard-finance/api";

export default {
  components: {
    "el-button": ElButton,
    "el-table": ElTable,
    "el-input": ElInput,
    "el-table-column": ElTableColumn,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    ElPagination,
  },
  props: {
    id: {
      type: [String, Number, undefined, null],
    },
    selected: {
      type: Object,
      default: null,
    },
    loadData:{
      type:Function,
      default:null
    }
  },
  setup(props) {
    const state = reactive({
      dataList: [],
      loading: false,
      rate: 0,
      form: {
        currentPage: 1,
        pageSize: 10,
      },
      total: 0,
    });
    watch(
      () => props.id,
      (n, o) => {
        if (n !== o) {
          state.form.merchantId = props.id;
          state.rate = props.selected.merchantRate;
          getList();
        }
      }
    );
    const getList = async () => {
      if (!props.id) return;
      state.form.merchantId = props.id;
      let {
        data: { list, total },
      } = await getMerchantRateLogListByPage(state.form);
      state.dataList = list;
      state.total = total;
    };
    const handlePageChange = (val) => {
      state.form.currentPage = val;
      getList();
    };
    const handleSizeChange = (val) => {
      state.form.currentPage = 1;
      state.form.pageSize = val;
      getList();
    };

    const editRate = async () => {
      let params = {
        beforeRate: Number(props.selected.merchantRate),
        merchantId: props.selected.merchantId,
        afterRate: Number(state.rate),
      };
      let { code, message } = await addMerchantRateLog(params);
      if (code === 0) {
        ElMessage.success(message);
        props.loadData()
        getList();
      }
    };

    onMounted(() => {
      if (props.id) {
        state.form.merchantId = props.id;
        state.rate = props.selected.merchantRate;
        getList();
      }
    });
    return {
      state,
      handlePageChange,
      handleSizeChange,
      editRate,
    };
  },
};
</script>