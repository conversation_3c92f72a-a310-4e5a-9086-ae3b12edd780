import { createApp } from 'vue';
import PrintTableModal from './printTableModal';

class PrintTable {
  constructor() {
    this.instance = null;
    this.container = null;
  }
  show(columns, dataList) {
    if(this.instance) {
      this.close();
    }
    const close = this.close.bind(this);
    this.instance = createApp(PrintTableModal, { dataList, columns, close });
    this.container = document.createElement('div');
    document.body.appendChild(this.container);
    this.instance.mount(this.container);
  }
  close() {
    if(this.instance && this.container) {
      this.instance.unmount(this.container);
      document.body.removeChild(this.container);
      this.container = null;
      this.instance = null;
    }
  }
}

export const printTable = new PrintTable();