<template>
  <kade-route-card style="height: auto">
    <kade-table-wrap title="补助发放列表">
      <template #extra>
        <el-button icon="el-icon-daoru" size="small" class="btn-green" @click="state.dialogVisibleAdd = true">补助发放
        </el-button>
        <el-button icon="el-icon-daoru" size="small" class="btn-yellow" @click="importRighting()">导入冲正</el-button>
        <el-button icon="el-icon-daoru" size="small" class="btn-purple" @click="exportList()">下载查询明细</el-button>
        <el-button icon="el-icon-daochu" size="small" class="btn-blue" @click="printClick()">打印</el-button>
      </template>
      <kade-tab-wrap :tabs="tabs" v-model="state.tab" @active="active">
        <template #qb>
          <kade-all-subsidy-grant />
        </template>
        <template #dsh>
          <kade-examine-subsidy-grant />
        </template>
        <template #ytg>
          <kade-to-pass-subsidy-grant />
        </template>
        <template #wtg>
          <kade-out-pass-subsidy-grant />
        </template>
      </kade-tab-wrap>

      <kade-add-subsidy-grant :dialogVisible="state.dialogVisibleAdd" @openAdd="openAdd" @offAdd="offAdd" />
      <kade-import-reversal :dialogVisible="state.isImportReversal" @offImportReversal="offImportReversal" />
    </kade-table-wrap>
  </kade-route-card>
</template>
<script>
import { ElButton, ElMessage } from "element-plus";
import { onMounted, reactive } from "vue";
import { print } from "@/utils";
import allSubsidyGrant from "./allSubsidyGrant.vue";
import examineSubsidyGrant from "./examineSubsidyGrant.vue";
import outPassSubsidyGrant from "./outPassSubsidyGrant.vue";
import toPassSubsidyGrant from "./toPassSubsidyGrant.vue";

import addSubsidyGrant from "./components/addSubsidyGrant.vue";
import importReversal from "./components/importReversal.vue";

import {
  getSubsidyTypeList,
  getRolelist,
  getSubsidyGrantListByExport,
  getSubsidyGrantListByPrint
} from "@/applications/eccard-finance/api";
import { useStore } from "vuex";

const tabs = [
  {
    name: "qb",
    label: "全部",
  },
  {
    name: "dsh",
    label: "待审核",
  },
  {
    name: "ytg",
    label: "已通过",
  },
  {
    name: "wtg",
    label: "未通过",
  },
];
export default {
  components: {
    ElButton,
    "kade-all-subsidy-grant": allSubsidyGrant,
    "kade-examine-subsidy-grant": examineSubsidyGrant,
    "kade-to-pass-subsidy-grant": toPassSubsidyGrant,
    "kade-out-pass-subsidy-grant": outPassSubsidyGrant,

    "kade-add-subsidy-grant": addSubsidyGrant,
    "kade-import-reversal": importReversal,
  },
  setup() {
    const store = useStore();
    const state = reactive({
      tab: "qb",
      dialogVisibleAdd: false,
      isImportReversal: false,
    });
    //获取补助类型列表
    const querySubsidyTypeList = () => {
      getSubsidyTypeList({stStatus:'ENABLE_TRUE'}).then((res) => {
        store.commit("subsidyData/updateState", {
          key: "subsidyTypeList",
          payload: res.data,
        });
      });
    };

    //获取身份类别
    const queryRolelist = () => {
      getRolelist().then((res) => {
        store.commit("subsidyData/updateState", {
          key: "roleList",
          payload: res.data,
        });
      });
    };

    const importRighting = () => {
      if (store.state.subsidyData.selectRow) {

        if (store.state.subsidyData.selectRow.entryStatus == '全部入账') {
          state.isImportReversal = true;
        } else {
          ElMessage.error("当前选择项目未全部到账！");
        }
      } else {
        ElMessage.error("请选择补助项目！");
      }
    };

    const exportList = () => {
      let data = store.state.subsidyData.exportParam;

      getSubsidyGrantListByExport(data).then((res) => {
        let url = window.URL.createObjectURL(new Blob([res]));
        let link = document.createElement("a");
        link.style.display = "none";
        link.href = url;
        link.setAttribute("download", "补助发放列表.xlsx");
        document.body.appendChild(link);
        link.click();
      });
    };

    const active = () => {
      store.commit("subsidyData/updateState", {
        key: "isEdit",
        payload: false,
      });
    };

    const offAdd = (val) => {
      if (val) {
        if (state.tab == "qb") {
          store.commit("subsidyData/updateState", {
            key: "addSuccess",
            payload: true,
          });
        }
        state.tab = "qb";
      }
      state.dialogVisibleAdd = false;
    };

    const openAdd = () => {
      state.dialogVisibleAdd = true;
    };
    const offImportReversal = () => {
      state.isImportReversal = false;
    };

    const printClick = async () => {
      let params = store.state.subsidyData.exportParam;
      let { code, data } = await getSubsidyGrantListByPrint(params);
      if (code === 0) {
        print(data, "补助发放明细")
      }
    }

    onMounted(() => {
      querySubsidyTypeList();
      queryRolelist();
    });
    return {
      state,
      tabs,
      printClick,
      exportList,
      importRighting,
      offImportReversal,
      active,
      offAdd,
      openAdd,
    };
  },
};
</script>
<style lang="scss" scoped>
.grant-search-box {
  :deep(.el-form-item__content) {
    .el-input__inner {
      width: 120px;
    }

    .el-date-editor {
      width: 360px;
    }
  }
}
</style>