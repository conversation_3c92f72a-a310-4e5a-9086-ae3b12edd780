<template>
  <div class="chart-box">
    <div id="echartLine" class="echartDiv"></div>
    <div class="ranking">
      <div class="title">商家销售额排行</div>
      <div class="rank-list" v-for="(item, index) in rankList" :key="index">
        <div class="number" :class="index>2?'after-number':'before-number'">{{index+1}}</div>
        <div class="label-box">{{ item.label }}</div>
        <div>{{ item.value }}</div>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from "echarts";
import { nextTick, onMounted } from "vue";

const rankList = [
  { label: "工专路一号店", value: "323,234" },
  { label: "工专路二号店", value: "323,234" },
  { label: "工专路三号店", value: "323,234" },
  { label: "工专路四号店", value: "323,234" },
  { label: "工专路五号店", value: "323,234" },
  { label: "工专路六号店", value: "323,234" },
  { label: "工专路七号店", value: "323,234" },
];

export default {
  setup() {
    const echartInit = () => {
      var chartDom = document.getElementById("echartLine");
      var myChart = echarts.init(chartDom);

      // 指定图表的配置项和数据
      var option = {
        title: {
          text: "销售额趋势",
          textStyle: {
            fontSize: 16,
          },
        },
        legend: {
          data: ["会员登录次数"],
        },
        grid: {
          top: "20%",
          left: "1%",
          right: "15%",
          bottom: "10%",
          containLabel: true,
        },
        xAxis: {
          data: [
            "10月",
            "11月",
            "12月",
            "01月",
            "02月",
            "03月",
            "04月",
            "05月",
            "06月",
            "07月",
            "08月",
            "09月",
          ],
          axisTick: {
            alignWithLabel: true,
          },
          axisLine: {
            lineStyle: {
              color: "#9498b0s",
            },
          },
          axisLabel:{
            margin:20,
            textStyle:{
              fontSize:14,
            },
          },
        },
        yAxis: {
          type: "value",
          splitLine: {
            show: true,
            lineStyle: {
              type: "dashed",
            },
          },
          axisLabel:{
            margin:20,
            textStyle:{
              fontSize:12,
            }
          }
        },
        series: [
          {
            data: [270, 760, 990, 510, 160, 510, 260, 375, 760, 510, 260, 375],
            type: "bar",
            color: "#3ba1ff",
            barWidth: 18,
          },
        ],
      };
      // 使用刚指定的配置项和数据显示图表。
      // myChart.setOption(option);
      nextTick(() => {
        myChart.resize();
        myChart.setOption(option, true);
      });
    };
    //挂载
    onMounted(() => {
      echartInit();
    });
    return {
      echartInit,
      rankList,
    };
  },
};
</script>

<style lang="scss" scoped>
.chart-box {
  display: flex;
  width: 100%;
  .echartDiv {
    height: 250px;
    width: 900px;
  }
  .ranking {
    flex: 1;
    min-width: 300px;
    margin-right: 200px;
  }
  .title {
    font-size: 16px;
    font-weight: 800;
    color: #000000d8;
    line-height: 40px;
  }
  .rank-list {
    display: flex;
    align-items: center;
    color: #000000a5;
    font-size: 14px;
    margin-top: 10px;
    .number {
      width: 20px;
      text-align: center;
      font-size: 13px;
      line-height: 20px;
      border-radius: 10px;
      margin-right: 20px;
    }
    .before-number {
      background: #314659;
      color: #fff;
    }
    .after-number {
      background: #f0f2f5;
      color: #314659;
    }
    .label-box {
      margin-right: 70px;
    }
  }
}
</style>