<template>
    <i :class="['kade-iconfont', 'element-icons', icon, { reverse } ]" :style="{ fontSize: size, color }"></i>
</template>
<script>
import { computed } from 'vue';
export default {
    functional: true,
    name: 'kade-icon',
    props: {
        size: {
            type: String,
            default: '18px'
        },
        name: {
            type: String,
            required: true
        },
        color: {
            type: String,
            default: '#333'
        },
        reverse: {
            type: Boolean,
            default: false,
        }
    },
    setup(props) {
        const icon = computed(() => {
            let str = '';
            if (/^icon/gim.test(props.name)) {
                str = 'el-icon-' + props.name.replace(/^icon/, '');
            } else if(/^el-icon-/gim.test(props.name)) {
                str = props.name;
            } else {
                str = 'el-icon-' + props.name;
            }
            return str;
        });
        return {
            icon,
        }
    }
}
</script>
<style lang="scss">
.kade-iconfont{
    cursor: pointer;
    transition: transform .2s linear;
    &.reverse{
        transform: rotate(180deg);
    }
}
</style>