<template>
  <div class="padding-box">
    <kade-tab-wrap :tabs="tabs" v-model="state.tab">
      <template #mjqx>
        <kade-auth-AC />
      </template>
      <template #yhqx>
        <kade-auth-person />
      </template>
    </kade-tab-wrap>
  </div>
</template>
<script>
import { reactive } from '@vue/reactivity'
import authAC from './authAC'
import authPerson from './authPerson'
const tabs = [
  {
    label: '门禁权限',
    name: 'mjqx'
  },
  {
    label: '用户权限',
    name: 'yhqx'
  }
]
export default {
  name: 'auth',
  components: {
    'kade-auth-AC': authAC,
    'kade-auth-person': authPerson
  },
  setup() {
    const state = reactive({
      tab: 'mjqx'
    })
    return {
      tabs,
      state
    }
  },
}
</script>
<style lang="scss" scoped>
:deep(.el-dialog__footer){
  text-align: center;
}
</style>