<template>
  <el-dialog :model-value="modelValue" title="反潜回批量设置" width="1000px" :before-close="handleClose"
    :close-on-click-modal="false">
    <div style="padding:20px 0 0">
      <kade-select-table :isShow="modelValue" :value="[]" :reqFnc="() => { }"
        :selectCondition="selectCondition" :column="column" :params="params" :labelWidth="120" @change="personChange" />
    </div>
    <el-form label-width="100px" size="mini">
      <el-form-item label="批量设置为：">
        <el-select>
          <el-option v-for="(item, index) in 10" :key="index" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose" size="mini">取消</el-button>
        <el-button type="primary" v-if="type != 'details'" @click="submit" size="mini">保存</el-button>
        <el-button type="primary" v-else @click="$emit('edit')" size="mini">编辑</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script>
import { ElDialog, ElButton,ElForm,ElFormItem,ElSelect,ElOption } from "element-plus"
import { reactive } from '@vue/reactivity'
import selectTable from "@/components/table/selectTable.vue";
const column = [
  { label: "所属区域", prop: "userCode", isDict: false, width: "" },
  { label: "所属控制器编号", prop: "deptName", isDict: false, width: "" },
  { label: "控制器SN号", prop: "roleName", isDict: false, width: "" },
  { label: "所控制的门", prop: "roleName", isDict: false, width: "" },
];
const params = {
  currentPageKey: "beginPage",
  pageSizeKey: "rowNum",
  resListKey: "dataList",
  resTotalKey: "totalCount",
  value: {},
  tagNameKey: "userName",
  valueKey: "id",
};
export default {
  components: {
    ElDialog, ElButton,ElForm,ElFormItem,ElSelect,ElOption,
    "kade-select-table": selectTable,
  },
  emits: ["update:modelValue"],
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
  },
  setup(props, context) {
    const state = reactive({
      form: {},
    })
    const selectCondition = [
      { label: "区域", valueKey: "areaId", dataKey: "id", placeholder: "请选择", isSelect: false, isTree: "area" },
      { label: "读卡器机号", valueKey: "userName", placeholder: "请输入", isSelect: false, },
      { label: "所属控制器编号", valueKey: "userName", placeholder: "请输入", isSelect: false, },
      { label: "所属控制器SN号", valueKey: "userName", placeholder: "请输入", isSelect: false, },
    ]
    const handleClose = () => {
      context.emit("update:modelValue", false)
    }
    return {
      column,
      params,
      state,
      selectCondition,
      handleClose
    }
  }
}
</script>
<style lang="scss" scoped>
:deep(.kade-table-wrap) {
  margin-bottom: 20px;
}

.padding-box {
  padding: 10px 10px 0
}

.el-divider--horizontal {
  margin: 0
}

:deep(.box) {
  padding: 10px 10px 0;

  .el-form {
    .el-select {
      width: 150px;
    }

    .el-input {
      width: 150px
    }
  }
}

.el-table {
  border-left: 1px solid #EBEEF5;
  border-right: 1px solid #EBEEF5;
}
</style>