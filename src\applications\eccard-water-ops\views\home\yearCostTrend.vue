<template>
  <div id="mychart5" class="echartDiv"></div>
</template>
<script>
import * as echarts from "echarts";
import { onMounted,ref,nextTick, watch } from "vue";
import { getYearCost } from "../../api/home.js";
export default {
  props: {
    queryDate: {
      type: Object,
      default: () => ({
        startTime: '',
        endTime: '',
        type: '1' // 按天
      })
    }
  },
  setup(props) {
    const chartData = ref({
      xAxisData: [],
      waterData: [],
      electricData: []
    });

    // 获取趋势数据
    const fetchYearCostData = async () => {
      console.log('父组件传递的参数:', props.queryDate);
      const params = props.queryDate
      const response = await getYearCost(params);
      console.log('年度运营成本对比:', response);
      if (response && response.code === 0 && response.data) {
          // 处理接口返回的数据结构
          const data = response.data || [];
          // 提取年份作为X轴数据
          const xAxisData = data.map(item => item.year);
          const waterData = data.map(item => parseFloat(item.waterCost) || 0);
          const electricData = data.map(item => parseFloat(item.elecCost) || 0);
          chartData.value = {
            xAxisData: xAxisData,
            waterData: waterData,
            electricData: electricData
          };
        // 重新初始化图表
        echartInit();
      }
    };

    const echartInit = () => {
      var chartDom = document.getElementById("mychart5");
      var myChart = echarts.init(chartDom);
      // 指定图表的配置项和数据
      var option = {
        title: {
          text: '年度运营成本趋势'
        },
        grid: {
          top: "20%",
          left: "0%",
          right: "0%",
          bottom: "5%",
          containLabel: true,
        },
        xAxis: {
          data: chartData.value.xAxisData,
          axisTick: {
            alignWithLabel: true,
          },
          axisLine: {
            lineStyle: {
              color: "#9498b0s",
            },
          },
          axisLabel: {
            margin: 20,
            textStyle: {
              fontSize: 14,
            },
          },
        },
        yAxis: {
          type: "value",
          splitLine: {
            show: true,
            lineStyle: {
              type: "dashed",
            },
          },
          axisLabel: {
            margin: 20,
            textStyle: {
              fontSize: 12,
            }
          }
        },
        tooltip: {
          trigger: "axis",
        },
        series: [
          {
            name: "水费:",
            data: chartData.value.waterData,
            type: "line",
            color: "#3ba1ff",
            smooth: true,
            symbol: "circle",
          },
          // {
          //   name: "电费:",
          //   data: chartData.value.electricData,
          //   type: "line",
          //   color: "#2fc25b",
          //   smooth: true,
          //   symbol: "circle",
          // },
        ],
      };
      // 使用刚指定的配置项和数据显示图表。
      // myChart.setOption(option);
      nextTick(() => {
        myChart.resize();
        myChart.setOption(option, true);
      });
    };
        // 监听父组件传递的参数变化
    watch(() => props.queryDate, () => {
      if (props.queryDate) {
        fetchYearCostData();
      }
    }, { deep: true });
    //挂载
    onMounted(() => {
      if (props.queryDate) {
        fetchYearCostData();
      } else {
        echartInit();
      }
    });
    return {
      echartInit,
      fetchYearCostData
    };
  },
};
</script>
<style lang="scss" scoped>
.echartDiv {
  height: 350px;
}
</style>
