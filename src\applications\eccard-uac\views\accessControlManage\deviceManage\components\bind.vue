<template>
  <el-dialog :model-value="modelValue" title="待绑定设备" width="1000px" :before-close="handleClose">
    <div style="padding:10px 20px">
      <kade-select-table :isShow="modelValue" :value='[]' :column="column" :selectCondition="selectCondition"
        :params="params" :reqFnc="getUnBindAccessDeviceList" :isMultiple="true" :isCurrentSelect="true"
        @change="handleChange" />
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="submit()" type="primary" size="mini" :loading="state.loading">保&nbsp;&nbsp;存</el-button>
        <el-button @click="handleClose()" size="mini">返&nbsp;&nbsp;回</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script>
import { onMounted, watch } from "vue"
import { getUnBindAccessDeviceList, addRoomDevice } from "@/applications/eccard-uac/api";
import { getModel,iotCfg } from "@/applications/eccard-iot/api";
import { ElDialog, ElButton, ElMessage } from "element-plus"
import selectTable from "@/components/table/selectTable";
import { reactive } from '@vue/reactivity';
const column = [
  { label: "终端型号", prop: "deviceModel" },
  { label: "所属区域", prop: "areaName" },
  { label: "设备机号", prop: "deviceNo" },
  { label: "设备名称", prop: "deviceName" },
  { label: "门号", prop: "doorName" },
  { label: "设备IP", prop: "deviceIP" },
  { label: "设备类型", prop: "deviceTypeName" },
];
export default {
  components: {
    ElDialog,
    ElButton,
    "kade-select-table": selectTable,
  },
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default: null
    }
  },
  setup(props, context) {
    const state = reactive({
      loading: false,
      deviceList: [],
    })
    const selectCondition = [
      {
        label: "型号",
        valueKey: "deviceModel",
        placeholder: "请选择",
        isSelect: true,
        select: {
          list: [],
          option: { label: "productMode", value: "productMode" },
        },
      },
      {
        label: "机号",
        valueKey: "deviceNo",
        placeholder: "请输入",
        isSelect: false,
      },
      {
        label: "IP地址",
        valueKey: "deviceIp",
        placeholder: "请输入",
        isSelect: false,
      },
      {
        label: "设备类型",
        valueKey: "deviceType",
        placeholder: "请选择",
        isSelect: true,
        select: {
          list: [],
          option: { label: "cfgValue", value: "cfgKey" },
        },
      },
    ];
    const params = {
      currentPageKey: "currentPage",
      pageSizeKey: "pageSize",
      resListKey: "list",
      resTotalKey: "total",
      value: {
      },
      tagNameKey: "deviceName",
      valueKey: "id",
    };
    watch(() => props.modelValue, (val) => {
      if (val) {
        params.value.areaId = props.data.areaId
      }
    })
    //获取终端型号
    const queryModel = async () => {
      let { data } = await getModel();
      selectCondition[0].select.list = data;
    };
    const getDeviceTypeList = async () => {
      let { data } = await iotCfg({ cfgType: 'ACCESS_DEVICE_TYPE' });
      selectCondition[3].select.list = data
    }
    const handleChange = (val) => {
      console.log(val);
      state.deviceList = val.list
    }
    const submit = async () => {
      console.log(props.data);
      if (!state.deviceList.length) {
        return ElMessage.error("请选择设备！")
      }
      let params = {
        areaId: props.data.areaId,
        buildId: props.data.buildId ? props.data.buildId : 0,
        floorNum: props.data.floorNum ? props.data.floorNum : 0,
        roomId: props.data.roomId ? props.data.roomId : 0,
        unitNum: props.data.unitNum ? props.data.unitNum : 0,
        nodeType: props.data.type,
        deviceList: state.deviceList
      }
      state.loading = true
      try {
        let { code, message } = await addRoomDevice(params)
        if (code === 0) {
          ElMessage.success(message)
          context.emit("modelValue:update", true)
        }
        state.loading = false
      }
      catch {
        state.loading = false
      }
    }
    const handleClose = () => {
      context.emit("modelValue:update", false)
    }
    onMounted(() => {
      queryModel()
      getDeviceTypeList()
    })
    return {
      getUnBindAccessDeviceList,
      column,
      selectCondition,
      params,
      state,
      handleChange,
      submit,
      handleClose
    }
  },
}
</script>