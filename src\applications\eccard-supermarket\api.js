import request from '@/service';

/**
 * 查询菜品标签列表
 */

export function labelList(params) {
  return request.post('/eccard-finance/dishLabel/labelList', params);
}
/**
 * 新增/修改菜品标签
 */

export function saveLabel(params) {
  return request.post('/eccard-finance/dishLabel/saveLabel', params);
}

/**
 * 删除菜品标签
 */
export function deleteLabel(params) {
  return request.post('/eccard-finance/dishLabel/deleteLabel', params);
}

/**
 * 查询菜品列表
 */
export function dishList(params) {
  return request.post('/eccard-finance/dish/dishList', params);
}
/**
 * 新增/修改菜品
 */
export function saveDish(params) {
  return request.post('/eccard-finance/dish/saveDish', params);
}
/**
 * 查询菜品详情
 */
export function dishDetails(params) {
  return request.get('/eccard-finance/dish/dishDetails', { params });
}

/**
 * 查询设备已绑定菜品
 */
 export function bindDishList(params) {
  return request.get('/eccard-finance/deviceDish/bindDishList', { params });
}
/**
 * 设备绑定菜品
 */
 export function deviceBindDish(params) {
  return request.post('/eccard-finance/deviceDish/deviceBindDish', params);
}

/**
 * 餐段参数列表
 */
 export function mealPeriodList(params) {
  return request.get('/eccard-finance/mealPeriod', { params });
}
/**
 * 餐段参数修改
 */
 export function mealPeriodEdit(params) {
  return request.put('/eccard-finance/mealPeriod', params);
}